<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WeMa IA - Debug</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: #4a9eff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
        }
        h1 {
            color: #4a9eff;
            margin-bottom: 10px;
        }
        .status {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .btn {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #357abd;
        }
        .logs {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1>WeMa IA - Mode Debug</h1>
        <p>Interface de diagnostic et de test</p>
        
        <div class="status">
            <h3>État du système</h3>
            <p id="backend-status">🔄 Vérification du backend...</p>
            <p id="api-status">🔄 Test de l'API...</p>
        </div>
        
        <button class="btn" onclick="testBackend()">Tester Backend</button>
        <button class="btn" onclick="openMainApp()">Ouvrir App Principale</button>
        <button class="btn" onclick="clearLogs()">Effacer Logs</button>
        
        <div class="logs" id="logs">
            <div>🚀 WeMa IA Debug Mode - Démarrage...</div>
            <div>📡 Connexion au backend en cours...</div>
        </div>
    </div>

    <script>
        let logs = [];
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            updateLogs();
        }
        
        function updateLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            updateLogs();
            addLog('🧹 Logs effacés');
        }
        
        async function testBackend() {
            addLog('🔍 Test du backend...');
            try {
                const response = await fetch('http://localhost:5001/health');
                if (response.ok) {
                    const data = await response.json();
                    addLog(`✅ Backend OK - Status: ${data.status}, Port: ${data.port}`);
                    document.getElementById('backend-status').innerHTML = '✅ Backend connecté';
                    document.getElementById('api-status').innerHTML = '✅ API fonctionnelle';
                } else {
                    addLog(`❌ Backend erreur: ${response.status}`);
                    document.getElementById('backend-status').innerHTML = '❌ Backend déconnecté';
                }
            } catch (error) {
                addLog(`❌ Erreur connexion: ${error.message}`);
                document.getElementById('backend-status').innerHTML = '❌ Backend inaccessible';
            }
        }
        
        function openMainApp() {
            addLog('🚀 Redirection vers app principale...');
            window.location.href = 'index.html';
        }
        
        // Test automatique au chargement
        window.addEventListener('load', () => {
            addLog('🎯 Interface debug chargée');
            setTimeout(testBackend, 1000);
        });
        
        // Test périodique
        setInterval(testBackend, 30000);
    </script>
</body>
</html>
