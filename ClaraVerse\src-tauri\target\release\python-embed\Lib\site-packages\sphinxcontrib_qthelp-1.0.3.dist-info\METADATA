Metadata-Version: 2.1
Name: sphinxcontrib-qthelp
Version: 1.0.3
Summary: sphinxcontrib-qthelp is a sphinx extension which outputs QtHelp document.
Home-page: http://sphinx-doc.org/
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Download-URL: https://pypi.org/project/sphinxcontrib-qthelp/
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Framework :: Sphinx
Classifier: Framework :: Sphinx :: Extension
Classifier: Topic :: Documentation
Classifier: Topic :: Documentation :: Sphinx
Classifier: Topic :: Text Processing
Classifier: Topic :: Utilities
Requires-Python: >=3.5
Provides-Extra: lint
Requires-Dist: flake8 ; extra == 'lint'
Requires-Dist: mypy ; extra == 'lint'
Requires-Dist: docutils-stubs ; extra == 'lint'
Provides-Extra: test
Requires-Dist: pytest ; extra == 'test'


sphinxcontrib-qthelp is a sphinx extension which outputs QtHelp document.


