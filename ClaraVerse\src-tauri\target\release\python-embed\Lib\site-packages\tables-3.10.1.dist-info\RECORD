../../Scripts/pt2to3.exe,sha256=WwHFgTJxURUJnklAo3PIGPNNXfGsD3nD_tjxI-WzHgM,108424
../../Scripts/ptdump.exe,sha256=bWOX-ZeAQAtEssp8WisfYZKdvo5xX8-G6pODh4Hq6cg,108424
../../Scripts/ptrepack.exe,sha256=0agzNu9bC64HIPeshPX5BcIXph2hbX1ieEPKIztwCWE,108426
../../Scripts/pttree.exe,sha256=2ygkN0z0a7JZM4cSuNSM0821i_osTXIpuioteq-U1PY,108424
tables-3.10.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
tables-3.10.1.dist-info/LICENSE.txt,sha256=SUbPclnoJI-rDNRXlzDp0CDfbrjGoPOflxuymYv2jOw,1721
tables-3.10.1.dist-info/METADATA,sha256=dEzPBwGP8aAeSuK67EHcrHPIU1O9r4hjaJp_JRbWqH4,2270
tables-3.10.1.dist-info/RECORD,,
tables-3.10.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tables-3.10.1.dist-info/WHEEL,sha256=KNRoynpGu-d6mheJI-zfvcGl1iN-y8BewbiCDXsF3cY,101
tables-3.10.1.dist-info/direct_url.json,sha256=pYCZwczekfpLsgZRhIPvuGzDQMUjjfaBxRobO2NWuw0,88
tables-3.10.1.dist-info/entry_points.txt,sha256=h42qnqwrimQr-JQ1gZCM9SNGq4jH7dh45VZlw3r6VHU,166
tables-3.10.1.dist-info/top_level.txt,sha256=m3W6sioRFzYyZByXzdell41O7YB9Kbe8ROEgs8Lyh6A,7
tables/__init__.py,sha256=CJQ-e2VcWpl5BvAHyUgnan4f_TEbFtitn3wAD_tAKRI,5571
tables/__pycache__/__init__.cpython-312.pyc,,
tables/__pycache__/_version.cpython-312.pyc,,
tables/__pycache__/array.cpython-312.pyc,,
tables/__pycache__/atom.cpython-312.pyc,,
tables/__pycache__/attributeset.cpython-312.pyc,,
tables/__pycache__/carray.cpython-312.pyc,,
tables/__pycache__/conditions.cpython-312.pyc,,
tables/__pycache__/description.cpython-312.pyc,,
tables/__pycache__/earray.cpython-312.pyc,,
tables/__pycache__/exceptions.cpython-312.pyc,,
tables/__pycache__/expression.cpython-312.pyc,,
tables/__pycache__/file.cpython-312.pyc,,
tables/__pycache__/filters.cpython-312.pyc,,
tables/__pycache__/flavor.cpython-312.pyc,,
tables/__pycache__/group.cpython-312.pyc,,
tables/__pycache__/idxutils.cpython-312.pyc,,
tables/__pycache__/index.cpython-312.pyc,,
tables/__pycache__/indexes.cpython-312.pyc,,
tables/__pycache__/leaf.cpython-312.pyc,,
tables/__pycache__/link.cpython-312.pyc,,
tables/__pycache__/node.cpython-312.pyc,,
tables/__pycache__/parameters.cpython-312.pyc,,
tables/__pycache__/path.cpython-312.pyc,,
tables/__pycache__/registry.cpython-312.pyc,,
tables/__pycache__/req_versions.cpython-312.pyc,,
tables/__pycache__/table.cpython-312.pyc,,
tables/__pycache__/undoredo.cpython-312.pyc,,
tables/__pycache__/unimplemented.cpython-312.pyc,,
tables/__pycache__/utils.cpython-312.pyc,,
tables/__pycache__/vlarray.cpython-312.pyc,,
tables/_comp_bzip2.cp312-win_amd64.pyd,sha256=NflQotfQ-IOnw-FK9c45g2aIwtdySNlCzpLrXnF2JFI,36352
tables/_comp_bzip2.pyx,sha256=v4Lz4KSyv347LgV408KdQGSLIlHlIHlIIB4WuUBegto,375
tables/_comp_lzo.cp312-win_amd64.pyd,sha256=S75RltRaXsatyv9gQHZa656tL8eXUEmzi6ZpyhY2aUM,39936
tables/_comp_lzo.pyx,sha256=wJxoYaEM9W6xSzuodMRfj7M2NWU5J3JxmI9-saK-e6o,369
tables/_version.py,sha256=p2FCs7u7_r-kjViblniz7yF3GLwwZ_C3n3LhMcYsfQE,58
tables/array.py,sha256=ifoJ1Tz88HJ6j4zPpV6Dc8t006sOMgHa65Fk7N3utc8,37665
tables/atom.py,sha256=YbUDGWvRhImt1m-ZxeMcuvgGisS3ITff4SbV7VPK7WQ,45206
tables/attributeset.py,sha256=-PUo2MjaAJJW1xQ_GEOf145WpnnRRCBUWr9-X5XZB9U,26591
tables/carray.py,sha256=1HXMgrA5VajC-5VW2VlfHrZCWoBAgop8pv8AS8sbsPk,11285
tables/conditions.py,sha256=dHqRG2riFr6gE4ly0A0tW75yPInY1hmsIj-Nine2B9A,17957
tables/definitions.pxd,sha256=Lf5lJ2XgwqlghLG3qiBYvbANcbLl1ONa7KHkxmr1C24,22441
tables/description.py,sha256=K4MtmW1dGzGCONufgUsyoiD7X_O-WZsz1MDeta0PJw0,40304
tables/earray.py,sha256=POFGBJstw09kE21sGSCyDG-hPRWh6qZQWCM-pP4z-tw,10343
tables/exceptions.py,sha256=H2VHok3DT806ZZJD5wBBD0aafDFWC2HHT2CtYfUpxoo,12794
tables/expression.py,sha256=wM1kjyhFJjMh9zg0SqfqiuWN7zSIK4u8261dPOmLTJk,29237
tables/file.py,sha256=-Ernv0F0fcgR0Ih5uHF5J79HSLP8fjzrDH4xfEGdunU,111254
tables/filters.py,sha256=p0lFVeflHw0NTgSejdLnLp_qbuHwE7ATvEJItZ1OIDU,16897
tables/flavor.py,sha256=XiAPQBKZpslvW3V7R17lLAfMdJFz2-f8DCsr82xHLG0,14349
tables/group.py,sha256=BIvdVIhtBKCT9zzI0gCZbRPyvakLq-Xbo3_XEQmExaY,49253
tables/hdf5extension.cp312-win_amd64.pyd,sha256=s9fsD6LT4DNRma7g_BY9eL23UaPLYAXfa0EOFccVATQ,400384
tables/hdf5extension.pxd,sha256=Vl-Ld9Os-J8ORCa9LLQj7ub-oxRuXr-zPs78J3QxMfU,989
tables/hdf5extension.pyx,sha256=767QMx5MmIu1lre4KoJW58xO8MuSzVam8kNzRY5sQQc,78972
tables/idxutils.py,sha256=_x9D3A60uxnvqPZ1uGOjIx6uqLNw12Ro9LDVU5YpOHc,17313
tables/index.py,sha256=x67KxcXpXlwY3X3XuebfKgyYz1ruziqQQ8As4elpbew,94005
tables/indexes.py,sha256=t2-Y2oMFCXMzmsg0--ifVF-6ZGL-CXMpB9wLv3rTAT8,6219
tables/indexesextension.cp312-win_amd64.pyd,sha256=N8P0qumplFqfkR0I8ZWTNz2nOxam2jmzevzUyO48y1Q,172544
tables/indexesextension.pyx,sha256=4aWT9nPdBsa_5UOXJy2fgbbwYfkMUgXzXucoKu6kOs0,49058
tables/leaf.py,sha256=DAXyHknzSKTVGqy4XVkFPC5SKbM5xtuNhykWQCB6g10,38203
tables/link.py,sha256=c80LIuBhNpr9whJa0phtgfvX5yTruQwRA-kMS7DedGk,13849
tables/linkextension.cp312-win_amd64.pyd,sha256=WfLhJwb2vhLAD32QXrqJICKjYdzQVo9WR8QFkAKRM3M,98816
tables/linkextension.pyx,sha256=3a0fX9kSJH3q8z_RdgzXRxlQHyEmORLQX-5IJWfS7-E,8212
tables/lrucacheextension.cp312-win_amd64.pyd,sha256=FWSkEUZwjFkkOdl5UAVL76JGBQ416Jy_2NCVhgsNcT4,129024
tables/lrucacheextension.pxd,sha256=_z8D4KXwxMGM7MP71E5xefMml7Q-7RMe2nUVf2b4zCg,2359
tables/lrucacheextension.pyx,sha256=9s_5KYp49qiRMUTlDMdMfG66W0nMgBLzjX9zjXF6rms,21614
tables/misc/__init__.py,sha256=V0q4zftKtuik-xRjvzEuhyeuvxVPB7n01arMdztGH5A,221
tables/misc/__pycache__/__init__.cpython-312.pyc,,
tables/misc/__pycache__/enum.cpython-312.pyc,,
tables/misc/__pycache__/proxydict.cpython-312.pyc,,
tables/misc/enum.py,sha256=C5icWlfXn9TLtQJ134PQy6bGeHyBkjqCQxIV1SdDCjA,13480
tables/misc/proxydict.py,sha256=aaPBQrq6ALwerptw1H0YiGXi5DaMR_nZDWBBLJBsP14,1851
tables/node.py,sha256=KlKD1_TRw_JnhG-6clTS4RaZIBZh6WZGj9Fg5udifSs,33291
tables/nodes/__init__.py,sha256=OMcIceEMcU2GqSTJZDRN9Kc4TA6b-AIOLilboS7Q1AY,382
tables/nodes/__pycache__/__init__.cpython-312.pyc,,
tables/nodes/__pycache__/filenode.cpython-312.pyc,,
tables/nodes/filenode.py,sha256=sH272sY0BNjIsLMKuyHVfoeZzb0E5PaETQb76DtGVD8,27128
tables/nodes/tests/__init__.py,sha256=EI_s1Izmrr3-IOIur1TCG3bTHTlpHxauQ4xg0aWB5V0,46
tables/nodes/tests/__pycache__/__init__.cpython-312.pyc,,
tables/nodes/tests/__pycache__/test_filenode.cpython-312.pyc,,
tables/nodes/tests/test_filenode.dat,sha256=quCxJVOxZgKEP-j2_aCp4e9q2ludR_h90bfxri9y4OM,3282
tables/nodes/tests/test_filenode.py,sha256=buX7kz0A2DsjqKwe9Oacg9o2NM8hnBLyGeJq7Xs8eUA,34415
tables/nodes/tests/test_filenode.xbm,sha256=quCxJVOxZgKEP-j2_aCp4e9q2ludR_h90bfxri9y4OM,3282
tables/nodes/tests/test_filenode_v1.h5,sha256=z_Gi0CPy_OHMjigm2FHPeE935-FNv0A58aQtPHv3VpQ,9062
tables/parameters.py,sha256=ParBOum5DNbAjVIs-3EyK81zbvLYDQYmp8J-G78veAU,14751
tables/path.py,sha256=xeMkgCM3PgglXTnhvUv3XIGCMQy8ELn3Kp_L3io_vtQ,6733
tables/registry.py,sha256=fdwggKq9GSMRa8_W4NMY0HkRqYxGAunZ4jh_0Q9q3g8,2331
tables/req_versions.py,sha256=umdV0Iky-vCEJjudEFpl3sFLROSM5tVzwd2y4qgmPFc,630
tables/scripts/__init__.py,sha256=JjWz1dumYE4fFfgzRero-suSzhA5oPL0fsUFqNRhRXY,168
tables/scripts/__pycache__/__init__.cpython-312.pyc,,
tables/scripts/__pycache__/pt2to3.cpython-312.pyc,,
tables/scripts/__pycache__/ptdump.cpython-312.pyc,,
tables/scripts/__pycache__/ptrepack.cpython-312.pyc,,
tables/scripts/__pycache__/pttree.cpython-312.pyc,,
tables/scripts/pt2to3.py,sha256=aF8A8K1cvVizMIlpM1xNLmHGn70LK19ctL0gKUmjlCU,23112
tables/scripts/ptdump.py,sha256=98H7XJEWd0CfZ2lFILfnl0gsinzBTlNnaneaqCaIf_0,5355
tables/scripts/ptrepack.py,sha256=x4tlRvxuNPemIVoPawZExwHTw440pyFuWsZjuao6nvI,22884
tables/scripts/pttree.py,sha256=wOPRYPon_3jwlf_KC6Z7cOh8dsD0OBYeXg1sj8P_R58,15294
tables/table.py,sha256=Wpqca8BTjU2OACneBZZKK-RgePyV65qn7Q64AkxHKYs,156385
tables/tableextension.cp312-win_amd64.pyd,sha256=JNfasLqKhk4sV6yEv6OAGhrmBLmIOwsvB55QPTIdA18,250880
tables/tableextension.pyx,sha256=Shnk4J9CLIWBKo5oejeagBFa4xazAsjcqbwSCqSBmzY,63108
tables/tests/Table2_1_lzo_nrv2e_shuffle.h5,sha256=7dHYCFHgzaMW2-vjztBny4g2jhCGl_ZRENev7i4_7VM,19206
tables/tests/Tables_lzo1.h5,sha256=1-qIKgkRIGXltiW-AErSkYRjb68dwbjmDIZu6XxYXfE,23363
tables/tests/Tables_lzo1_shuffle.h5,sha256=SLhi7MtxxuFzY5yym9ftaDKWou2TyEYcboDCB_5_4o8,21097
tables/tests/Tables_lzo2.h5,sha256=NTWN31-mBuWZbmroozN1foN5LYlkNVnvxmMmx7mYPqw,23398
tables/tests/Tables_lzo2_shuffle.h5,sha256=F4EhtzIptQQuVHEj5qC8O6cf4GoDoS3iBCjw4_0GUUc,21097
tables/tests/__init__.py,sha256=TEuSaCFqmblx1HRJiYUlGBNFSyr3A7ijOY7Vou6pZ9k,340
tables/tests/__pycache__/__init__.cpython-312.pyc,,
tables/tests/__pycache__/check_leaks.cpython-312.pyc,,
tables/tests/__pycache__/common.cpython-312.pyc,,
tables/tests/__pycache__/create_backcompat_indexes.cpython-312.pyc,,
tables/tests/__pycache__/test_all.cpython-312.pyc,,
tables/tests/__pycache__/test_array.cpython-312.pyc,,
tables/tests/__pycache__/test_attributes.cpython-312.pyc,,
tables/tests/__pycache__/test_aux.cpython-312.pyc,,
tables/tests/__pycache__/test_backcompat.cpython-312.pyc,,
tables/tests/__pycache__/test_basics.cpython-312.pyc,,
tables/tests/__pycache__/test_carray.cpython-312.pyc,,
tables/tests/__pycache__/test_create.cpython-312.pyc,,
tables/tests/__pycache__/test_direct_chunk.cpython-312.pyc,,
tables/tests/__pycache__/test_do_undo.cpython-312.pyc,,
tables/tests/__pycache__/test_earray.cpython-312.pyc,,
tables/tests/__pycache__/test_enum.cpython-312.pyc,,
tables/tests/__pycache__/test_expression.cpython-312.pyc,,
tables/tests/__pycache__/test_garbage.cpython-312.pyc,,
tables/tests/__pycache__/test_hdf5compat.cpython-312.pyc,,
tables/tests/__pycache__/test_index_backcompat.cpython-312.pyc,,
tables/tests/__pycache__/test_indexes.cpython-312.pyc,,
tables/tests/__pycache__/test_indexvalues.cpython-312.pyc,,
tables/tests/__pycache__/test_large_tables.cpython-312.pyc,,
tables/tests/__pycache__/test_links.cpython-312.pyc,,
tables/tests/__pycache__/test_lists.cpython-312.pyc,,
tables/tests/__pycache__/test_nestedtypes.cpython-312.pyc,,
tables/tests/__pycache__/test_numpy.cpython-312.pyc,,
tables/tests/__pycache__/test_queries.cpython-312.pyc,,
tables/tests/__pycache__/test_suite.cpython-312.pyc,,
tables/tests/__pycache__/test_tables.cpython-312.pyc,,
tables/tests/__pycache__/test_tablesMD.cpython-312.pyc,,
tables/tests/__pycache__/test_timestamps.cpython-312.pyc,,
tables/tests/__pycache__/test_timetype.cpython-312.pyc,,
tables/tests/__pycache__/test_tree.cpython-312.pyc,,
tables/tests/__pycache__/test_types.cpython-312.pyc,,
tables/tests/__pycache__/test_utils.cpython-312.pyc,,
tables/tests/__pycache__/test_vlarray.cpython-312.pyc,,
tables/tests/array_mdatom.h5,sha256=yI2XaJ3l_ufBtIBUzwZMTkRRFP8GK7YrKKY_t_SHDOc,5150
tables/tests/attr-u16.h5,sha256=ZBZglj-y-SfSXILM0wQv6bQ415cFFRQi0aVlz_Ah9eY,28782
tables/tests/b2nd-no-chunkshape.h5,sha256=OVRQCTXa2FVvtqnhGt47UXKCg6CjvG2Ob0j8nOgMVMY,5168
tables/tests/blosc_bigendian.h5,sha256=V2rYecoLpXsfizabzZth9kqaadjH8XeKGhT7AMSuEBc,11974
tables/tests/bug-idx.h5,sha256=tRjZIKEKcEpliZBWvCBLRUczIWs5ixSr3uhjiy-M6VU,14649
tables/tests/check_leaks.py,sha256=reOwg3jtUpUQX6k4-APmZIPWwq6r-gajeyBRTMIWpds,12032
tables/tests/common.py,sha256=_SXY66dmsSDfc3KhvOXSdbmcDEpua6X2-CDhxI8Ak1w,12250
tables/tests/create_backcompat_indexes.py,sha256=1n7yJuVaquoK6GbbVOHr1UG2XlCWsIRrD4MeYIB6vH4,1167
tables/tests/elink.h5,sha256=E87HIdLeiE-a2ngwztUC8PaZp29Q1v4AuSovyudUM6s,3550
tables/tests/elink2.h5,sha256=TAT75aaJca7JxIyNk9QYsAsCXWAkDJHn6lSyXuNtSe8,2238
tables/tests/ex-noattr.h5,sha256=D1eYQLISzBQDZmT1GjcIWcNKstxK0iyEunCvH5okIGo,12342
tables/tests/flavored_vlarrays-format1.6.h5,sha256=C7SqiBCqh610efYIT95FuWk2psZnmRxAV2FKrqHPMv8,12621
tables/tests/float.h5,sha256=B4scBb4HkRyT-NrWhMAsQNwx-NXtPc7vWUqL3DWbGyw,4742
tables/tests/idx-std-1.x.h5,sha256=hrFJRX7SfCs5puOhQH9OAFRbWlwnzNHPOy6DQ7UcyDs,26662
tables/tests/indexes_2_0.h5,sha256=ItdvkzBQUfnUkp_0A_9F6jtRy8WzQe71wBr0urO3lBs,60801
tables/tests/indexes_2_1.h5,sha256=NrkKELb0wBYzDm_MaelYRzQZ0K4wbYtHKJAP8Kmz4fE,147256
tables/tests/issue_368.h5,sha256=xN9zpKKPKkzCtCY1ymERdMpOFM3D-ekW62gFPLHhTu4,1232
tables/tests/issue_560.h5,sha256=wkmMOuRm6U5QTHftw4WdHR-exjP6YnDzf9bhJ9SANA0,2344
tables/tests/itemsize.h5,sha256=q_I3NP5tyl7XwDNMmcjlhtOubgirV0cUIOTkNzeUw60,2096
tables/tests/matlab_file.mat,sha256=82TEze2TcREplHrC30e_9qYgvA9gJ1Pq_2FghR1bxcE,1942
tables/tests/nested-type-with-gaps.h5,sha256=mZyufnF-OyTPolUfhGoBKmsDEt0uzikS_tWbN7tF2ms,1830
tables/tests/non-chunked-table.h5,sha256=Ysdo_pXT97JvNlY_CKTMfC4ynPWJV3liWKRG2G2ySx0,6184
tables/tests/oldflavor_numeric.h5,sha256=w680ov-6uccul5UX6A0aPJcwA3wUHQrQYG55eYUOjo8,112296
tables/tests/out_of_order_types.h5,sha256=lN1j642UrQXdF8NI6UbOzVYbApvrVuMnFxtRJwwnD10,71001
tables/tests/python2.h5,sha256=CA6vtG47XsHHRIYdSLwrtJOs7QRgpbKOcV5Tlp_umTY,79658
tables/tests/python3.h5,sha256=Ol-OePDlUJ2NRPoXafvCcrtGnhjpg-qZ06GiaJ8IQ3I,79658
tables/tests/scalar.h5,sha256=pcl25oxrtALDINAEWoU320_1bJSZSL7IiDbrprnmTRM,8294
tables/tests/slink.h5,sha256=ujUfyrlVaxusn5GARiDvo-jn0bAGYAKd8FAaUobHfco,5502
tables/tests/smpl_SDSextendible.h5,sha256=Cfn2UXNzm_ueLU_y-sSZnXRr7a2yGv-YerYkgaFL8qc,6246
tables/tests/smpl_compound_chunked.h5,sha256=tezHxr8yvoC-VcGzip7wLpTxCzIxhZshSoapyyzGy_0,5774
tables/tests/smpl_enum.h5,sha256=1P4oyQw2K3_m2CBjFUNTRGhuYgg-iDbrVzruug5qiJY,2094
tables/tests/smpl_f64be.h5,sha256=npUgBxMhRqjnDHzQrBZHVLUCsvw6f22mVa0cBW5Spss,2294
tables/tests/smpl_f64le.h5,sha256=u2WYVtfssgU9RaVWG1FCVj4U-kSRTVJfYL_Cb8ykyUE,2294
tables/tests/smpl_i32be.h5,sha256=gVZaKpYEKqrQuXssQTS0KuxxyzlBRaDtH-gEUhiVtoo,2174
tables/tests/smpl_i32le.h5,sha256=ZB2aInSE3SIwWzdGIdpphkI9KQ4CTiZWNCzQqvPmRrY,2174
tables/tests/smpl_i64be.h5,sha256=iYRlTC6OeU9oQsFR3tOEHPmcHXh7s-gMnnmzdzYGTZs,2294
tables/tests/smpl_i64le.h5,sha256=JYaXS3zTrAiaxmSpuYO3bzwKE5NzqWdtYee2NBXO4m4,2294
tables/tests/smpl_unsupptype.h5,sha256=GRiBHqqhS2Pub6q-lWOAc1L9Uj4nHbU6qngfymRZ2sY,11870
tables/tests/test_all.py,sha256=fjz0XRC66DFSH5lYn4vNITmomXOQ3pDbpSKBkmtHpPY,1744
tables/tests/test_array.py,sha256=gt-jnT_i8AGUN2kkU74-RBoMx31nRgarixD09tZqY2M,95060
tables/tests/test_attributes.py,sha256=CAyGlfbK3ikBB4hAuK-iY8A2VAe_5b2dfVuAgHsrlc0,70901
tables/tests/test_aux.py,sha256=TxipS4MQIbA7275IDnLO7YvwKz92aLTuQVanGKbjy04,885
tables/tests/test_backcompat.py,sha256=Q3lfLh_1vmMVddZRpc-9Hl3biFSxEpq8UC1nMTud8SU,8363
tables/tests/test_basics.py,sha256=DjwMy2Ukz-eztyLZWpwlXKDLp02AndlV_aVzYBwdkrs,93674
tables/tests/test_carray.py,sha256=0p6hf8gIH31gZupmXmIK22ksdqkXj5SvstKD6BHXu1k,103392
tables/tests/test_create.py,sha256=9at6f3HK9GkjmvcFcA2S44JIql6Cx26J3MyzrQY1bhQ,98593
tables/tests/test_direct_chunk.py,sha256=JuRHTO5d8cTl0-z7_wbyL7LG-5Mwfc384sYi3rOlJ-c,14020
tables/tests/test_do_undo.py,sha256=sweTHysyp_kAJZ7BW5nFCa9jYckpfJ9Jxr7FImSa7FQ,98061
tables/tests/test_earray.py,sha256=F2GtoF-j6kgMi1PLmJHxCyS3OgjXxch0zhB5_Y93bss,105158
tables/tests/test_enum.py,sha256=L09QvLJauTwco4Ef-Mm6uTkqkHUS4l4dfs7sArWS90o,22209
tables/tests/test_expression.py,sha256=zW27O2-A1cV4ovDEopyzsz4P1_HK362yAAJ9i4a2-Po,56401
tables/tests/test_garbage.py,sha256=gnIH9ZyoQkYElsbC14XUQNHbbGUo_je-hBz8-la0bmQ,1498
tables/tests/test_hdf5compat.py,sha256=sHwJgBRn0sBTVTYDJMvOBBTSJqfVE04uc4-VCAGQ4kA,13314
tables/tests/test_index_backcompat.py,sha256=MBIqYML3JdFE_gJicU9JZDANAX7JMBBGxs39kZVp1AM,5379
tables/tests/test_indexes.py,sha256=YUvB_LLlf0Twx84o1YKRP2v_pI8lSgEcOez8WyzbDug,100389
tables/tests/test_indexvalues.py,sha256=m2w_MSVpA2yAUtdQ0bsDWTF9Ws8TcfUL65AqMxGlKHk,122307
tables/tests/test_large_tables.py,sha256=Kxz3CDabhxRIxQnGjsTm5CjKwoCnjh6SM1u_47XWOpY,3036
tables/tests/test_links.py,sha256=2YGh0zbRVfUBkEq4rkhds8p3BTcCdNqRqGkILSK7thY,23307
tables/tests/test_lists.py,sha256=FjVKRTHV-mEcy8JTRgrPKfXXtox5fM6Ivd2-G1ZnjA4,15043
tables/tests/test_nestedtypes.py,sha256=WDk5tcqV_9PTeXIrBWvElsEK7Y8XVzTnXwgHqrCZB44,52543
tables/tests/test_numpy.py,sha256=caH19VXZThxqDLJI5mUZsq_XYNK-83nxseG_k-d7xq8,50816
tables/tests/test_queries.py,sha256=tb63Bi4RaKlMjYbrfe5j47DFbUnAWjFfT2bICiuIBsU,44097
tables/tests/test_ref_array1.mat,sha256=rrV46WkkhAbyRo0Y6s2w-eQYxYkUVARMzf1nqlRLKB4,16192
tables/tests/test_ref_array2.mat,sha256=pn9dUPhy9NZlxlnjfKEWZGCKVgP5GCVEM3hX_Kj9lwA,4832
tables/tests/test_suite.py,sha256=D_E1UL7ce_yvhRiIl1pQUWaYNiLqt9pZWQZkL3NuUho,3123
tables/tests/test_szip.h5,sha256=mQUuflKOjGgBJGfn-42GXPGd1xwt1W48WDs2cz5ecaY,5594
tables/tests/test_tables.py,sha256=B4m2sMxJCM-ChPblQln3axcC_ZJjxNsBBbRovnA6gLM,271456
tables/tests/test_tablesMD.py,sha256=eiHvL9a0N-cBGSO1WWPAhyf7OYNFqi9ATTf06bJ-wmA,79850
tables/tests/test_timestamps.py,sha256=R3ohHBf_ZAHBVtjHwiiVGsPHwzBXRD8cFymhhSgghog,6109
tables/tests/test_timetype.py,sha256=StQhle7vV-JpPrHUer-cKznURxC5luwlHFlsPAdvT_k,17679
tables/tests/test_tree.py,sha256=_3toF1WCiN3a6CrvCV0BobwaQKdejBC1hzE9uHWN5lA,40536
tables/tests/test_types.py,sha256=wcTa1CY3Uo0mb0PTL61WhUF9Ydb_U6Z5dFzlP2Fe7_E,11998
tables/tests/test_utils.py,sha256=sk1IMyrYjE_1kyOg84Bsa2jNCOadtu5g-F54ek0TIx8,2936
tables/tests/test_vlarray.py,sha256=5PS5u-cLTMmlrCOGcj5p6p73IUvTZ9S3Z26P5jijYWY,158832
tables/tests/time-table-vlarray-1_x.h5,sha256=hYTomgMXpmvI5dVqMNqoj49GpmiHJy2yJ37RYOJOd9w,3766
tables/tests/times-nested-be.h5,sha256=jcri1G2O7Qz94eFc0O9hPvVRJl2pWJgvKkVOuWNIDb0,22674
tables/tests/vlstr_attr.h5,sha256=zuqgBjcpz9sMcrdpyRjxKUV-QTS1MXuobb10NL8sVmg,5294
tables/tests/vlunicode_endian.h5,sha256=Xc91gNHoLArSszzCGWMd1eeq4wqsssllZuu5tVMVe94,82022
tables/tests/zerodim-attrs-1.3.h5,sha256=sDUqt8zs26wKePl7B3ZYZ07YImQXrho6jfBvBm2D_8E,5102
tables/tests/zerodim-attrs-1.4.h5,sha256=2jMOHUsizyI6Gsxew4bfADKulFBEFIY1Nelf5nTdv20,4366
tables/undoredo.py,sha256=nd2Hskh4qsPxA3XeIYEr6jGR8VnB8kKoH7JUOHb6ScI,4506
tables/unimplemented.py,sha256=Z1LFEcoxLa3bsZThxhGKNl1Vstsi8PESy4WBgRnHFrg,5698
tables/utils.py,sha256=d4hZNPEtn76CfP4WW3_7ygSsnGQM90RMySSm3mZV9fs,15023
tables/utilsextension.cp312-win_amd64.pyd,sha256=kmihrPPTi10SH32g3-TsaGpsWNDu4NhG1QqrDO5ne6Y,234496
tables/utilsextension.pxd,sha256=ias7B8jS6byapxE4t1KjwAfWXQ4S1Tkk4vU7nWGgdLk,734
tables/utilsextension.pyx,sha256=HWDcUGdv54UxL9ObpPslr_0tLJS0_PiBBdBswsvLv0k,46236
tables/vlarray.py,sha256=5P9Vj9yjg8s2D_R5owHHopCAgmlZNHsI--wrGAfEjkY,33623
