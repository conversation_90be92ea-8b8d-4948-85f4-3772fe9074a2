textdistance-4.2.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
textdistance-4.2.1.dist-info/LICENSE,sha256=oOV_OJnxc9uQzeaMabB-rqV8Gti-Q1kQXusPgFxoEJI,1049
textdistance-4.2.1.dist-info/METADATA,sha256=QN2y6kCM6u8tsNd439Qsrj_8szfzIJYHdvTlWHYre0Y,17489
textdistance-4.2.1.dist-info/RECORD,,
textdistance-4.2.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
textdistance-4.2.1.dist-info/WHEEL,sha256=OqRkF0eY5GHssMorFjlbTIq072vpHpF60fIQA6lS9xA,92
textdistance-4.2.1.dist-info/direct_url.json,sha256=66PdAsRSQB3Ixnn0Fe64FXB-zLMlVz4t3yCbqkITm2c,85
textdistance-4.2.1.dist-info/top_level.txt,sha256=GBIsLNa3pcbaPSp8KNq93YdHKE3CNIQMQrLrZdectak,13
textdistance/__init__.py,sha256=O6vILNc22ycg4lvEX3qjogYqqWbA6rQstcPPfrawtEE,355
textdistance/__pycache__/__init__.cpython-39.pyc,,
textdistance/__pycache__/benchmark.cpython-39.pyc,,
textdistance/__pycache__/libraries.cpython-39.pyc,,
textdistance/__pycache__/utils.cpython-39.pyc,,
textdistance/algorithms/__init__.py,sha256=1raagDGcgHenA-Ncj3oKHTCk0ai8ltLdqQzTA__clkg,217
textdistance/algorithms/__pycache__/__init__.cpython-39.pyc,,
textdistance/algorithms/__pycache__/base.cpython-39.pyc,,
textdistance/algorithms/__pycache__/compression_based.cpython-39.pyc,,
textdistance/algorithms/__pycache__/edit_based.cpython-39.pyc,,
textdistance/algorithms/__pycache__/phonetic.cpython-39.pyc,,
textdistance/algorithms/__pycache__/sequence_based.cpython-39.pyc,,
textdistance/algorithms/__pycache__/simple.cpython-39.pyc,,
textdistance/algorithms/__pycache__/token_based.cpython-39.pyc,,
textdistance/algorithms/__pycache__/vector_based.cpython-39.pyc,,
textdistance/algorithms/base.py,sha256=zPpd259vcsLRfB-UQ5g43tbxJBcWLV4SlopVGJORuNc,5698
textdistance/algorithms/compression_based.py,sha256=GrfXVHBba3etyDpOmiocrWMuj5Pyn8DWRtnG6zx3_Ug,7544
textdistance/algorithms/edit_based.py,sha256=tVL185jMpLMMiEmCy1UbB3GKSTrbwKOOea0d1c_xDtA,24174
textdistance/algorithms/phonetic.py,sha256=Zfc-93p8jHBjrRXtYgBBUXWYa-KH6TCs96Nr6cH_Q_g,5545
textdistance/algorithms/sequence_based.py,sha256=gI1lH3046Cwk8ocOftcDQxJCv8FLowah-nwmcor3PGc,5787
textdistance/algorithms/simple.py,sha256=GjTh41YhDCjZ-rQtLMy3J3Xb6xK-H9TIjtsLPc4dfSU,2880
textdistance/algorithms/token_based.py,sha256=HHjKA9jPlEgiE4BkJ0qvU53YKbnCyIn4Jt-3klbSAf8,8695
textdistance/algorithms/vector_based.py,sha256=PkNIL9O7XpNcdCiaCP0J9vm61zhV9c3zj0VOh7z23eQ,2667
textdistance/benchmark.py,sha256=3wDObIVUmUsn57K9weOdmdhLpyfhpeEIRemIZst8SU8,3437
textdistance/libraries.json,sha256=j58Oco-g0Zysn9kMYofHU88HIuTS0eEK2H97TDJ1jNs,1132
textdistance/libraries.py,sha256=kEHzP3_j3rLm6MoKDtp3box1BxTWpAVgGIZGyYGJTgs,6239
textdistance/utils.py,sha256=OXBPx4j69BrZwuNNLzmq3Oz8_FEhTLj1tXoJuVIgrzw,680
