{"rustc": 16591470773350601817, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 367816849085071872, "path": 2858064028641174053, "deps": [[555019317135488525, "regex_automata", false, 1288647418313472275], [2779309023524819297, "aho_corasick", false, 16029392829927983407], [9408802513701742484, "regex_syntax", false, 9270184084865196698], [15932120279885307830, "memchr", false, 13287116547952953302]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-c9978fc0a93558d0\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}