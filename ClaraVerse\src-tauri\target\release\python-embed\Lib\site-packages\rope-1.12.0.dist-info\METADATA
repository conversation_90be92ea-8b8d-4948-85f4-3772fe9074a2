Metadata-Version: 2.1
Name: rope
Version: 1.12.0
Summary: a python refactoring library...
Author-email: <PERSON> <<EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>
License: LGPL-3.0-or-later
Project-URL: Source, https://github.com/python-rope/rope
Project-URL: Documentation, https://rope.readthedocs.io/
Classifier: Development Status :: 4 - Beta
Classifier: Operating System :: OS Independent
Classifier: Environment :: X11 Applications
Classifier: Environment :: Win32 (MS Windows)
Classifier: Environment :: MacOS X
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 or later (LGPLv3+)
Classifier: Natural Language :: English
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: COPYING
Requires-Dist: pytoolconfig[global] >=1.2.2
Provides-Extra: dev
Requires-Dist: pytest >=7.0.1 ; extra == 'dev'
Requires-Dist: pytest-timeout >=2.1.0 ; extra == 'dev'
Requires-Dist: build >=0.7.0 ; extra == 'dev'
Requires-Dist: pre-commit >=2.20.0 ; extra == 'dev'
Provides-Extra: doc
Requires-Dist: pytoolconfig[doc] ; extra == 'doc'
Requires-Dist: sphinx >=4.5.0 ; extra == 'doc'
Requires-Dist: sphinx-autodoc-typehints >=1.18.1 ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme >=1.0.0 ; extra == 'doc'
Provides-Extra: release
Requires-Dist: toml >=0.10.2 ; extra == 'release'
Requires-Dist: twine >=4.0.2 ; extra == 'release'
Requires-Dist: pip-tools >=6.12.1 ; extra == 'release'


.. _GitHub python-rope / rope: https://github.com/python-rope/rope


=========================================================================
 rope -- the world's most advanced open source Python refactoring library
=========================================================================

|Build status badge| |Latest version badge| |Download count badge| |ReadTheDocs status badge|

.. |Build status badge| image:: https://github.com/python-rope/rope/actions/workflows/main.yml/badge.svg
   :target: https://github.com/python-rope/rope/actions/workflows/main.yml
   :alt: Build Status

.. |Latest version badge| image:: https://badge.fury.io/py/rope.svg
   :target: https://badge.fury.io/py/rope
   :alt: Latest version

.. |Download count badge| image:: https://img.shields.io/pypi/dm/rope.svg
   :alt: Download count

.. |ReadTheDocs status badge| image:: https://readthedocs.org/projects/rope/badge/?version=latest
   :target: https://rope.readthedocs.io/en/latest/?badge=latest
   :alt: Documentation Status

Overview
========

`Rope`_ is the world's most advanced open source Python refactoring library
(yes, I totally stole that tagline from Postgres).

.. _`rope`: https://github.com/python-rope/rope


Most Python syntax up to Python 3.10 is supported. Please file bugs and contribute
patches if you encounter gaps.

Since version 1.0.0, rope no longer support running on Python 2.
If you need Python 2 support, then check out the `python2` branch or the 0.x.x
releases.

Getting Started
===============

* `Documentation <https://rope.readthedocs.io/en/latest/overview.html>`_
* `How to use Rope in my IDE or Text editor? <https://github.com/python-rope/rope/wiki/How-to-use-Rope-in-my-IDE-or-Text-editor%3F>`_
* `Configuration <https://rope.readthedocs.io/en/latest/configuration.html>`_
* `List of features <https://rope.readthedocs.io/en/latest/rope.html>`_
* `Overview of some of rope's features <https://rope.readthedocs.io/en/latest/overview.html>`_
* `Using as a library <https://rope.readthedocs.io/en/latest/library.html>`_
* `Contributing <https://rope.readthedocs.io/en/latest/contributing.html>`_

Why use Rope?
=============

- Rope aims to provide powerful and safe refactoring
- Rope is light on dependency, Rope only depends on Python itself
- Unlike PyRight or PyLance, Rope does not depend on Node.js
- Unlike PyLance or PyCharm, Rope is open source.
- Unlike PyRight and PyLance, Rope is written in Python itself, so if you experience problems, you would be able to debug and hack it yourself in a language that you are already familiar with
- In comparison to Jedi, Rope is focused on refactoring. While Jedi provides some basic refactoring capabilities, Rope supports many more advanced refactoring operations and options that Jedi does not.


Bug Reports
===========

Send your bug reports and feature requests at `python-rope's issue tracker`_ in GitHub.

.. _`python-rope's issue tracker`: https://github.com/python-rope/rope/issues


Maintainers
===========

Current active maintainer of Rope is Lie Ryan (`@lieryan`_).

Special Thanks
==============

Many thanks the following people:

- Ali Gholami Rudi (`@aligrudi`_) for initially creating the initial Rope project and most of Rope's code
- Matej Cepl (`@mcepl`_) as former long-time Rope maintainer
- Nick Smith <<EMAIL>> (`@soupytwist`_) as former Rope maintainer
- `all of our current and former contributors`_
- `all authors of editor integrations`_
- all maintainers of distro/package managers

.. _`@aligrudi`: https://github.com/aligrudi
.. _`@soupytwist`: https://github.com/soupytwist
.. _`@lieryan`: https://github.com/lieryan
.. _`@mcepl`: https://github.com/mcepl
.. _`all of our current and former contributors`: https://github.com/python-rope/rope/blob/master/CONTRIBUTORS.md
.. _`all authors of editor integrations`: https://github.com/python-rope/rope/wiki/How-to-use-Rope-in-my-IDE-or-Text-editor%3F

Packaging Status
================

.. image:: https://repology.org/badge/vertical-allrepos/python:rope.svg?exclude_unsupported=1
   :target: https://repology.org/project/python:rope/versions
   :alt: Packaging status

.. image:: https://repology.org/badge/vertical-allrepos/rope.svg?exclude_unsupported=1
   :target: https://repology.org/project/rope/versions
   :alt: Packaging status

License
=======

This program is under the terms of LGPL v3+ (GNU Lesser General Public License).
Have a look at `COPYING`_ for more information.

.. _`COPYING`: COPYING
