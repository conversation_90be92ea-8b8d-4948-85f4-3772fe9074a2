zope.interface-5.4.0-py3.12-nspkg.pth,sha256=v_t1oIorEnrHsL8_S45xOGNzLyFVAQCg1XkrPrk0VV8,530
zope.interface-5.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
zope.interface-5.4.0.dist-info/LICENSE.txt,sha256=PmcdsR32h1FswdtbPWXkqjg-rKPCDOo_r1Og9zNdCjw,2070
zope.interface-5.4.0.dist-info/METADATA,sha256=iLZyRdy3QbEXRXoPTcWMuKw76qIkodL-xtq9fz3olgY,41700
zope.interface-5.4.0.dist-info/RECORD,,
zope.interface-5.4.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
zope.interface-5.4.0.dist-info/WHEEL,sha256=aDrgWfEd5Ac7WJzHsr90rcMGiH4MHbAXoCWpyP5CEBc,102
zope.interface-5.4.0.dist-info/direct_url.json,sha256=zJY-FpGnhUcsr_UfMxAyhahKICnEhAjNnSuXF4o8OVA,122
zope.interface-5.4.0.dist-info/namespace_packages.txt,sha256=QpUHvpO4wIuZDeEgKY8qZCtD-tAukB0fn_f6utzlb98,5
zope.interface-5.4.0.dist-info/top_level.txt,sha256=QpUHvpO4wIuZDeEgKY8qZCtD-tAukB0fn_f6utzlb98,5
zope/interface/__init__.py,sha256=n3r9PajonfuD_4EJn_XT9DwRtt3JcgU9-ebbTgV17rA,3623
zope/interface/__pycache__/__init__.cpython-312.pyc,,
zope/interface/__pycache__/_compat.cpython-312.pyc,,
zope/interface/__pycache__/_flatten.cpython-312.pyc,,
zope/interface/__pycache__/adapter.cpython-312.pyc,,
zope/interface/__pycache__/advice.cpython-312.pyc,,
zope/interface/__pycache__/declarations.cpython-312.pyc,,
zope/interface/__pycache__/document.cpython-312.pyc,,
zope/interface/__pycache__/exceptions.cpython-312.pyc,,
zope/interface/__pycache__/interface.cpython-312.pyc,,
zope/interface/__pycache__/interfaces.cpython-312.pyc,,
zope/interface/__pycache__/registry.cpython-312.pyc,,
zope/interface/__pycache__/ro.cpython-312.pyc,,
zope/interface/__pycache__/verify.cpython-312.pyc,,
zope/interface/_compat.py,sha256=5FoI0eLiOZ7x664mIfdMBsusxhrzYpEruoIaAwy-CQ8,5191
zope/interface/_flatten.py,sha256=nY3YJjWfeslmcWfjfxVYkoZb2lFrVGbw30xF_sAyQ60,1056
zope/interface/_zope_interface_coptimizations.c,sha256=Hqz-ObWSJIUgTN7NtFWInG5NUuLI5WwosNmFaAjyk9g,58093
zope/interface/_zope_interface_coptimizations.cp312-win_amd64.pyd,sha256=_u2vLNx6ydoY-yjUxWoN4U3zsEnzGA6FMfjqw3mY0ac,31232
zope/interface/adapter.py,sha256=sNiGpxPMsBTblkb_gK_LxCK8fgDqCX6ZWy7I_cMecPE,36498
zope/interface/advice.py,sha256=CpHhuAT8nbaaymDBEXuxvJv9ZRMEtq7fgTmoUOwsS3g,7612
zope/interface/common/__init__.py,sha256=6oLVayU_bWd4HCX7guqlxYORQr6odBzlnCobBLQpfAI,10457
zope/interface/common/__pycache__/__init__.cpython-312.pyc,,
zope/interface/common/__pycache__/builtins.cpython-312.pyc,,
zope/interface/common/__pycache__/collections.cpython-312.pyc,,
zope/interface/common/__pycache__/idatetime.cpython-312.pyc,,
zope/interface/common/__pycache__/interfaces.cpython-312.pyc,,
zope/interface/common/__pycache__/io.cpython-312.pyc,,
zope/interface/common/__pycache__/mapping.cpython-312.pyc,,
zope/interface/common/__pycache__/numbers.cpython-312.pyc,,
zope/interface/common/__pycache__/sequence.cpython-312.pyc,,
zope/interface/common/builtins.py,sha256=BgxJ2wLLxIa6RCD4c-i6DydzBwyXzb-7KrG8jcsmwOI,3303
zope/interface/common/collections.py,sha256=vBfq9yfxZI3dNwqMhO5B99VGw8dQKGsYio1X-0fMymo,7920
zope/interface/common/idatetime.py,sha256=FS1ksWeQSxq4K31ah0I0eXhOf_qT5Jg0jKMapOC2vE0,20858
zope/interface/common/interfaces.py,sha256=JxTBJkgBGi3Frcmq0_HIY9-Ei0A88P2E-fD4MPywfJg,5506
zope/interface/common/io.py,sha256=botfdBaLK1WSBHBAZonnUzSYkAV5yu1ec7KiM48oRPI,1525
zope/interface/common/mapping.py,sha256=HhoJ3lutssKHtyAdh3FQJE6V9P9wINL4XebyOgenuOs,5202
zope/interface/common/numbers.py,sha256=QPDIMnE5FpagWB6d9cgW_7F5kabwya9zPLT9m0VKyK4,2140
zope/interface/common/sequence.py,sha256=XFEJ0NYgWGMoluOGmuIb7i4hNPjTlEhy9Np-8ZfGbDU,6311
zope/interface/common/tests/__init__.py,sha256=lUYE0s9cHRiTZn85UBocgIAgSMqyC9Sop2cQKfTpaNs,5122
zope/interface/common/tests/__pycache__/__init__.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/basemapping.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_builtins.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_collections.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_idatetime.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_import_interfaces.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_io.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_numbers.cpython-312.pyc,,
zope/interface/common/tests/basemapping.py,sha256=3Tw8raoHaGOsJJo3aqPOrrZu5z5pGEGbZcq1TQbUcbM,3914
zope/interface/common/tests/test_builtins.py,sha256=J9WLqoMDPTmUM8GdgDMU0dw909cPAAN0YvgN4P3Ir8M,1463
zope/interface/common/tests/test_collections.py,sha256=qzSPJI1YMbOVD_Bg72k7KGecDbi8JXVEuUsknAzPr54,6237
zope/interface/common/tests/test_idatetime.py,sha256=LoyLA7wgDrma8U3AOwQeZtTpwZMRtdsqEkDLFN4u8rA,1594
zope/interface/common/tests/test_import_interfaces.py,sha256=_x5QozzgaVN5YvhG7k2GpDnld11SLwsWYfNjCqd0phw,812
zope/interface/common/tests/test_io.py,sha256=k6sbyqPHaNnzggLFYupn7mfcOtVyJVk--QoSNTfyqp4,1862
zope/interface/common/tests/test_numbers.py,sha256=CEQxxOfmxDDmAym15gnGd8wtGnC7tMG94_d3L0m_lqc,1394
zope/interface/declarations.py,sha256=rKHcoTOnYqFgkfnog4-dZxxGyFQmZpQZYsMJz2Ro1Lk,47665
zope/interface/document.py,sha256=N4v7Etau5Zsq0PXGszF_MAoW70uyfl0tDOjW8kYiqFY,4052
zope/interface/exceptions.py,sha256=znoGonilZ___OP5IHWQdeO5x9fRS2m5nW0BX3a3gnFo,8643
zope/interface/interface.py,sha256=OwddJ23UC6Z0NUGGbbM30RTuIAKVDFC52rO4HczLF-0,40059
zope/interface/interfaces.py,sha256=9DtZ8t_r0T8fJrx392IDkSPEmS9P0WRcM1Qeb14SdYo,53559
zope/interface/registry.py,sha256=-JvTbQ_OgQdHfTl_8VGs-kBm0TydXwYy2CJKOaQl_uQ,26023
zope/interface/ro.py,sha256=G939HT-IsWzYaN-n-Q8KCPDVX92c3V-bk9pmLxdA4pM,24226
zope/interface/tests/__init__.py,sha256=G_RHsg7tPNLNBMT_GybOuzB9Vs7vxlWrbwN5dUI7XFc,3985
zope/interface/tests/__pycache__/__init__.cpython-312.pyc,,
zope/interface/tests/__pycache__/advisory_testing.cpython-312.pyc,,
zope/interface/tests/__pycache__/dummy.cpython-312.pyc,,
zope/interface/tests/__pycache__/idummy.cpython-312.pyc,,
zope/interface/tests/__pycache__/m1.cpython-312.pyc,,
zope/interface/tests/__pycache__/odd.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_adapter.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_advice.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_declarations.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_document.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_element.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_exceptions.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_interface.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_interfaces.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_odd_declarations.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_registry.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_ro.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_sorting.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_verify.cpython-312.pyc,,
zope/interface/tests/advisory_testing.py,sha256=0aMo3ahuLiykjk1Fro-s4T-22mGL8nLORjqPdEjb3Zk,1256
zope/interface/tests/dummy.py,sha256=80FPlPOyoshfHF1jFFpeYIyn_15z5MVVfLVf4irpNQU,911
zope/interface/tests/idummy.py,sha256=Hc-iM7RwEzcVp7znPx7yMmFwERQXhBGeRQsmKIPfkns,889
zope/interface/tests/m1.py,sha256=uEUxuYj_3ZVSF2yeNnHbcFX0Kyj_FFzUAM5O-gbcsMs,812
zope/interface/tests/odd.py,sha256=uVGU__t9BiZREPr7seTo_JWslIZolAoOUHVsgMC_9uY,3210
zope/interface/tests/test_adapter.py,sha256=zeT29gHx0vAUC1spLIff385ef_7hicuPOpRdOZT6orQ,79857
zope/interface/tests/test_advice.py,sha256=_QqHPvWbEJ9bfF6Z_JU2_t6mBTJvBOVB5Xd61aw2Tn0,10708
zope/interface/tests/test_declarations.py,sha256=ZtOjf5TzElRJFWm7zdrAyfwEs4NppJ1UGHF83bi-qHM,91450
zope/interface/tests/test_document.py,sha256=j6D7CTGRLy8IHYtMvqQBcFtXsoLaZfIpAn9Rl9cNGPU,16637
zope/interface/tests/test_element.py,sha256=HgaP7r-M2odSPVTCbby3HQADJIBIJbYmDjK01ErwkZk,1118
zope/interface/tests/test_exceptions.py,sha256=NyWDTIFdVwGd4znrYOhKpaXEkIzTZYpx-DhG9wwoWMY,6439
zope/interface/tests/test_interface.py,sha256=pyo1rcHRPmUg_qqLPTV8ppMRhgT4vjMBfX-O_MlKhjc,92500
zope/interface/tests/test_interfaces.py,sha256=gBfiCFIeHu-Qkg5O3CZkIwKr4o32WvxR4DfnCbGK6Kg,4385
zope/interface/tests/test_odd_declarations.py,sha256=pjLvKiHYrb9x05-tXZWPj3XwfN_sOaAocaBicmpMvbI,8009
zope/interface/tests/test_registry.py,sha256=FCJkQow1vNb6gX0QGmH9A8uOKMt86BjbVmjixq-cQ08,113672
zope/interface/tests/test_ro.py,sha256=v6EkC2w8KA6plLfQMU6nVyY28kw2oaRVjKHKcVayxOk,14289
zope/interface/tests/test_sorting.py,sha256=fJ8-V-cQJhb77YGbO0H_qbHCZKdPFALf-eD_o6IknIA,1933
zope/interface/tests/test_verify.py,sha256=aSiDBRUt1xg3sVjVrX0ex3P_P-a0RwfPPzJy8rjWiLg,19156
zope/interface/verify.py,sha256=nXSE82E4s9kSrX8En6MKVaykQLo4ebyje1KOlVo6pEc,8420
