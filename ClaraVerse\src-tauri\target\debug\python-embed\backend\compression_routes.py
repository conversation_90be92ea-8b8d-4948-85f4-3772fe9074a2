"""
🗜️ Routes de compression pour WeMa IA
Endpoints pour la compression intelligente de contexte
"""

import logging
import json
import time
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from perfect_compressor import PerfectCompressor
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

# Router pour les routes de compression
compression_router = APIRouter(prefix="/api", tags=["compression"])

# Modèles Pydantic pour les requêtes
class CompressionRequest(BaseModel):
    context: str
    compression_ratio: Optional[float] = 0.5
    preserve_recent_messages: Optional[int] = 3

class CompressionResponse(BaseModel):
    success: bool
    compressed_context: Optional[str] = None
    original_length: Optional[int] = None
    compressed_length: Optional[int] = None
    compression_ratio: Optional[float] = None
    compression_time: Optional[int] = None
    error: Optional[str] = None
    compression_info: Optional[Dict] = None

# Instance globale du compresseur
compressor = None

def get_compressor():
    """Obtenir l'instance du compresseur (singleton)"""
    global compressor
    if compressor is None:
        compressor = PerfectCompressor()
        logger.info("🗜️ PerfectCompressor initialisé pour les routes")
    return compressor

@compression_router.post('/compress-context', response_model=CompressionResponse)
async def compress_context(request: CompressionRequest):
    """
    🗜️ Endpoint pour compresser le contexte de conversation
    """
    try:
        # Validation des paramètres
        context = request.context.strip()
        if not context:
            raise HTTPException(status_code=400, detail="Le contexte ne peut pas être vide")
        
        compression_ratio = request.compression_ratio
        preserve_recent_messages = request.preserve_recent_messages
        
        # Validation des paramètres
        if not (0.3 <= compression_ratio <= 0.7):
            compression_ratio = 0.5
            
        if not (1 <= preserve_recent_messages <= 10):
            preserve_recent_messages = 3
        
        logger.info(f"🗜️ Compression demandée: {len(context)} chars, ratio: {compression_ratio}")
        
        # Obtenir le compresseur
        comp = get_compressor()
        
        # Simuler une conversation à partir du contexte
        fake_conversation = [
            {
                "role": "user",
                "content": "Contexte de conversation précédent"
            },
            {
                "role": "assistant", 
                "content": context
            }
        ]
        
        # Exécuter la compression
        start_time = time.time()
        compressed_messages, compression_info = comp.compress_conversation(fake_conversation)
        end_time = time.time()
        compression_time = int((end_time - start_time) * 1000)  # en ms
        
        # Extraire le contexte compressé
        if compressed_messages and len(compressed_messages) > 0:
            compressed_context = ""
            for msg in compressed_messages:
                if msg.get('content'):
                    compressed_context += msg['content'] + "\n"
            compressed_context = compressed_context.strip()
        else:
            compressed_context = context  # Fallback si compression échoue
        
        # Calculer les métriques
        original_length = len(context)
        compressed_length = len(compressed_context)
        actual_ratio = compressed_length / original_length if original_length > 0 else 1.0
        
        logger.info(f"✅ Compression terminée: {original_length} → {compressed_length} chars ({actual_ratio:.2f})")
        
        return CompressionResponse(
            success=True,
            compressed_context=compressed_context,
            original_length=original_length,
            compressed_length=compressed_length,
            compression_ratio=actual_ratio,
            compression_time=compression_time,
            compression_info=compression_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Erreur compression: {e}")
        import traceback
        traceback.print_exc()
        
        return CompressionResponse(
            success=False,
            error=f"Erreur interne: {str(e)}"
        )

@compression_router.get('/compression/status')
async def compression_status():
    """
    📊 Endpoint pour obtenir le statut du système de compression
    """
    try:
        comp = get_compressor()
        
        return {
            "success": True,
            "status": "ready",
            "lmstudio_url": comp.lmstudio_url,
            "compression_model": comp.compression_model,
            "compression_threshold": comp.compression_threshold,
            "max_tokens": comp.max_tokens,
            "target_tokens": comp.target_tokens,
            "stats": comp.compression_stats
        }
        
    except Exception as e:
        logger.error(f"❌ Erreur statut compression: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur statut: {str(e)}")

@compression_router.post('/compression/test')
async def test_compression():
    """
    🧪 Endpoint de test pour la compression
    """
    try:
        # Contexte de test
        test_context = """
        Voici une conversation de test pour vérifier le fonctionnement de la compression.
        
        Utilisateur: Bonjour, comment allez-vous ?
        Assistant: Bonjour ! Je vais très bien, merci de demander. Comment puis-je vous aider aujourd'hui ?
        
        Utilisateur: Pouvez-vous m'expliquer l'intelligence artificielle ?
        Assistant: Bien sûr ! L'intelligence artificielle est un domaine de l'informatique qui vise à créer des systèmes capables de réaliser des tâches qui nécessitent normalement l'intelligence humaine. Cela inclut l'apprentissage, le raisonnement, la perception et la prise de décision.
        
        Utilisateur: Merci pour cette explication !
        Assistant: Je vous en prie ! N'hésitez pas si vous avez d'autres questions.
        """
        
        comp = get_compressor()
        
        # Test de compression
        fake_conversation = [
            {"role": "assistant", "content": test_context}
        ]
        
        start_time = time.time()
        compressed_messages, compression_info = comp.compress_conversation(fake_conversation)
        end_time = time.time()
        
        compressed_context = compressed_messages[0]['content'] if compressed_messages else test_context
        
        return {
            "success": True,
            "test_result": "compression_working",
            "original_length": len(test_context),
            "compressed_length": len(compressed_context),
            "compression_time": int((end_time - start_time) * 1000),
            "compressed_preview": compressed_context[:200] + "..." if len(compressed_context) > 200 else compressed_context
        }
        
    except Exception as e:
        logger.error(f"❌ Erreur test compression: {e}")
        raise HTTPException(status_code=500, detail=f"Test échoué: {str(e)}")
