use std::process::Command;
use std::fs;
use std::path::Path;

#[cfg(windows)]
use std::os::windows::process::CommandExt;

// Fonction pour copier récursivement un répertoire
fn copy_dir_all(src: impl AsRef<Path>, dst: impl AsRef<Path>) -> std::io::Result<()> {
    fs::create_dir_all(&dst)?;
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        if ty.is_dir() {
            copy_dir_all(entry.path(), dst.as_ref().join(entry.file_name()))?;
        } else {
            fs::copy(entry.path(), dst.as_ref().join(entry.file_name()))?;
        }
    }
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }

      // Démarrer le backend Python complet
      std::thread::spawn(|| {
        let exe_path = std::env::current_exe().unwrap();
        let exe_dir = exe_path.parent().unwrap();

        // Répertoire d'installation de l'application
        let app_data = std::env::var("LOCALAPPDATA").unwrap_or_default();
        let install_dir = std::path::Path::new(&app_data).join("com.wema-ia.app");

        // Chemins des ressources dans l'installation
        let python_portable_install = install_dir.join("python-embed").join("python.exe");
        let backend_dir_install = install_dir.join("py_backend");

        // Chemins des ressources dans le répertoire de l'EXE (pour copie)
        let python_portable_source = exe_dir.join("python-embed");
        let backend_dir_source = exe_dir.join("py_backend");

        // MODE DEV : Chercher dans le répertoire de développement
        let current_dir = std::env::current_dir().unwrap();
        let backend_dir_dev = current_dir.parent().unwrap_or(&current_dir).join("py_backend");

        // Copier les ressources si elles n'existent pas dans l'installation
        if !python_portable_install.exists() && python_portable_source.exists() {
          println!("📦 Copie de Python portable...");
          let _ = std::fs::create_dir_all(install_dir.join("python-embed"));
          copy_dir_all(&python_portable_source, &install_dir.join("python-embed"));
        }

        if !backend_dir_install.exists() && backend_dir_source.exists() {
          println!("📦 Copie du backend Python...");
          let _ = std::fs::create_dir_all(&backend_dir_install);
          copy_dir_all(&backend_dir_source, &backend_dir_install);
        }

        if python_portable_install.exists() && backend_dir_install.exists() {
          // MODE PRODUCTION avec Python portable
          println!("🚀 Démarrage backend Python (mode production avec Python portable)...");
          println!("📁 Python path: {:?}", python_portable_install);
          println!("📁 Backend path: {:?}", backend_dir_install);

          // Vérifier que main.py existe
          let main_py = backend_dir_install.join("main.py");
          if !main_py.exists() {
            println!("❌ main.py non trouvé: {:?}", main_py);
            return;
          }

          let result = Command::new(&python_portable_install)
            .arg("main.py")
            .current_dir(&backend_dir_install)
            .stdout(std::process::Stdio::piped())
            .stderr(std::process::Stdio::piped())
            .creation_flags(0x08000000) // CREATE_NO_WINDOW pour masquer la console
            .spawn();

          match result {
            Ok(mut child) => {
              println!("✅ Backend Python portable démarré avec succès");

              // Capturer STDOUT et STDERR en arrière-plan
              if let Some(stdout) = child.stdout.take() {
                std::thread::spawn(move || {
                  use std::io::{BufRead, BufReader};
                  let reader = BufReader::new(stdout);
                  for line in reader.lines() {
                    if let Ok(line) = line {
                      println!("🐍 Backend STDOUT: {}", line);
                    }
                  }
                });
              }

              if let Some(stderr) = child.stderr.take() {
                std::thread::spawn(move || {
                  use std::io::{BufRead, BufReader};
                  let reader = BufReader::new(stderr);
                  for line in reader.lines() {
                    if let Ok(line) = line {
                      println!("🐍 Backend STDERR: {}", line);
                    }
                  }
                });
              }

              // Surveiller le processus en arrière-plan
              std::thread::spawn(move || {
                match child.wait() {
                  Ok(status) => {
                    if status.success() {
                      println!("🐍 Backend terminé normalement");
                    } else {
                      println!("❌ Backend terminé avec erreur: {:?}", status);
                    }
                  },
                  Err(e) => println!("❌ Erreur attente backend: {}", e),
                }
              });
            },
            Err(e) => {
              println!("❌ Erreur démarrage backend portable: {}", e);
              println!("🔍 Vérifiez que Python portable est correctement installé");
            },
          }
        } else if backend_dir_dev.exists() && backend_dir_dev.join("main.py").exists() {
          // MODE DEV avec Python système
          println!("🚀 Démarrage backend Python (mode dev)...");
          let result = Command::new("python")
            .arg("main.py")
            .current_dir(&backend_dir_dev)
            .spawn();

          match result {
            Ok(_) => println!("✅ Backend Python système démarré avec succès"),
            Err(e) => println!("❌ Erreur démarrage backend système: {}", e),
          }
        } else {
          println!("❌ Aucun backend Python trouvé !");
          println!("   - Python portable install: {:?}", python_portable_install);
          println!("   - Backend install: {:?}", backend_dir_install);
          println!("   - Backend dev: {:?}", backend_dir_dev);
        }
      });

      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
