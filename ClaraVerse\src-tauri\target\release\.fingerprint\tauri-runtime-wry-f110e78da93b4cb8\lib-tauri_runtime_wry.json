{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 367816849085071872, "path": 12311593708891281061, "deps": [[376837177317575824, "softbuffer", false, 14463794809566718057], [2013030631243296465, "webview2_com", false, 16467297904636269633], [2671782512663819132, "tauri_utils", false, 1822794662215125040], [3150220818285335163, "url", false, 1318321174758569141], [3722963349756955755, "once_cell", false, 14898880017031817328], [4143744114649553716, "raw_window_handle", false, 16995855243923100015], [5986029879202738730, "log", false, 5444832254770226198], [6089812615193535349, "tauri_runtime", false, 14947029587340180920], [8826339825490770380, "tao", false, 11400709630008686805], [9010263965687315507, "http", false, 3748586209917174119], [9141053277961803901, "wry", false, 16633337693670751623], [11599800339996261026, "build_script_build", false, 5757258261823579547], [14585479307175734061, "windows", false, 11646464322459264762]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-f110e78da93b4cb8\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}