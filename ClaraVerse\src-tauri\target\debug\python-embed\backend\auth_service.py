"""
🔑 Service d'authentification par token
Gestion des tokens utilisateurs pour chaque pôle
"""

import json
import os
from datetime import datetime
from typing import Dict, Optional, Any

class TokenAuthService:
    def __init__(self):
        self.tokens_file = '../user-tokens.json'
        self.pole_id = os.getenv('POLE_ID', 'unknown')
        self.authorized_users = os.getenv('AUTHORIZED_USERS', '').split(',')
        
    def load_tokens(self) -> Dict[str, Any]:
        """Charger les tokens depuis le fichier"""
        try:
            if os.path.exists(self.tokens_file):
                with open(self.tokens_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"❌ Erreur chargement tokens: {e}")
        
        return {}
    
    def save_tokens(self, tokens: Dict[str, Any]) -> bool:
        """Sauvegarder les tokens"""
        try:
            with open(self.tokens_file, 'w', encoding='utf-8') as f:
                json.dump(tokens, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ Erreur sauvegarde tokens: {e}")
            return False
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Vérifier un token et retourner les infos utilisateur"""
        tokens = self.load_tokens()
        
        # Vérifier dans le pôle actuel
        if self.pole_id in tokens:
            pole_tokens = tokens[self.pole_id]
            
            for user_id, user_info in pole_tokens.items():
                if user_info.get('token') == token:
                    # Vérifier que l'utilisateur est autorisé pour ce pôle
                    if user_id in self.authorized_users:
                        # Mettre à jour la dernière connexion
                        user_info['lastLogin'] = datetime.now().isoformat()
                        tokens[self.pole_id][user_id] = user_info
                        self.save_tokens(tokens)
                        
                        return {
                            'userId': user_id,
                            'name': user_info.get('name', user_id),
                            'poleId': self.pole_id,
                            'token': token,
                            'lastLogin': user_info['lastLogin'],
                            'features': os.getenv('FEATURES', '').split(',')
                        }
        
        # Vérifier token admin
        if 'admin' in tokens:
            admin_tokens = tokens['admin']
            for user_id, user_info in admin_tokens.items():
                if user_info.get('token') == token:
                    return {
                        'userId': user_id,
                        'name': user_info.get('name', 'Administrateur'),
                        'poleId': 'admin',
                        'token': token,
                        'isAdmin': True,
                        'lastLogin': datetime.now().isoformat()
                    }
        
        return None
    
    def get_pole_users(self) -> Dict[str, Any]:
        """Obtenir la liste des utilisateurs du pôle actuel"""
        tokens = self.load_tokens()
        
        if self.pole_id in tokens:
            pole_tokens = tokens[self.pole_id]
            users = {}
            
            for user_id, user_info in pole_tokens.items():
                if user_id in self.authorized_users:
                    users[user_id] = {
                        'name': user_info.get('name', user_id),
                        'lastLogin': user_info.get('lastLogin'),
                        'createdAt': user_info.get('createdAt')
                    }
            
            return users
        
        return {}
    
    def is_user_connected(self, user_id: str) -> bool:
        """Vérifier si un utilisateur est connecté (simplifié)"""
        # Dans une vraie implémentation, on utiliserait des sessions/WebSocket
        tokens = self.load_tokens()
        
        if self.pole_id in tokens and user_id in tokens[self.pole_id]:
            last_login = tokens[self.pole_id][user_id].get('lastLogin')
            if last_login:
                # Considérer connecté si login dans les 30 dernières minutes
                from datetime import datetime, timedelta
                try:
                    login_time = datetime.fromisoformat(last_login.replace('Z', '+00:00'))
                    return datetime.now() - login_time < timedelta(minutes=30)
                except:
                    pass
        
        return False
    
    def get_connected_users(self) -> list:
        """Obtenir la liste des utilisateurs connectés"""
        users = self.get_pole_users()
        connected = []
        
        for user_id in users:
            if self.is_user_connected(user_id):
                connected.append({
                    'userId': user_id,
                    'name': users[user_id]['name'],
                    'lastLogin': users[user_id]['lastLogin']
                })
        
        return connected

# Instance globale
auth_service = TokenAuthService()

def require_auth(func):
    """Décorateur pour vérifier l'authentification"""
    def wrapper(*args, **kwargs):
        # À implémenter selon le framework web utilisé
        # Pour FastAPI, on utiliserait Depends()
        return func(*args, **kwargs)
    return wrapper
