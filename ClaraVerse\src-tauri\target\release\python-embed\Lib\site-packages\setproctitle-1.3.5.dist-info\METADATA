Metadata-Version: 2.2
Name: setproctitle
Version: 1.3.5
Summary: A Python module to customize the process title
Home-page: https://github.com/dvarrazzo/py-setproctitle
Download-URL: http://pypi.python.org/pypi/setproctitle/
Author: <PERSON><PERSON>
Author-email: <EMAIL>
License: BSD-3-Clause
Platform: GNU/Linux
Platform: BSD
Platform: MacOS X
Platform: Windows
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: C
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: POSIX :: BSD
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Topic :: Software Development
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
Provides-Extra: test
Requires-Dist: pytest; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: download-url
Dynamic: home-page
Dynamic: license
Dynamic: platform
Dynamic: provides-extra
Dynamic: requires-python
Dynamic: summary

A Python module to customize the process title
==============================================

.. image:: https://github.com/dvarrazzo/py-setproctitle/workflows/Tests/badge.svg
    :target: https://github.com/dvarrazzo/py-setproctitle/actions?query=workflow%3ATests
    :alt: Tests

:author: Daniele Varrazzo

The ``setproctitle`` module allows a process to change its title (as displayed
by system tools such as ``ps``, ``top`` or MacOS Activity Monitor).

Changing the title is mostly useful in multi-process systems, for example
when a master process is forked: changing the children's title allows to
identify the task each process is busy with.  The technique is used by
PostgreSQL_ and the `OpenSSH Server`_ for example.

The procedure is hardly portable across different systems.  PostgreSQL provides
a good `multi-platform implementation`__:  this package was born as a wrapper
around PostgreSQL code.

- `Homepage <https://github.com/dvarrazzo/py-setproctitle>`__
- `Download <http://pypi.python.org/pypi/setproctitle/>`__
- `Bug tracker <https://github.com/dvarrazzo/py-setproctitle/issues>`__


.. _PostgreSQL: http://www.postgresql.org
.. _OpenSSH Server: http://www.openssh.com/
.. __: http://doxygen.postgresql.org/ps__status_8c_source.html


Installation
------------

``setproctitle`` is a C extension: in order to build it you will need a C
compiler and the Python development support (the ``python-dev`` or
``python3-dev`` package in most Linux distributions). No further external
dependencies are required.

You can use ``pip`` to install the module::

    pip install setproctitle

You can use ``pip -t`` or ``virtualenv`` for local installations, ``sudo pip``
for a system-wide one... the usual stuff. Read pip_ or virtualenv_ docs for
all the details.

.. _pip: https://pip.readthedocs.org/
.. _virtualenv: https://virtualenv.readthedocs.org/


Usage
-----

.. note::
   You should import and use the module (even just calling ``getproctitle()``)
   pretty early in your program lifetime: code writing env vars `may
   interfere`__ with the module initialisation.

    .. __: https://github.com/dvarrazzo/py-setproctitle/issues/42


The ``setproctitle`` module exports the following functions:

``setproctitle(title)``
    Set *title* as the title for the current process.

``getproctitle()``
    Return the current process title.

The process title is usually visible in files such as ``/proc/PID/cmdline``,
``/proc/PID/status``, ``/proc/PID/comm``, depending on the operating system
and kernel version. These information are used by user-space tools such as
``ps`` and ``top``.


``setthreadtitle(title)``
    Set *title* as the title for the current thread.

``getthreadtitle()``
    Get the current thread title.

The thread title is exposed by some operating systems as the file
``/proc/PID/task/TID/comm``, which is used by certain tools such as ``htop``.


Environment variables
~~~~~~~~~~~~~~~~~~~~~

A few environment variables can be used to customize the module behavior:

``SPT_NOENV``
    Avoid clobbering ``/proc/PID/environ``.

    On many platforms, setting the process title will clobber the
    ``environ`` memory area. ``os.environ`` will work as expected from within
    the Python process, but the content of the file ``/proc/PID/environ`` will
    be overwritten.  If you require this file not to be broken you can set the
    ``SPT_NOENV`` environment variable to any non-empty value: in this case
    the maximum length for the title will be limited to the length of the
    command line.

``SPT_DEBUG``
    Print debug information on ``stderr``.

    If the module doesn't work as expected you can set this variable to a
    non-empty value to generate information useful for debugging.  Note that
    the most useful information is printed when the module is imported, not
    when the functions are called.


Module status
-------------

The module can be currently compiled and effectively used on the following
platforms:

- GNU/Linux
- BSD
- MacOS X
- Windows

Note that on Windows there is no way to change the process string:
what the module does is to create a *Named Object* whose value can be read
using a tool such as `Process Explorer`_ (contribution of a more useful tool
to be used together with ``setproctitle`` would be well accepted).

The module can probably work on HP-UX, but I haven't found any to test with.
It is unlikely that it can work on Solaris instead.

.. _Process Explorer: http://technet.microsoft.com/en-us/sysinternals/bb896653.aspx

Releases history
----------------

Version 1.3.5 (unreleased)
--------------------------

- Fix bouncing Dock icon on macOS (issue #143).
- Fix building on C23 compilers (issue #145).


Version 1.3.4
-------------

- Add support for Python 3.13 (issue #139).
- Drop support for Python 3.7.


Version 1.3.3
-------------

- Add support for Python 3.12.
- Fix package metadata to include Python 3.11, 3.12.


Version 1.3.2
-------------

- Restore import-time initialization of macOS to avoid crash on thread+fork
  (issue #113).


Version 1.3.1
-------------

- Fixed segfault on macOS 12.5 in forked processes (issue #111).
  Note that, as a workaround, Activity Monitor will show the title of the
  parent.


Version 1.3.0
-------------

- Added fallback no-op implementation if building the extension fails.
- Added support for displaying title as the process name in MacOS Activity
  Monitor (issue #10).
- Fixed "Symbol not found: _Py_GetArgcArgv" error when using Xcode provided
  Python (issues #82, #103).
- Fixed FreeBSD support, broken in 1.2 (issue #94).
- Added package type annotations (issue #101).
- Dropped support for Python 3.6.


Version 1.2.3
-------------

- Added Python 3.10 packages (issue #102).
- Added Wheel packages for macOS (issue #96).
- Package build moved to cibuildwheel, other wheels provided (issue #47).


Version 1.2.2
-------------

- Fixed Windows build (issues #89, #90).
- Added wheel packages for Windows (issues #47, #90).
- Added wheel packages for aarch64 (issue #95).


Version 1.2.1
-------------

- Fixed segfault after ``os.environ.clear()`` (issue #88).


Version 1.2
~~~~~~~~~~~

- added ``getthreadtitle()`` and ``setthreadtitle()``.
- Initialisation of the module moved to the first usage: importing the module
  doesn't cause side effects.
- Manage much longer command lines (issue #52)
- Improved build on BSD, dropped ancient versions (issue #67).
- Fixed build for Python 3.8 (issues #66, #72)
- Added support for Python 3.9
- Dropped support for Python < 3.6


Version 1.1.10
~~~~~~~~~~~~~~

- Fixed building with certain ``prctl.h`` implementations (issue #44).
- Use ``setuptools`` if available (issue #48).


Version 1.1.9
~~~~~~~~~~~~~

- Fixed build on VC (issues #20, #33).
- Added ``MANIFEST.in`` to the source distribution to help with RPM building
  (issue #30).


Version 1.1.8
~~~~~~~~~~~~~

- Added support for Python "diehard" 2.4 (pull request #3).
- Fixed build on Mac OS X 10.9 Maverick (issue #27).


Version 1.1.7
~~~~~~~~~~~~~

- Added PyPy support, courtesy of Ozan Turksever - http://www.logsign.net
  (pull request #2).


Version 1.1.6
~~~~~~~~~~~~~

- The module can be compiled again on Windows (issue #21).


Version 1.1.5
~~~~~~~~~~~~~

- No module bug, but a packaging issue: files ``README`` and ``HISTORY``
  added back into the distribution.


Version 1.1.4
~~~~~~~~~~~~~

- The module works correctly in embedded Python.
- ``setproctitle()`` accepts a keyword argument.
- Debug output support always compiled in: the variable ``SPT_DEBUG`` can be
  used to emit debug log.


Version 1.1.3
~~~~~~~~~~~~~

- Don't clobber environ if the variable ``SPT_NOENV`` is set (issue #16).


Version 1.1.2
~~~~~~~~~~~~~

- Find the setproctitle include file on OpenBSD (issue #11).
- Skip test with unicode if the file system encoding wouldn't make it pass
  (issue #13).


Version 1.1.1
~~~~~~~~~~~~~

- Fixed segfault when the module is imported under mod_wsgi (issue #9).


Version 1.1
~~~~~~~~~~~

- The module works correctly with Python 3.


Version 1.0.1
~~~~~~~~~~~~~

- ``setproctitle()`` works even when Python messes up with argv, e.g. when run
  with the -m option (issue #8).


Version 1.0
~~~~~~~~~~~

No major change since the previous version.  The module has been heavily used
in production environment without any problem reported, so it's time to declare
it stable.


Version 0.4
~~~~~~~~~~~

- Module works on BSD (tested on FreeBSD 7.2).

- Module works on Windows. Many thanks to `Develer`_ for providing a neat `GCC
  package for Windows with Python integration`__ that made the Windows porting
  painless.

  .. _Develer: http://www.develer.com/
  .. __: http://www.develer.com/oss/GccWinBinaries


Version 0.3
~~~~~~~~~~~

- Module works on Mac OS X 10.2. Reported working on OS X 10.6 too.


Version 0.2
~~~~~~~~~~~

- Added ``prctl()`` call on Linux >= 2.6.9 to update ``/proc/self/status``.


Version 0.1
~~~~~~~~~~~

- Initial public release.
