{"build": {"beforeBuildCommand": "", "beforeDevCommand": "", "devPath": "../dist", "distDir": "../dist", "withGlobalTauri": true}, "package": {"productName": "WeMa IA", "version": "0.1.2"}, "tauri": {"allowlist": {"all": true}, "bundle": {"active": true, "targets": "all", "identifier": "com.wema.ia", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["../py_backend/**/*", "python-embed/**/*"], "windows": {"nsis": {"installMode": "perMachine"}}}, "security": {"csp": null}, "windows": [{"title": "WeMa IA", "width": 1200, "height": 800, "decorations": false, "resizable": true, "center": true, "fullscreen": false, "maximized": false, "visible": true, "transparent": false, "alwaysOnTop": false}]}}