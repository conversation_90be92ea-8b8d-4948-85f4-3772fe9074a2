{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "WeMa IA", "version": "0.1.2", "identifier": "com.wema-ia.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "WeMa IA - Assistant IA Professionnel", "width": 1400, "height": 900, "resizable": true, "fullscreen": false, "decorations": false}], "security": {"csp": "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: file: app: http: https: ws: wss:; script-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: file: app: http: https:; style-src 'self' 'unsafe-inline' data: blob: file: app: https:; img-src 'self' data: blob: file: app: http: https:; connect-src 'self' data: blob: file: app: http: https: ws: wss:; font-src 'self' data: blob: file: app: https:;"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["../py_backend/**/*", "python-embed/**/*"]}}