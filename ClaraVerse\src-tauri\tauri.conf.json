{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "WeMa IA", "version": "0.1.2", "identifier": "com.wema.ia", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:1420", "beforeDevCommand": "", "beforeBuildCommand": ""}, "app": {"windows": [{"title": "WeMa IA - Test Interface", "url": "simple.html", "width": 1200, "height": 800, "resizable": true, "decorations": false, "center": true, "fullscreen": false, "maximized": false, "visible": true, "transparent": false, "alwaysOnTop": false}], "security": {"csp": null}, "withGlobalTauri": true}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["../py_backend/**/*", "python-embed/**/*"], "windows": {"nsis": {"installMode": "perMachine"}}}}