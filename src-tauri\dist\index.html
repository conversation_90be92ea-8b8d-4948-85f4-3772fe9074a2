<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WeMa IA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* Barre de titre personnalisée */
        .title-bar {
            background: rgba(255, 255, 255, 0.95);
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            -webkit-app-region: drag;
        }
        
        .title-bar .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: #4682B4;
        }
        
        .window-controls {
            display: flex;
            gap: 8px;
            -webkit-app-region: no-drag;
        }
        
        .control-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
        }
        
        .minimize { background: #ffbd2e; }
        .maximize { background: #28ca42; }
        .close { background: #ff5f56; }
        
        /* Interface principale */
        .main-container {
            flex: 1;
            display: flex;
            background: white;
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .new-chat-btn {
            width: 100%;
            padding: 12px;
            background: #4682B4;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .new-chat-btn:hover {
            background: #5a9bd4;
        }
        
        .chat-list {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
        }
        
        .chat-item {
            padding: 12px;
            margin-bottom: 4px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 14px;
            color: #666;
        }
        
        .chat-item:hover {
            background: #e9ecef;
        }
        
        .chat-item.active {
            background: #4682B4;
            color: white;
        }
        
        /* Zone de chat principale */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }
        
        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .welcome-message {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .welcome-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #4682B4;
        }
        
        .welcome-subtitle {
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .status-indicators {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-warning { background: #ffc107; }
        
        /* Zone de saisie */
        .input-area {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .message-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 22px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .message-input:focus {
            border-color: #4682B4;
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            background: #4682B4;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }
        
        .send-btn:hover {
            background: #5a9bd4;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 240px;
            }
        }
    </style>
</head>
<body>
    <!-- Barre de titre personnalisée -->
    <div class="title-bar">
        <div class="logo">
            🎯 WeMa IA
        </div>
        <div class="window-controls">
            <button class="control-btn minimize" onclick="minimizeWindow()"></button>
            <button class="control-btn maximize" onclick="toggleMaximize()"></button>
            <button class="control-btn close" onclick="closeWindow()"></button>
        </div>
    </div>

    <!-- Interface principale -->
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn" onclick="newChat()">
                    ➕ Nouvelle conversation
                </button>
            </div>
            <div class="chat-list" id="chatList">
                <div class="chat-item active">
                    💬 Conversation de test
                </div>
                <div class="chat-item">
                    📄 Analyse de document
                </div>
                <div class="chat-item">
                    🔍 Recherche internet
                </div>
            </div>
        </div>

        <!-- Zone de chat principale -->
        <div class="chat-area">
            <div class="chat-header">
                <div class="chat-title">Assistant WeMa IA</div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <div class="welcome-title">🎉 Bienvenue dans WeMa IA !</div>
                    <div class="welcome-subtitle">Votre assistant IA intelligent avec OCR et recherche internet</div>
                    
                    <div class="status-indicators">
                        <div class="status-item">
                            <div class="status-dot status-online"></div>
                            <span>Interface Tauri</span>
                        </div>
                        <div class="status-item">
                            <div class="status-dot" id="backendStatus"></div>
                            <span>Backend Python</span>
                        </div>
                        <div class="status-item">
                            <div class="status-dot status-warning"></div>
                            <span>Services externes</span>
                        </div>
                    </div>
                    
                    <p>Commencez une conversation en tapant votre message ci-dessous !</p>
                </div>
            </div>
            
            <div class="input-area">
                <div class="input-container">
                    <textarea 
                        class="message-input" 
                        id="messageInput"
                        placeholder="Tapez votre message ici..."
                        rows="1"
                        onkeydown="handleKeyDown(event)"
                    ></textarea>
                    <button class="send-btn" onclick="sendMessage()" id="sendBtn">
                        ➤
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let backendConnected = false;

        // Fonctions de contrôle de fenêtre
        function minimizeWindow() {
            console.log('Minimize window');
        }

        function toggleMaximize() {
            console.log('Toggle maximize');
        }

        function closeWindow() {
            console.log('Close window');
        }

        // Gestion des messages
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // Ajouter le message à la conversation
            addMessage('user', message);
            input.value = '';

            // Simuler une réponse de l'IA
            setTimeout(() => {
                addMessage('assistant', 'Merci pour votre message ! WeMa IA est en cours de développement. Toutes les fonctionnalités seront bientôt disponibles.');
            }, 1000);
        }

        function addMessage(sender, content) {
            const messagesContainer = document.getElementById('chatMessages');
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');

            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                margin-bottom: 16px;
                display: flex;
                ${sender === 'user' ? 'justify-content: flex-end;' : 'justify-content: flex-start;'}
            `;

            const messageBubble = document.createElement('div');
            messageBubble.style.cssText = `
                max-width: 70%;
                padding: 12px 16px;
                border-radius: 18px;
                ${sender === 'user'
                    ? 'background: #4682B4; color: white; border-bottom-right-radius: 4px;'
                    : 'background: white; color: #333; border: 1px solid #e9ecef; border-bottom-left-radius: 4px;'
                }
                box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            `;
            messageBubble.textContent = content;

            messageDiv.appendChild(messageBubble);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function newChat() {
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.innerHTML = `
                <div class="welcome-message">
                    <div class="welcome-title">💬 Nouvelle conversation</div>
                    <div class="welcome-subtitle">Que puis-je faire pour vous aujourd'hui ?</div>
                </div>
            `;
        }

        // Test de connexion au backend
        async function testBackend() {
            try {
                const response = await fetch('http://localhost:5001/health');
                if (response.ok) {
                    backendConnected = true;
                    document.getElementById('backendStatus').className = 'status-dot status-online';
                } else {
                    throw new Error('Backend non accessible');
                }
            } catch (error) {
                backendConnected = false;
                document.getElementById('backendStatus').className = 'status-dot status-offline';
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Test initial du backend
            testBackend();

            // Test périodique du backend
            setInterval(testBackend, 10000);

            // Auto-resize du textarea
            const textarea = document.getElementById('messageInput');
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        });
    </script>
</body>
</html>
