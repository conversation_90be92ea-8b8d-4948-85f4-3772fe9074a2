shapely-2.1.0.dist-info/DELVEWHEEL,sha256=RK2nA5dNd5kdP1UZEMGgb1bSs0KU_ME-f7wnMdPMfEs,448
shapely-2.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
shapely-2.1.0.dist-info/METADATA,sha256=prT5JYOF4Emu78YlMjUsxY73sj8Du0vzxwYJfT6D5MQ,7000
shapely-2.1.0.dist-info/RECORD,,
shapely-2.1.0.dist-info/WHEEL,sha256=ovhA9_Ei_7ok2fAych90j-feDV4goiAxbO7REePtvw0,101
shapely-2.1.0.dist-info/licenses/LICENSE.txt,sha256=6M19NGBNJ1j_WJ8PNNbtecP3-xSq9eEvsJIhQhORo78,1612
shapely-2.1.0.dist-info/licenses/LICENSE_GEOS,sha256=kaW7Ed9NRL_dwMaNtDaBcFMmEHeBWZ-2xv-CEEIHLWQ,27295
shapely-2.1.0.dist-info/licenses/LICENSE_win32,sha256=mzDNGgl4mITdVOWCjjHSv45oeQNOfPe24DIURM9t0R0,1179
shapely-2.1.0.dist-info/top_level.txt,sha256=fxc5UIKKldpKP3lx2dvM5R3hWeKwlmVW6-nfikr3iU0,8
shapely.libs/geos-09e9ae5b354ae95c622e115700f2f52a.dll,sha256=CIKXN3Kv2hq9dnewuAAFkfqy7r1yBOVkKR0vx4UyEyQ,2540032
shapely.libs/geos_c-16785b0a6a000a315b8d3e82abc3aa1b.dll,sha256=T64gGZkMBAj7g1HJLs_8RwsFpQ746_Lg5hMBQlBBr24,455168
shapely.libs/msvcp140-8a79f4687fc453279df1092923244d9e.dll,sha256=JstFQiTuk0ODEft1EkKBqe5_2V7S1u05HjXWn1gBLv0,567368
shapely/__init__.py,sha256=ty_fkptd1oTNQlNkBJl0zcsrmxATOek1tW0qTXtUbVQ,1389
shapely/__pycache__/__init__.cpython-312.pyc,,
shapely/__pycache__/_coverage.cpython-312.pyc,,
shapely/__pycache__/_enum.cpython-312.pyc,,
shapely/__pycache__/_geometry.cpython-312.pyc,,
shapely/__pycache__/_ragged_array.cpython-312.pyc,,
shapely/__pycache__/_version.cpython-312.pyc,,
shapely/__pycache__/affinity.cpython-312.pyc,,
shapely/__pycache__/conftest.cpython-312.pyc,,
shapely/__pycache__/constructive.cpython-312.pyc,,
shapely/__pycache__/coordinates.cpython-312.pyc,,
shapely/__pycache__/coords.cpython-312.pyc,,
shapely/__pycache__/creation.cpython-312.pyc,,
shapely/__pycache__/decorators.cpython-312.pyc,,
shapely/__pycache__/errors.cpython-312.pyc,,
shapely/__pycache__/geos.cpython-312.pyc,,
shapely/__pycache__/io.cpython-312.pyc,,
shapely/__pycache__/linear.cpython-312.pyc,,
shapely/__pycache__/measurement.cpython-312.pyc,,
shapely/__pycache__/ops.cpython-312.pyc,,
shapely/__pycache__/plotting.cpython-312.pyc,,
shapely/__pycache__/predicates.cpython-312.pyc,,
shapely/__pycache__/prepared.cpython-312.pyc,,
shapely/__pycache__/set_operations.cpython-312.pyc,,
shapely/__pycache__/speedups.cpython-312.pyc,,
shapely/__pycache__/strtree.cpython-312.pyc,,
shapely/__pycache__/testing.cpython-312.pyc,,
shapely/__pycache__/validation.cpython-312.pyc,,
shapely/__pycache__/wkb.cpython-312.pyc,,
shapely/__pycache__/wkt.cpython-312.pyc,,
shapely/_coverage.py,sha256=jC7BunWou2pyPX-_dKRyKe_U93zwMaWd25GUTKl3HcI,5900
shapely/_enum.py,sha256=BXK9orqIVkDc0NpLtJGtfQfCVSegNcw1q2l8Z48oTG8,727
shapely/_geometry.py,sha256=j8-O0ExHK1mVXNXqzn0q35AFtW24JjSKMUYCCoHpUUQ,30182
shapely/_geometry_helpers.cp312-win_amd64.pyd,sha256=a6yjjnogCBHjUC6TUYZgYwjw_iaBOC_C7BAdVdBLFS0,237568
shapely/_geos.cp312-win_amd64.pyd,sha256=05z7xe_dXwarjopfYVk8ai0fGsWm8sl_M4VAYRQlVuY,47616
shapely/_geos.pxd,sha256=QFbsWM2tZSfUg_rCL_X4KzTWMBylwpAAh-5XP8VGALA,3266
shapely/_pygeos_api.pxd,sha256=o7PHZlQBieKIaWYJClSMMsgs3Qh-I1x1N18mj6JaA_c,2148
shapely/_ragged_array.py,sha256=v_EZsb6uhVGA-nIP4O-YkwvMyleGEfvD9X6V96z16ks,17920
shapely/_version.py,sha256=MW5r3uZxvgrxRFzVeIQIATkIQjp0cGzcBS4pwBM6Dj4,518
shapely/affinity.py,sha256=X4ghrHeaqYuZ06T6aRbYctMyV-4PaEPIthwP4KtT68g,8521
shapely/algorithms/__init__.py,sha256=gKHi5bgMJDFY11TRIQRc1tmQfm61SAViPQLh6lN2Jn0,42
shapely/algorithms/__pycache__/__init__.cpython-312.pyc,,
shapely/algorithms/__pycache__/_oriented_envelope.cpython-312.pyc,,
shapely/algorithms/__pycache__/cga.cpython-312.pyc,,
shapely/algorithms/__pycache__/polylabel.cpython-312.pyc,,
shapely/algorithms/_oriented_envelope.py,sha256=ukUTqcm9VEH_0gsfi2UmhAaH8m-C1bYOFRtG_LgPRmQ,1989
shapely/algorithms/cga.py,sha256=7jj2vDuTzBnuPOz-tzdaeLk-1SzLw1XmSMVOxV03iDE,1807
shapely/algorithms/polylabel.py,sha256=6uxGdA-HMI5TtXux964NqXB6pb7Lp8oMMFefjHKaXk0,1387
shapely/conftest.py,sha256=MWO_Q66H-fMpvuxoe77AL4LpoNAvhOR9ag8sqkyDGM0,2017
shapely/constructive.py,sha256=5tAX1ScsvulOfs1yUvIFipV-Z6KHSYMCmGalbISOJaI,57741
shapely/coordinates.py,sha256=vNTmuP5WLFJ_usDqKBpBgRDjQ-Sp7TxKL2WVEJcN3FE,12910
shapely/coords.py,sha256=A_xcsk0oSRRUlRUtQ1CE_WajtzlARv7hb2rlnSMlNQU,3151
shapely/creation.py,sha256=JEHe6vHF7HzzsttcKuIYODrjAvDZDfQ2REWWgFdxXbo,31818
shapely/decorators.py,sha256=wQDh2f06_lCeyH7iBtTh4zTU52Jb_kB8mlS-1ZpM9WY,4440
shapely/errors.py,sha256=7rp8KlhXuqgXthW0BYU1lnp5ilsoRbzAqqaq_JoQKdY,2477
shapely/geometry/__init__.py,sha256=XOb7gR_CiJO2WMTnyJjUipoNhOJoNAn66Mn3eD4nDPc,790
shapely/geometry/__pycache__/__init__.cpython-312.pyc,,
shapely/geometry/__pycache__/base.cpython-312.pyc,,
shapely/geometry/__pycache__/collection.cpython-312.pyc,,
shapely/geometry/__pycache__/geo.cpython-312.pyc,,
shapely/geometry/__pycache__/linestring.cpython-312.pyc,,
shapely/geometry/__pycache__/multilinestring.cpython-312.pyc,,
shapely/geometry/__pycache__/multipoint.cpython-312.pyc,,
shapely/geometry/__pycache__/multipolygon.cpython-312.pyc,,
shapely/geometry/__pycache__/point.cpython-312.pyc,,
shapely/geometry/__pycache__/polygon.cpython-312.pyc,,
shapely/geometry/base.py,sha256=uXhRHpspFL3AmU0sqpUgXhAVy942yiqTOH1Gjh63h5o,41513
shapely/geometry/collection.py,sha256=Y9wSe7BoOlkbEid4fk9pagM_q4x5SuDCYAcX3xx4bqA,1794
shapely/geometry/geo.py,sha256=UsIPPqc1MI8Tl82NEYgqgorgTe77JAPxqVteuu6YAfU,4509
shapely/geometry/linestring.py,sha256=gBOEvDggQ-0Gwl4kb0MJyHJutmjOQHUgqV2rKJXkS4w,7810
shapely/geometry/multilinestring.py,sha256=m7ko91-hHdRwCEIfEMhSu6e-hN5_exfxrSardNiMDys,3037
shapely/geometry/multipoint.py,sha256=UrmR91lga1EgwJ8CD12uYKFm93KNn8tQ3m78G2YgUUE,3326
shapely/geometry/multipolygon.py,sha256=BD_e1p5u8_4_znxvruxyJtm5V7mH6eBdOXELKecouR0,4080
shapely/geometry/point.py,sha256=ATWrHfD1JooJ2OxQXkBnkGG_87TV0AGtZViNZwVJ1wI,4897
shapely/geometry/polygon.py,sha256=yJnR_mRi4ZU9UO6eY5mIlT6WhtHg8cbFs_7MGMIyu8Y,11427
shapely/geos.py,sha256=FzQt3XRMevaInvNNGqgheIUCkbadDjm0Q16dOv_qGpE,537
shapely/io.py,sha256=3g93rxqDKzTvBj8Lqz2AOGOF3rSv610NgtfXVsn9Bjo,15458
shapely/lib.cp312-win_amd64.pyd,sha256=1g_7BGUy535hP2Jafw8dn-FaaDA-ceG8509LSOzjdLo,155648
shapely/linear.py,sha256=9ATypzG9QjZ77ANStvV3hRAYS4HHvZq3LJkmWKnIjFE,8911
shapely/measurement.py,sha256=j90aRaeWWyfU1cOoUl46PIUlLtzdD4tMMmRn_buJmtg,10977
shapely/ops.py,sha256=4coKhUZpsdN1VC7P62obg4LzGdkvQi3QtwMR__wYNgE,26399
shapely/plotting.py,sha256=izrVm2UZtG2l5oimex2AlVJagFlNM7to_JZs8DAC1l8,6544
shapely/predicates.py,sha256=E6Lk_-s-ZkDYIZvImQtzAH_evLHEN6lgbMDXtZYh4kY,41023
shapely/prepared.py,sha256=lVcq8nnbEFr0XNFC1QT71KqRLIQ0ubShdD_MEPmetf0,2425
shapely/set_operations.py,sha256=XyN91QFHY0bf7Q8L6OVPTs0Ah0dRiSUTdr7maJczYzQ,28057
shapely/speedups.py,sha256=I7oExYelb02Cv1F-1TeRlA3oSWwSxWlpD6upnwABH0c,1077
shapely/strtree.py,sha256=gZRiqJ9uh4Iu_8ewaiDmSAb62Omwyb65cqd2HqUOdjA,21199
shapely/testing.py,sha256=Y7zW35t77DdYl7PZun8G0NLpju7igON02JVf_W8V8fg,6680
shapely/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/__pycache__/__init__.cpython-312.pyc,,
shapely/tests/__pycache__/common.cpython-312.pyc,,
shapely/tests/__pycache__/test_constructive.cpython-312.pyc,,
shapely/tests/__pycache__/test_coordinates.cpython-312.pyc,,
shapely/tests/__pycache__/test_coverage.cpython-312.pyc,,
shapely/tests/__pycache__/test_creation.cpython-312.pyc,,
shapely/tests/__pycache__/test_creation_indices.cpython-312.pyc,,
shapely/tests/__pycache__/test_geometry.cpython-312.pyc,,
shapely/tests/__pycache__/test_io.cpython-312.pyc,,
shapely/tests/__pycache__/test_linear.cpython-312.pyc,,
shapely/tests/__pycache__/test_measurement.cpython-312.pyc,,
shapely/tests/__pycache__/test_misc.cpython-312.pyc,,
shapely/tests/__pycache__/test_plotting.cpython-312.pyc,,
shapely/tests/__pycache__/test_predicates.cpython-312.pyc,,
shapely/tests/__pycache__/test_ragged_array.cpython-312.pyc,,
shapely/tests/__pycache__/test_set_operations.cpython-312.pyc,,
shapely/tests/__pycache__/test_strtree.cpython-312.pyc,,
shapely/tests/__pycache__/test_testing.cpython-312.pyc,,
shapely/tests/common.py,sha256=4T0TCdEdSBOxDc2xoD1h42jwZScWrm_FUxau97B4uX4,10436
shapely/tests/geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/geometry/__pycache__/__init__.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_collection.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_coords.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_decimal.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_emptiness.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_equality.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_format.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_geometry_base.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_hash.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_linestring.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multi.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multilinestring.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multipoint.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_multipolygon.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_point.cpython-312.pyc,,
shapely/tests/geometry/__pycache__/test_polygon.cpython-312.pyc,,
shapely/tests/geometry/test_collection.py,sha256=BD6VLitesiBfo2zEMOrzMYoWATgxMs2tM72er6YmBtQ,2764
shapely/tests/geometry/test_coords.py,sha256=LZK-3-Dzi7Q6cPv5KJ73u-zQXZ-NB8z1bjilBWeOazE,4117
shapely/tests/geometry/test_decimal.py,sha256=CACfZiguJXUEbO0wY2FpjzKQMkQ2pUg0MPhQi06Qx4Y,2963
shapely/tests/geometry/test_emptiness.py,sha256=y-5qtOovLKCiMU0weM3uVc19bXjw2oYm157jKU5hgig,2460
shapely/tests/geometry/test_equality.py,sha256=gny5W6iGeekXyyPc1fCrmG0uOtYD9BkdDbOCq0NFCqM,9720
shapely/tests/geometry/test_format.py,sha256=7Yi3psWtTpHVhRwvxne0WUK1YXbaSwxBEv2ycbXGw08,4636
shapely/tests/geometry/test_geometry_base.py,sha256=XSR7IhCLMfHuw30iK9hN725bfjRELCAaCsgoS0SUGhA,11095
shapely/tests/geometry/test_hash.py,sha256=qR-Sqlcs3cCzHKsL3YC5U1-duF6X80C7wKsMW2mdBmU,701
shapely/tests/geometry/test_linestring.py,sha256=eqwV_bzWf17-J9uMxaibkluxleXfQEg-QDqwBJrRqm4,7275
shapely/tests/geometry/test_multi.py,sha256=Vs4AWLbj6IxCZrNq4XPzTQLOuMMGS0Hcy6cvcHEH25Q,308
shapely/tests/geometry/test_multilinestring.py,sha256=G2VSP_a2vWcv1fAXyMQ2ExnOeFh0-aFx0kb-MtYTvvU,2911
shapely/tests/geometry/test_multipoint.py,sha256=UE99Ka-H7rNM7tYquFmHz_s3mFR7zFqB-Fks77a-pdc,3140
shapely/tests/geometry/test_multipolygon.py,sha256=Nv7YBFgdqfuRD4m8ob8H2vF_FlqkqMnKbq5iIkT2POI,5063
shapely/tests/geometry/test_point.py,sha256=l7OGiVJINgV-izbH2rBFIADWcMGOvZn4Pzy-u_QVi7E,5533
shapely/tests/geometry/test_polygon.py,sha256=Kpva6Gm8IZrPvgdXoicFqS1PDMTZSPrkCRAQpPmqWOg,15763
shapely/tests/legacy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
shapely/tests/legacy/__pycache__/__init__.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/conftest.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_affinity.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_box.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_buffer.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_cga.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_clip_by_rect.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_create_inconsistent_dimensionality.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_delaunay.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_empty_polygons.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_equality.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_geointerface.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_invalid_geometries.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_linear_referencing.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_linemerge.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_locale.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_make_valid.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_mapping.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_minimum_clearance.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_ndarrays.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_nearest.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_operations.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_operators.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_orient.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_parallel_offset.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_persist.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_pickle.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_polygonize.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_polylabel.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_predicates.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_prepared.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_products_z.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_shape.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_shared_paths.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_singularity.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_snap.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_split.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_substring.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_svg.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_transform.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_union.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_validation.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_vectorized.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_voronoi_diagram.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_wkb.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/test_wkt.cpython-312.pyc,,
shapely/tests/legacy/__pycache__/threading_test.cpython-312.pyc,,
shapely/tests/legacy/conftest.py,sha256=p6jJZKylDNhHl0ycKeiClIsykuqdXwv6bkRjb80FnC8,346
shapely/tests/legacy/test_affinity.py,sha256=gSOZSxxjhWpHwWLs89paDbzcGJVgeHFnK-kNEN3Iqcc,12791
shapely/tests/legacy/test_box.py,sha256=SUlhrgWOIXkE38pNPO0e2nabDFXD1mLBVze7IniXO8E,619
shapely/tests/legacy/test_buffer.py,sha256=hROICNB47R4vJZfafxypTcwP7i5ypdTu_f9ffgfW91k,6769
shapely/tests/legacy/test_cga.py,sha256=xxLZUg5t0JO08yXWY8-Q4H3nd6LWODAVG3P_trWZNc8,1560
shapely/tests/legacy/test_clip_by_rect.py,sha256=Q9bgHlFxGnZG-yqttpv0qnWWbCh8kNWL7iSqPJZTAH0,4156
shapely/tests/legacy/test_create_inconsistent_dimensionality.py,sha256=sP7oasxUAaQzakbANLVHf7haRPOGNwS4TGlgBiGktxk,10889
shapely/tests/legacy/test_delaunay.py,sha256=XsoaoB5NpKHf_njWFpLO8aMiJTWwWwPf7F-ZAeQRemY,870
shapely/tests/legacy/test_empty_polygons.py,sha256=NGwmu47YMEIlUyhMu6-0NhTwpndpb10d8IOPjZzEj8U,705
shapely/tests/legacy/test_equality.py,sha256=Drk2QcQPGYBglDY0PL3icJ2sIcEfMxFrBg_KwS4xinQ,771
shapely/tests/legacy/test_geointerface.py,sha256=nOVut4xdBtzhcUtZ1nYCUIfTLtUb2pf0rzBNiHbakhs,3766
shapely/tests/legacy/test_invalid_geometries.py,sha256=wy54Xrn-HNH-NHFM95nlotIqjJyvgO05CpXTRolf-oM,917
shapely/tests/legacy/test_linear_referencing.py,sha256=4Dk1w-IqvDz7qscb3ehnn7ytOo5c6_4PKI6juTsxFK4,2986
shapely/tests/legacy/test_linemerge.py,sha256=50ypKSu63pGPkupDNR7IsRq8lSw4s0um-BrnvL7ml3k,1372
shapely/tests/legacy/test_locale.py,sha256=1zG_rHEJU87jj9sQd-82QXt349Ze7YfeyhDyZpeJ6Cw,1488
shapely/tests/legacy/test_make_valid.py,sha256=PWzvA3FfHzAT-n7spV-q-R1Mc_2jzE7SWPZP_N_QcgQ,494
shapely/tests/legacy/test_mapping.py,sha256=LDLVt3GpiUiDctmiRGRcBvWiBapnOjKroCTs69bXIsM,409
shapely/tests/legacy/test_minimum_clearance.py,sha256=VKwP9a1o_Ycej80vzJFK6olR325_uXqCtAwhbTC17DI,724
shapely/tests/legacy/test_ndarrays.py,sha256=CJqtQswPW_5zJbNvyWrAi8DgIYtbrgGJN4WqaUhlS-o,1286
shapely/tests/legacy/test_nearest.py,sha256=t2laaDOzrLp0N3kod_dAsVE2Vh55A7v3iE_eNKFIujs,494
shapely/tests/legacy/test_operations.py,sha256=MMz6c-AzQNeIJ8yJdQ-cDM9Qopy-9-nSLwbCL3lb_c8,4431
shapely/tests/legacy/test_operators.py,sha256=6tBr4jguWQfvCN0PHKT7c75cQ8VuzhRatTyClT2ha1Q,2008
shapely/tests/legacy/test_orient.py,sha256=a8E5Ew0917N6Hy74rVAsfOhDEi6B1MxF1BCzvll2v6U,3607
shapely/tests/legacy/test_parallel_offset.py,sha256=PTgQI0YRTmVOZ3hJvA8mbIarv6V1ey_PvYQDwETFACg,2392
shapely/tests/legacy/test_persist.py,sha256=8fZcbb_9ircx-zrHLfe_MSjZIUhdpdztsNk3BuxkHIc,1758
shapely/tests/legacy/test_pickle.py,sha256=f_pC7Ilm3Cm2v7pNf02mh1FOG_-DGyZeALpTKfnTGtA,2449
shapely/tests/legacy/test_polygonize.py,sha256=afl33mXflRiV-jpXw9Ih1DO4MiQUhFBgxE9VRAhLqYQ,1431
shapely/tests/legacy/test_polylabel.py,sha256=qn-_aBSWDSNAxWtRvLZu-nxHAqqj6eKuwAvSpp2HOWY,3053
shapely/tests/legacy/test_predicates.py,sha256=UWo-wgGPd-OBmqGy1lH0uejy38oEiA1DA1NPYFe-sgY,3053
shapely/tests/legacy/test_prepared.py,sha256=ypuwV9WzqpF9B4ZvFJ5ngBipNrHvrVKbLljSMSzpdvo,2541
shapely/tests/legacy/test_products_z.py,sha256=ZUIwvBhnb1E7wnSHmQMYEznDAUDDe6ZrpqTzb3zOR_Q,401
shapely/tests/legacy/test_shape.py,sha256=Ibc-8oKBwfRm_7qdDoN4Lxd_rTLiyQSPQ7tl57uVmAY,1872
shapely/tests/legacy/test_shared_paths.py,sha256=C85sQrc6LWkGX1UK70MnGXagJOYdTvehdfCfOivF-sc,1479
shapely/tests/legacy/test_singularity.py,sha256=1RA1VwOpmbYyd8Z51L8WZP2eDEG1r9VbSga4jZSIhOg,396
shapely/tests/legacy/test_snap.py,sha256=ZKYBNTm6rpn8RlfZ5pX8I1CXY1z722bHXMPULvo_Cy0,787
shapely/tests/legacy/test_split.py,sha256=sUi0DaWzMj_96Kx94qp0VuavNXQbVGRojsWYDHLlxlc,11616
shapely/tests/legacy/test_substring.py,sha256=EtBEl10ztLMbNv4CDzkgqZ46LGuh1W7iuMDUYK4M-Jo,21382
shapely/tests/legacy/test_svg.py,sha256=k6t_Swtcxm2x0IpXzfmJmWPyEUuYmXA1ctEVEhb6bFU,8466
shapely/tests/legacy/test_transform.py,sha256=jqcm1hgDhEibUvsjyPEo0nhMrHvyfK0-NiJ5-k3A9Bk,2785
shapely/tests/legacy/test_union.py,sha256=meb370rCmz7v4JrhURj2b97wbnUaq1-pBxaAPpCSnMY,2160
shapely/tests/legacy/test_validation.py,sha256=pF143oq5dwri0wgirycIwTDeXGct5Y0gfqMRHewFPfM,247
shapely/tests/legacy/test_vectorized.py,sha256=QYYCczr9lR5UBsy-QQcmcxRDzDAgUvFHItbE7-pEkiU,3831
shapely/tests/legacy/test_voronoi_diagram.py,sha256=rfbqj8tkvULQMBscPSUBvmp7Oe-riW1JTngcZetEfhE,4209
shapely/tests/legacy/test_wkb.py,sha256=h_HtdSFd-AMyYhA1etNFZLathOkOHCLZWysOy7CNXyM,5734
shapely/tests/legacy/test_wkt.py,sha256=6qWaumMDNbiZx8bWDuk7ThwaqFWVnjZ4O_tykSSZsVU,1661
shapely/tests/legacy/threading_test.py,sha256=9EaJ5QMPKPHHTOs-byX1VVvmb7IRK31Xw67OHRM1JVs,1048
shapely/tests/test_constructive.py,sha256=WwIYZdQREQ8DxkOO5-Oau0fU1rBhJcfBUzA4RCL81zw,47473
shapely/tests/test_coordinates.py,sha256=NYUUt3_fcfBqgJipEQiTsj_xEheN2Os4PdwQYxHPUXA,13716
shapely/tests/test_coverage.py,sha256=yUN1ZWQlL3QEnSz89nYAxNkLogUCCbUjjc7rV-RouXM,9827
shapely/tests/test_creation.py,sha256=i77cxZhvJUv3yC2UzfSaj6wpBuxopiMa04F868JIInw,23087
shapely/tests/test_creation_indices.py,sha256=Ul1xrYrRwDO9btaMbRUYzt_4NEDLqBndilJtmlKcSb0,19182
shapely/tests/test_geometry.py,sha256=5Xf4Rz-40bGvSclVMLeDSQHhn7hc-5uAayKolZxtR5o,24560
shapely/tests/test_io.py,sha256=Ry3f74X7MlOYuNmFh3_7301HsluVDkqxKy3bG7q6IPM,51413
shapely/tests/test_linear.py,sha256=V3zakrqjrz910HS9gAETuBZeZUKx3mcV6tgE9pXeV54,7950
shapely/tests/test_measurement.py,sha256=eyatnfpLC5ger1rVFEA6Bg54tvAbUna6unNKmtKSIu8,10292
shapely/tests/test_misc.py,sha256=4dL3wG3XuDOdiiBhXtWio8KYJadz4whWMdhi1oN8ySA,5583
shapely/tests/test_plotting.py,sha256=d0mFw892jP7eAv3Kf1m7PVciLdP4ONXpW73aOITidLs,4164
shapely/tests/test_predicates.py,sha256=x43l0jY9swyf9wOVZ96H1vFhHlAQ9fb_xQWFUzXm0dI,14128
shapely/tests/test_ragged_array.py,sha256=RDth3U5TJ-YPLDgArSize7FzWkYCT2h6PYk948s8mJk,18891
shapely/tests/test_set_operations.py,sha256=2yY0GpsVu5XVAe9lWFMXHOFax0f5j60SFGb8fF2dD4A,17953
shapely/tests/test_strtree.py,sha256=9VEaBqG1fPqn2kOohAv9SVodNmmiBLDxBipGF_mMiOU,75475
shapely/tests/test_testing.py,sha256=N4xkupVi3REhDj8mPWBny9DZyMHtVuGsL0xKD-Cn8Q4,2943
shapely/validation.py,sha256=4wsjQ2PDzYLwDyu7jUiSPfCuRzc7_OeVV1aldAB74gs,1526
shapely/vectorized/__init__.py,sha256=xzBDD6Eo8zLV7tJsZi4m4YyQMeLJ2OhJ9XL5rNvpSHE,2903
shapely/vectorized/__pycache__/__init__.cpython-312.pyc,,
shapely/wkb.py,sha256=sife8abiC1ce8VYOqznYpB8pGcy6i5oERM6YCF0M-I8,2007
shapely/wkt.py,sha256=6taXCrIc0uTKRCHjiDb82PUbRGMs7QlK6X59AaFTNzo,1815
