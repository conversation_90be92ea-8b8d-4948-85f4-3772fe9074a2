Metadata-Version: 2.1
Name: red-black-tree-mod
Version: 1.22
Summary: Flexible python implementation of red black trees
Home-page: http://stromberg.dnsalias.org/~strombrg/red-black-tree-mod/
Author: <PERSON>, <PERSON>
Author-email: <EMAIL>
License: MIT
Platform: Cross platform
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
License-File: COPYING


A pair of python modules implementing red black trees is provided.

Red-black trees are a little slower than treaps (some question this), but they give a nice
low standard deviation in operation times, and this code is rather flexible.

A module is provided for red black trees that enforce uniqueness.
They allow for set-like use and dictionary-like use.

This code is known to work on CPython 2.x, CPython 3.x, Pypy and Jython.

Much of the work here was done by <PERSON>.  <PERSON> just put some finishing touches on it.
