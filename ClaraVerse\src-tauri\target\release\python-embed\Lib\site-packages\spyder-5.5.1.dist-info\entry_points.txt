[gui_scripts]
spyder = spyder.app.start:main

[spyder.completions]
fallback = spyder.plugins.completion.providers.fallback.provider:FallbackProvider
lsp = spyder.plugins.completion.providers.languageserver.provider:LanguageServerProvider
snippets = spyder.plugins.completion.providers.snippets.provider:SnippetsProvider

[spyder.plugins]
appearance = spyder.plugins.appearance.plugin:Appearance
application = spyder.plugins.application.plugin:Application
breakpoints = spyder.plugins.breakpoints.plugin:Breakpoints
completions = spyder.plugins.completion.plugin:CompletionPlugin
editor = spyder.plugins.editor.plugin:Editor
explorer = spyder.plugins.explorer.plugin:Explorer
find_in_files = spyder.plugins.findinfiles.plugin:FindInFiles
help = spyder.plugins.help.plugin:Help
historylog = spyder.plugins.history.plugin:HistoryLog
internal_console = spyder.plugins.console.plugin:Console
ipython_console = spyder.plugins.ipythonconsole.plugin:IPythonConsole
layout = spyder.plugins.layout.plugin:Layout
main_interpreter = spyder.plugins.maininterpreter.plugin:MainInterpreter
mainmenu = spyder.plugins.mainmenu.plugin:MainMenu
onlinehelp = spyder.plugins.onlinehelp.plugin:OnlineHelp
outline_explorer = spyder.plugins.outlineexplorer.plugin:OutlineExplorer
plots = spyder.plugins.plots.plugin:Plots
preferences = spyder.plugins.preferences.plugin:Preferences
profiler = spyder.plugins.profiler.plugin:Profiler
project_explorer = spyder.plugins.projects.plugin:Projects
pylint = spyder.plugins.pylint.plugin:Pylint
pythonpath_manager = spyder.plugins.pythonpath.plugin:PythonpathManager
run = spyder.plugins.run.plugin:Run
shortcuts = spyder.plugins.shortcuts.plugin:Shortcuts
statusbar = spyder.plugins.statusbar.plugin:StatusBar
toolbar = spyder.plugins.toolbar.plugin:Toolbar
tours = spyder.plugins.tours.plugin:Tours
variable_explorer = spyder.plugins.variableexplorer.plugin:VariableExplorer
workingdir = spyder.plugins.workingdirectory.plugin:WorkingDirectory
