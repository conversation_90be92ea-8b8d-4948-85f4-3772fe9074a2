tlz/__init__.py,sha256=KH1pMvZOKkVJ_WAPX6vZUsC0KGKxGQBzxumWCzgvKSU,338
tlz/__pycache__/__init__.cpython-312.pyc,,
tlz/__pycache__/_build_tlz.cpython-312.pyc,,
tlz/_build_tlz.py,sha256=E5k4PUdwkW4j9dv94LbYrRRKXh4QSXQ-bB3HZz9H98o,3143
toolz-0.12.0.dist-info/AUTHORS.md,sha256=0aTPbfxkAVlbcP3TN0e8PM_5gTQeeaviWCkhFZBOU0c,1561
toolz-0.12.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
toolz-0.12.0.dist-info/LICENSE.txt,sha256=BTZkBXspWy8MEzIpGncQLwGgmdh5JuRJ4IoRfuqWYL8,1492
toolz-0.12.0.dist-info/METADATA,sha256=Wx7epnFZVGKtTpVtiviHf3XAXqF-_Mp15V4wYfYqwh8,5287
toolz-0.12.0.dist-info/RECORD,,
toolz-0.12.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
toolz-0.12.0.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
toolz-0.12.0.dist-info/direct_url.json,sha256=s8ftDz4vxCha9nhNgf1T4YRy7YhU6blJidLaYAUiLdU,113
toolz-0.12.0.dist-info/top_level.txt,sha256=oesCzopYB9UXPR4nDzhYaf-TbpkQoR6gYSsXvZDgk3s,10
toolz/__init__.py,sha256=9c9B1vNRz_0LO4-t3tTDEnZGNBTYsLevrc9ELtaxPpw,381
toolz/__pycache__/__init__.cpython-312.pyc,,
toolz/__pycache__/_signatures.cpython-312.pyc,,
toolz/__pycache__/_version.cpython-312.pyc,,
toolz/__pycache__/compatibility.cpython-312.pyc,,
toolz/__pycache__/dicttoolz.cpython-312.pyc,,
toolz/__pycache__/functoolz.cpython-312.pyc,,
toolz/__pycache__/itertoolz.cpython-312.pyc,,
toolz/__pycache__/recipes.cpython-312.pyc,,
toolz/__pycache__/utils.cpython-312.pyc,,
toolz/_signatures.py,sha256=800rPZ7TOWEFcRp2AxczTepkpRv0usAayDJMhoakJwQ,20542
toolz/_version.py,sha256=2JIhUGrKsrgMu3MhpEVt1vfFd7HMDINBN0l3a38Dag8,519
toolz/compatibility.py,sha256=giOYcwv1TaOWDfB-C2JP2pFIJ5YZX9aP1s4UPzCQnw4,997
toolz/curried/__init__.py,sha256=U_d0nFlOWYN1DJc5n5J2rToauHU7QEe45CME1jRLT1Y,2700
toolz/curried/__pycache__/__init__.cpython-312.pyc,,
toolz/curried/__pycache__/exceptions.cpython-312.pyc,,
toolz/curried/__pycache__/operator.cpython-312.pyc,,
toolz/curried/exceptions.py,sha256=31Q9I-Ro-IMA3eRuDC5wSQXQBYlpdxonnJr4Fp0hquI,337
toolz/curried/operator.py,sha256=miFisWLO3TeVFS_pwnaZdSw4ZTHhNrLz-vGSc--7iRo,529
toolz/dicttoolz.py,sha256=sE8wlGNLezhdmkRqB2gQcxSbwbO6-c-4SVbY-yFjuoE,8955
toolz/functoolz.py,sha256=nfGQZkap4ILVyaf2oAXdi8sNYov5XI1SHqlmPHG3gc0,29820
toolz/itertoolz.py,sha256=VxReBkhReig08vpJOkoyA5jmU-yZ2h5HgQQ7B7VSjcw,27617
toolz/recipes.py,sha256=r_j701Ug2_oO4bHunoy1xizk0N-m9QBwObyCITJuF0I,1256
toolz/sandbox/__init__.py,sha256=ysAYIaGROpbNy2-lYEeiVflJCqEOX9MWIHAIR9Bc6AA,68
toolz/sandbox/__pycache__/__init__.cpython-312.pyc,,
toolz/sandbox/__pycache__/core.cpython-312.pyc,,
toolz/sandbox/__pycache__/parallel.cpython-312.pyc,,
toolz/sandbox/core.py,sha256=aM_rF0S87FSvzU9qLdfmBeskyRwN985hCbnAbFOtzTw,4335
toolz/sandbox/parallel.py,sha256=5c_as0NHs85os9lTej9QIUCMC5MWlSXvmQYFl9uBb7E,2787
toolz/tests/__pycache__/test_compatibility.cpython-312.pyc,,
toolz/tests/__pycache__/test_curried.cpython-312.pyc,,
toolz/tests/__pycache__/test_curried_doctests.cpython-312.pyc,,
toolz/tests/__pycache__/test_dicttoolz.cpython-312.pyc,,
toolz/tests/__pycache__/test_functoolz.cpython-312.pyc,,
toolz/tests/__pycache__/test_inspect_args.cpython-312.pyc,,
toolz/tests/__pycache__/test_itertoolz.cpython-312.pyc,,
toolz/tests/__pycache__/test_recipes.cpython-312.pyc,,
toolz/tests/__pycache__/test_serialization.cpython-312.pyc,,
toolz/tests/__pycache__/test_signatures.cpython-312.pyc,,
toolz/tests/__pycache__/test_tlz.cpython-312.pyc,,
toolz/tests/__pycache__/test_utils.cpython-312.pyc,,
toolz/tests/test_compatibility.py,sha256=Xbgk60ow92Oqbpmhspwy72T9YpUad_tu55Hj-4o9le4,261
toolz/tests/test_curried.py,sha256=LF2PkbaGVlN4J0c58T7ekTuWNlh0r7eRpV2oWzuf4jM,3724
toolz/tests/test_curried_doctests.py,sha256=9p_RwDKeG_8EXUoqKFAJ-zyp4KHp2w3lLFxOB5XXqww,274
toolz/tests/test_dicttoolz.py,sha256=7RVKgjun-OY9-FeQR9T1xJwM03wfedVYu6SHCsWwMZ0,9070
toolz/tests/test_functoolz.py,sha256=qvtpQyoeV8ZpVY0bBbXshsrhXd9EQ0Dk2E5Yy4QE8KY,20205
toolz/tests/test_inspect_args.py,sha256=pXzo8tHtGWa4TJKWqpv7TUsGo1LJiWDCCCupP8rp-mo,15960
toolz/tests/test_itertoolz.py,sha256=gsL7M9lZGTUU2M0ZtZ-M91WToJQSnXmOR3rkiT5TQ4k,18181
toolz/tests/test_recipes.py,sha256=hZ_nuGAOIafJrJwnnj9-JZnaRq9srIXyPQyzBdNS1FQ,820
toolz/tests/test_serialization.py,sha256=wsvAClD4eOxQdt_CWx2et_dD0Zy24iQypzDfTZiwnf0,5791
toolz/tests/test_signatures.py,sha256=X8K_rXS1OXI22yscj3zUhWxWWTVII5Cf2RJyXfh_2HA,2873
toolz/tests/test_tlz.py,sha256=LS5ICqieRLkjAUP-C5TjdVeld6S9OLXTYGgH85fYkWw,1593
toolz/tests/test_utils.py,sha256=2LIhS_9xXeAE1_onN868gZeAN8E3jTXtpnYF0W-L2OE,156
toolz/utils.py,sha256=JLlXt8x_JqSVevmLZPnt5bZJsdKMBJgJb5IwlcfOnsc,139
