../../Scripts/spyder.exe,sha256=jDZt5Zj_BkNCd89UHpbr0vahq_4e90cCyR4m0B_0a8Y,102274
../../scripts/spyder.ico,sha256=mTe2RwxRxKOWqWAp5MOSsKFN1d6JdBqCx-PZ9lLnAJ0,311358
../../scripts/spyder_reset.ico,sha256=LhU-cleWAGpALh-Zffwuxg88ngJlvKSVQU0E2bfNs7k,99495
spyder-5.5.1.dist-info/AUTHORS.txt,sha256=E00aNgRz9ihwIQvyWnzNrDF0N-DUtcdkkQEnDYRHMKo,1194
spyder-5.5.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spyder-5.5.1.dist-info/LICENSE.txt,sha256=fqH7xzTb5VuRXkGgzMaPEDuVVXXft-pOc28aNGIL0Lc,1199
spyder-5.5.1.dist-info/METADATA,sha256=AnpAYyAfWt8K9Ld-Uzc_aaju0N09L2qgQG4s2AhzBsc,23170
spyder-5.5.1.dist-info/NOTICE.txt,sha256=KUGP1dpVhNkB6ppHbxxnMST1J8eZwuCV5Y7xckqOj_M,201184
spyder-5.5.1.dist-info/RECORD,,
spyder-5.5.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder-5.5.1.dist-info/WHEEL,sha256=GV9aMThwP_4oNCtvEC2ec3qUYutgWeAzklro_0m4WJQ,91
spyder-5.5.1.dist-info/direct_url.json,sha256=3XFxC1y0CZVSdZMKZCzajoHGCv8zi3Qqf7KrfBV3L1Y,86
spyder-5.5.1.dist-info/entry_points.txt,sha256=RKGosuR2_-0EfFIgr6RAYaEo6wKWqsrfc4SuyG43LrI,1964
spyder-5.5.1.dist-info/top_level.txt,sha256=U78-ZIj3acTHX1m_zZ66nQ4XtmdvqBeAXHuP8O4VSME,7
spyder/__init__.py,sha256=_Cu8O-QGYGPE8DqX16W9mM7WlZAGsba7BqYw8Sdpkbw,4337
spyder/__pycache__/__init__.cpython-312.pyc,,
spyder/__pycache__/dependencies.cpython-312.pyc,,
spyder/__pycache__/otherplugins.cpython-312.pyc,,
spyder/__pycache__/pil_patch.cpython-312.pyc,,
spyder/__pycache__/py3compat.cpython-312.pyc,,
spyder/__pycache__/pyplot.cpython-312.pyc,,
spyder/__pycache__/requirements.cpython-312.pyc,,
spyder/api/__init__.py,sha256=jH_i-Qk3e7EIXhb1eEEm9xI3V7S_YYRJ3I_MsFzVCEo,441
spyder/api/__pycache__/__init__.cpython-312.pyc,,
spyder/api/__pycache__/_version.cpython-312.pyc,,
spyder/api/__pycache__/editor.cpython-312.pyc,,
spyder/api/__pycache__/editorextension.cpython-312.pyc,,
spyder/api/__pycache__/exceptions.cpython-312.pyc,,
spyder/api/__pycache__/manager.cpython-312.pyc,,
spyder/api/__pycache__/panel.cpython-312.pyc,,
spyder/api/__pycache__/preferences.cpython-312.pyc,,
spyder/api/__pycache__/translations.cpython-312.pyc,,
spyder/api/__pycache__/utils.cpython-312.pyc,,
spyder/api/_version.py,sha256=nkPetFhrZC22c3qCBUx2ey4T6ZlQVydNeHbzEOYuHvs,254
spyder/api/config/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/api/config/__pycache__/__init__.cpython-312.pyc,,
spyder/api/config/__pycache__/decorators.cpython-312.pyc,,
spyder/api/config/__pycache__/mixins.cpython-312.pyc,,
spyder/api/config/decorators.py,sha256=qGS9RAs3rpkcP0oVw5QIeldlOGDNiMN45YiTCypA0t0,2179
spyder/api/config/mixins.py,sha256=mrpecfZD8Kp_CG3ea2WuoxElUL-32uAYJx9vlEP8jZg,12546
spyder/api/editor.py,sha256=iCjsl5z5dtvrbV7XJBA08BUYdxj6EzE556q8IESy6Ow,270
spyder/api/editorextension.py,sha256=smZ-FRpCvRf_Vw0By2FJDECdIoZEil4nFDCtx8yF380,4507
spyder/api/exceptions.py,sha256=l9le5ze83hGJ3VCQAvAg2zLvEplgOyHcouzmbRgIPIA,244
spyder/api/manager.py,sha256=R81uwXC6__rVkklLj-E8ItE1j5X2laRDw-v6t7HvLYc,1482
spyder/api/panel.py,sha256=zjZuwXXHACL1wWQMJklPVCPhuf4f7F1L6-QeOJGSsGo,6902
spyder/api/plugin_registration/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/api/plugin_registration/__pycache__/__init__.cpython-312.pyc,,
spyder/api/plugin_registration/__pycache__/_confpage.cpython-312.pyc,,
spyder/api/plugin_registration/__pycache__/decorators.cpython-312.pyc,,
spyder/api/plugin_registration/__pycache__/mixins.cpython-312.pyc,,
spyder/api/plugin_registration/__pycache__/registry.cpython-312.pyc,,
spyder/api/plugin_registration/_confpage.py,sha256=4b0DmcOjSKKvtGjQh-5Xp34cjec5L3m5zNB-Qa7FxG8,5368
spyder/api/plugin_registration/decorators.py,sha256=9lkjiZMde3OA2fFi3gm7w0dlQ4cDwEKCZ9I3wGhIf7Y,2466
spyder/api/plugin_registration/mixins.py,sha256=qVEQKGMxc4nx9OBEHeEX7GbaOJejYspKHw1siNyjngQ,4432
spyder/api/plugin_registration/registry.py,sha256=ZE0WbBwhKggjzlKDKCt3DEqSmylJTKTOCP7PLY91qsY,31200
spyder/api/plugins/__init__.py,sha256=MWeGR1vltzeJLQOgNskEmkBVlJ8X7g0rtSflR9Ozqf0,831
spyder/api/plugins/__pycache__/__init__.cpython-312.pyc,,
spyder/api/plugins/__pycache__/enum.cpython-312.pyc,,
spyder/api/plugins/__pycache__/new_api.cpython-312.pyc,,
spyder/api/plugins/__pycache__/old_api.cpython-312.pyc,,
spyder/api/plugins/enum.py,sha256=48TO8v_iwBh09-L-cg8N9Ck6yigHE3179FVn2eisZFA,1682
spyder/api/plugins/new_api.py,sha256=SNIOPLqmvyCVL31VBemkUXgS0Vfi7QHbdqH54DpECXo,37701
spyder/api/plugins/old_api.py,sha256=LcQI05QGotemNEzPRH9odXpmW0H9QZvISyyogk3vP8o,18767
spyder/api/preferences.py,sha256=KzpZCzGZkWYi9iFL76WzDrPm8S0YMqfe9duCgtL2kEY,6939
spyder/api/shellconnect/__init__.py,sha256=FtCh0Vj2uRL8LGV-nYymZQD7OQMRPDLVmwJcwb2eXww,236
spyder/api/shellconnect/__pycache__/__init__.cpython-312.pyc,,
spyder/api/shellconnect/__pycache__/main_widget.cpython-312.pyc,,
spyder/api/shellconnect/__pycache__/mixins.cpython-312.pyc,,
spyder/api/shellconnect/main_widget.py,sha256=jwSoHZvlr3WCwmQYUlRWQGcefiTKou7jMN2f83BmPDI,4518
spyder/api/shellconnect/mixins.py,sha256=rcwKg5Lw7k5Vg3afuy4kQoQ54IPmgro0xqoavfpdxCc,3388
spyder/api/translations.py,sha256=IlXCuNeORvwzeGvMkxU8zf4H-KdoF4myBaKE7f0lDYk,265
spyder/api/utils.py,sha256=6kMxI6NeT8etexcfv0kk9u95PzLk1kZ61661Dfgl55I,1430
spyder/api/widgets/__init__.py,sha256=jM8Y7mVZFD8BcxsscS_ZT7h5a-pboQ9CbV-p97RwxP4,205
spyder/api/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/api/widgets/__pycache__/auxiliary_widgets.cpython-312.pyc,,
spyder/api/widgets/__pycache__/main_container.cpython-312.pyc,,
spyder/api/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/api/widgets/__pycache__/menus.cpython-312.pyc,,
spyder/api/widgets/__pycache__/mixins.cpython-312.pyc,,
spyder/api/widgets/__pycache__/status.cpython-312.pyc,,
spyder/api/widgets/__pycache__/toolbars.cpython-312.pyc,,
spyder/api/widgets/auxiliary_widgets.py,sha256=CZ0PJkj3-INkdcrpA83EnsOZvOM9G_DQhbUjZmWgYvk,3279
spyder/api/widgets/main_container.py,sha256=oJU71MG4j7MBaIrmKpq6C_4kNuKPcd8Z7BUYYM1IlC8,5390
spyder/api/widgets/main_widget.py,sha256=FtraUM5OMb-_p0nMyy-QFReJDKgK3l0Mk62AdchMrKc,32952
spyder/api/widgets/menus.py,sha256=U1ZtIUW_3X-ahNyQxvk1oMdaMSxUGosIn4m6VwcLQWA,10217
spyder/api/widgets/mixins.py,sha256=fvDZ3untkp1tZMfe3EoKuhcTyEBeVohgnIoEY-sAHlA,23269
spyder/api/widgets/status.py,sha256=dqUb3caUy0dljpLmRroK2YesP79u27V_c2Yuc1WvCIE,7713
spyder/api/widgets/toolbars.py,sha256=fkrGCt61rDpLujHy_lIT4m5XRFzDHen1ilwn1o9wmrg,11969
spyder/app/__init__.py,sha256=qFxNzCsem6eqIKKFqtvUiR1UGmgyMW-16abS8XnEpgk,252
spyder/app/__pycache__/__init__.cpython-312.pyc,,
spyder/app/__pycache__/cli_options.cpython-312.pyc,,
spyder/app/__pycache__/find_plugins.cpython-312.pyc,,
spyder/app/__pycache__/mainwindow.cpython-312.pyc,,
spyder/app/__pycache__/restart.cpython-312.pyc,,
spyder/app/__pycache__/start.cpython-312.pyc,,
spyder/app/__pycache__/utils.cpython-312.pyc,,
spyder/app/cli_options.py,sha256=eEXro85kV31T17zR99L0yEibkeoiZZhbRDk6257hK6E,4844
spyder/app/find_plugins.py,sha256=fEqAYnxb4PTzUqQ61sKn4oGmqwLKEJ3tzqKzLCAt2fU,2743
spyder/app/mainwindow.py,sha256=0QhH30ewSHcDKStbhSq5gCyT8mdSCK8TY8z3lsgrSQ0,72637
spyder/app/restart.py,sha256=QtcMCgV9TAO7BgUwsPkMilzOcDlJAb7_zI_rmj74W0k,10871
spyder/app/start.py,sha256=H4kB-JH-FoWSz6kv3wJl1Xqg8yfAOvk70e_z-LtXSPY,9617
spyder/app/utils.py,sha256=S37VgwG-tS8Tga3x2C5GmbWkGR4NmKq8zIU_SCi2Y9A,10408
spyder/config/__init__.py,sha256=hbfpjN4AXCof_qNam251EBkb4rNRMpUwG9Jit-21NCU,324
spyder/config/__pycache__/__init__.cpython-312.pyc,,
spyder/config/__pycache__/appearance.cpython-312.pyc,,
spyder/config/__pycache__/base.cpython-312.pyc,,
spyder/config/__pycache__/fonts.cpython-312.pyc,,
spyder/config/__pycache__/gui.cpython-312.pyc,,
spyder/config/__pycache__/lsp.cpython-312.pyc,,
spyder/config/__pycache__/main.cpython-312.pyc,,
spyder/config/__pycache__/manager.cpython-312.pyc,,
spyder/config/__pycache__/snippets.cpython-312.pyc,,
spyder/config/__pycache__/types.cpython-312.pyc,,
spyder/config/__pycache__/user.cpython-312.pyc,,
spyder/config/__pycache__/utils.cpython-312.pyc,,
spyder/config/appearance.py,sha256=L4WZMNh1gRIFAUOOTifKYLxKvsZ0Um1XBMcSUf8pV8Y,21070
spyder/config/base.py,sha256=SCtJ0vfWmOPBy5L5Lu1mV5qkT3tX3ZxgJxqKIyO5Avo,21290
spyder/config/fonts.py,sha256=0bt67RmZglAsoWeb3krssTRPDzSaY8Rfxvguai7wIVk,1530
spyder/config/gui.py,sha256=IPKC4ilERNID0_24rxHHYt27VCxxa0n8X_y81B94Hu4,5445
spyder/config/lsp.py,sha256=ePeRVZHmSbnLi0ZVLdJCVnUnbHgKsgQ6pnjyEAxQqzk,5221
spyder/config/main.py,sha256=_wj9oWEyYELUEndsM65jrDDxwrZL8hWUC94avzyyYUA,24712
spyder/config/manager.py,sha256=ETKdZczygwvjdhjUJHOYjeNaeRS4SECRB48Mns9yvIE,27504
spyder/config/snippets.py,sha256=f8BVLvNgTAQTyWdfptOAY35Ch2fPj5RKVShlYAS1R8Y,7312
spyder/config/types.py,sha256=4XNr_o8ydea68HyUiFcYKCahkBs9b4NcV92Zr25serk,1199
spyder/config/user.py,sha256=Wp-OSScoBl0GO_TbzOPdY1dLxGagxojDQQattDBEACk,35396
spyder/config/utils.py,sha256=csQpjsUFwGw0HqnRpj4hhlZObeEljACee4bsFoDbNBo,6997
spyder/dependencies.py,sha256=--2khqJoqUMvuxZvfaFd2UEZ-ureM3ALroHkgc9hKAk,16792
spyder/fonts/IdeFont.sfd,sha256=kj7WI-gdi_YiqhMM0xVGOCdx39rNBIyKL7snPXd44CM,59910
spyder/fonts/spyder-charmap.json,sha256=NyBmCSVMYjRLFIWVB0weq7wQ_FaxHiARUf8_Q1xMcvU,1379
spyder/fonts/spyder.ttf,sha256=5PisBiWjOe72zvr8KCYBJtIXhBttObYL5RMbBVeSoAI,8800
spyder/images/LICENSE.txt,sha256=V1i3q_S-5QsneQvGSzMTConJseMXAYtl2m7pKf3R684,1121
spyder/images/actions/1downarrow.png,sha256=-ghEE8HXAj1L5kDmelTTR-zNaPlV7I-GyLZXjhOGMQ4,652
spyder/images/actions/1uparrow.png,sha256=1wJPWBu7g11NvksPOlnLiC6_v4FDaevJaW60cixCkIU,656
spyder/images/actions/2downarrow.png,sha256=a2bKq5Z29FmZ0T-1qYgjiiUT9L7mcOzHNNjhZT5Zz04,903
spyder/images/actions/2uparrow.png,sha256=nLBGHkhvnPFkoXlDS9uWoUknT8HwueQpfjQ4Xe_o47w,902
spyder/images/actions/arrow-continue.png,sha256=8tP91BRy7jnMiFltGfyum2QdQXiAsCUyZexR8798qXw,1368
spyder/images/actions/arrow-step-in.png,sha256=xpC2CGigPrdqG1xO_BpBMMKcrzQt_zIUG4iJ9WeEL5Q,722
spyder/images/actions/arrow-step-out.png,sha256=7GRvOYgSt90A5lUcdOPnetppwgZYybFqNMdHwIDAflI,488
spyder/images/actions/arrow-step-over.png,sha256=U8s_pYN57vLTEpbfMtNPvN78pdg5fU0EldFoVyTcxWQ,767
spyder/images/actions/browse_tab.png,sha256=Ah00VnMiPZkQZNetsw5hp1Dk1PeFtl-HvDyLC8ouyHY,819
spyder/images/actions/check.png,sha256=nNqSVNWDQTAUJEtVgDehRFqP_K4y4ttDKcUQbaZP_xM,1035
spyder/images/actions/collapse.png,sha256=msh2rtl7mb-AObMgmZX4nrqchuhK0tZ0ptZ6nEC__3g,662
spyder/images/actions/collapse_selection.png,sha256=_wfIK4YTsN9sABQB_7hygUBWUDc9Gg2GwgmHC_kjGqw,784
spyder/images/actions/configure.png,sha256=qZ-FEk3eSMp82NZrvTKTb_d4G64Kgjtk7mRO-ww01MA,1429
spyder/images/actions/copywop.png,sha256=XX6sezofj4ivDhAGGQOJQD2QdatllD-nqKp4UB7cyLM,971
spyder/images/actions/delete.png,sha256=zh_jrDJyXVj2sgfc3EVoiQhCKOxhZgOPnP4xrvqM7G0,768
spyder/images/actions/edit.png,sha256=20aI67nFWIQuf3KOUVSVbahoARsElm4u42p4tDiS5IY,735
spyder/images/actions/edit24.png,sha256=d-QEu_9Yy-c0M8PR8KP2x_CapnXx3YoTRr2Yg6YKKB4,769
spyder/images/actions/edit_add.png,sha256=5KLxqjaOlY1YCtLUpuz40S12zBAgriEFDqXXsKac_g0,798
spyder/images/actions/edit_remove.png,sha256=ieMIiTNNFE_QxT0M3fFSLa1r3Yz9A7k3N9Jo9XMdMyY,510
spyder/images/actions/editcopy.png,sha256=IT8f3yexbtTYkEgnl7a7hAhJRZukqJOiKXjvI1gUxr8,846
spyder/images/actions/editcut.png,sha256=VPtVk0vNf_3KzAX13sTu_xu82Qp4nX-YDSpuhRmopA8,978
spyder/images/actions/editpaste.png,sha256=hX0Pzga0zVEX4sBqx5jaZWG7ZPRILYx4xJS6x_95bgU,1018
spyder/images/actions/eraser.png,sha256=f-pUXgLJjH9-cpoxlBFcPEoiB9VCxGNyIZlSbmrJ4do,729
spyder/images/actions/exit.png,sha256=k-uIeiyTgL4LA_4jW10g3uiL4GtgNLILm1qkSSEo2VA,952
spyder/images/actions/expand.png,sha256=twzUY9B5SvQ9waP5pPmlJjNcNSUTena4SwwwMWEtikE,671
spyder/images/actions/expand_selection.png,sha256=GMnPyEZLBbf_u6N-33GooCfHZg6dsI0_rppWvskSU4U,799
spyder/images/actions/filter.png,sha256=Fc5qfIcoIjAGyKObDT3Jef7NRG8w9Vlcp82SR81C0O0,1060
spyder/images/actions/find.png,sha256=UWJMlOd4iN9aW33cr22QJAzOrwss9_Kl2JPNYHZfRig,1166
spyder/images/actions/findf.png,sha256=iJJZJS5FyIqAgEKyDNam3Sc0YTGBK6o2zfVRsBaslV8,975
spyder/images/actions/folder_new.png,sha256=ZOB4hpSwuY6YqdHWcpKdVND-RVdaJgHcWn3EaUYZqtQ,828
spyder/images/actions/hide.png,sha256=uXDdPjBpaQba9p4zG65QDUIOSw8sJkllAUtlukk9YCo,269
spyder/images/actions/hist.png,sha256=O5FSuzf_Ixmdbq_yCPdu42YXb267xF-FPFA2ALDiSOs,348
spyder/images/actions/home.png,sha256=fevEHecY3nPgfnvQ5xSiLzWr04nu6ySKY1rasfEWaGY,893
spyder/images/actions/imshow.png,sha256=qdZ9U_MjEdRqeMNqvyYC4F1-PF5Qht8Nyko94VwoSBI,510
spyder/images/actions/insert.png,sha256=t6LOti06W3TNG6Hlcrl16ztsAzueqKo51-fqx8-P5fE,392
spyder/images/actions/lock.png,sha256=9dAGw7rh-hnl5GDZJVEQZZdW2XDO5FEM-6zMgcN5RtY,855
spyder/images/actions/lock_open.png,sha256=OyviEdWg3VMEYhTEH8bHFs8V6CIiVZS9LJlUm3AxeKQ,386
spyder/images/actions/magnifier.png,sha256=FtN9HQhXrHn4Q7RfQznsL_-ybhsMXmiiqdOO4UXfgq4,734
spyder/images/actions/next.png,sha256=F2vNq31LcQxJ_DmS-_3urcbElOEkDy9R8AdrM4TdRvo,749
spyder/images/actions/options_less.png,sha256=k_5lGyRm9TQ_BUlBQg2Tfx_F5czFO5Rj-7dane7uT8w,833
spyder/images/actions/options_more.png,sha256=r6Ltnz8nrOUwo9q7nUxpO1MpQdY6O4qag5LtylMOVd8,876
spyder/images/actions/plot.png,sha256=fJDk6HftzCTGnfkQAVKEMRRoLFlxYe2jNLDylbqUd_A,382
spyder/images/actions/previous.png,sha256=NZS6aiSJ0clM6cATGP1-ZtmzI5le32XY07mM3vlgrfM,743
spyder/images/actions/redo.png,sha256=BGPLXJ2GP2bUAcz26YLAlZnqnW36rDYhUMk8nteO0EY,1031
spyder/images/actions/rename.png,sha256=IaxAesvYyp8vQ2H8l-1N2wu8kiOuqol0o2VJeQoMQqU,525
spyder/images/actions/replace.png,sha256=ZSAZ8Ai7SBCSFUjtCk2-maoawAypmCr6F7bfC7VoT6s,1136
spyder/images/actions/restore.png,sha256=eEbY3Ca-Rt3Vh_zH6aRPrv-usnbnD83KqioLRLs6DEA,456
spyder/images/actions/show.png,sha256=A-sj108irmurKNAk-szL6fMG2cjsi07IO2HCPOOMsqk,261
spyder/images/actions/special_paste.png,sha256=4uYK26lCrfBR-ttPErwrH9zUA6FC6wqDpBdanCn9hbA,777
spyder/images/actions/stop.png,sha256=CqisRG4JUa2norKJ_TyZcCg2NfKF0YWtiZAQAB2PrRI,474
spyder/images/actions/stop_debug.png,sha256=56EUAtNeVluhJ8VZtO6-SNZGmCZKeWMt1rjjBupV0-I,1090
spyder/images/actions/synchronize.png,sha256=gXXe6vVjvB_ewdqTinN1vF3v7q_XHLtgFSYb2OUjkKU,653
spyder/images/actions/tooloptions.png,sha256=t46P-ty5e1m5mzASw-DGKGUlNfbW-ohdNWk6roy78y0,789
spyder/images/actions/undo.png,sha256=Nyv1m2d_5h858lr1IEbEjIEUNqYxljOu4DAOXoy2Jlk,1208
spyder/images/actions/unmaximize.png,sha256=4KWO9yQJPf8erW7ugzfdMFzdUu3Ku5lfCwNQ4h6I2b4,1096
spyder/images/actions/up.png,sha256=yGgTR72RvyWJvWs7bIfBn_kgnIN-A2QTY0p2b8KkzHY,971
spyder/images/actions/window_fullscreen.png,sha256=ykyrDLvEBE_bbLwJExbNNIWlWSMOzLYITv4MVKSnOFU,299
spyder/images/actions/window_nofullscreen.png,sha256=aDlyOobDZdTskm65B5RfH51gcjG0-klU2mr-FqKlE1I,703
spyder/images/console/console.png,sha256=-XXoViqcNrdYcmbcpl6z_tW6q8HN8BpTuRzI9S9uy68,592
spyder/images/console/editclear.png,sha256=k7e_aEUGV3NtS1PDpMnEexCPKwUhPPtX66xQvC85hO0,1694
spyder/images/console/history.png,sha256=ej1UPfE-5S9mzXyfT6n0IZWOCwi9Lsl7I0oa_hEJ3jU,404
spyder/images/console/ipython_console.png,sha256=pEjyDVUHhLOFowGT8Ab34PhRxEjWBlWOjS471BC3q7c,571
spyder/images/console/kill.png,sha256=W8eddKtzeySGRPYhgPhpNt41bX74FdL2TaoS2Pz4xhg,889
spyder/images/console/loading_sprites.png,sha256=rEgFdROoxqjBqgt4GW46O8NitpM3jKPi0nlj2YzWMmU,3292
spyder/images/console/prompt.png,sha256=JKZEfsXa3xijzZb50MJOQZv6kGngGS-eJB9UiUybUTQ,277
spyder/images/console/python.png,sha256=RadOBAFb_7x-cZ2bnS3jtPOYt8G1qsUprnXmaNMvJPE,724
spyder/images/console/restart.png,sha256=EQn7G77CIpBcb_uoZCyVARIjjPIWazpgA24YJki79PI,899
spyder/images/console/run_small.png,sha256=YF7q8Z8tkexo1IhxIxG8IYtsTW7D5n8bVFTh1KsRB9w,593
spyder/images/dark/ArchiveFileIcon.svg,sha256=uwNtqRXOMwYwS0Fb9MkmbAReLqI0OkMJhbPO9m3-EWM,471
spyder/images/dark/binary.svg,sha256=Ft1NpO1meFE0hvG92HtrecrWrQ2g9RDkOyESTKw1J1k,1370
spyder/images/dark/debug_cell.svg,sha256=S0qqMGSAiXlHabFdu1Gde8xFvs9BKQzmdrOjdTdGY9o,1903
spyder/images/dark/dock.svg,sha256=4OAwkcXRxe-Eyn6W6AMKOFQgAZAQZdyX0gNONFchozI,506
spyder/images/dark/editdelete.svg,sha256=ki8JvndpLCxQyF8eVCUfSVMBBlOKZy0tE9G3VchcCgM,807
spyder/images/dark/environ.svg,sha256=8ecnbDJ9-rtmZWf3kEqZRvN7-IgNbL8-LIysOHeznR4,1579
spyder/images/dark/file_type_tex.svg,sha256=QjJadtMZuApxvE9Lv4ub6mz_vIi86eSfv99Ai87bfug,1612
spyder/images/dark/filecloseall.svg,sha256=a5-F6h_--c63dR7XBFvnW3Hdh6fE1rFhWUHda1vL1uw,537
spyder/images/dark/findnext.svg,sha256=sJC9lgZ3CZy0HMupyWu9Qu8ZAztpdQJ0y3LsXZiMMBg,1020
spyder/images/dark/findprevious.svg,sha256=fsnSXO2ED0CCY6DYOO2b4GpBVHMkudUITIW4pj1f4yA,975
spyder/images/dark/genprefs.svg,sha256=PsAhjsi1KAFLzBV8xXG0DwsemJmNDWJre3ym-rikMLs,240
spyder/images/dark/horsplit.svg,sha256=XgDU3EvPxJybyLBxJxcEn4-ZY7EPuAlChnFK3lfj3aI,218
spyder/images/dark/kite.svg,sha256=rTUmxCRm_UhXNkIt6XYxPn3bKZrhvYg-InapfN5WRqo,335
spyder/images/dark/last_edit_location.svg,sha256=gyY_ybB-AiS9yvPxVrme9ZKVFaf_AzEOQCiaIe6_2as,732
spyder/images/dark/maximize.png,sha256=54wv_KRUhVMVsDz-faT3x0jtdKM4gQ3ejty3KETR70Q,264
spyder/images/dark/maximize_svg.svg,sha256=yVaId3pZaIi3uAN1FVB92KwkDyl6yi92J2eRWVdWGIg,1420
spyder/images/dark/notebook.svg,sha256=VZJ9P1yPZNaO9vcNQ-JfvdaVg3PjAcwFqgOxGn60sa4,282
spyder/images/dark/package_new.svg,sha256=G3_JIZ1rgDi5B-CQo4q3wSz7eaAsToA2rws4UKDgCyc,2144
spyder/images/dark/pylint.svg,sha256=_M2RJlsAP8CoesGXffhIch7ax4Zg9x0-7XDncLsENuM,836
spyder/images/dark/reset_factory_defaults.svg,sha256=YfhN5pT5TGirbl-vlmHydCA-L9Iy_9n8wHkMwgAwfZE,967
spyder/images/dark/run_again.svg,sha256=1r1AsBKUZn3FknK4ToCUJMspDmCIjdGTWLb2llZqFMo,1998
spyder/images/dark/run_cell.svg,sha256=5X6UH2faETwzMeuMWQOz8Qp5-JvSVxU5jLKbpgGvVSQ,1851
spyder/images/dark/run_cell_advance.svg,sha256=cggUbTJyAtIsJanmAwwnFczIRs1zotNrhJUVHGNS9X4,1907
spyder/images/dark/run_selection.svg,sha256=e2sEHCV5VPOTlHkFhWeFeUv1Fye0CejHMvkCLEufa_Q,2114
spyder/images/dark/run_settings.svg,sha256=KxUaHUxlNrUAQRjj30VLyHmjbjsu1k1AV5afDukpQe0,2191
spyder/images/dark/spyder_about.svg,sha256=7rYLF20rMLlzvl6GMIoO6svf1zRnykdbZuIKTFyUzWI,1536
spyder/images/dark/syspath.svg,sha256=zUs7ccFMTv-mIWKeMVLb1oFL5MgilJbqTM8KbO1byx8,3422
spyder/images/dark/versplit.svg,sha256=WFrzUWQQ94B7PzmGHh6Fh1T2I829kdhfXWbseUl1G9Y,180
spyder/images/dark/zoom_in.svg,sha256=DRNCtospRcQM7B0eM2nfQewm330cPiCvnVNH0Nvw7zk,1017
spyder/images/dark/zoom_out.svg,sha256=iCAWA25UzH3Z7PYT4G51OmvrcGYyVI58iD5AydaGW6M,923
spyder/images/editor/attribute.png,sha256=HUkiaPpEfps1E8Fj0AYDKXLCwQEs0v-ovCqAJt0L3vU,534
spyder/images/editor/blockcomment.png,sha256=0_XC86RBeRDzC4Waa3l7_5r-cOMuRBzrifwbBbq80Jw,320
spyder/images/editor/breakpoint_big.png,sha256=MD9pPALE-DI6vG5gJKqKuEpEwaVqyTfjG3KuzAah-nA,1347
spyder/images/editor/breakpoint_cond_big.png,sha256=AkiUWH7XmMrHY3bG5dxQZdiQnfQNkf5e366bL0AfWAg,1376
spyder/images/editor/breakpoint_cond_small.png,sha256=Y6IybGXSn91R_xezJ2402X8PQMcooBbIyO-GYoAvBvU,819
spyder/images/editor/breakpoint_small.png,sha256=8hFJF__1jIGz7V1hHqN9xK4p3S3F2n266ATAidm5EZs,532
spyder/images/editor/bug.png,sha256=ME-yhk1Sh69M7-eOaj49znQN8eOtfErfNY7eU6Y6RMg,1704
spyder/images/editor/cell.png,sha256=mUOSm3J5Qhz-G5EE1J4O-gQPeLLvlC-ptiQxvy82vK8,209
spyder/images/editor/class.png,sha256=Tp4p-WCGkUyEeTnKsiVwja_I1D_89_15K9_wZevYj70,380
spyder/images/editor/close_panel.png,sha256=XDyfHBp2D0pBRvDKsYrFt4IYDuVzg3OoO6R_9K7M1mo,1091
spyder/images/editor/comment.png,sha256=7UIvEn9s7MNEnNUbjmSBdMl6HLdBS7D_wPv3oTNAeCY,873
spyder/images/editor/convention.png,sha256=Ins4jQRsciK5nH3QwdLBUfJj6TKI7qm1VpfoTDIeOj8,410
spyder/images/editor/debug.png,sha256=i63BE8FI44ruBUiuFcGIu8ZhaiBaufFVDvRKUJnlioI,1442
spyder/images/editor/error.png,sha256=oGmbQ0vCPcHJQxXoadzgMRbMWuVDItJb5pBpxGtIcWY,450
spyder/images/editor/file.png,sha256=hgcN6xlWhAYCs8HULXRtBbInOUcV5FGOrHOkNmVYMZs,261
spyder/images/editor/filelist.png,sha256=W1b_5y2IYtEr0SyseAQr39RQ6WozUtpCkysPte5VECk,568
spyder/images/editor/fromcursor.png,sha256=FBtRm1zROYIY8iBiGnAqKiSZslLnpdRRZbOEZySuMJc,448
spyder/images/editor/function.png,sha256=qCZTo-XAaqil6Sg73XzIsOAkUZqtLrBGNWS6Tnpygx4,403
spyder/images/editor/gotoline.png,sha256=ib4UXJQtQSV-mlWurFRlYJjNSQBWb6DCCxA4tVuKn7E,399
spyder/images/editor/highlight.png,sha256=tU40ZLyKXbZq01bNUZMdiDqPcIuyv-8rvPnl21v0rso,581
spyder/images/editor/indent.png,sha256=MAYIY380tn-KgG98Z2CQUEIKLOZrq3Bp0Yqt7ekKd54,439
spyder/images/editor/method.png,sha256=TQSrxi7-GKh1_T23ul0nMnEZ99CqzmTQpWX7hYtsNSE,431
spyder/images/editor/module.png,sha256=DL1tiUT3xg47FicsH7fK_AMA8xU9Dux2XsMsxRDPI6Y,727
spyder/images/editor/newwindow.png,sha256=UM8atjLP94WFKJoHjBIb_gL9bQFHCNvDJPTOKknc5IA,1063
spyder/images/editor/next_cursor.png,sha256=fsrnIy4Riv7f5wkbV_q0JiklPvRizvhOhP2lWZDW4mc,941
spyder/images/editor/next_wng.png,sha256=ejV6y_rH8-NFvHs_3hwvDYPAc4WHi6tv5V2rGHaMazE,936
spyder/images/editor/no_match.png,sha256=cW3RlG3o5303Y4-_lF0PEu98xjazsKJtUU9Ky-K74fo,161
spyder/images/editor/outline_explorer.png,sha256=tQMuE4i3r0-W5OZYYqIZO-R8hb1JmybK4u2G6cRgpj0,775
spyder/images/editor/outline_explorer_vis.png,sha256=Tzw3TLEIzpDvmG3Rd0oOPDXDi_Frtstf8NSqHtRGxqM,1046
spyder/images/editor/prev_cursor.png,sha256=vvqYWwgbOywsJbGPzz2SbVmWD41Rc1jbfGkp6dlBqCo,938
spyder/images/editor/prev_wng.png,sha256=XVhL3Ee4s7UqJP7nIFchoyLUKXmeVAzXot8jxU41jrA,930
spyder/images/editor/private1.png,sha256=UxeYpPeuiyHa0Bl7BgSoYKSVtDp_L9Y2rqPW_VOjFpU,431
spyder/images/editor/private2.png,sha256=EhMi3QU9YJ0M1x9vndhIX5sucqG3WN8pTesoM5xtuXg,428
spyder/images/editor/refactor.png,sha256=U1ju3_jQlvZ_q8MLRoYvwhyhWUQIYSR0_lGyjCPti8M,396
spyder/images/editor/run.png,sha256=qWz8RPmQBefTCCyV2vzihOP8ic5Je7k1UvLOkzEG52c,870
spyder/images/editor/select.png,sha256=xvoezAbOmm_B1QJrtjjITQJEC79LpZuK_lPmVgJEyY0,365
spyder/images/editor/selectall.png,sha256=FE5V8pNTUIm9atRylPXt8llsM1zgf3KFrekob4SmcPE,1221
spyder/images/editor/todo.png,sha256=z-aDtrl2wCPXIwW907qvzbbBzbAJBKOsGZDIiujSH40,577
spyder/images/editor/todo_list.png,sha256=3fok92Xq1ig9bmBlnlRSNpIiIDWl6cgP_YhtE94O_sQ,829
spyder/images/editor/uncomment.png,sha256=nd-F_uYJeEsjN5LzmHXIvzRsa2VKBEYwd7Qp15XB5CM,840
spyder/images/editor/unindent.png,sha256=ZxgFiXfXU0qQg_drWTUuuY4ZmA72W2zeXRQBAqLXGGA,444
spyder/images/editor/warning.png,sha256=EGWq0_xeIcp8XDDKyzU_tkrOEU9EqcooShyllEmKmw0,496
spyder/images/editor/wng_list.png,sha256=99fto1HkRz1gl5te_Gu-6MSNfgNigrSgSqcTFOhv-qQ,816
spyder/images/file/fileclose.png,sha256=hO2zaYPAO9GkAuBBa-0WfF6Er5rnJKSQf2yNBYEHamw,1204
spyder/images/file/fileimport.png,sha256=YJRjYPAnoVKsnOzOGYm0LHCIT4o0gSg_QJrdT9Q4Pmg,2117
spyder/images/file/filenew.png,sha256=CllQ0F8H7PnEyimvOhkmK_lUvthCh3ILddevIMGmLjU,330
spyder/images/file/fileopen.png,sha256=ityPNsqRw98eo-5iN7DzavYuhUSqw7MK0_ZbKKvZwK8,1276
spyder/images/file/filesave.png,sha256=nQxU_gt4rYU-3eP3C0UFPpdF2579mRFOOpzEO9Ej_0U,1029
spyder/images/file/filesaveas.png,sha256=zJkyRS6eHkNaVRIOF5LNkV4M7aB65GDtohzTAiVqWck,1326
spyder/images/file/print.png,sha256=npq5L4tb8IM9hKnioSJW5GtdoE9CXHlrpNKaKZv3hG8,1165
spyder/images/file/save_all.png,sha256=cFqE3d2W7o4KhqQhIvQLzHf0T2ET-_ZoOKlSobhiXV0,1485
spyder/images/kite/kite_completions.png,sha256=qe9zV4crOapnXgsAbHgqhK4hub_yveIgKkFC8iE7vsU,20567
spyder/images/kite/kite_copilot.png,sha256=TwPgvPCQ93oSRj0bX8yEBwsbORYLL1LAvsgTQEeo6fo,161956
spyder/images/light/ArchiveFileIcon.svg,sha256=bOz51CSKdwHLZ5FzvizYaL-l86GKf5yS0Xh9lqplpmA,471
spyder/images/light/binary.svg,sha256=vKknAuSMEeUM2oYA21h_YIkUi3l59jWccUZ4zUhLuuM,1370
spyder/images/light/debug_cell.svg,sha256=AY1nSOY36GNyn3kLx1vcb41geYsdcRPC3_LJtHRoNuA,1908
spyder/images/light/dock.svg,sha256=gIkighj49Abz9vAMe05Ii4v9CffjbxbiZy1x_wr4Ivc,506
spyder/images/light/editdelete.svg,sha256=vxp5TowM9vP5SI4GY9-DawAJJiN_Hee1WrHToq9hyf8,807
spyder/images/light/environ.svg,sha256=FyljN3B_Mf6R7WlP0Wlz5We6u7_B1y191wJS-TJg--o,1579
spyder/images/light/file_type_tex.svg,sha256=fmPVfwHRHndu3m40CtYmb6xz44sCcTpRLd3FYNW-QFA,1612
spyder/images/light/filecloseall.svg,sha256=-jFt1ocskjFkR_2QE4vfz5O3jAAhFeTu6Zh3vAeCXDs,537
spyder/images/light/findnext.svg,sha256=S8zWs4uvAXhHuNB1NsORoyblh4o_9W4k5fwJYax6BQs,1020
spyder/images/light/findprevious.svg,sha256=9_EMNCYWAcITZ2of6Aa4TOKLy7geER962bCNSurBRls,975
spyder/images/light/genprefs.svg,sha256=I27xVr-Xu6B9KWmjCQSLsVXPIgwSKaClPJBmv76zcek,240
spyder/images/light/horsplit.svg,sha256=494OMYkHhBaQZpOQmyUCyDpyLWGSOmRdZk_P8ErQTvU,218
spyder/images/light/kite.svg,sha256=oEZl56UKtx5V8Rkyv3rxQ97PZxpphC3ZFnHu2F9PtvE,335
spyder/images/light/last_edit_location.svg,sha256=aiSouPbQR5pxO-a5kONWQW2kDtIMyF1GkQcfsBkNe5o,732
spyder/images/light/maximize.png,sha256=ey9dVa-SMn7Bp_2S0SUHehBm82IU_bRLk3dIGDjeqFY,287
spyder/images/light/maximize_svg.svg,sha256=B38CLYR-cVORIj1cXHgLz6tu3dHCXbiuhfyM0S2Fe7E,1430
spyder/images/light/notebook.svg,sha256=W-yWon0jwMCcVPXJJzhdhuea4ar4bSvhmLMc5zNOiEw,282
spyder/images/light/package_new.svg,sha256=TzIZpqhrNwxdTLLZ023d8EXW69MuDp0cT9NdDYm04kM,2144
spyder/images/light/pylint.svg,sha256=M8MZlY11SIo0h-PdJItOrJOdSW8qRsUBwrWhVIjmn8U,836
spyder/images/light/reset_factory_defaults.svg,sha256=C47A-V6w1_eRq7d0sOjKh_AIuymPmCgXXUsAv0HKDck,967
spyder/images/light/run_again.svg,sha256=6dsRdQSJEo3quhapIx7FaV4wUi3LMqQee-zRuHlnrf8,2003
spyder/images/light/run_cell.svg,sha256=HAxYniKl7fkJ8WmQ6oSIgE4vvrJLv0Fk5N9_54ZD2Eg,1896
spyder/images/light/run_cell_advance.svg,sha256=HUwoeVIGz7-GIInWO6fyLCi27oYFqQBLmqy_FT-fX-4,1952
spyder/images/light/run_selection.svg,sha256=JaPPjUFsGiJWkO3WROccJNgD6j6dAJlCS0w5spoeYZ0,2155
spyder/images/light/run_settings.svg,sha256=YNFGhfktqdYV_8QM7a-O5SqI302GcBtAnFO4PnYtjuw,2196
spyder/images/light/spyder_about.svg,sha256=G6SkCnuBiOT6bCMXRx17vihsshygTR9RL-xOlRyETIw,1556
spyder/images/light/syspath.svg,sha256=9yRv60eXWfwO3iLwNVHzGFss4j_ISHB4QBE5mP5fIcs,3422
spyder/images/light/versplit.svg,sha256=jsikST-9o9gT50tOdZQPUtydCfNSvHEwEyvGoyegUTs,180
spyder/images/light/zoom_in.svg,sha256=76kvoHHsFAWFyZuLrLRM7VXZET_pZ_mrwT6I7zVnUPE,1017
spyder/images/light/zoom_out.svg,sha256=FX9JMFlQJgKmkY4nEi7UFEDVQ62eZmTF4OFM6xBgCRw,923
spyder/images/mac_light_icons/close_button.png,sha256=KMly9tDSzkdPUlM18QQyJgGBJlub0TENthQWbaivok0,310
spyder/images/old_svg/CloseButton.svg,sha256=Lj8kd8ZZsTL8M0WxGy01Fvq9EIgkFx5b7kUGNYwSt5s,2044
spyder/images/old_svg/options.svg,sha256=fsysRq7NtJpM5RredDDUSkJve71n9QpT6oZTJQxQ3_E,4698
spyder/images/projects/add_to_path.png,sha256=ZOB4hpSwuY6YqdHWcpKdVND-RVdaJgHcWn3EaUYZqtQ,828
spyder/images/projects/folder.png,sha256=uWpsIsgRp3Y_odPOOU1fNCbtWDewBqDCxwBnWBTNIMs,768
spyder/images/projects/package.png,sha256=i6BDqMg0Wu0QTlOLB2TReqsburwHZnYzrajF6mz_2gI,816
spyder/images/projects/pp_folder.png,sha256=QK-m6z_BAeXNB1htcGnGAtX-b2a-wW2rPiWSZ6RjvMM,758
spyder/images/projects/pp_package.png,sha256=gxY_WR3Z8XHEMZpdq9rsVQvF4QtybEBLUAxOlEPvP-E,802
spyder/images/projects/pp_project.png,sha256=qADqFaGsW_j9WlgagF6Z6gaFp3mOxX8mgaVwHg1jtwo,757
spyder/images/projects/project.png,sha256=RSqGGcHDeC4zZIMOI49YiA_24FRZzdntnL1odlpTYg0,737
spyder/images/projects/project_closed.png,sha256=NAHyJYzypYX86aCa3GysDYIBcbEWvoyfo-7KG8xdm_A,414
spyder/images/projects/pythonpath.png,sha256=Y5vF-l60fVW6CpJY2sEMdBBlfxbQeuFO35tUeLU_MBw,864
spyder/images/projects/remove_from_path.png,sha256=6u9LwrBlzgV1eko5BXOWDc3isC3Hk34yI2vVa9uQ4HI,776
spyder/images/projects/show_all.png,sha256=Z0rksQZL_5mQXRZMNEegIWaqZb6Ia06mBGHXWLhNwt0,376
spyder/images/splash.svg,sha256=TOEYZsV17ysJHGHKG7i9lcqbql3Q9Nmp7vCvGnMPE1U,5737751
spyder/images/spyder.svg,sha256=bYYgnRX39FWMO0I5Zccz4A_zQfziz6GWlzCPTt1nCA0,1619
spyder/images/spyder2_icon_theme/advanced.png,sha256=c8P9yurtsgKfZBtvhM8xPVx6paBFOL3UC68qTgI-HU4,871
spyder/images/spyder2_icon_theme/code_fork.png,sha256=VvdrrR8Gw-WHaBRBQXS8tJerB9ydBqNZN83H8SziO1E,289
spyder/images/spyder2_icon_theme/dictedit.png,sha256=qgwful-CgkOCqOhvTyLUVWEspjv47_vuOeZpHPHzcjM,693
spyder/images/spyder2_icon_theme/eyedropper.png,sha256=8IGs1Yby8Zs_DRYDwU0HREU9ztAg7CwDB3s6qfKzc_8,1363
spyder/images/spyder2_icon_theme/folding.arrow_down_off.png,sha256=vb0m7BHFX4Z6ylJqtSwoJkop_IJEPqlfONgDOnjXMcc,687
spyder/images/spyder2_icon_theme/folding.arrow_down_on.png,sha256=xEz6naJf_P32mseEYY8moulT8Af5fKKEct9HJM425M0,670
spyder/images/spyder2_icon_theme/folding.arrow_right_off.png,sha256=ipuiz2BCP8hikQTvFjiNze7sqCFp4_pRXelZfReCOVc,638
spyder/images/spyder2_icon_theme/folding.arrow_right_on.png,sha256=IhUWyUHVKAUk3HDYWBDD4VU9Zs_fV9EibdKr_o8hQr8,621
spyder/images/spyder2_icon_theme/help.png,sha256=2fsF0Lm7o6n_QNHsomsihK8AAtggxp3oId8CfGLbhVU,1231
spyder/images/spyder2_icon_theme/italic.png,sha256=twI7NyAyCckaZ813Pr9RfC2zJ7AJMGEgH1h8mk1SqAI,186
spyder/images/spyder2_icon_theme/keyboard.png,sha256=8IGs1Yby8Zs_DRYDwU0HREU9ztAg7CwDB3s6qfKzc_8,1363
spyder/images/spyder2_icon_theme/not_found.png,sha256=xiPw7qqiFQ_UMQnuYmGC1tmIo9iwoDHhD67HeUHWL6M,362
spyder/images/spyder2_icon_theme/vcs_browse.png,sha256=-HQD1Np51fLKMhoErtxJLvEf49ZO7qc21EVQuD0yVok,680
spyder/images/spyder2_icon_theme/vcs_commit.png,sha256=E0_iPSYlst9doWcdjp1rxK7OF8qd7Px-7SF1Upn3LlQ,606
spyder/images/spyder2_icon_theme/win_env.png,sha256=vWIotrvNeg3oiZnSz16x-jkr5DpuQfZGReDU8SbdYzw,1078
spyder/images/tour/tour-spyder-logo.svg,sha256=isbcZ_li25N7Jr_53mxmZ1FiulzSvduxTqkiKB4liXU,4592
spyder/images/windows_app_icon.ico,sha256=mTe2RwxRxKOWqWAp5MOSsKFN1d6JdBqCx-PZ9lLnAJ0,311358
spyder/locale/de/LC_MESSAGES/spyder.mo,sha256=rTY1V15vRMBcJVtzk2MlBXbg_4hWA1NI5Y8Vq6PsLk8,176441
spyder/locale/de/LC_MESSAGES/spyder.po,sha256=QSwdXJ3w5Dzfy1UdDONGxawKbqcMMgiqOqVHZtztp6k,293798
spyder/locale/es/LC_MESSAGES/spyder.mo,sha256=HN-Nvw5VKjeExVL9Cx6TVmFdqoh-c9DtjFPAYRowtDU,182634
spyder/locale/es/LC_MESSAGES/spyder.po,sha256=1T7s9mIXeZOO1TQNZqxw6eN0JHAkI4zNjfrOaU3JOpQ,296477
spyder/locale/fa/LC_MESSAGES/spyder.po,sha256=mJ5mE89aEcaeM1iqF8VeaRETohuwNzxjF5KdLGr12Pk,215868
spyder/locale/fr/LC_MESSAGES/spyder.mo,sha256=ltIAxL60A3yIzOccVMji5BHGzT0lZZjLPK0wgelTWKY,183008
spyder/locale/fr/LC_MESSAGES/spyder.po,sha256=OKSr3mnGrpF2jpAIIe_5veofVPOBDPr642bIWWY2w1c,299174
spyder/locale/hr/LC_MESSAGES/spyder.po,sha256=bEJQl9gQ7G9ZsvRMjPmUCzAV1T6Yzg7qQi1VEKDDJfg,213773
spyder/locale/hu/LC_MESSAGES/spyder.mo,sha256=fhBSCeJc7i9nHv8GP75HHvhJCLwPOpUsjqxmTsoYLyI,42118
spyder/locale/hu/LC_MESSAGES/spyder.po,sha256=zm8kjyqHI53TfMGLdYSrVZpqmx8wXs5xCqk6M5eMfRA,229255
spyder/locale/ja/LC_MESSAGES/spyder.mo,sha256=u2-i0SMZzuRscQUJDfiX6eKqQr_b2YXcRMcmlJgvrJI,194635
spyder/locale/ja/LC_MESSAGES/spyder.po,sha256=EGERq1R5AKw-oIbZMFZnlhutExv88Dr5Dpyn_iWStLM,308406
spyder/locale/pl/LC_MESSAGES/spyder.mo,sha256=RiIrw6h3Sl6l5sC-RCXjL8Sck0FX_Ta9O2m3a4xmHhc,84747
spyder/locale/pl/LC_MESSAGES/spyder.po,sha256=tVz3OvdKJ4y5Y6Azi-99SEyvEOKHTCJHVrHYhg3Ja_8,247279
spyder/locale/pt_BR/LC_MESSAGES/spyder.mo,sha256=rcEOkOtuYgUmZSBP3UynRcQkaD6A7MPvKPNlQKeo8Fs,175392
spyder/locale/pt_BR/LC_MESSAGES/spyder.po,sha256=oxsRwZYxUqEOV54G37-4g7Hu6r7IqtmJK0TsKoWov-k,291597
spyder/locale/ru/LC_MESSAGES/spyder.mo,sha256=EPuaYnKWp-7PPeTWihb4OWNZJEPZBqXGle_FyM95sNg,194179
spyder/locale/ru/LC_MESSAGES/spyder.po,sha256=ZCO92r1tB3kteHb1yjdmsjAp5Fw-BrF8XgxFsI8NWuA,323021
spyder/locale/spyder.pot,sha256=qXBmDNDJ_zkL43l03XrFF8FxV54ziGnAjWLQ-IzAF_U,213678
spyder/locale/te/LC_MESSAGES/spyder.po,sha256=_C-OKR0GncBHUr_PU7JI7RvIqq5-28f9rn8fNMLVEZY,215266
spyder/locale/uk/LC_MESSAGES/spyder.po,sha256=ITcNRyXF1SbYVN9Y8Z22ZdTRYhxtiua1GShvhdUYPWY,259970
spyder/locale/zh_CN/LC_MESSAGES/spyder.mo,sha256=w_M_usA-fzD66fPLTskKa0fVQ3DrgKAfE_pxkSPuO0U,163789
spyder/locale/zh_CN/LC_MESSAGES/spyder.po,sha256=Hg4ZLdqNOlCM0ZT2FY9qqLtiexwLF4HOiMue46f9UAY,277489
spyder/otherplugins.py,sha256=GLb_EhBtSiuLeqc7rDq0_Xczwd0ra0tSOv0hdX2Ct3M,4078
spyder/pil_patch.py,sha256=afAs9TaDqNdcyPPtjSVF69A0xZz1oHfsWPggNyS2vg0,2220
spyder/plugins/__init__.py,sha256=jIKPiuneK-kWuwmK02-sE0Y4G-H1fnxJGgMzmB1BRcw,183
spyder/plugins/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/__pycache__/base.cpython-312.pyc,,
spyder/plugins/appearance/__init__.py,sha256=NLkqcWZzUhOfBhZ05l0rcM7L_nq9puTSim0coNK3sEc,235
spyder/plugins/appearance/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/appearance/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/appearance/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/appearance/__pycache__/widgets.cpython-312.pyc,,
spyder/plugins/appearance/confpage.py,sha256=Ncfm2nNSI2zVOcTb7R9U3SB0xC0kYYtFmvlsJ6qWvv4,18429
spyder/plugins/appearance/plugin.py,sha256=LzqiQ9x0hKSdG_DXloaMzWCKWXNmvsv5I4G7YjEMlzE,2135
spyder/plugins/appearance/widgets.py,sha256=xbnMTh7_0xqf8-TTFXsaF-XK3GWBL0--TMUwBt-PnFU,6526
spyder/plugins/application/__init__.py,sha256=aWW7W4i57CS23y9eneXDtCcrzrU0Ux4ekq66LlNqWzc,238
spyder/plugins/application/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/application/__pycache__/api.cpython-312.pyc,,
spyder/plugins/application/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/application/__pycache__/container.cpython-312.pyc,,
spyder/plugins/application/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/application/api.py,sha256=U2RxaCmWk5Xf3INOVEbhSnqpuuAv_gMNtw9WNAvIPQE,256
spyder/plugins/application/confpage.py,sha256=bxt_gd1sc_HAaFI07fLvoIFw7Lnz_InVOM_yFatFeBU,12138
spyder/plugins/application/container.py,sha256=O4XwlDnFp-_OYMiq5tLRAfGPBj0m7oVvNsTMkK14Rok,28521
spyder/plugins/application/plugin.py,sha256=uivrgX9iiafLSYfqvUnR1fQQkNlkbKNJED1v90x-F0Y,17324
spyder/plugins/application/widgets/__init__.py,sha256=gjBW6LEfqn2AccAsxVwMrmtOYHhrpui4TBva_wF8Law,199
spyder/plugins/application/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/application/widgets/__pycache__/install.cpython-312.pyc,,
spyder/plugins/application/widgets/__pycache__/status.cpython-312.pyc,,
spyder/plugins/application/widgets/install.py,sha256=uza1rEmZlKugJG1nAzEixBk2FSpRQaOveqMmyokFkd8,11346
spyder/plugins/application/widgets/status.py,sha256=4ecPIC8eAidXmxk0gq2uYqgGDVUtDYF6DFNt0VXk7J0,5593
spyder/plugins/base.py,sha256=NKdm-RRorwyLsvowSzOXKx0HaS_IH07yClgNil-kHL8,18284
spyder/plugins/breakpoints/__init__.py,sha256=sWwPCafYxVagjfjJEGhHABKIL39qTgosNJDr0rHT_zc,408
spyder/plugins/breakpoints/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/breakpoints/__pycache__/api.cpython-312.pyc,,
spyder/plugins/breakpoints/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/breakpoints/api.py,sha256=Ut45fLawlWuFVf6xAZD_Zv6VrN_dckLuWNTtttt1fY4,372
spyder/plugins/breakpoints/plugin.py,sha256=J5cr1rPeW8Z845iduux7Urp2K8m4f6pqpXc6kfcFYhw,7084
spyder/plugins/breakpoints/widgets/__init__.py,sha256=hbfpjN4AXCof_qNam251EBkb4rNRMpUwG9Jit-21NCU,324
spyder/plugins/breakpoints/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/breakpoints/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/breakpoints/widgets/main_widget.py,sha256=w66eDJX9Zhw1qcHuLr5PVh8JYQipqzQqRWNR48zc-q4,13139
spyder/plugins/completion/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/completion/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/__pycache__/api.cpython-312.pyc,,
spyder/plugins/completion/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/completion/__pycache__/container.cpython-312.pyc,,
spyder/plugins/completion/__pycache__/decorators.cpython-312.pyc,,
spyder/plugins/completion/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/completion/api.py,sha256=-nKrTTteO78tM258Fap8rlyJMdqI-sz6YpizdKZXleg,46659
spyder/plugins/completion/confpage.py,sha256=CkvX7SWBoHXWI9uPqTr3KmgiYjaL0IKx9ktLhs_WDJM,4302
spyder/plugins/completion/container.py,sha256=DvRhyY3z8OXe6-6ELA0vyfyF_XQH4HNtct02DnnnlnI,2686
spyder/plugins/completion/decorators.py,sha256=vgnyRYi4P8I8Phc0P0Y1kKUPWkzOxDskDTTYbCL2P48,1308
spyder/plugins/completion/plugin.py,sha256=7YJII1egsaNuaGiqCtS3tYaxH7CxufBYlygUTh5CulA,52575
spyder/plugins/completion/providers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/completion/providers/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/fallback/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/completion/providers/fallback/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/fallback/__pycache__/actor.cpython-312.pyc,,
spyder/plugins/completion/providers/fallback/__pycache__/provider.cpython-312.pyc,,
spyder/plugins/completion/providers/fallback/__pycache__/utils.cpython-312.pyc,,
spyder/plugins/completion/providers/fallback/actor.py,sha256=cD-QhLp4O2XvSjlMWrACD5cYnNAv0Nqpo7TElZK0z2E,5248
spyder/plugins/completion/providers/fallback/provider.py,sha256=7vpOMuj15qbR2wHCCOpXbtHidcgL378AkWCEuYI1fUs,1937
spyder/plugins/completion/providers/fallback/utils.py,sha256=UU7QR7TrxEqIR-0fql_XrFD3-VZB8Ui3rg1n5PGlX_g,5336
spyder/plugins/completion/providers/kite/__init__.py,sha256=J78Cj7RfXblNYy8miXXzyByDK3rQdeqbTw1jw5MmgcI,1993
spyder/plugins/completion/providers/kite/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/__pycache__/client.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/__pycache__/decorators.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/__pycache__/provider.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/bloomfilter/__init__.py,sha256=lfDhzdv8ephJp1Qn5nw3Kh_9YJQRyB9w2iSLsPzd7cc,862
spyder/plugins/completion/providers/kite/bloomfilter/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/bloomfilter/kite.bloom,sha256=nE0rf0rKxlaB7f6nJjE4L6awXDM29afty3cZQCMtp7A,23283
spyder/plugins/completion/providers/kite/client.py,sha256=exL7RiBVUwv2zNtQJsC3pRrB4oHNOHZlPxeB50cqavc,6898
spyder/plugins/completion/providers/kite/decorators.py,sha256=a8vztS2LGKLu9_Q8pcydLfZqWQQYAsi6Ao3rsWUesT4,1401
spyder/plugins/completion/providers/kite/parsing/__init__.py,sha256=UAdmwpBZNxm4GMYVfyGzZ1mA8mwAq36PCaD06xYm6-k,1617
spyder/plugins/completion/providers/kite/parsing/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/provider.py,sha256=m0MfVxMH6aI7mFcr3_jwMR-HIuA0CztYztVsorYTivk,9369
spyder/plugins/completion/providers/kite/providers/__init__.py,sha256=j34LcCe-DyYlpIdbeqPMUO-bK6wdI4zyf8eUFdNpS6Q,301
spyder/plugins/completion/providers/kite/providers/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/providers/__pycache__/document.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/providers/document.py,sha256=uDQG7oErwa4lWK5EYvdV6N_FmeSCTMhK8HEiq6NNVBo,10403
spyder/plugins/completion/providers/kite/utils/__init__.py,sha256=kw8gdxDJEh3CwpzIbK1H6DZhAHKUT0AKsII8oFObHcI,176
spyder/plugins/completion/providers/kite/utils/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/utils/__pycache__/install.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/utils/__pycache__/status.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/utils/install.py,sha256=IpWneSM5u0fTIthSgyAwcM1pYp0OVYbZb8yW6D8f334,8117
spyder/plugins/completion/providers/kite/utils/status.py,sha256=-NihYVlmSKmkh2SSpfZomGCxXhYuLbvfmRCT2AgnQoI,3677
spyder/plugins/completion/providers/kite/widgets/__init__.py,sha256=YQeixu74zsA_5Ga1ukvLEdduITv1h1k3W_zDIEbVcA0,273
spyder/plugins/completion/providers/kite/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/widgets/__pycache__/install.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/widgets/__pycache__/messagebox.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/widgets/__pycache__/status.cpython-312.pyc,,
spyder/plugins/completion/providers/kite/widgets/install.py,sha256=lak12GNQ2HqSno-l58RVGJ9gQLnoLjdgXJ3lDE8j8Uo,15257
spyder/plugins/completion/providers/kite/widgets/messagebox.py,sha256=2w17aK1wBC4_WUI56bdc_Ub7GXCIoCDX6VoV0lIGjHk,1193
spyder/plugins/completion/providers/kite/widgets/status.py,sha256=Y3Hs0vb9Ps7E1SMxDl3lvIv0SAYxODb2fNGVj2yXEus,4734
spyder/plugins/completion/providers/languageserver/__init__.py,sha256=_wDQw80q-ufBeFugqrKPg6fBABJStWTBXowPGacMd_w,217
spyder/plugins/completion/providers/languageserver/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/__pycache__/client.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/__pycache__/decorators.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/__pycache__/provider.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/client.py,sha256=zpnS5fny5MSYgwH9CK9qmtGJo8LCLAaGZaDFgYg2ZFk,24187
spyder/plugins/completion/providers/languageserver/conftabs/__init__.py,sha256=gaKFwRDUHjNYaBiegy0pFDN56-V9kO9nMDKI7il6zDY,666
spyder/plugins/completion/providers/languageserver/conftabs/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/conftabs/__pycache__/advanced.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/conftabs/__pycache__/docstring.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/conftabs/__pycache__/formatting.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/conftabs/__pycache__/introspection.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/conftabs/__pycache__/linting.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/conftabs/__pycache__/otherlanguages.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/conftabs/advanced.py,sha256=xu3ejgwULemxkBYpmkJThCQBHMPFwuTjFlLl3zG4uUU,8950
spyder/plugins/completion/providers/languageserver/conftabs/docstring.py,sha256=U649bH5WksC-81Vmk_iEjsqajKV1RLq167_zhgEavAo,6887
spyder/plugins/completion/providers/languageserver/conftabs/formatting.py,sha256=M4BOmnw9aqKezDgXOBGBklYnWsU77skPFjF0Amlkmnk,7611
spyder/plugins/completion/providers/languageserver/conftabs/introspection.py,sha256=PYnazAhgbEp3dVu9iWvoKmsaHb5lKJwj5_1RWtiV9T8,2868
spyder/plugins/completion/providers/languageserver/conftabs/linting.py,sha256=HG_zOwxrJKlXrMRdiHBLX6VsJGwKMws1eeWtSzYvZVo,1687
spyder/plugins/completion/providers/languageserver/conftabs/otherlanguages.py,sha256=KBQWQr89oVqMbRWXZpTp1U4JDZy5_RlgjsnfEmaL-RM,3749
spyder/plugins/completion/providers/languageserver/decorators.py,sha256=V3u_LxSGLWTlQDlahAntJ3X_5888OKnrs8t3MN293ck,2090
spyder/plugins/completion/providers/languageserver/provider.py,sha256=oFzvNl9eGNmhvkCgVbqeN5K6Ws3V5r6LTkwZuvjv_qQ,35731
spyder/plugins/completion/providers/languageserver/providers/__init__.py,sha256=lDuJ4sIAZjpZUv1eVLtfF1HFvDESRNk91EmEpSPJQao,507
spyder/plugins/completion/providers/languageserver/providers/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/providers/__pycache__/client.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/providers/__pycache__/document.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/providers/__pycache__/utils.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/providers/__pycache__/window.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/providers/__pycache__/workspace.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/providers/client.py,sha256=wIRgPj4l9Dx4-X3zoVQexiVMJqBCDefIiZnMw7tZx1w,755
spyder/plugins/completion/providers/languageserver/providers/document.py,sha256=JtT_SZ3B7UrmDKhXlq26TVrWNLl9UjWM0l0X7mGVrQA,12781
spyder/plugins/completion/providers/languageserver/providers/utils.py,sha256=iiF7gGG2tWkmfOAvw7MrANIlhsDE82TrWEp6iDgBZv8,2407
spyder/plugins/completion/providers/languageserver/providers/window.py,sha256=S5W-0Sv2CJs8a_AQpXvXnJL2fuMjuqwcP0SDoyk0Oa0,936
spyder/plugins/completion/providers/languageserver/providers/workspace.py,sha256=vzZx8-gr1s_sbgqDevHe7QGaI2fATzews1HGObu6IoM,7735
spyder/plugins/completion/providers/languageserver/transport/__init__.py,sha256=TUb-gJrQKCKfGIstB7XsydXDcfB_Y1QJlWrVl6XuLLQ,262
spyder/plugins/completion/providers/languageserver/transport/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/__pycache__/main.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/completion/providers/languageserver/transport/common/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/common/__pycache__/consumer.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/common/__pycache__/producer.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/common/consumer.py,sha256=tqmvkYPT7tQLVbHwbO0N2LZvoivvgEZ2SwrX2L3OPBQ,4333
spyder/plugins/completion/providers/languageserver/transport/common/producer.py,sha256=kHVcPSk7CqIB_NbTSMhWzLrqAxzPPFqmlvYQ60wULns,4327
spyder/plugins/completion/providers/languageserver/transport/main.py,sha256=As6uZYiYgyvOsAx3bTl3dwtkjegKWCwwKXQnQPkk0qc,5862
spyder/plugins/completion/providers/languageserver/transport/stdio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/completion/providers/languageserver/transport/stdio/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/stdio/__pycache__/consumer.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/stdio/__pycache__/producer.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/stdio/consumer.py,sha256=WvVK7DAdSrj_3y0iww9eJcb0XsDWhqrggmeaKn3lEfk,904
spyder/plugins/completion/providers/languageserver/transport/stdio/producer.py,sha256=UXQ9h5fBdJ3HDekSPCF4EXEpGg5M1FdZlhm0n-uyB4s,3407
spyder/plugins/completion/providers/languageserver/transport/tcp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/completion/providers/languageserver/transport/tcp/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/tcp/__pycache__/consumer.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/tcp/__pycache__/producer.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/transport/tcp/consumer.py,sha256=zbtsAF5NuNfOO5mvN1Ld75JzY_HSSreVZxRk5wFfiNA,882
spyder/plugins/completion/providers/languageserver/transport/tcp/producer.py,sha256=oKGJ67eiyJQCOngitHSLNGY5oi-Ur367jBppmtSIApE,3359
spyder/plugins/completion/providers/languageserver/widgets/__init__.py,sha256=GaKipYlSPwk9b1Ria4qVShez4exo2iaAP2hNSnb9hmY,323
spyder/plugins/completion/providers/languageserver/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/widgets/__pycache__/messagebox.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/widgets/__pycache__/serversconfig.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/widgets/__pycache__/status.cpython-312.pyc,,
spyder/plugins/completion/providers/languageserver/widgets/messagebox.py,sha256=jfeaK03Jjpjfo0BrTTofjndZxC18eIS86mnX9TJdJCA,1317
spyder/plugins/completion/providers/languageserver/widgets/serversconfig.py,sha256=sLRDSyocZDlKaOn2Pj5y3iN9z_ayxCWtO3g5f12TvRY,27189
spyder/plugins/completion/providers/languageserver/widgets/status.py,sha256=cFsy0Iu07RNPQz0YGn7bK26vu8--xasEcx2eI4bSH1E,4362
spyder/plugins/completion/providers/snippets/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/completion/providers/snippets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/snippets/__pycache__/actor.cpython-312.pyc,,
spyder/plugins/completion/providers/snippets/__pycache__/conftabs.cpython-312.pyc,,
spyder/plugins/completion/providers/snippets/__pycache__/provider.cpython-312.pyc,,
spyder/plugins/completion/providers/snippets/__pycache__/trie.cpython-312.pyc,,
spyder/plugins/completion/providers/snippets/actor.py,sha256=dlTPhsJApivToUVslAip7qYIsbCMUn4DOOaOflxr5sg,4146
spyder/plugins/completion/providers/snippets/conftabs.py,sha256=oCmG8mVugI1JNQN_Al8-lLgSEIwbUGbhzPuxbAa7UQE,8487
spyder/plugins/completion/providers/snippets/provider.py,sha256=tqAv5LF4Ks2VLIvntMnJeSp2irEjMbDYiIgYpN0trBM,3039
spyder/plugins/completion/providers/snippets/trie.py,sha256=NY_f4N1LoHiJPQuyDbOOJ_KgmNR4FIPSOamyKzm1PdQ,2540
spyder/plugins/completion/providers/snippets/widgets/__init__.py,sha256=FKnB4ALw1jD4vHSkdBXTQdKvCPQhZOcP0f8lKYfMY5A,291
spyder/plugins/completion/providers/snippets/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/providers/snippets/widgets/__pycache__/snippetsconfig.cpython-312.pyc,,
spyder/plugins/completion/providers/snippets/widgets/snippetsconfig.py,sha256=bhIf_QkAspILTAHSSkpW0Kntx96Tgwk1xpPD5TNMe_0,31846
spyder/plugins/completion/widgets/__init__.py,sha256=kZpOEXD1QCxAc0uhFfRWDxf-BLQYtFxBKP6cNkQ5FpA,198
spyder/plugins/completion/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/completion/widgets/__pycache__/status.cpython-312.pyc,,
spyder/plugins/completion/widgets/status.py,sha256=JSDvP2-8XgM0_Y-a0LzuwLBmgz1YII90oIz2inhCekU,2184
spyder/plugins/console/__init__.py,sha256=P3zDlCZV3hH2vku3H0uTe-6eC4iseFjfEsXsx6Q269E,406
spyder/plugins/console/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/console/__pycache__/api.cpython-312.pyc,,
spyder/plugins/console/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/console/api.py,sha256=KbuUT_Il2pvuchecCx_X9YCytX5COv8_2-iZ6lNSqiA,459
spyder/plugins/console/plugin.py,sha256=J6OKPKHnF4WQ9ntPfqotRt7UzPQd6VlpAZU7ph8Hd_A,7702
spyder/plugins/console/utils/__init__.py,sha256=WaaUnKVP5s1OmrnVzSIl-D6vQuqeeZC8nu4M40MLK0c,283
spyder/plugins/console/utils/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/console/utils/__pycache__/ansihandler.cpython-312.pyc,,
spyder/plugins/console/utils/__pycache__/interpreter.cpython-312.pyc,,
spyder/plugins/console/utils/ansihandler.py,sha256=mShKLY-c41kedu5gVT6lgyun4XZ26dr1KGjV-0ylTjA,4174
spyder/plugins/console/utils/interpreter.py,sha256=NvOR9DVVMKbx4vQW_7SnLPuiG1xfbfZUZwYhLu43ZPI,12032
spyder/plugins/console/widgets/__init__.py,sha256=YCfLecpx2rvnol-ekjSahVoh1jEX1TKudN1--U6zj3o,225
spyder/plugins/console/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/console/widgets/__pycache__/console.cpython-312.pyc,,
spyder/plugins/console/widgets/__pycache__/internalshell.cpython-312.pyc,,
spyder/plugins/console/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/console/widgets/__pycache__/shell.cpython-312.pyc,,
spyder/plugins/console/widgets/console.py,sha256=aAxPouua2y_1Jcozmp7A4pvttRCLaOiNmXYBOH_WRj4,11716
spyder/plugins/console/widgets/internalshell.py,sha256=VP-WPEglZRHuXfiXnbfs4tO5ZMQRBJdk4B1jP30brdc,16094
spyder/plugins/console/widgets/main_widget.py,sha256=XfyAlwrW2JtWTDphqZpZkX0wvpPGz5P1v4TzrjOWt3A,20316
spyder/plugins/console/widgets/shell.py,sha256=cIBXG8Cw_MXM7LVIqrOe6DZIFZbCwzCWIkrGNXaMav8,37055
spyder/plugins/editor/__init__.py,sha256=Hwlov5sTK9FOZjY9_EJIWIEwqh0B6qhH81F_uotX3N8,394
spyder/plugins/editor/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/editor/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/editor/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/editor/api/__init__.py,sha256=badHH-VXmDnZsea9HY3cI10PgdFYc7sWISgCH92OCTs,448
spyder/plugins/editor/api/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/editor/api/__pycache__/decoration.cpython-312.pyc,,
spyder/plugins/editor/api/decoration.py,sha256=uaaMKU_1GhgY3PWqsleQzFm0UFBeSlUkwY9OGNajnDM,8399
spyder/plugins/editor/confpage.py,sha256=MI0LG6TbA7OD68899h-NBMwbTadJElPhfkLucN9w7SY,15658
spyder/plugins/editor/extensions/__init__.py,sha256=8YEd59VADI2LJRm66Wi5fa8GV29Gi8TrXfId1XCcmRA,622
spyder/plugins/editor/extensions/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/editor/extensions/__pycache__/closebrackets.cpython-312.pyc,,
spyder/plugins/editor/extensions/__pycache__/closequotes.cpython-312.pyc,,
spyder/plugins/editor/extensions/__pycache__/docstring.cpython-312.pyc,,
spyder/plugins/editor/extensions/__pycache__/manager.cpython-312.pyc,,
spyder/plugins/editor/extensions/__pycache__/snippets.cpython-312.pyc,,
spyder/plugins/editor/extensions/closebrackets.py,sha256=C06wxrs4eJF7ltzyyUwNV7pe08DCD2uDOaJ3-EnhZw8,4970
spyder/plugins/editor/extensions/closequotes.py,sha256=9bjwm1kQWxouCWJV8ol1UFlJtFILRaZCvVW09zt_QXE,4873
spyder/plugins/editor/extensions/docstring.py,sha256=92NXGfU6dQSUIQaLCZz6kDVMfW0nT-ycaCq_nYUSe2o,38525
spyder/plugins/editor/extensions/manager.py,sha256=2gxlLIzwjYZxE1R7ksi8wtYyoCapvmzJ3StCEgqjH2s,3200
spyder/plugins/editor/extensions/snippets.py,sha256=qgjp3BIRXcm-AT53ALuFdKgngpuucL10ge0bfZAUayc,37280
spyder/plugins/editor/panels/__init__.py,sha256=_1T4kU8hO3qFzzAb07gpiEamrzmLtMPLPwkIrQlv4II,585
spyder/plugins/editor/panels/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/classfunctiondropdown.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/codefolding.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/debugger.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/edgeline.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/indentationguides.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/linenumber.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/manager.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/scrollflag.cpython-312.pyc,,
spyder/plugins/editor/panels/__pycache__/utils.cpython-312.pyc,,
spyder/plugins/editor/panels/classfunctiondropdown.py,sha256=PG8hAbCXYzRo2VP_AtVnodpmEGgbp-iMiy2bdD1-onE,8045
spyder/plugins/editor/panels/codefolding.py,sha256=TdVWUj08Vq8YFJmpwcs56_V4Dm3PWV5llacFx4f02sA,29217
spyder/plugins/editor/panels/debugger.py,sha256=atM60CQKidtLkfMCOEJSYoH4_M3a0e6FbzlfH1Bj2a4,5172
spyder/plugins/editor/panels/edgeline.py,sha256=R5iO1_J9wrs1hGbZ_r6H9rUeInTsqSuHCBFDdD8Znm8,1927
spyder/plugins/editor/panels/indentationguides.py,sha256=8tYmi3TBJ2jyJoLr_moONdn0Y0teUlOyU-8sHhEdGiw,5338
spyder/plugins/editor/panels/linenumber.py,sha256=prohGL1hpDKbQTvOSmjxBC-nk0XovdRud6EmgrMo2dA,11847
spyder/plugins/editor/panels/manager.py,sha256=nuVFU9AKpMHrxgfIMGoFLeeqnzUCUkzt2qLKRCGT-so,11456
spyder/plugins/editor/panels/scrollflag.py,sha256=IjmQrSVM280Aoinmn7OmYFuaX1gx6sDwTk02ioAvk2Y,16018
spyder/plugins/editor/panels/utils.py,sha256=4a81vmbt-8wroGZQPOy7XCh_fBQ9a6jMFwNWjh3riYk,6624
spyder/plugins/editor/plugin.py,sha256=T7qBpsXcnykdq8E8tOp62nWwrK637BUnJGVUI6M4pjU,150631
spyder/plugins/editor/utils/__init__.py,sha256=lWJe8b297REKvWw7ocz6-FY3aU3UTx5ya8qj3aKmdIo,271
spyder/plugins/editor/utils/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/autosave.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/bookmarks.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/debugger.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/decoration.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/editor.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/findtasks.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/kill_ring.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/languages.cpython-312.pyc,,
spyder/plugins/editor/utils/__pycache__/switcher.cpython-312.pyc,,
spyder/plugins/editor/utils/autosave.py,sha256=cEngJgvDjxo1SqplrGyBaJI_29OiKdxbgIqBkFhdQQ8,16730
spyder/plugins/editor/utils/bookmarks.py,sha256=4u2r-2WUkFhMto5mngIOcEFKvcM9QuPsQn6XUDEtiyQ,1314
spyder/plugins/editor/utils/debugger.py,sha256=I1px9gZYT3fI-SA--a4eU6gSOG9AbU5STtz9btDYKYk,6564
spyder/plugins/editor/utils/decoration.py,sha256=XvoALlUA9uCkb_3MLC9lpj6xZHiU6qKFS2K5zS-obMc,6470
spyder/plugins/editor/utils/editor.py,sha256=6KJzGnYhAiWhau9ROFrvJV1iQMZanG8f1kuumjnKrtY,27317
spyder/plugins/editor/utils/findtasks.py,sha256=0FT9lzi5JmJNOtzawyPrzTNG-XmhIBcYJM660EStBmE,1009
spyder/plugins/editor/utils/kill_ring.py,sha256=9ueFTwmmo3deB5lEu1Ctm0xPVd68kw9qcHIjlhwNsq0,4255
spyder/plugins/editor/utils/languages.py,sha256=HQZXek22-1aBqPAq77xAofbLu8tFwNhiQTATGZPUciI,931
spyder/plugins/editor/utils/switcher.py,sha256=a0L_pJVfZo522oe0i-lH-5-iwRMCciAtaXvenKiQnis,9907
spyder/plugins/editor/widgets/__init__.py,sha256=tu71a_hEV9Ck9dg4cqGZ8yieNbKPJpur97AGKk9zjw0,470
spyder/plugins/editor/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/autosaveerror.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/base.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/codeeditor.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/completion.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/editor.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/editorstack_helpers.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/gotoline.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/printer.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/recover.cpython-312.pyc,,
spyder/plugins/editor/widgets/__pycache__/status.cpython-312.pyc,,
spyder/plugins/editor/widgets/autosaveerror.py,sha256=NT72yQLg2Z5oRAGmtJIqHb4lZt13mTt4sDMew61eAis,2347
spyder/plugins/editor/widgets/base.py,sha256=VkE_f3UwSNEtAMLSWgor7GNj7BpKsRxkzYFqb7u3474,48384
spyder/plugins/editor/widgets/codeeditor.py,sha256=aouLG2HL-kpys_JSOiYnGkfjrA-8V1f7N-4RqAzn6mk,229932
spyder/plugins/editor/widgets/completion.py,sha256=PHPQ29AtaRqc5DTNWNPTBZR0aV5ZeposIyvHxPvZKRY,20286
spyder/plugins/editor/widgets/editor.py,sha256=jRi5EtZsH2eDTv0MIriLw7OUCB9G1Z243TfHbjLPimc,147514
spyder/plugins/editor/widgets/editorstack_helpers.py,sha256=AtZz_DN029V6ufBvMzP-dqySw40j9oDlQAb0ojaS1f8,9508
spyder/plugins/editor/widgets/gotoline.py,sha256=KN9r_i3B4ZlE6vJNGmaWxGBrsETQ0La2zgwbktOiVDU,3381
spyder/plugins/editor/widgets/printer.py,sha256=lNb9E_vGCJLp_FBXaIcDsWvyvXDE3pREo9a5_cuyVGM,4391
spyder/plugins/editor/widgets/recover.py,sha256=fAVrO0GtTieeikaAjaeeb6WdkyQ6v7iUdcjZhIY5gjM,13376
spyder/plugins/editor/widgets/status.py,sha256=J5PphNjfNxBBt1yn9xmWCK-fN-2qbzn12YS8S6TpuwE,4927
spyder/plugins/explorer/__init__.py,sha256=Yfsa7wqwFayWqvqy_Ktpz_4Am8DpI0kXknFpI9mLzlA,251
spyder/plugins/explorer/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/explorer/__pycache__/api.cpython-312.pyc,,
spyder/plugins/explorer/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/explorer/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/explorer/api.py,sha256=fDSrrnREWKoh4wDdM1raAQmAg37bVgMtzlhTF9AxW_0,509
spyder/plugins/explorer/confpage.py,sha256=UDGR1Qxg3PbAas4_UYXOZrIDFOa9cKa-cWnlxa279CY,4068
spyder/plugins/explorer/plugin.py,sha256=gT3uoM61B0rprY-NtkoZbiz0oCI5_ecdSCmG8OGK0YU,8312
spyder/plugins/explorer/widgets/__init__.py,sha256=Hicm2HgBxl4pURUDMut2egRJQBs7NaxXtctgvBWNcS0,271
spyder/plugins/explorer/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/explorer/widgets/__pycache__/explorer.cpython-312.pyc,,
spyder/plugins/explorer/widgets/__pycache__/fileassociations.cpython-312.pyc,,
spyder/plugins/explorer/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/explorer/widgets/__pycache__/utils.cpython-312.pyc,,
spyder/plugins/explorer/widgets/explorer.py,sha256=IT9h7vTXsaVCOsYvfCwLUbtcH76Jonj-anHKjofCqAk,69801
spyder/plugins/explorer/widgets/fileassociations.py,sha256=pV5kverYd_i876xg2UbzMXeM0SHLHigCyq0kGsiiIOY,21537
spyder/plugins/explorer/widgets/main_widget.py,sha256=x1XAZYH1uDKFdmGMtT4P2dwkREb1bQ6TaxqKB_h0s_g,10733
spyder/plugins/explorer/widgets/utils.py,sha256=MV3f5BOZ0xc5fw2qAlIFJXFiWDM--h7-s9EpGU4aURA,3630
spyder/plugins/findinfiles/__init__.py,sha256=IY4mXokKp-5JxO-oHe_emNAhaT9LhgRQntXQWeHnmnQ,240
spyder/plugins/findinfiles/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/findinfiles/__pycache__/api.cpython-312.pyc,,
spyder/plugins/findinfiles/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/findinfiles/api.py,sha256=hO6JtKAR7Ms64VwqSlt-P2aJ2XNW0k1n2MpvGaMvKxY,378
spyder/plugins/findinfiles/plugin.py,sha256=to4qwieJrd7m1sbX11U6qZCd1c6QTTMAZdRMEOprpTI,6370
spyder/plugins/findinfiles/widgets/__init__.py,sha256=kqhSxKvqGQbnFbX8J2GB4TV98IjaGNAY0XXJzex_TEQ,268
spyder/plugins/findinfiles/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/findinfiles/widgets/__pycache__/combobox.cpython-312.pyc,,
spyder/plugins/findinfiles/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/findinfiles/widgets/__pycache__/results_browser.cpython-312.pyc,,
spyder/plugins/findinfiles/widgets/__pycache__/search_thread.cpython-312.pyc,,
spyder/plugins/findinfiles/widgets/combobox.py,sha256=dO7gKjGEUHGiNQdP8N4YNHCqyRM9dhB9X3JQJA2lW3o,7391
spyder/plugins/findinfiles/widgets/main_widget.py,sha256=JS6ZnGpW0zlIjiJHJfQoWPAT7vbO3b7LKuur38RXl5I,22650
spyder/plugins/findinfiles/widgets/results_browser.py,sha256=Pp-nSorqv7sOvUutxGH-GUzEMVsXGQbAlGGd8kkLrH0,11443
spyder/plugins/findinfiles/widgets/search_thread.py,sha256=rIaTyRUnHIHsOEQW7_FqtWS8oPxxejuB4CNjeW2BONM,14420
spyder/plugins/help/__init__.py,sha256=bBRics-MXxpKCrP1IVFHmAkVgOhe-u_yhDZll3MzZDU,232
spyder/plugins/help/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/help/__pycache__/api.cpython-312.pyc,,
spyder/plugins/help/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/help/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/help/__pycache__/widgets.cpython-312.pyc,,
spyder/plugins/help/api.py,sha256=CMUufkMr8zGfmN2cQcaH98p6vv2L9G38mNdZ-qS4fAg,452
spyder/plugins/help/confpage.py,sha256=xCYFObPMu-FcEsWKRXFPMygxMQtfK6zizu0tas88T94,2846
spyder/plugins/help/plugin.py,sha256=AGZDKOUCuStlA1FsOhFXUuhJ58HgybdhGiqnNxKrDWk,12411
spyder/plugins/help/utils/LICENSE.txt,sha256=9YhCzYwMA844koL26ufNE6qji8f5DmnTaoDFMeaSLS8,1020
spyder/plugins/help/utils/__init__.py,sha256=Zh8SIo8cuA3d474ag83ZBdEl9WJbdmrxz95yd1lWx2Q,718
spyder/plugins/help/utils/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/help/utils/__pycache__/conf.cpython-312.pyc,,
spyder/plugins/help/utils/__pycache__/sphinxify.cpython-312.pyc,,
spyder/plugins/help/utils/__pycache__/sphinxthread.cpython-312.pyc,,
spyder/plugins/help/utils/conf.py,sha256=r7PudrRywV7RhuMff1PEQI9rNuRB8JKffA52wPdSp1I,4513
spyder/plugins/help/utils/js/collapse_sections.js,sha256=2348fFzDa8A4BznF2BNDTK7189EwKJDBuSaZsLkYf1w,2521
spyder/plugins/help/utils/js/copy_button.js,sha256=lypNXTKUn9QCBVcFfKgg6gbi5V9hi9xPV3PmU526nwg,3411
spyder/plugins/help/utils/js/fix_image_paths.js,sha256=s1FJ54t60HtAjw5QxQth-tEekyFC6RMfnGzVOn2066I,697
spyder/plugins/help/utils/js/jquery.js,sha256=kR3sUv5PtY_Ot8sc4IXXkSY1AbxjzAo49HT5bAfsR6w,252880
spyder/plugins/help/utils/js/math_config.js,sha256=UP_KZ4CfYJLGyaYz4MkuCJQT1xphf-EuJgeRb2QEKuw,1967
spyder/plugins/help/utils/js/mathjax/LICENSE.txt,sha256=wbnfEnXnafPbqwANHkV6LUsPKOtdpsd-SNw37rogLtc,11359
spyder/plugins/help/utils/js/mathjax/MathJax.js,sha256=nvJJv9wWKEm88qvoQl9ekL2J-k_RWIsaSScxxlsrv8k,63532
spyder/plugins/help/utils/js/mathjax/config/AM_CHTML-full.js,sha256=QerWuV8nMsxijLmIY_SrIErNVKw-9oNkkghD1H4bdfY,244982
spyder/plugins/help/utils/js/mathjax/config/AM_CHTML.js,sha256=cTARTjrKqDdXWLXFfYzEr-YVLqJShNckucrli8ItxJ0,174813
spyder/plugins/help/utils/js/mathjax/config/AM_HTMLorMML-full.js,sha256=B9Ivl0lfS8Y0agWULXUGBxK6IMC8u86is__OBj1kpBQ,298626
spyder/plugins/help/utils/js/mathjax/config/AM_HTMLorMML.js,sha256=BBGn7Yd2CfWGj1qi8SAndI_zxcWiiwPCLjtkzTeytVc,178815
spyder/plugins/help/utils/js/mathjax/config/AM_SVG-full.js,sha256=1MsgVeb3pHNLYBuAc_i6zYuYVGzZ3NtYQTaILiSwd3E,236838
spyder/plugins/help/utils/js/mathjax/config/AM_SVG.js,sha256=GmcW4yRn9A8jelP3J78OzSCAiLdhf_amlDfVgoBpj7A,150417
spyder/plugins/help/utils/js/mathjax/config/Accessible-full.js,sha256=SKyeUenFJYUIp4ILkOzbla0e2RlUV7slDJXJTE353ro,364830
spyder/plugins/help/utils/js/mathjax/config/Accessible.js,sha256=hh1-UmGm_HeylG_Lf-tO6PX8amd-khe7QkOCYGITMdY,245019
spyder/plugins/help/utils/js/mathjax/config/MML_CHTML-full.js,sha256=uDzWzJq5crT-hyvMsAQ9qi0OpXACMIU2baFRskvb9EQ,226492
spyder/plugins/help/utils/js/mathjax/config/MML_CHTML.js,sha256=TQh5M2HWWbBQ6wANjP-lMF3JSoDgFoSkOhS9NM188rk,156323
spyder/plugins/help/utils/js/mathjax/config/MML_HTMLorMML-full.js,sha256=tkq_PffFSL23IugdWg5F23HsfyVCfFRsF8SnDxh9Bgs,280136
spyder/plugins/help/utils/js/mathjax/config/MML_HTMLorMML.js,sha256=r12A59kcmfTNSetG2UUWqP01F6vKf7v3akgJplRw-iA,160328
spyder/plugins/help/utils/js/mathjax/config/MML_SVG-full.js,sha256=Jij1itmnHdS1upCKCi8hpVTd57dVjQMrP6fb_Z88yb0,218348
spyder/plugins/help/utils/js/mathjax/config/MML_SVG.js,sha256=6OoaxWx0Zkgpa2gvynnwGoVYPvp8ZpRBnR_Yt7RkkIQ,156833
spyder/plugins/help/utils/js/mathjax/config/MMLorHTML.js,sha256=44-6w7kkNdEwEDubRxXfKy_lE4TLoCWBc-eJS2uu_1Q,1953
spyder/plugins/help/utils/js/mathjax/config/Safe.js,sha256=w9nxSa-4J5inPRSpc3qCtcuLuNIX3mMQMKcC1K4MHY4,885
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS-MML_HTMLorMML-full.js,sha256=E-pA-pznQ2DmNs7P30HHue9SUHHr40B65YmeKDuZSMU,364690
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS-MML_HTMLorMML.js,sha256=84DKXVJXs0_F8OTMzX4UR909-jtl4G7SPypPavF-GfA,244879
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS-MML_SVG-full.js,sha256=AJVho3CEv0-iqzmF9u4c0dnmek7y7E3OdWzu42aEYIM,302902
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS-MML_SVG.js,sha256=Wqm67bmjQ1aYW4bH2okqegXtTMiUzuM3B54ErC9Msu4,241387
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS_CHTML-full.js,sha256=zyw8mpfD_8vSLIKJaaTpT1dQopvGoHvgGOnTUOnVJ1Q,289927
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS_CHTML.js,sha256=Zc43XSRpY5YXgifns_h0f5_EBWpGLlWWdvUAlFbI9dY,219758
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS_HTML-full.js,sha256=oT_Lq3lkNHPCApsQ3ddxHajVsV_KpQdHutHxO-2az84,314739
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS_HTML.js,sha256=FtAL7icFiG-jNXBLfTgvvF5IZ3b24eC8R59fD5MWUHU,221860
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS_SVG-full.js,sha256=7VETzjAei-lS8eDUDYcs1TXgo2SuT-DDXK089xwp8zA,281783
spyder/plugins/help/utils/js/mathjax/config/TeX-AMS_SVG.js,sha256=u7ar134arNwmWhodToNT2hfeNNhsQP8N_VmZHA89F9Q,220268
spyder/plugins/help/utils/js/mathjax/config/TeX-MML-AM_CHTML-full.js,sha256=2Xp3HalL_ek6qfEjUTFVAMSEpo83eHsGjgIuoZp2DLA,350647
spyder/plugins/help/utils/js/mathjax/config/TeX-MML-AM_CHTML.js,sha256=JRZ-Iq1HdlMYw8rtRJu_fiuuEQO3VH2I6S05FaxRu3s,280478
spyder/plugins/help/utils/js/mathjax/config/TeX-MML-AM_HTMLorMML-full.js,sha256=ZYhy7cJj6Gu4IPUZ0Ta8ca6-qg9sjAT65KweBCQsprk,404291
spyder/plugins/help/utils/js/mathjax/config/TeX-MML-AM_HTMLorMML.js,sha256=vip1R7R63Z_PBSm_8dut9l2fUZnEDoE5CLBKL-c_Juk,284480
spyder/plugins/help/utils/js/mathjax/config/TeX-MML-AM_SVG-full.js,sha256=EDVpvmjG9jSecNtkj1b-m4AROns2LpNq_DVJGgg5Iak,342503
spyder/plugins/help/utils/js/mathjax/config/TeX-MML-AM_SVG.js,sha256=nxWVwA6O4qn1teLONdORUYgeprbbxJmwoVF17EgvH44,280988
spyder/plugins/help/utils/js/mathjax/config/default.js,sha256=LcwoEBVX5oY49Tq9S9gXtUjoABdiDU2amacDth9La0E,42923
spyder/plugins/help/utils/js/mathjax/config/local/local.js,sha256=mSqwgKwJVaxWArM614mjhsRWqfaIVElMLNe0FBOho1M,1491
spyder/plugins/help/utils/js/mathjax/extensions/AssistiveMML.js,sha256=37nOeV0vEo0oFVaN7F5iTiBnHD07jmBB2LtETEl0Qdg,3206
spyder/plugins/help/utils/js/mathjax/extensions/CHTML-preview.js,sha256=guyp05lpnYYeil9v9VfUmuytW6xIwMPlOJ6txyNP-cM,829
spyder/plugins/help/utils/js/mathjax/extensions/FontWarnings.js,sha256=x_bv7D1XDrSYfiOEIuNCh7arrOFdxYUv8S-VX34BqqE,5612
spyder/plugins/help/utils/js/mathjax/extensions/HTML-CSS/handle-floats.js,sha256=Qkdfp4jQ_zTkAuPhhhODZkddASN8BEJqBFEUZydsKp4,882
spyder/plugins/help/utils/js/mathjax/extensions/HelpDialog.js,sha256=b0akTBn-_mD5E_UHoGDo9vqhVTSGzxuiHZNrlcDvh_E,5972
spyder/plugins/help/utils/js/mathjax/extensions/MatchWebFonts.js,sha256=Y5S6V5gNofYZbsZOcuigJFi9HB0MJslRIsHdu4JWENE,5347
spyder/plugins/help/utils/js/mathjax/extensions/MathEvents.js,sha256=B4Dtk1GIvlbJDwXjyrvHp0gKDKsezoBwBpP-coSrUYo,10991
spyder/plugins/help/utils/js/mathjax/extensions/MathML/content-mathml.js,sha256=nLjqibU3nmJ-OOJb8eQzo2RqYsBL1ncXU7X_n8HG3EI,30340
spyder/plugins/help/utils/js/mathjax/extensions/MathML/mml3.js,sha256=KX8ySBel8vsFb7-JzKzVKE0JU2I-FILcDBlJ6Cpuakc,24682
spyder/plugins/help/utils/js/mathjax/extensions/MathMenu.js,sha256=z35ijYxrLgJuIL2JXskmBuJFdtQ-dmXkhCqfWC7YeIA,38244
spyder/plugins/help/utils/js/mathjax/extensions/MathZoom.js,sha256=thLPwxTPxj_puUu2dOnvIpP1RScA6e6cqxwx85N_8dA,8662
spyder/plugins/help/utils/js/mathjax/extensions/Safe.js,sha256=G0SntI2ZGraS2pB0_oWCzUfUOLV0Ra8GTOMNLqw4cd0,6280
spyder/plugins/help/utils/js/mathjax/extensions/TeX/AMScd.js,sha256=aaj4aXp5E-ld-uqygaYSB2DUYQarKYUnQS-_TplY8-A,3168
spyder/plugins/help/utils/js/mathjax/extensions/TeX/AMSmath.js,sha256=fUYi8pu8vBFQbfHMB90q-2tp9KqupnN39OveItiFDr8,13790
spyder/plugins/help/utils/js/mathjax/extensions/TeX/AMSsymbols.js,sha256=-Opc68lTFSMz1DJJBv5zYn8FUPkNZCZF3sB-gsTAgrc,6614
spyder/plugins/help/utils/js/mathjax/extensions/TeX/HTML.js,sha256=YXE88kWryu9-sUhDM-ZkyP9lLFrOOQd2o-7kgT54FQs,1745
spyder/plugins/help/utils/js/mathjax/extensions/TeX/action.js,sha256=oQhLBg9VQD40Xckk1cIrNr96SLwfkfAFGEhGV-UKv3w,1407
spyder/plugins/help/utils/js/mathjax/extensions/TeX/autobold.js,sha256=HQyC3y56QAuZT_zfS55Y7FH4p-n0e_EkfehuJHVLwYU,1290
spyder/plugins/help/utils/js/mathjax/extensions/TeX/autoload-all.js,sha256=ap7uk03nW2YiJ36ZJRj9gue_wB3ZjO9IUt8tw8OQskQ,2181
spyder/plugins/help/utils/js/mathjax/extensions/TeX/bbox.js,sha256=EccBAV_-19s3xtiCc5t-nLVbLGlwYXPRvBaoF6I__mo,1997
spyder/plugins/help/utils/js/mathjax/extensions/TeX/begingroup.js,sha256=BAhro-Ns_BVRWvlYhJJy7ywLIhgv7qVI0sAf3feLd7g,3913
spyder/plugins/help/utils/js/mathjax/extensions/TeX/boldsymbol.js,sha256=2fjctpg5iVhwM8z_m0puamtCk8KAFv2V64vYTqKi3to,1740
spyder/plugins/help/utils/js/mathjax/extensions/TeX/cancel.js,sha256=Ip9Wu43z2xYjTuMKd2KAbhjodziw-K0qc28p34lqlcc,1989
spyder/plugins/help/utils/js/mathjax/extensions/TeX/color.js,sha256=szkeex79vztn-FkDP1_QrqsSXv3W1F3RGxioGmPbbOc,5351
spyder/plugins/help/utils/js/mathjax/extensions/TeX/enclose.js,sha256=TAd_BzYOOkDntElYvmsslZ7RB0Cq3AMTvihl_YAz_3s,1607
spyder/plugins/help/utils/js/mathjax/extensions/TeX/extpfeil.js,sha256=MRHn3Vsni-f6HeOhxMFz8Mb_iHe52RcPPGyQ7MNOdyE,2074
spyder/plugins/help/utils/js/mathjax/extensions/TeX/mathchoice.js,sha256=vdChT2XurrmOAfRjrl5OJdYDV7DHDgGcwh3CpBldPrg,2510
spyder/plugins/help/utils/js/mathjax/extensions/TeX/mediawiki-texvc.js,sha256=JqK6ZP_6QsEFsT7vzDEBW6yB5UI7puqDD5XDHPBLJos,3986
spyder/plugins/help/utils/js/mathjax/extensions/TeX/mhchem.js,sha256=JS7EJoy8z5J_8LOILke9xNRf6jU6IkNAK0w4-gH075w,8728
spyder/plugins/help/utils/js/mathjax/extensions/TeX/mhchem3/mhchem.js,sha256=b4qgL6w4rkrQhlS3EfESRxvZ8fnTUos2B_Bk6oxMxx0,26896
spyder/plugins/help/utils/js/mathjax/extensions/TeX/newcommand.js,sha256=gtC2Y_5JKlKed8pF2rMDuFJyTR-k8j0nJiNOpMGreIs,5249
spyder/plugins/help/utils/js/mathjax/extensions/TeX/noErrors.js,sha256=ZPtQqwdHCgH_zqciTWCWvJB_zi-pPkv7XWSIMAA6ZvA,6135
spyder/plugins/help/utils/js/mathjax/extensions/TeX/noUndefined.js,sha256=tY_ef9fWqw9wvbgcQtulhcWEiiCMMgqb-i5K9UbhxW4,1366
spyder/plugins/help/utils/js/mathjax/extensions/TeX/unicode.js,sha256=X7HkBbjfbZgHD3wudRpe3PFuzcWJk4euCsjf5TvRIHU,3031
spyder/plugins/help/utils/js/mathjax/extensions/TeX/verb.js,sha256=jjeQ3tbdOm606PLyyf_fnmQofo2h0-Y2Q3cUmDyUmyA,1442
spyder/plugins/help/utils/js/mathjax/extensions/a11y/accessibility-menu.js,sha256=QDFNMY5KepwVziwt9c4lYXVnG25BAqTDcW0ue9xmOLc,2841
spyder/plugins/help/utils/js/mathjax/extensions/a11y/auto-collapse.js,sha256=Hyz7ukL0UyY4K19BFZFLblQOSfIOWnE1rlKm40vqVto,8030
spyder/plugins/help/utils/js/mathjax/extensions/a11y/collapsible.js,sha256=do7n4K8ZIZDhbjz0PR7NACA5FCcT6FTi4H4-V7wHcaU,10382
spyder/plugins/help/utils/js/mathjax/extensions/a11y/explorer.js,sha256=L8soCvM_QqWJuHkoYciV_v1HRDMiQ2twyDhP0EPvXBE,14016
spyder/plugins/help/utils/js/mathjax/extensions/a11y/invalid_keypress.mp3,sha256=IJ5lin7nJQEffUHgd_cXAHTanYEjrHOacyb6Qbodu-A,9030
spyder/plugins/help/utils/js/mathjax/extensions/a11y/invalid_keypress.ogg,sha256=o4f5xK-ZFdMiL_Fu0XnrnJDVySC4zfl7YKRwidOcNas,5353
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathjax-sre.js,sha256=RL6TITqLPDekVCRuHywT6mDpROBxiz4XdWy2X4uAJYQ,485762
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/functions/algebra.js,sha256=8IWJhdIPxBZ7FkkixZsDoSD2Qqwdn2RdQQTu84j5xTM,638
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/functions/elementary.js,sha256=XCjJp-Lq5V3lobvQ0SEw9IFVTYcZ1GbqHFyzN1T6s20,2774
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/functions/hyperbolic.js,sha256=cUmAIdchPQ7IZ0yBjSLZx0ZNDNH4ZqPvX9hvpZ9oWtM,2228
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/functions/trigonometry.js,sha256=jJ67bX46C6SKaFKUnHjTXxMxYy7LKGO9ZCGVtlG_N44,1920
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/greek-capital.js,sha256=edVUyQusUzSyA9eepkagKUn2_KOu0XNknwiutJRAI5k,3671
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/greek-mathfonts-bold.js,sha256=0ZNFoovf2H9RVvzZN8Jk_uDeOjMaGSeeuRbteNpe0ss,8610
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/greek-mathfonts-italic.js,sha256=KHVqWATLJ-4TuhH_gIYP0Kq4j9pm0oNPAnbAaKNy5tg,8952
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/greek-mathfonts-sans-serif-bold.js,sha256=6OU4RnMAUuiNXwlfNCZs0qzNV56FuXgObGhX8HNfbLU,10491
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/greek-scripts.js,sha256=FlQ2NAVLUxM5sZb9JKFdiCxlbNNlUmf3Hwsw2Nmw3ng,2133
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/greek-small.js,sha256=n9gnfQoeXn7X1bxLSumzfUFu83AyCXgliWbKFyvvFF4,2755
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/greek-symbols.js,sha256=qh9kI8XhDc0Vy4oiWoVUfjTqiduteFfeQkGnbtWcabo,6344
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/hebrew_letters.js,sha256=J-9UFe2R2ubcMI4BbaUx2pdF9ih_ypcdU8NrQGpY4Og,574
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-lower-double-accent.js,sha256=VRFfTHwD9cupRIO8sVDwSJrQ8v3i8OcwZFnBz_v7cYQ,10968
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-lower-normal.js,sha256=F-yLIxJUX3Q6QKnVERhzG4TAH8tOUb77VDY3vsPE5YQ,5530
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-lower-phonetic.js,sha256=IbZ4fpwV8yLHasZ7tAB5OjlFj__eKs2lgx4r0WZP5l0,31239
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-lower-single-accent.js,sha256=N_7gxDWJRTvmjfPYz9dkNjz1JtHbbHRN5MG7Xvv_irY,54823
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-bold-fraktur.js,sha256=wkQk2Llo_8YQohntQ2KLNFhpQ1BEm2YyDPNlqoQOhR0,7376
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-bold-script.js,sha256=sZQUbrg5VJHtBwm7eFYJNPnmzdkWBfctb73ixGrnFZ8,7246
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-bold.js,sha256=mQ6tEcHCPjUhn_sJ_0h7wK5lSCXbX7ypRssD7XhvQKw,6336
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-double-struck.js,sha256=r-paFufjObsztrm4GbAZv72g6D53YRd6ceLQBFKCeC8,7499
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-fraktur.js,sha256=LHJClnrvVWNMNSmdFpXIugE7SluhzZMaMARy4VAIYf8,6721
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-italic.js,sha256=HB62WJM8ibBn15gxIvZ-zWXysw0u62St1lgBlrZfhkw,6635
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-monospace.js,sha256=esdQVNI2CjO6eW0_m9tHkl3sK-C_2-mM2fxdpza2VB4,6986
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-sans-serif-bold.js,sha256=t6ZmWtaxb05vvjzZfZOe6lCMWR-GhjpHQAqetv-5PJQ,7766
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-sans-serif-italic.js,sha256=lFewXnNjrKR3WBS3mz8Ek7UXOT7wD8UBl55nuTypWCk,8026
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-sans-serif.js,sha256=MOkyPmxmRiqyR6r7KiqUrxPpxNAZFL46aGWlr8xlfq0,7116
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-mathfonts-script.js,sha256=Sg1cwrvb4CJtlOayWYqoY_-e8Dfm0wNb7G_HpNxuO5c,6585
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-rest.js,sha256=wwXZ2fV_shbCvPId4Cteu958uNsulWWk3T2b9pThgEE,37660
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-upper-double-accent.js,sha256=rcgIgiguF6fdF8szUuFyj_giD4xhDzG70oCUP7W1KOQ,14059
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-upper-normal.js,sha256=1OTs_EIY35wZrxUxXpgvgV5-QOWscyfqxEjYxz9Qshs,8598
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/latin-upper-single-accent.js,sha256=cvxw4nrFP3xigW83bToau5t-QncRuzVb09EF4AVJyew,57533
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_angles.js,sha256=wn_Z2KjF-xRISUr9VXSnHwdIYzgOi8SS0ef2Uxkd86I,2582
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_arrows.js,sha256=2LA73Rot0YaaP6nQO9sTC5veetONZplOqvwPn74pQII,60254
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_characters.js,sha256=IU0bXDVWTUJnXT2n1FJqrvNeJyjPUK_3dknx0lQG7s4,1938
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_delimiters.js,sha256=iudkww7ubOGBUj1p_jUOwZ4pOlyQjD8dqimtRz-Mh4k,31145
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_digits.js,sha256=0Afq_sw1GA3WQpZHqFpeRCjlVQB5Hw7sscfeSzOOb9s,45168
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_geometry.js,sha256=r1veeKC5Jw8j-fw7NQTqxbq2C58Owp3sONec1FTGKL4,37167
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_harpoons.js,sha256=uf0eh26f-VYEagXitkHTsD8mvLlvhJhSMurzXYRoOcU,7889
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_non_characters.js,sha256=ZPlpAPtcxLqV1CA_1KCSV7F0yWEByTFF8DjzLwiMUMo,3142
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_symbols.js,sha256=20XEDlgc-iF-FL08QQJWtQk_4H0okXLzCsAFpCuJFfE,122327
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/math_whitespace.js,sha256=NeitTfBRzUfMSPa_JA3qtQemYtvX-uWqhLqSL7IHPOY,3715
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/symbols/other_stars.js,sha256=OM7hI3_hkWoTaes0jWEZZEf9n73ArE9z2ITGJe0hr1E,5381
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/energy.js,sha256=g2Gv_BG5T4mfbu4GCgVnGmESc2_yn0QLzqMEGk0eQ_Y,1448
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/length.js,sha256=TnghvfSSJotMUDDoA_pX4ROz681hlspwjplKSn9wi2g,884
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/memory.js,sha256=9bLgEhURL9VVt-2FQ8-mJhJa-LZQMB8_ghzGw3pdV9U,539
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/other.js,sha256=WPNPFwzOZeURf_4xkpmoY1hheF2feNHINFG5kX7o5Yc,302
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/speed.js,sha256=vnnQMlnX7_jSFGFIb6Kup_bgA3VGsLRoQGooeHBjrlY,409
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/temperature.js,sha256=vGHQMtewjCFTrVBouMCn__QvYh9rReFXtyUuQgZ16GQ,325
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/time.js,sha256=bHOeA-FuVM9xGKtIAYyjyZOknuA-VZDrH5yZMp8KXt0,581
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/volume.js,sha256=MCdQJ-OpZN29cg5AKxcsbk8OVFzROfV2qekFfTL4u8o,1222
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/en/units/weight.js,sha256=LIcQ9QfJVmI0VKDhqfZ0HgCIuJd-AQS2Jlqxc6a_5LM,833
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/functions/algebra.js,sha256=w2QdOFMLUF7r1TTrdx3OhuKgKqfN8KDzFzwksMeZP_M,612
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/functions/elementary.js,sha256=4X3xbz-qcgQCrDU16Zl5o7PeZvOwZ90IAvzA5h9D0Yo,2095
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/functions/hyperbolic.js,sha256=k4LJ6AaVZ18wF9K6aKZFGvePeWFInfyoLKaOK-MhA1M,1468
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/functions/trigonometry.js,sha256=fE_HWSMb9x2ZqNbRv6cu32SxWuON09lfpeQsu8PFx5g,1321
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/greek-capital.js,sha256=WyhSONlw4w4xGNGjKOQde4n7ZhzhnOYaDKSHz7yOaQ4,2039
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/greek-mathfonts-bold.js,sha256=lADvG8MTCZgbwkQ3bZlyhOgoyZQV9b-yUBthxu4ULVw,4317
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/greek-mathfonts-italic.js,sha256=qPCc9QF2Yif88XoaSObDPpocHT5URYx14R9TrLRNOHY,4317
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/greek-mathfonts-sans-serif-bold.js,sha256=Py1RDDNuHSrBgDOBoEmUXdvRJS0SANUOGKclQ1WeqQ0,4856
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/greek-scripts.js,sha256=olEk32aQVSyxI-O31YG_wdmNADnt6QeBhh4CsKL5h2U,18
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/greek-small.js,sha256=56s9WA6bdjiSTdgU39_K7N3tyZOjkyzsRDfXYodo16s,1855
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/greek-symbols.js,sha256=LqZBOQ28rgvIHodoaaWjWpwhXcYlx2GQXbqNNawniW4,1285
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/hebrew_letters.js,sha256=YElY2Wy6WVRtWDFvhih_WVdFqOejBwwzjAQyNM7YzF4,314
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-lower-double-accent.js,sha256=NDLvasFxzSG0HjnPMqyaNgGBauhMB8GPVPsAjIsVaeg,4435
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-lower-normal.js,sha256=akIE1wtbvPLgxiOpC70gJF04vY_Oqkb6NywRD9Qx42U,1855
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-lower-phonetic.js,sha256=GdoVDeQkyWoY7VTzFS-338ycm54By-89WogF3TtgHok,1073
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-lower-single-accent.js,sha256=s4eDi3SaxxHJf56KyqXHRPFHrvjebKl1kqdqd9hbq9M,13426
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-bold-fraktur.js,sha256=gMfRxi9yBzc1UOAxR74yoNBAyYqZpCHZhqWmPyx0hS4,4828
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-bold-script.js,sha256=ZKjU-7Nv-YQV7IUQ8weRO-li95gNvep6rysN4ht_sFc,4776
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-bold.js,sha256=PYJQ6tEID1zcMj0Wej97uKF6v7YCQNe8iCP6H7JPVIU,4412
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-double-struck.js,sha256=aYHMkNHMdt--hx1StZSGneCqSr-0jTcGUOuimf3gAUc,5012
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-fraktur.js,sha256=cnS4RuEgevTeYMgAtLoFAWaTdD5s9xAWc508Fq-mE0Q,4407
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-italic.js,sha256=HCe4Nfnu4lekNGw4KPBeYI2Io6r0sYtRuNShahVGJxM,4421
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-monospace.js,sha256=ncXQveiSPlL6zMxbSaGNyRV97oqd2jyDAIkbXYcj3PM,4620
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-sans-serif-bold.js,sha256=vKy9q09IhRAGQE6FfEHOsSIqKh_JBmlYbSaHz9XIqYA,4984
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-sans-serif-italic.js,sha256=wrluj_DbMz1jvR2bubmbvuQi6x945IDyJW9EqXrSzk0,4984
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-sans-serif.js,sha256=aVKydhyq8Zbo18eYdFuVAIt7REsM1GZJbACLnBXzI14,4568
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-mathfonts-script.js,sha256=v3YGunmMqMlHChVdNH6nyGcQzkYim7xsKDetwyKWq28,4349
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-rest.js,sha256=9gONpWDAtGTytWi0KCsPSqCOaXOkvjzNl-NXo742atA,100
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-upper-double-accent.js,sha256=LcO1wjVzq0AZsB5nfnDgwSD-vtm5jKn2ylce8pkBU2U,4944
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-upper-normal.js,sha256=lOumAGIlqJy8ND86ykXhJHbLN4gV8fPA2DHvFhFu7qU,2141
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/latin-upper-single-accent.js,sha256=eCG9BnmiC13l5PuzgQewebFlChEn_Fx8L_zHGJlwYrM,14626
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_angles.js,sha256=kzNa_TzzpA3EpqfWbpcLlKKH8odbg3vn8fXTLSbK-R0,1907
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_arrows.js,sha256=j-5PkHXkPmx9Z0Ozded4_bwILhuiRJoUuCYm4qhitz4,14801
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_characters.js,sha256=EKT5QlfeXy0-qNvafb3MuiGTVEgXS-bgSwfZBteQvLI,645
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_delimiters.js,sha256=qtSjeLoM1s6s89YU_xiTyQfYQmGOty6eyi3PGCDbCcs,7416
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_digits.js,sha256=hcg-vBfmrjKnjo7Fl1P0FCRDg_YVlHTkQLsre7vllyQ,6520
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_geometry.js,sha256=0Hz7VxkNZ-RFDjd8vZlt8BTD1GIbq0DTpkOsmZVqF-g,12353
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_harpoons.js,sha256=9_zscIbs7za8L0-f-wyOT8j_3wZIw1V0tlkIUI69POs,5610
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_non_characters.js,sha256=p47OiGZboKAKe7Srrpsy6K8L96qVz1MKQFuJ1Mxms48,910
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_symbols.js,sha256=xT1gPqTkD_JvP6tQfMElPodtFTZIp0eqtkdTkEkKYYo,50869
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/math_whitespace.js,sha256=R3wXBX4ChQRpBYNsAuZqbozFd414dI7ArAHtRyyAlpU,1671
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/symbols/other_stars.js,sha256=QzThrZ-4nmw5OdecW0_QSKiIQyvwrx6XcpZiKrkF9Rg,624
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/energy.js,sha256=2cejj9VFx6wl7XgSfoYk09XbNRnpjwvQiuWvGOhScBc,1366
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/length.js,sha256=rAvA6YL4A2vdnzDT1Qq5oLtAIxc6d3lq9RxDQp_VJng,871
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/memory.js,sha256=RZl7Y-IeN9oJhqCbISfm3WzkP9RQmwyjxJTa0OgctEk,449
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/other.js,sha256=XtJnXOAg6oWPRvmLldpBlyVJy2Mo3yTSwzN-o2JcWW0,267
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/speed.js,sha256=W_jvHRceIxl309QbZ8g2QCmBwBLNYCcS2kDppkcKWCQ,356
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/temperature.js,sha256=dz0InSd2W-yAeoERENV53F3vypAESBfQFbImXXTPw1g,261
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/time.js,sha256=_wrLcyXKGO7T8K1xYZGlaGX0jIm2UYxU4D-Jhk33Tq4,583
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/volume.js,sha256=R0cI0afCKonoleKqm7aXgtbSSEVAYH47vAY8LJCSBAw,1221
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/es/units/weight.js,sha256=nSnm9kHL774zEPsJWOUxdnb6oPo1K4Wna3rwUV-yuQw,646
spyder/plugins/help/utils/js/mathjax/extensions/a11y/mathmaps/mathmaps_ie.js,sha256=ZbOzgbTCxLes1qlnmhjV4AKfoZGgm8gMQntA1A6rCIs,907862
spyder/plugins/help/utils/js/mathjax/extensions/a11y/semantic-enrich.js,sha256=lvtJSud0jc7s9bavc8wPt_ypRA98XoXjEF05keeVzQw,3246
spyder/plugins/help/utils/js/mathjax/extensions/a11y/wgxpath.install.js,sha256=DoxgrXrZstsaH-BC09RuA7X_qMn4F0zu42WbBxzi1z8,28985
spyder/plugins/help/utils/js/mathjax/extensions/asciimath2jax.js,sha256=nHhI9RkTrHtv4QCTemXzVEF9jxtdbc9_rQ3YnNI3h5Q,5533
spyder/plugins/help/utils/js/mathjax/extensions/fast-preview.js,sha256=auIETCb04AJtt_8v4alM4gyVGYcg9pfsdrmE6KtmBQU,3177
spyder/plugins/help/utils/js/mathjax/extensions/jsMath2jax.js,sha256=41aPndm0d9WJfxsX4pLszKVcKK7AwMnpRfZvqUgsuMM,2415
spyder/plugins/help/utils/js/mathjax/extensions/mml2jax.js,sha256=sVxlI0TRVX8rRd0A9tXSM8rrO0RSBr9tiOgRPPNouxg,5564
spyder/plugins/help/utils/js/mathjax/extensions/tex2jax.js,sha256=jxokCU5eeKTawznVrIXC2qtY1RRtwyEwualQ-HgVvb8,7131
spyder/plugins/help/utils/js/mathjax/extensions/toMathML.js,sha256=8qn4ZSctDgviCKx7jAzQJcSy1yrQNAYt86DT2jikQDw,6203
spyder/plugins/help/utils/js/mathjax/images/CloseX-31.png,sha256=4LDjwcoYswuN8fAQSFMPbzxoc_9BGnupsh4Xt512zGM,1461
spyder/plugins/help/utils/js/mathjax/images/MenuArrow-15.png,sha256=IeZCnz-EDxLsGo_-7nlXC9VEKV-y2jAejg5m4tdL0v4,564
spyder/plugins/help/utils/js/mathjax/jax/element/mml/jax.js,sha256=Pycp0840WgzUjXS0OeE6rAmEH_r77mtGT2n8ZKDlHNw,39440
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/Arrows.js,sha256=T8NDi22hp-GGuU_Mrqahn5IQdhvvyZeVy_cCrkF3ysk,2636
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/BasicLatin.js,sha256=dnMPCTcUl6frWVxg6SfZqITdcMeMqV-BaBojsYq_Rx4,1532
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/CombDiacritMarks.js,sha256=6Itl2RkGPvWt43ajKwgPF71X1eROl12BqdYhVROuezc,900
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/CombDiactForSymbols.js,sha256=Oa0IFn_rxUVwvJnA_z0srlGhHluXGJprZAFpZtPuzOU,924
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/Dingbats.js,sha256=lyzplYjp0H1jhUBY9QST28JFOCi3NJfP6borgdG8qgg,908
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/GeneralPunctuation.js,sha256=fixJJK68HeXZcopdYOzrr0sgQ-2rh7WxflmLo21rNc0,1133
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/GeometricShapes.js,sha256=Zyv-FOY8Fs0kXlmG2zNplTqgu0kB0sGtFFlLvr2fmMk,1390
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/GreekAndCoptic.js,sha256=E81QUKWPfEufRB1j77aAzPL4qjxK15vMinPVua6moDU,891
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/Latin1Supplement.js,sha256=eo3EyzzTmlW9gq4MY9oFMz4hU8nbhV0jF7yVPAb2APs,933
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/LetterlikeSymbols.js,sha256=q1ZQidRqjWox1Zh9k9ZdRXhnC4jRl298u2xdYCzQWRM,921
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/MathOperators.js,sha256=3xmPoChNKKqHEGUx2lxrfmU5mbaLOw6HQB3HZS3kX2A,3914
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/MiscMathSymbolsA.js,sha256=JYBTtcw7PNzEv0c931u_BcH89ZI90PelxaFn6RzM_d0,990
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/MiscMathSymbolsB.js,sha256=MHV-hDaoMg5RVSHTX0B2-q6rU5Y74hAvNCDi1S6_pXk,3048
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/MiscSymbolsAndArrows.js,sha256=uxQMOcS5e3JyUxdO489NVwTpph8czFdgYyzwQ8j-4zQ,932
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/MiscTechnical.js,sha256=3_dXKLq7MuURF61WYATlhl5733A9lc9bOD3U9vwAdUk,1008
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/SpacingModLetters.js,sha256=nWPKI6xCtlInetZVhV1Epw7Pnxw2f3WzLnzp5I_6Bfg,964
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/SuppMathOperators.js,sha256=D2LOF9CisZt8Vbq2KmJXZqd6a2KxqoatSawoRyNeGJM,4864
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/SupplementalArrowsA.js,sha256=UpmJTILdeo0jMD3aghlmnQAYyv6wrLaz2uGKwoEqqts,1006
spyder/plugins/help/utils/js/mathjax/jax/element/mml/optable/SupplementalArrowsB.js,sha256=kfDRqkaobS0Y0vW1q3M4Y4BQZ8WfkO8jcP7kXm7pXJw,3358
spyder/plugins/help/utils/js/mathjax/jax/input/AsciiMath/config.js,sha256=E1mpW7MA4sFYXQXlUgpDMPlr1976vSrRpUNy2mHxgH4,1041
spyder/plugins/help/utils/js/mathjax/jax/input/AsciiMath/jax.js,sha256=-WE1pS18QsNHtt2q6BmQ2_KhzCKoI29riykQ_LmogfM,34910
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/config.js,sha256=Gb01heYfgjn91VRF36A1dX2M7N7oDL8eqb3vGtjiRrc,1025
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/a.js,sha256=Df1QlWENOlH3ZE630kc88_fBHjBLM4xOkCqFSuI9_fM,1771
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/b.js,sha256=nCRmMhwWB_shuwAUH_KbJIuaAVP7UybqwgqHkhtr9Ic,2193
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/c.js,sha256=L500YTh-wRrUYW3jwRdSNjUc6kr4e6daHCOF701YTFY,2294
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/d.js,sha256=-7YOml0jQkQj5h8h11H48ozmwlTAHG2ae_9d4FX2G5Y,2421
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/e.js,sha256=aoWXez2Qo_DFIUW4OPT8miyZrUHoYzbcLJLZG0bjnXA,1824
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/f.js,sha256=0enm0VK9DwaZsx7v8eVCUIXF8c61QIwHGGyuCzkco4s,1341
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/fr.js,sha256=BY6Avt9m249iyKMv8MZaBCbitnrH1RR3dOM9dr08ngk,1758
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/g.js,sha256=ZhB9RjB8Hjos26syCx0Fjw4rHSQ8dh_EN8aD2BD1n5g,1663
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/h.js,sha256=BLu_6xXEuGiEB_Fc2tLIq5TQVR8tZxsdUoixdXgDdL4,1211
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/i.js,sha256=YYiLep0tLvGjtT_RPL6hO5hHngNVj1mXKf2wzya_u4Y,1691
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/j.js,sha256=9WqAbUUJG9b4h5pyxYIDLTb2cknyb8w-MisUdqw_c3s,916
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/k.js,sha256=EfHNIoAQ69zKQFvlfWXz4RwDH2Nsq706gXKthgLcp10,943
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/l.js,sha256=Oqim4nYN4AY-9XVCtxDecryVFyl_1KRPouBGJ5mnQHs,3410
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/m.js,sha256=133Ypu3lL0cCTdP2_8xuow8N8u4BF-BtDt1VSCo5Qjo,1335
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/n.js,sha256=XBe4OcDxqV4BMheiHwW5TtIQfQ63V-ih5vL1qrOupIY,4581
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/o.js,sha256=6Li_tRG3zQ3dIzb1AhQmHC5VgM9CrYDFJgCDQkB_l6o,1776
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/opf.js,sha256=GCF5zTTFY1Aj7chtZ_CpAamTAymgcs3TjLmHUXGaY1M,1800
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/p.js,sha256=YdN2K1XSn37ofEcgkU41V1YvpKwTpTwmmOv-IDAmTaA,1699
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/q.js,sha256=C4_iDUYvb_BNdho_oDuufDn5tNH6qi94VDRmBtH7ORo,926
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/r.js,sha256=KaFdUvpcTPFe9LLUjE37rgoK5MdnnsFiBFjt0nbzAww,2720
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/s.js,sha256=a7eHTShS7ryNPk9BnCoRYgMA_Dz9CunEZY3L4gXSTuU,3134
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/scr.js,sha256=ya2vaR0B8Dgr89mY9NsPF8zV1EO9vEW_a2rEU3qEobY,1776
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/t.js,sha256=AR6f937U0WB0S4e7LAEq55r5EKZHNQML1ULFEnft4zg,1796
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/u.js,sha256=pWeipU8GCvwep4oK1XdaUNaG6pemodNAfqA50xkFIPc,1878
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/v.js,sha256=l6dNxiZ89qfOa-SdHwe8sqE--m_NWRGoaCpwL9KTgyA,1649
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/w.js,sha256=OCuWEzj4KU8HBNCmPhErDEeWGYd9qtCTwMud-8tbeH4,915
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/x.js,sha256=3K8L2UrZYWi9ylSEsr1BCIQ1Fh0DnPAs7Ijfu281LJI,1098
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/y.js,sha256=-nbZm61qtjsEg5Vve2SaOznReFCvFlaHaz6eFH_fBGU,998
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/entities/z.js,sha256=qO5P3lD4BQs42bevqUVsfninHxCKjBC9WXWB3Xs3EjQ,1025
spyder/plugins/help/utils/js/mathjax/jax/input/MathML/jax.js,sha256=ef7mMuIDt-UZgJHxEZ7wi6vAUqu71APUrZ6CfEs6S3k,16403
spyder/plugins/help/utils/js/mathjax/jax/input/TeX/config.js,sha256=z3EhsORwEtvODt-QrwEKmbEX198JrWlEh7aC0ix6rvc,1268
spyder/plugins/help/utils/js/mathjax/jax/input/TeX/jax.js,sha256=vu154CT2QNEVAA_zPz7J7srjKUZu2ukhvJ1CWc2GDmI,52623
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/annotation-xml.js,sha256=ZThY51V0m8WxMYosw2xHRiz84y3t2oECQJuSHUJMdBY,1518
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/maction.js,sha256=FgBvwOsH5F5UAREqkp6vcz98GmgJ3xK-xlbTaEf8HKk,4081
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/menclose.js,sha256=EU9k4VE-5qAOwRwtelLn5IFChmWc0pUIJCB2E5O_eUU,6010
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/mglyph.js,sha256=abPE-v3aHE3E6nmy27UiF3bKdIVUD2pu9C3dx-qO5_o,2522
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/mmultiscripts.js,sha256=-_XoqfFY9OWhXTe-3xtf6cOPBcZ3vHCWLxKIMHrLJGs,6382
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/ms.js,sha256=20-KUo5qBTZu2hy3enISBrQ7Ya85jb4NUPxV0rlaBI0,1595
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/mtable.js,sha256=FRZ4RBUBcWLFTnNLlwRU6-VKgWjBW0U70n-lqa9ClE0,10948
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/autoload/multiline.js,sha256=qshmiE_FP7GH1Y3F144Ihmr1E-EjP0jFG-eNaoXgi-g,13985
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/config.js,sha256=ADVYan51j1h_zeTbaDnb_3SB_8ZIxy5cyyOQLMZE21w,1466
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/AMS-Regular.js,sha256=T3GbOozhp5SYFiqSwVrBMpD8XstOLIlaGLIYQid7Qyw,7424
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/Caligraphic-Bold.js,sha256=jV6HwOD2HV48NYD7ETNwPJPBeFPLVPDmuYimhimqii4,2036
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/Fraktur-Bold.js,sha256=OgEaxzxUvnxHFqOB2xbdXagIzMI4-edzSB66d11FMNk,3114
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/Fraktur-Regular.js,sha256=y1eGc2LxPjkuqm11SCaf7aFTO4zruRfKPgsa2ne5LCc,3119
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/Main-Bold.js,sha256=9rWyjFA1KsXzYnnManVWac1_iz7MfUufRrhIJfaJdXw,5418
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/Math-BoldItalic.js,sha256=wFFQaY4NCb3gUFx3QMXPUuXBHm86Js2WYz4Fc0o3BM0,3975
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/SansSerif-Bold.js,sha256=uk4O21W5otgoYDRN-TOFY_5RT94Sx8CEiStQZvkcBds,3728
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/SansSerif-Italic.js,sha256=zBEbYqnWA7BVI0DHMQ8NCkRMjRXHdQEJo60sYeUPSlI,3739
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/SansSerif-Regular.js,sha256=EU9D_ZouMhBjxf2TxyT9y474Hy-rdibe-I7JNhp3K9I,3697
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/Script-Regular.js,sha256=eE3TLVpGzcLcIDhPAWBgtuQX1wq0n1KifHoUxNke9wM,1774
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/Typewriter-Regular.js,sha256=M-r_3xjpTttT0IqAlti6iiJIBFV1bxBSXra8bs6q9l8,3795
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/fontdata-extra.js,sha256=NL2Y0PpVDFM4vTHb_4inAE7yfplVaUYeI2ClExHXAnU,4178
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/fonts/TeX/fontdata.js,sha256=Gfi0JSpTD2o5ARVrPMRmEF802yohb_P66YehLF7e7t8,39123
spyder/plugins/help/utils/js/mathjax/jax/output/CommonHTML/jax.js,sha256=E-7bYrVpbFzgHee0OTUIpbF9QxTgiCkI6_PDZ10LlIQ,60465
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/annotation-xml.js,sha256=urVJ7UNPgBXd-pje2BG4be16PWGH2RJgbWVyooNOCck,1500
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/maction.js,sha256=mJldw3M6056tvrcWlxYlmiPNHz0gehxYx9pahdjkBTI,5430
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/menclose.js,sha256=WJ5973ib676KYDVGmLKKiw6jYZS3Z8CX8aMGNvyv4ZI,9415
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/mglyph.js,sha256=D5vcUWjDSRTanrFdOrChy2tShyV1oEipA3mzrHk6nAk,2902
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/mmultiscripts.js,sha256=HAGYjkzcNOz7hjSl9rPUQLyJBO4H2qqti_WwHDJG1js,4887
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/ms.js,sha256=r8gIRNIXFyEXNvknjXx22gFkE8i_DxjEt9EePHS0uWk,1489
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/mtable.js,sha256=ipBkT8m32Hf_BlavFFPo_RrKRPwluskR334Ugh5-GoA,11552
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/autoload/multiline.js,sha256=_fW3Ti8Fo9kATPtSjkraqQ-ufTAlaB-V4T2sbEAZvP4,14456
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/config.js,sha256=hz9hLCehIXKZBSwRgPrxP9IZD0OqLntWOCKqLC15ZSw,3570
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Alphabets/Regular/Main.js,sha256=ngu1t7jTlE3AybDZEN5taOBjy5WDvZuyKkRxlySq53k,4589
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Arrows/Regular/Main.js,sha256=YbCcb84oJMBF0NOoQloOaCo1i7MkSLvzDp8caTFDCoI,6318
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/DoubleStruck/Regular/Main.js,sha256=WLBmt9UN2crrhk_JgQGqOfLwVyvzSgihAXcUk-aiX04,3055
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Fraktur/Regular/Main.js,sha256=O7myj3iF9s9SBby-kHoREE2X2mSl2uWVHYJEamtLNF0,3923
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Latin/Regular/Main.js,sha256=jvFfS7kbDVAQ0Ff3XfcJgJC4YG7p0aou4Uew9imIdns,3015
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Main/Regular/Main.js,sha256=Jzu8riwen0L6TF4uq8_6Z3hjl3YIPCuhAMkah7de9pU,14024
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Marks/Regular/Main.js,sha256=MEUMGc_06vnLK9JLjkWuuNAyAQTp-hnP9HXhkIP_G-s,2688
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Misc/Regular/Main.js,sha256=6EbjNerzPgVDUULrx6bdTWGugjNQ9bbVLaIQnRT0ysE,3174
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Monospace/Regular/Main.js,sha256=Dk17wYNsezVZDzDuS1MdTOWBcCmTaBy6JO3nvdbxsPg,2888
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/NonUnicode/Regular/Main.js,sha256=LxaUGSdWKVP-kjwAEfaW0fr50F5r4-2ss1qz-kFi9m8,8317
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Normal/Regular/Main.js,sha256=y9ABO_7BXt1V3CsOp3_mz1SQvckVARAF-3JhBPK9N3Y,10388
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Operators/Regular/Main.js,sha256=bNuQ3eCbJxsokChkYwe2uLpZLCzXMQpT1opn5EfPSic,9585
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/SansSerif/Regular/Main.js,sha256=s7rE0j3ZYi3LFYZcNSF2zdJxyJR1aOwlAyka87xuCUE,10505
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Script/Regular/Main.js,sha256=L0z4_bEESKWNk0fZ3BakWH4hnA9u_34OgU0jb3cC1Js,3999
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Shapes/Regular/Main.js,sha256=CmBow0dudyXFM6Z1ZJLdon8l_hgncdDeqMBjJB7BMOw,3371
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Size1/Regular/Main.js,sha256=tMK6KG2TTVf59RqmSftqhyaIsyswZ9MAttNt6sqQ5fM,3579
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Size2/Regular/Main.js,sha256=_7Qf5oC9xCyMUOYXSeQMPrez8b6JhMhdBxJrSLbfKbw,3628
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Size3/Regular/Main.js,sha256=5D3lnmm_7TjRPoKsfqu3Stk4TBPR5ij6V-ok3_UAow4,3272
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Size4/Regular/Main.js,sha256=yA7YgQmf_4K5S038xBH1QPgKMEdClkjKuS6helIeCZ0,1497
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Size5/Regular/Main.js,sha256=uMOTIxaIRLMlEqUfvDmtEEHrXB3f3c3OdPkuvYHFnpk,1230
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Size6/Regular/Main.js,sha256=sVDUfAu5szbxFOO7jE0gmFOU-yQ5ixSFsKQy6c4UTtE,2460
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Symbols/Regular/Main.js,sha256=CdtSdIgjG3lWIfLMq1BVhdKFEqZzkIClBd2j5mFmNMI,6440
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/Variants/Regular/Main.js,sha256=YGeWAlQ_xhqVBIRsaSruzKWiOSQFdSoHpeLibP5ar74,2747
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/fontdata-extra.js,sha256=w2q8sdywCeOxUA6CvK7gcf3y45TIVpXDOi683poqvcM,6145
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Asana-Math/fontdata.js,sha256=VrKoNdb-Psg9d1SrJhPFwZpRQ0pfSdKCFYC6AbkA16U,11863
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Alphabets/Regular/Main.js,sha256=sSM-dUiCBdUt58ueJX_cTC3FuYTRDfToilA3s6BxUXw,1530
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Arrows/Regular/Main.js,sha256=TQLBvmsTFvJ5cbh46BVM4Mh1XyR91713Yx4YRDupnhY,1939
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/DoubleStruck/Regular/Main.js,sha256=ObmIe1GzeHSPmWTJUhjp4fRIxqJNzaWcZ8cWqOHYh9w,3093
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Fraktur/Regular/Main.js,sha256=sVvWGpqeTP9s37W5zgb2pISAgzAzeIFqA9Cm7oddqVU,4026
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Latin/Regular/Main.js,sha256=xnjmf8bzKx9cK9vNyduts0MchnbgAIF0Sg6zK0Fs2CE,7742
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Main/Regular/Main.js,sha256=5vXYoZ_YrfVYqx3_xI0IiBB9Aif4JhdtxoWIpwX4598,13817
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Marks/Regular/Main.js,sha256=aD-M-pwlYFPTlZwmwJchZJWhxe2mId_Z0ciLMRDH2uw,3044
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Misc/Regular/Main.js,sha256=-bE3DwZLkAEzF1XYmODpTdb3R7Izft78WZ5zAdCj15I,1351
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Monospace/Regular/Main.js,sha256=xlBR9Kqm4L5cu3T2A-u2-idUQsFQYC1YnZYB6K7Vt2E,2896
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/NonUnicode/Regular/Main.js,sha256=DC5P8u9nrhkXUnRhpLzdd1xaHqqaTeNQBiIE4H9h6gA,38996
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Normal/Regular/Main.js,sha256=rZZkWNFo4aLDQW9pMPXy3pn9o5I_BrssHgD9Q9-zyMU,10435
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Operators/Regular/Main.js,sha256=tiF95Vc53TSHQNMr1bizozuvyKTLHJqZ2JQZrcLHmeQ,3353
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/SansSerif/Regular/Main.js,sha256=wZEoRmZssyA0MoqVhUmsW5slnNfIIGY_bEqRi7_fzFs,10446
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Script/Regular/Main.js,sha256=MLM5ZkkDepb4tMIsAqEyqD_9VEUceX5elQQxSjo-w50,3977
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Shapes/Regular/Main.js,sha256=FzX1Q7EAADXwCXOwBWkQXLUBJCFl9bWy__fUofM0mGA,2173
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size1/Regular/Main.js,sha256=XulcrrXZvZKXgGN73tKu8QIH3nSUgUWnaQ3phm7QxxU,5926
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size2/Regular/Main.js,sha256=fXFqwpYZiz3l3FP5I8zFpsXUyXQG9vAswp7tETXQBts,2387
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size3/Regular/Main.js,sha256=VU-UoqkJuDMrUyUcmiPDuJpvK5NPNLEjfxJxoxrfuYE,2396
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size4/Regular/Main.js,sha256=aUjiLdu1fkJbcWhdq6_byN9BzE11hy376_-zjo57skI,2437
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size5/Regular/Main.js,sha256=DjLOD5Mkws-956PdLREvBulLnHoDxAAdRTyyhR2JIJM,2450
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Size6/Regular/Main.js,sha256=PnHAbZQBD_rzoQaU1LWDcZLl8Fe0RFNFvADU9NYOYW8,10899
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Symbols/Regular/Main.js,sha256=UOcbiMi_gm4uP5n_ztB5ej8Z7-qYPN7ctNdKTWxfC6A,2528
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/Variants/Regular/Main.js,sha256=h5YIMJBF1ZRsUy1vx77M8ScuKon_Xe3kUtiKfgh9g6w,1329
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/fontdata-extra.js,sha256=2VmTWkvYCsjyiU1MtdFgvBDyD9_3ftBiwk2KRwSYhO4,11630
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Pagella/fontdata.js,sha256=JQ_9XVvTGaoY95q0cGcS5nqLT7InKaZkt3sIBZZ90Gk,13173
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Alphabets/Regular/Main.js,sha256=d_C3m2YdvFOpDTJMXnC-ljSUXyB0i91JOqFcRMQzs78,1524
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Arrows/Regular/Main.js,sha256=tpyrTO7xotO4R-_JSxQo32nfruiQ8PlH-orFa0fbB3A,1930
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/DoubleStruck/Regular/Main.js,sha256=UGs-BNgRKejwcKI5O7op-5-29VJVoLGOC_c7I1XSRRg,3087
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Fraktur/Regular/Main.js,sha256=vtVi3ksHhe7Glxe7t40LYg5oHjljotGy0iYpS7z7QXw,4017
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Latin/Regular/Main.js,sha256=qqSRg_Uqs140OZT2cxBFSyhsGkzdHuvwRPBk1gbUCGY,7718
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Main/Regular/Main.js,sha256=QemSkfLSkhew5FHcuKeqBZ0z9QiRW8_l7rsh1SRmuJA,13764
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Marks/Regular/Main.js,sha256=4WEY0PrzWy9ffVkGT9V0u-DnRAEOYQEjDzWQ06ZthHY,3042
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Misc/Regular/Main.js,sha256=6gCZLj3FL7aE9tYBWQ8Nuy9hnZebb6wUD9p1aogpmv4,1346
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Monospace/Regular/Main.js,sha256=aWDYnPvWw_bpvzqut2aaxdPHqqD12HTQXJ4yQ7fCn1A,2922
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/NonUnicode/Regular/Main.js,sha256=y1s-fPirljbS2gPXEieiNS6Xjw2St_2TWUjgXy47UhI,39372
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Normal/Regular/Main.js,sha256=E6lxmNkuWp6AKDlU07ydNm7taPtQyw1FevHFauy-cRQ,10442
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Operators/Regular/Main.js,sha256=BV8htDzLBYGSrObhHzvSznXEq9oSNAOmrgPsJTds2O4,3344
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/SansSerif/Regular/Main.js,sha256=DfHDr0_TFOECFQci65_mvKby0owIeqLRF-OeUmWLhmQ,10401
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Script/Regular/Main.js,sha256=szUAJrDaDD4aUjBLWid2YfNA5u3tfuphSSmZKeLqVeY,4036
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Shapes/Regular/Main.js,sha256=RrPTsC7WmaL--v7BcBXSuVItRFsLuLyHxAL_tmidM-4,2174
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size1/Regular/Main.js,sha256=I3-RmjGyhMPVvi6F6VIXiXmCn95jmUAYpXOMYo5DPwA,5887
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size2/Regular/Main.js,sha256=yX5D72TFgfSEQ9o1h1piwow988xSasBuixOz3MTT9NQ,2380
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size3/Regular/Main.js,sha256=ba7yOr1xcdSFW5qeybvBPxBWc2W0p5iog6J_aOJhk4E,2389
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size4/Regular/Main.js,sha256=bKy11JLvGACkDT6367J5-ftT2T5Ktb-E_TkG4tO8gZE,2427
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size5/Regular/Main.js,sha256=jjtDDImXx-OcZLmN-SyapuVv971ELjBq0q-g56sZUxY,2443
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Size6/Regular/Main.js,sha256=YSxsXqv54ndJORhf6Ayk8XZUj7pnSHA7gWaYANiubqk,10781
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Symbols/Regular/Main.js,sha256=Pl4wRTFaGpypMeN2ZLbZEg4g9TvSDj7cR7FVmNN-Nzg,2525
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/Variants/Regular/Main.js,sha256=bN_yuJ9dH8EvucpEBhE3Hsf51yJWQS7HhC62QcHb0ZI,1325
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/fontdata-extra.js,sha256=jwjczLR_mKv-P4KevOas_9BmBKk5jgfV9Na5YKZOMh8,11489
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Gyre-Termes/fontdata.js,sha256=gUR19QnP3fHB6PCp3IGIED92DHNo8lwwNQ0HHxAP64g,13197
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Alphabets/Regular/Main.js,sha256=gvLIy88RMQr6m10B1aWUVBIm6LBUD-kM8qdqH6kwSfs,1497
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Arrows/Regular/Main.js,sha256=HhNQtSzPGr2FKeVBRQ9iynEs6Wnu3g9D2OkMgoTabtc,1936
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/DoubleStruck/Regular/Main.js,sha256=dFY_r0kriVxdEk2seAhAjU6Xf6G_272gJjGEGCPJvbk,3081
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Fraktur/Regular/Main.js,sha256=MPM5zTng9k_mJlmovs1jLsHP3qVlEbirtMjfA3YHs8A,4026
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Latin/Regular/Main.js,sha256=ZwScuiBzNMxMF6leIIgEjvOApuGiwJ6s4pa3x85TyTU,7699
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Main/Regular/Main.js,sha256=dV3ivm4MWrMt67zkos--6E3226LXy3qcOgyceUsqoh0,13822
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Marks/Regular/Main.js,sha256=sFvxZlDZlgCEIq6DtUe_bg2RT0iGXbcjnIlBn_frxH0,3053
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Misc/Regular/Main.js,sha256=R58Tc6qLFUhqFmkA5MF1SSaoDqqAClsp4fF_SLqUj1I,1200
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Monospace/Regular/Main.js,sha256=JvpSiDcqNYx7n5XNxzA5ZcCdAD2XXsK1nJ0vyg6FEus,2908
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/NonUnicode/Regular/Main.js,sha256=9fY3YKHsr5-2rxQVqeKBAgA4GWfSDJ1y6YlvMEEbYoY,54915
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Normal/Regular/Main.js,sha256=OQIDCbGXiCgVhFlzz8ln8J1HCagQfjJ2U9ZcQH63htM,10378
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Operators/Regular/Main.js,sha256=2GfUNeL-Gr0cM-0kV1CA6vclE89ZM6jzrsSIHnLU1wE,3363
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/SansSerif/Regular/Main.js,sha256=zEQEo7q6G6l9Xlxbi5ShP24WILkZmkXG3aUNyxfn6VM,10489
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Script/Regular/Main.js,sha256=bLVxflZfk6qtFBOIDxLoQ2xjv6Mp6q2TWpRPg07ADbA,2603
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Shapes/Regular/Main.js,sha256=VQWBdxZ369qhWqJxq3zm3EaVxA3LkJbQ_4fpG_ECHy0,2188
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Size1/Regular/Main.js,sha256=pfsdYFs9Dkv-AcCNgDK0kVWMPqNtzwBwRFl2ZIqef_M,5879
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Size2/Regular/Main.js,sha256=BXw3ZKDUT1PX6xc7xii8dgisI5ctJhqstszM4Diy2ts,2399
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Size3/Regular/Main.js,sha256=c8dx8_P0t0fxU4CI11P98ZO5yPj80esfAktdIo0JZyM,2405
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Size4/Regular/Main.js,sha256=SGjfWd0MNY9e_cIhAZuqulpUWJtnT5uIpClRpLn_STo,2452
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Size5/Regular/Main.js,sha256=g_aSfore0-Q-Sfzm2X-5aTJDY-rb6UmoIE29gaMl98E,2428
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Size6/Regular/Main.js,sha256=UvF69cVFsZfN8Z5R0pdIMDXWbjJyDsPh04rW3k_lE0E,2430
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Size7/Regular/Main.js,sha256=HAVdqED0CRTLOEk_JlGrCq7N0Iwnaz4rJDmVTBsSMAY,10573
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Symbols/Regular/Main.js,sha256=nai_LFCGw8k3BAP477WmeNIQGWRCWHYrKZxaKeBOa3Y,2504
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/Variants/Regular/Main.js,sha256=r6rr2mhq270XJw71R1YSLHqOhqf5zKYpjxsSKSpyKO4,1322
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/fontdata-extra.js,sha256=8s3KIqhV2wSSjCd9FaVAt5sGHx4kMYG_xrg6uORcfUA,11610
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Latin-Modern/fontdata.js,sha256=DhJ-qZPMD6jRhFDWFwiOW53BDTX5Lq0_pqarZswcir0,14085
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Alphabets/Regular/Main.js,sha256=Nv33CztubVT0XcSRfwCyXxMVGhxrLL9tLNRPdlkBCgA,1120
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Arrows/Regular/Main.js,sha256=m_a_taqB_u_EKrz2ullTdVo3L3HA-huwg_7Wlf7EmCE,1204
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Fraktur/Regular/Main.js,sha256=L3JYOOkR4bvPYrQSmFCOK8ZvzacgvDi0uYM7zbI6JJ8,4014
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Main/Regular/Main.js,sha256=dRcHkIIP-ZmjCm6Lzma0py6zqrEJj5EwmxgVOWblAIw,9042
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Marks/Regular/Main.js,sha256=DQerqGhQ8n1Sdp3KLOF9qxJHWfoNxNzUxxav1uPoQOE,1678
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/NonUnicode/Regular/Main.js,sha256=-MC5cRgQhzv9EvVQZ7QyrwUt8gS2IKWSMI9UWIkXzs8,30551
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Normal/Regular/Main.js,sha256=KOs4OoVE4jpgBvSh1Zy_Ox1Cr_jQwe9RmuZQRYXqchI,4378
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Operators/Regular/Main.js,sha256=GvmB1y8a8FFMI6G79kygOQnaHHzLZPScF4sK5bdhYgE,2127
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Script/Regular/Main.js,sha256=_yxZoKZs2-Xo3W0fQ5ncRJ4-y5DU6O7g5bA0S4nF2go,2575
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Shapes/Regular/Main.js,sha256=sb4M5WXX6FCXBVCRvexUTk3oFaNe3t8fg37GTyIRwHQ,1103
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Size1/Regular/Main.js,sha256=ZpdVYtHL3S566PMGedNk7pMoDOWWqrHvFqMmMyjcDtg,2402
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Size2/Regular/Main.js,sha256=jGLp4R4CTskJhZH2YYIjsz6_P6gQ9r5QaXH1po7A3OI,2133
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Size3/Regular/Main.js,sha256=oKt_pG75MwViZPckFrsq4kl24b_DedOYlAWOkhsRJUQ,1885
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Size4/Regular/Main.js,sha256=WMt_tqEDjnQKjDrpqks3PtQt9TykTJVJ_L7Mgg2AVnM,1891
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Size5/Regular/Main.js,sha256=YOQ0WfbGDNFi_EtIgyxWMAU929WCkqM8Ag-x1sg4M-U,1892
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Symbols/Regular/Main.js,sha256=LJPPuRRiHvMH7fKcnRn0Ee6N_LAVqMP_9v7IsQimnTs,1771
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/Variants/Regular/Main.js,sha256=VScrZXGvnrNwjYXX4tqPjTHV08c32e25FcsCDYiDG8E,1616
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/fontdata-extra.js,sha256=JA7PNpouEPJdi7Dnp8zflLKzp-Jaq0Y6-Erd2HzPGB0,2701
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/Neo-Euler/fontdata.js,sha256=WmZBXlB5NF3STzgXaHikR0U7KTCstxqUd5mSPVvOEHs,9875
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Alphabets/Bold/Main.js,sha256=oHY_B71fEtx8pUv2yghJygnjsFZ3LQAfJM5pVtOziV8,5511
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Alphabets/BoldItalic/Main.js,sha256=77mpkHScEMOUWSfIfrAOAEjUbdwRdGq0cOM4YC-Bqko,5742
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Alphabets/Italic/Main.js,sha256=q65WbDN-DWTYBwCD5nfHfkoxmVq0Z7C0PfCGfxkJ8M0,5645
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Alphabets/Regular/Main.js,sha256=-4gvHZE7UXsBgDkuh4qWYjq-HGV--KKYBixTBcatpdc,5847
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Arrows/Bold/Main.js,sha256=jSVso612k2XwEPxXhGBCY_o_h8RL7CtSnTo-I-4j7g8,1942
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Arrows/Regular/Main.js,sha256=3vQyeQfzb8n1H6CHu-ecnXLeFxgjuqrVl9R5CF8TmKM,6990
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/DoubleStruck/Bold/Main.js,sha256=eC3xb0c3myO35ZnSO6CcGCvieT9aVXz7A2xSw1Fn7Vw,2699
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/DoubleStruck/BoldItalic/Main.js,sha256=bhFPX_-r0xY4p7952ma7n0muSyvqxJn4mBjbz6S6E2Q,1588
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/DoubleStruck/Italic/Main.js,sha256=K6T4AKylLJs5pIOEXDvR3JzEF8zfKJD7V_ysZn_9ypI,1552
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/DoubleStruck/Regular/Main.js,sha256=TpfWTuwtyIMFLiA5KFrmu80eYQR8nfivsFqo4JbJLFA,2946
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Fraktur/Bold/Main.js,sha256=gaQ20q1lEa78K1_lbh9CcYyoLv00u7BPj8b1GgpflHs,2733
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Fraktur/Regular/Main.js,sha256=7YJDDLKJEDEv9iuqp_6cttSoVLWXYnqMKQhP1A4SA64,2578
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/Bold/Main.js,sha256=LMpotyKJJcDnzejvrZps1dX-7485qWnbglolthVlBEU,7172
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/BoldItalic/Main.js,sha256=HAArkvn1u6lhBAnx14tbtOU7JWwHrTtDJtj6qa-qi74,7295
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/Italic/Main.js,sha256=htffk8A21fl3jXZaBnzg2ejv1ujzpe2YRTHhSkF6F_o,7238
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Latin/Regular/Main.js,sha256=eZ5FdfUKAZNZgsUbCON3P2M5B5yA9taJRTVYIoBEedI,7276
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Main/Bold/Main.js,sha256=FjZQKNUfylKommuVzJGFoarp8446A4NVmGy12yRwch8,13110
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Main/BoldItalic/Main.js,sha256=cXWywVrcUNJQXwEwlabFjq_0UHOWQUoQsmCuLOcuXBM,5941
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Main/Italic/Main.js,sha256=nqkvZ7DEnB3ZSRD74Tz3_hWvEMGknhcLI-OImiyLAIE,5891
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Main/Regular/Main.js,sha256=KeOvT0EvpbDsOmnOdcfRNxMp2KetnzxfjYdsgXPsONs,14908
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Marks/Bold/Main.js,sha256=fb_x4i5HgALC2a8tUNilbHn-c-LmEinvVbvb3ejyYBE,5479
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Marks/BoldItalic/Main.js,sha256=gz7n85jAJI47YKffuxFQRHRnpU2xBJNqLNQoxw_MRbA,2110
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Marks/Italic/Main.js,sha256=5EvIoQ4jdMyxS8IQ0C1ZzgflRzZnGphRIwAW1b9dwrA,2546
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Marks/Regular/Main.js,sha256=sZ56L2lqLLDsDOyP6-W9xPrqwAbcL6hg_Oqx30SBdyw,5740
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/Bold/Main.js,sha256=NuNe5__Y-Vha8axRx7mQ8T6dSt1YeZTWpxqUNWsDmXU,5262
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/BoldItalic/Main.js,sha256=hZEQiFTz3U8B11cyY-3q_lDX3GxzaKLe7EQNMqTEczA,4929
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/Italic/Main.js,sha256=zfCl1YK64Gy9CCbLXtca1icob7v0c-YYNC7zWCmlZEU,4878
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Misc/Regular/Main.js,sha256=83MsoXoNK4MrdSqdCxnJ77xB8JV0AHx0xiXX-IPS-SY,7195
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Monospace/Regular/Main.js,sha256=s6q7MTM59UojvSUWfPkF_KdodAYD_0wlzVTLzC1sMnE,2882
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Normal/Bold/Main.js,sha256=D9YUVJzWgN5LdtVvrx3WZwAVM8grm30eMqhdEm2A2PQ,4465
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Normal/BoldItalic/Main.js,sha256=zXGfiK5TyTNYQzbm1wrlFowquBgE2-bOsglgAL0wjn4,4241
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Normal/Italic/Main.js,sha256=dg3cP5VBUpx_LhRVYc0FWXA-nr8LAbJs5tcUsoVFA80,4252
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Operators/Bold/Main.js,sha256=Bj0T5WfXDiYZj_aryJDtW0Zo3gvPTRTfzJqeMSYONZ0,5028
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Operators/Regular/Main.js,sha256=jAV4KelTOqP4f4IeqT4w5dHYnstGijs0lMafD3B_9bo,12031
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/SansSerif/Bold/Main.js,sha256=KHTuYwAuyCg6IEnXYFLwvjDPujPOz_eEv1X0TM9azdQ,4467
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/SansSerif/BoldItalic/Main.js,sha256=hOVXDB_3GMVCz5qfXgH_wn3-UelE0K4xLWCWqqa1gx4,4469
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/SansSerif/Italic/Main.js,sha256=cW6fWVYlEm7zc_QKJd3qQNkttpRg7FN23qDZWVhFjfE,4295
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/SansSerif/Regular/Main.js,sha256=PkFwx_QFiJTj3SON98-3aGl5UwuQ77q9X2SsdYUZDF0,4256
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Script/BoldItalic/Main.js,sha256=k27kVssFCQJkK2SIBJslAi8YEgJSs6gqmwSr8O_GDpM,2949
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Script/Italic/Main.js,sha256=slcE4eni-HFsbbN6b6Sjr12esfUE2bhH7fHFdi0BTeQ,2584
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Script/Regular/Main.js,sha256=gu0s2OFC8uJwiIZ-RYvdsA11QqLuW38xslIsNEk1bDQ,2555
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Shapes/Bold/Main.js,sha256=CaWJ6eTEMvYj1SUl3EMU2-s_cV7fosAciltfn-z0boA,1505
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Shapes/BoldItalic/Main.js,sha256=LKrLOh0N7W39QRqU9E2wiWhfwUCrdETjSb3IN1lJt4s,1155
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Shapes/Regular/Main.js,sha256=Li9hw4Mcrw9wFJ1Rm0CL8S-lHRFmNvNKy4uw4OD63V0,8807
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Size1/Regular/Main.js,sha256=866Y2SzlaEf267162G3PQnIHus5erYDG2NvT1h5nZrQ,4091
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Size2/Regular/Main.js,sha256=ECpadsijkrDXCbFg7QtWMPLVUw2UF7IrC2wyBN3u6I8,2718
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Size3/Regular/Main.js,sha256=3HewrjxUemisWxw3n3Z1eloxOLdKhIT65dfZE74r9rI,2663
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Size4/Regular/Main.js,sha256=SC_tK7nhaskKuq9qMZ_JpRBjXauivhvhxVLdq7KJQ3Y,2583
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Size5/Regular/Main.js,sha256=gWhzWo1AG5EiUZswjTIRUO-bzLegfYy1jU8CLzF9vz0,3669
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Symbols/Bold/Main.js,sha256=pEYhjpj22O7zwIxUPfLgEMOnEtG5Q_ajlVrGj95IvWs,1726
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Symbols/Regular/Main.js,sha256=xXsxxVAHmXFKzHS9AGxP7o3phzuY3B0yuY6XTs6GgbE,7440
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Variants/Bold/Main.js,sha256=6r21ovBdDaa69SpTCKgyU4XOADkU-kLSBQL_xQ7QGHc,3026
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Variants/BoldItalic/Main.js,sha256=Oq9eC0J61xpiL1QmcZRUMRC5uV_O-GjQQtF-3Z528gE,2159
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Variants/Italic/Main.js,sha256=gNSQH1BpY_JPJYW-JtAYjw1-e8v4kKbG-PfbJQEWe3k,2121
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/Variants/Regular/Main.js,sha256=2Le7KiUno-VV2RbRm23YAXaaCmPHE4InDNpTO52Jog0,4612
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/fontdata-extra.js,sha256=iG2luOHN64PSElLLAN9SHnBo4Cfz6qmkuP5by7-_iUA,12607
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX-Web/fontdata.js,sha256=D-LC4OLGNXh-4QDaCGXZrm5j0dD8GgFTEbvexTgYwU0,15799
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/AlphaPresentForms.js,sha256=BQy60MqM5ywaXsuzSspmdT_RMNkaT6DaLP82AoMHkUE,1022
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Arrows.js,sha256=8CRazYh4g8NnLufDz7Nv50LYjkarLjtLsHaZBku91cY,2999
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/BBBold.js,sha256=4GC-vQwwEbA6zIQuTyBA3BWgH7s4g3kCP43EYAmrR24,2070
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/BoldFraktur.js,sha256=vKjwNeZtGagw3I9btiAo8wmbFFGH1Us8VLY3HGh-9Ko,2308
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/BoxDrawing.js,sha256=fHsnf3LV0v7mtOxAT_eCH6pe0HO9Jbr0bXvguColF1o,1977
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/CombDiacritMarks.js,sha256=yiR8NRHqMzrnJ4pUbiXosrFvs8Pv6zmb4jOxz8s0Cx8,2748
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/CombDiactForSymbols.js,sha256=zpbKCZIqZUmlHKPWlX2h9W9DVB3Pz5h4JFUQZ4MunBc,1477
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/ControlPictures.js,sha256=kB1-TTz9d5mF-FTzNipkzLrtKHCRfM2f61cqEZvQoQE,918
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/CurrencySymbols.js,sha256=Z5rqYB7To5Vj7_UFXmGx-6-H2TGrHdAcW0g2Q61IoLM,994
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Cyrillic.js,sha256=pMHnII9WAXv3T76NhMKnmE3yx_Sf56EA3jcaetoV49A,3396
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/EnclosedAlphanum.js,sha256=8-vLFC3CZpo_v309_CIvrPM_OSizkYfVZuZIyXLA72M,2383
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/GeneralPunctuation.js,sha256=8S8U8vpZ6xsYqkii2XZ7NMp4roi0rr9PJJ5H-CO5QK0,1960
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/GeometricShapes.js,sha256=JovjguI0ipbiVDvQ92LoxGni-8SgrvnNWP9Df1GiS9E,1058
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/GreekAndCoptic.js,sha256=JEli0EXmVlKMOlDsIfPCqqk3PVR0SCleNgD0Pb0_8iQ,2865
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/GreekBold.js,sha256=EZx1A7dJh7YuBN1iawV6ro4hc6W7ekrHLN6jO-qABnQ,2433
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/GreekSSBold.js,sha256=0jksE1s8b-UgC_xXSbojkZW84rjmlsfu-0MAZSNcMSk,2439
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/IPAExtensions.js,sha256=8EGDv7xGG0Llot-7-XY_vexqmuYqMS9TcplHRiCe5_k,3038
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Latin1Supplement.js,sha256=7aTEZBI5X39fzzlT8DZqdAVYXOz8YNUyDsXMxUx4RDU,3201
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LatinExtendedA.js,sha256=purDAUGzcuJcT2GgVR9qFVTnoghZ7aNVxYmOZrT66Lo,3946
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LatinExtendedAdditional.js,sha256=qYzqJMmUS9r50F7D-xCnXNj1QEmU3ZmR-915s7Uyr4c,1112
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LatinExtendedB.js,sha256=nh-_r_CcC1ITvor_LkC2T6Y0i-8NNNGdooqJ2m9nsUY,1657
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LatinExtendedD.js,sha256=OoGGpmkmIjJews2GfpbqmHnkxtR0uqdD5E0s6g9yyc0,917
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/LetterlikeSymbols.js,sha256=2pFUAiUPI2LdC5QMQKINx01t2dnciQ-TZ_rcD--lCrU,1846
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/Main.js,sha256=Cr90cxQ10NWJtlDB16cyIWi-s4OnwDHtKV84wFlP0s8,4523
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MathBold.js,sha256=xcJc_hULG85dZdRrDLEX9wsHWCgjvgYeJrQJkhLopHI,2528
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MathOperators.js,sha256=TtBNEilR3c3wZurx337gGYQ3ePXliLLBLsQQQXvHhrI,6976
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MathSSBold.js,sha256=G3p55krr3Mr1981ivLZc9Ou2G2LdFEbZmASIMqEct_Q,2533
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MiscMathSymbolsA.js,sha256=k0fWgoKI9WI0ww17hmJYZTREy94vHr3Ec7v7cA_MoRM,1003
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MiscMathSymbolsB.js,sha256=bHvnZ9WkbYl2Beq3qY0qVDGLQGe1mh_RAgdqj1-PJ6U,1166
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MiscSymbols.js,sha256=fN9-i1aZt-h_L9NgK6xhOZU56PO748Chvs8SbdhRArc,961
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/MiscTechnical.js,sha256=gmPndI9Wrd4OiMHua096McJDrw-reJra9g0uAImcJe8,1285
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/NumberForms.js,sha256=ySJiEPMvxIAGHaaYtI7NGH-U2sw440-68k1PURdlgDU,1185
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/PhoneticExtensions.js,sha256=i1IBrtP61wFK2wkCvPTl1xmn2nMDmeGnTFMpkesJk5o,971
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/SpacingModLetters.js,sha256=N7zbrCTRv1ZOL6joNNoOL-glReWS_LtKDx60yKu4MJQ,2448
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/SuperAndSubscripts.js,sha256=NBekCW5ygM4Xs2da1Cmand1cXOhdsY8aRzgXJiuiS5o,926
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Bold/SuppMathOperators.js,sha256=BJthXD9qVyJGxoad1Na-AEPw6xx7QqG5vNJA2RV8t5c,2452
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/AlphaPresentForms.js,sha256=riCkFIFP-187B09NOzCy4a67SBdlHMm1qNYFK8Qt_ZM,1061
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/BasicLatin.js,sha256=CnemrQ0J1mGuV93ZhBbKHAOyZYjD7PdHj6IB6lUh9Jc,3155
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/BoxDrawing.js,sha256=mF9lMiqj1pAZs9DR3yRsY9EfaJapX6wcY2FfroNqvgc,1996
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/CombDiactForSymbols.js,sha256=cQZVm2X92kUcjReJvNyTG3sKeUsEQF2F1aypAr_hvLQ,946
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/ControlPictures.js,sha256=xAjpS-ZZfX7TvonigNh1xhRUeGtFQItj0goqr9ANQzU,937
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/CurrencySymbols.js,sha256=kP7qamDW5mBcfs-lDa_tfkAFihzWwyKGfKNAGov_O40,1016
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/Cyrillic.js,sha256=-mRs0VLjGvM02U05UzO9zuMCa0lLdMC0l5DDyFVUwPc,3465
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/EnclosedAlphanum.js,sha256=o2y7-XTSZKwXXMLTBAmx8a5rszPboOAoXp_ReA6HEtU,2402
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/GeneralPunctuation.js,sha256=NH6s9BGNSoaeKdUWNGthSyYiS3W2G9Ha4T_faEdmuVI,1537
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/GreekAndCoptic.js,sha256=Rb0XYfR01pIIx7fg0uIqtK537j1wWiiYNtdDPIQCqZU,3135
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/GreekBoldItalic.js,sha256=OnJwQQl6jiSOUu2MywbqtqqJC5Gtbreo-tj0bk7wNqQ,2468
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/GreekSSBoldItalic.js,sha256=WBkki8IJXr47OknhsSsZxcSrS6V2zVmv1A-vZfaFIsY,2480
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/IPAExtensions.js,sha256=GsLku0HT7O5G9AQRnhtvkshD5SV_li2YJIxSqi0SDfc,3073
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/Latin1Supplement.js,sha256=E3Ld2yIbzerDFuPTs4VwY2EWMcmKZd32mJzwofmQDPA,3248
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/LatinExtendedA.js,sha256=sUiUC3FTLtjHV4QJEFx2ZANX-vCb7x2xfN8EVozJEBY,4009
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/LatinExtendedAdditional.js,sha256=OJ11zZOjkqCI1EfdwdhuwDDb5KuwcVTmaQmST0Lr1-A,1129
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/LatinExtendedB.js,sha256=IBJbAkiS6JWXNaXUNBrYix1XyxaDgzMWpHydxZs74Dg,1691
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/LetterlikeSymbols.js,sha256=jBlWRxBFxn6X30kFqus5QSwsZ6PMVz-K0p9k9QkqBwk,1651
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/Main.js,sha256=SgG1WsqtufNUT3U_uIzuX9C2613uujw0CUp72Ft-_7c,1723
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/MathBoldItalic.js,sha256=3zFVVdckRnfoApjLUV2qpxtZrn41P3Lvfh7kT7j_Nno,2303
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/MathBoldScript.js,sha256=8VFudN5xujNrYeGCjsWZ66_TuF0JzCKOjfd3kM0nrjI,2347
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/MathOperators.js,sha256=BMApinBLeSWBDUdyhKS8yz6etB4EdiT42b7r67dg8kY,960
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/MathSSItalicBold.js,sha256=VgFpSkZkx4N8RkofhEsTMfm8dLJyf_PNsypX003S0zc,2311
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/BoldItalic/SpacingModLetters.js,sha256=5hVicQvQIWVJqDdKMKuc--nt0eyi5b8jBoYqcbDe70E,1634
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/AlphaPresentForms.js,sha256=bLuMsHWz0iL9P3ywbiT3grJP8cyTLwOWyjnMsOyf1TE,1048
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/BoxDrawing.js,sha256=_jrsOn5LECNqdtSWrhzUN1cm5VPuNhufVhaUZDSdElc,1983
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/CombDiactForSymbols.js,sha256=AvaoVdyuwMlvvixY0KVoU2E_9MtNnrLr27GzdwIdFGU,1431
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/ControlPictures.js,sha256=HednnBspmCNIijTkxowqibKQFTVaskpD8l37nmmsIvw,924
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/CurrencySymbols.js,sha256=fvoK2RKss1nMD11bXU_ZnLe1vgVIghJwnMpsro65fis,997
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/Cyrillic.js,sha256=DkbOcF6eQ70Y3gTddGjdENzKHFk0-BBQJuioqtTVjn0,3447
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/EnclosedAlphanum.js,sha256=doVabjnuvg6ntIni31eCmbmr5zVCtB0P8ZDCp0mPKTI,2389
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/GeneralPunctuation.js,sha256=CoeUfXhteiIAY8yK42Ck1o_5znX2ITWrV_JPuCSmn0M,1523
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/GreekAndCoptic.js,sha256=T0yPvV5EMbiouNuu_iqcHJm3BTpOTgpcyeVbsvqsqxI,2116
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/GreekItalic.js,sha256=3S6mzhieM8uBOOx8X2VtpvlRSoBVPCuD1vuPE1KAkyQ,2447
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/IPAExtensions.js,sha256=yzWRDzyzsQxlZZYZhZL87JejHR9izETcE2sfJkJEBRs,3051
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/Latin1Supplement.js,sha256=4bU6UO2Hl-a37wgtFyWWZHsgrHCWPDskpYToauuOabQ,3239
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/LatinExtendedA.js,sha256=9BpScpsh-1kbPfBXPzPIjsvRXJGtQio-1Hsgcgs4blE,3949
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/LatinExtendedAdditional.js,sha256=Up6UfzBKp0gG2Zb2SgnOF2Ed1wkSPniFO08RvNcS_Q0,1116
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/LatinExtendedB.js,sha256=AhPHJAuRTmn2A8pr1cWZN5MHm_47VVXk2_qhCRCrAPE,1649
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/LetterlikeSymbols.js,sha256=UVnBaqPIlFbwvQRrRD5YxB9peZZFpBAF0YOCSDI09HU,1629
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/Main.js,sha256=G_KzoQFi2vSBQBA0uwXuAE_cVbnjJRnk3jL1rIvM3WY,4928
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathItalic.js,sha256=_X-HWWdaSvo4UcLRNcHl6AFhKU3HxYfQy9IsPSIpOPM,2253
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathOperators.js,sha256=MSW-gLSzPnZaX0xXNaO_5jIhuR7mhzY1o0SDviNguKM,947
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathSSItalic.js,sha256=xUuFQOhH2dex7a8KKDSvV8yJmbk1CzgBpiiZoOrQcRc,2289
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/MathScript.js,sha256=fnY2UBqrcRI_lx1RemYDunMWowWJoqKtEbEaurjjMfc,2005
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/SpacingModLetters.js,sha256=5LaUSjy9R1I1x6zOr8_a_ZHyeOW-dhxUGgC6rVdpkoI,1626
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Italic/ij.js,sha256=Bdj_At1NjPlBshNn5Bi2OnAUbGnxQYYNACgoeTyuMUA,930
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/AlphaPresentForms.js,sha256=IbwyRXnKvJTeOsEXpBjZLvcSfLoSmlgf5PeP-2aJ19w,1020
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Arrows.js,sha256=DAH03_yVskPsYzw1HxdwhCtyGxlpBuB4spQBKPJ-ZqQ,2916
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/BBBold.js,sha256=oi0_OTE4Evh9mE6OpLCsvhXsVCHlN-jZUPGK2LDnTqA,2334
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/BlockElements.js,sha256=PxitRkYaegaFjh44fq88CV9Xn_CKti8K1knyGPyzRSE,1105
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/BoldFraktur.js,sha256=GwYHw_ckor6cuFs1sSB59gdvfvhy5r2EHpqLU6woJSo,2306
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/BoxDrawing.js,sha256=f_jlxh48-Hb--ccLzTdbc4SBwENzuz84mniOvLzFh2c,2111
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/CJK.js,sha256=kS9RK6w3Ol5oZ33mchWwA6OOtD4ldQpg6amRklctBlE,921
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/CombDiacritMarks.js,sha256=zv5Qfn9oCxr6TH3cPeJkxTsze-ZdHoY7B8YQSLjuN5Q,2430
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/CombDiactForSymbols.js,sha256=1SdOHFyqwjgSoi-yO_1CN0A0DwPslFJMqEiQfh5gVGA,1506
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/ControlPictures.js,sha256=e-FsAWuPJa9K9fy-eHaEB9Z04TZ7k4OOHDZN_ufqWPQ,916
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/CurrencySymbols.js,sha256=djG1JGzHIz5lIy3XGCrhvCmRAD0HxEbmELHJW8SbTpQ,991
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Cyrillic.js,sha256=S74OWmSWlucYw5WtU_0bsD9L-RtKlJXDre31-95bDyU,3370
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Dingbats.js,sha256=PHfGeThrFAR0N29_zBxmj4uUmQ7Ug1amjJK4RE4HAMY,1665
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/EnclosedAlphanum.js,sha256=vpVVDBjSeX2RSt2y3aqvbFGhT4hPcCuC6oHCC2AV_2Y,2381
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Fraktur.js,sha256=04IWWlpBLKggytD8yAU-7DmhSzMC6E7Mz5N_A7aVJMU,2161
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GeneralPunctuation.js,sha256=FOSnMQwIdji5-XIN-MAeJku6XxMrmIAFOD6Wavuryo8,1977
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GeometricShapes.js,sha256=fjiqt8UUXCOmfOtPKCWWQ0wN62HnvHo0vDvEqv_K_38,3276
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekAndCoptic.js,sha256=Mh6xaXaMpgzZbzrWqxnYmInergps_gT9Da-d12DgIT4,2857
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekBold.js,sha256=b1GDt2-h6F0yWvl1ThW1BNLIsO07sTsxHsmX3pmAj8g,2431
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekBoldItalic.js,sha256=m6MnMDTzDp8JHflLwJoOIFO4h7UrG4ngsVRmOCEjFro,2447
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekItalic.js,sha256=fQp8n9xjxAlMYrcZKRaeY4VdCu9dTvPix5yhvvsrtXc,2439
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekSSBold.js,sha256=xAj6eBKZzdqA96astLCQrkFKOmPwhV2o6KJr3Ak3gBA,2437
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/GreekSSBoldItalic.js,sha256=pjkNPJHWomv4EhEbjtAlM_1yLYuir3dngO-t4EmfYGE,2459
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Hiragana.js,sha256=X5PmGT5ouxHrlvsaRDD51LylKV7b2GIcGiRCGa0vwIs,903
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/IPAExtensions.js,sha256=v5t779-teA7Rkaa6JuETSeQmyzsp-ZvvR2mlUo1WTUU,3084
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Latin1Supplement.js,sha256=2sexTFidSzLPBZNgYmrC0RlCjucT0FM19bM9iqEV5oU,3001
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LatinExtendedA.js,sha256=4Z-gbE94IDQ7ERFBtfStqL307soRgPgEiQvqS_BuqYs,3906
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LatinExtendedAdditional.js,sha256=lRbvzXv73ZZO2eUqjBzzLSeTmssWv31TDHuGG8tF_nQ,1104
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LatinExtendedB.js,sha256=VtqkbiZ941ubypbopUhXRYe7qPLIMsnOqXDr8Fsz-3w,1748
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LatinExtendedD.js,sha256=SCEfXi5yWtg7R8mq7ZDlvd6F-ImEsBBX89qzhq0Ph84,941
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/LetterlikeSymbols.js,sha256=uKQ0OVa3Azlb6KVrouE9zFJ6GYQUBe_n9NLgt7gFBdg,2291
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Main.js,sha256=WC_m-PCqsjeQ5wtgCgq3HAIsxjcWnGfccWtIfyi9e4A,9954
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathBold.js,sha256=vDdvj9eejkmdv1bPhjYAzve4z-Yhl-BYIOeRshn-ohI,2526
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathBoldItalic.js,sha256=X5eHNl3yJ3SrAGRDphyOMQD2xWXOWaZXNrgfjrsNM_M,2282
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathBoldScript.js,sha256=Vp5MBceAes4JuO-NAFBp4IkGmSYRubBwAVZ4RaZS4EA,2326
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathItalic.js,sha256=-FoYPVykA1egczk-ubOSIL31b4Ap7cFAem7AZPrCisw,2245
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathOperators.js,sha256=ET3i37N8xqAVLEq_LEpyicshBWXNGCh9TwoaUmkX34c,5509
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathSS.js,sha256=YT6FU1VKppRZduK9cbs8x9UOWZvkGN_NI_avKjj1-wA,2524
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathSSBold.js,sha256=Napf3gRAGD-VIrjcGwwIzrD9pyEXX-EP1WaOSiITa2w,2558
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathSSItalic.js,sha256=aeIGnN4JpIOMe8UTUke9ADrvtX3A_Uy4TjkyxJ_RM6A,2281
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathSSItalicBold.js,sha256=ZSP-PcwHWbwpfIzri8OeQb9grmZy1p1tBFcpqfC86do,2290
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathScript.js,sha256=OJ5BULWKJoIVlWf2dH7PAOHr6D4TRmZ9m8jaYsB6y3I,1997
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MathTT.js,sha256=B1ReL55352dysvWXmebkL9UotDSSzvV4eEHo_gdb4_0,2483
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscMathSymbolsA.js,sha256=qIVbQugT0F6tbv_xyWJhXp4ReqD-TYTlUiM8AUfnOQA,1997
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscMathSymbolsB.js,sha256=rIJoFU2YI4vcPfPDs1yZztpwxwAuzST6Rsl5pR7Unf0,4306
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscSymbols.js,sha256=9ohwIhlAJ1tZE2d-YkUb-tWfUTiTEY0bPH8PHwBBpN8,2137
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscSymbolsAndArrows.js,sha256=oEI2ow6pMIuSeJHYm6-_Zwd2Ve9GHfsgQD7K709rYqw,2637
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/MiscTechnical.js,sha256=4SWT93KrF2gZM-HYA3Iw_RweYiGw5XCTwrrmwIoaJho,2277
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/NumberForms.js,sha256=zLbySFu89GUPzmpJ0epZokou5MAAUuXhzjyFI6jCv_c,1183
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/PhoneticExtensions.js,sha256=_ohQNfCjD4SBDAahjAwVupJ0qn0fQZz_JUTupHdcpBw,1179
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SpacingModLetters.js,sha256=LaTfm1GEAlwOoPAy4a7l8zF747X2vbFurqzMNoi9sYM,2237
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/Specials.js,sha256=jNTtRT6uDNf573BbONkmp0jfXw8_vb5mTZTg7P17ZW8,904
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SuperAndSubscripts.js,sha256=0n48ZLjt_t2xyTA93Onesi4gv8WKNKKiWidjJercIHY,924
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SuppMathOperators.js,sha256=XYoqzBnBvgcgEJpwon5NBmYwzpOrrRLUPryVIDvF6Zo,7497
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SupplementalArrowsA.js,sha256=xr8sELr1nM5OXeDsaTBO3-7GDOF96cYT42S5Udnaan8,1122
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/SupplementalArrowsB.js,sha256=-XLqvqrcOpV8SUgn5-OsxyTctu3wPRwvvLiYVT5X1Sw,4336
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/General/Regular/ij.js,sha256=YR0pTEiavSIxSIE3R23S5u6KuhWyBaT1bxdxMdjmNcU,922
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsD/Bold/All.js,sha256=koXi8M1cUsLf-bwhDEh377jw7ywd3RUw6zq71BN9E1Y,1663
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsD/Regular/All.js,sha256=Bhs-L4KFMKF56thhl2UFUwJaTXBg3eoGwuwdO2ot-WQ,1632
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsD/Regular/Main.js,sha256=nIIi8qvoUpxwA7fpEmQDwrmzgEBXtL8W2tQuNWzdfQs,1103
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsSm/Bold/All.js,sha256=09emS35IyIE2QiQ-7tXEN9uenAF1Z9rweIoK6qJyC4Y,1614
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsSm/Regular/All.js,sha256=4v_Be97pRgQsXAbOewt8J7HQ9M0wWQIlv0uSfmdXsR4,1587
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsSm/Regular/Main.js,sha256=s5hg2OlM4GF3kAKNPpaycD3TtMvIf5s4ySCnEME8ynI,1105
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUp/Bold/All.js,sha256=FN0U6HfNJyHY7XkO_C2xmU44hb3uluzxFpzXrZMl2nY,1614
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUp/Regular/All.js,sha256=_gq-UyoR7-gMBJ-qB44YWIybNEHQ5AYSkdNlNaxJYDc,1584
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUp/Regular/Main.js,sha256=H4jKpgSEw7DmLhT4R6YxT9SvtqZ91bl5n9c8grZuUwc,1105
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpD/Bold/All.js,sha256=E1aRdK83E1GFLYwT6ZVq_ue8XnEpDmOfajNZsgG1i_Q,1647
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpD/Regular/All.js,sha256=EBlRyPDXdYcKj5kYR-Hc1Fmxb3V5LvwqE1VBZTU6CEM,1616
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpD/Regular/Main.js,sha256=qDqBxL8-dDUIYEhWpwX4f5jUWPwseBdK-eAuu10CJTo,1113
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpSm/Bold/All.js,sha256=yWMru57IckiSMiUMQvGYVPpi1d2t7TEgYS67s_dypQI,1616
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpSm/Regular/All.js,sha256=fz5l0SQiVGr7JqTA2dNg6BPAAIW61tmFlrKHCijO-ak,1590
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/IntegralsUpSm/Regular/Main.js,sha256=WTfoLBVtHjEsKaIEHMsg1laSdyC7-AjeHvn9Zf8R33c,1117
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Bold/All.js,sha256=d4ZT-gwBEvKNaHbVw4kX0DxRYS0jJ19wIed2AKRdOzc,913
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Bold/Main.js,sha256=JcUlJpk1fqHcEn8Cd5dTDMrTRSfR7YQONF2NL-Pca1Q,1054
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Bold/PrivateUse.js,sha256=DeRGaslSPpzFjiaPdIemz5ricWj2PlfZwE9LGhnLK88,3899
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/BoldItalic/All.js,sha256=p4bW5-qG3dLHlA5Vb9AGXz6DEjvK_B1MjnP1BSFPPW4,932
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/BoldItalic/Main.js,sha256=0vdrbqEGI5k2w7Eqxk9RJxSn-wqVxdfbfbpeUkJWA7c,1101
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/BoldItalic/PrivateUse.js,sha256=TY-FYYNcsvcY-ERbsSy6STtgmH7wDLxSbAVzhKrOII8,5884
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Italic/All.js,sha256=32m6yGVbZjhUkSwfqdcdm_11vIn43HErSNQgg2PzHjc,919
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Italic/Main.js,sha256=wwMBhaUZY5_O2ZPrzCHaWJKSQ_1dMCeY772UmwGQ1VQ,1738
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Italic/PrivateUse.js,sha256=W1c4K8-50QUmvvsqso2A5wnUaK8ATYNdDoQ8jXH-4Lk,6558
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Regular/All.js,sha256=4tXz-BWvEPiWDAYNpInyYuegZiChDFIQEV9fzmXHX3g,911
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Regular/Main.js,sha256=AUhGSESsKKPDowpnXveybuBGmej1MzfRrqx-660018E,1698
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/NonUnicode/Regular/PrivateUse.js,sha256=cshryErApaC-aMumK3LKF4RrtUGY2XMTQEYB42PttJE,10385
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeFiveSym/Regular/All.js,sha256=Zvbh4LA0tA5VmkMuO73IVTp8CebTUxu2M8FerW9vD1o,1517
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeFiveSym/Regular/Main.js,sha256=Qb5sxCnUND08pk3cZrLosMbH5gW9QHGW0zwuzp4HY7E,1260
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeFourSym/Bold/Main.js,sha256=unh22H9AbwWtxJ2kxFcyBF_VEIfeWPx-Y3uRqx1X6CE,1432
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeFourSym/Regular/All.js,sha256=THHZuPCZjLk40ZEIwXr49PyN3zQeDGDoxbvp0HNYHS0,1893
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeFourSym/Regular/Main.js,sha256=XT0v1UZcmsoEoyUJrRkpTZvfuYLJ5_HbXIzgwjkJ1Bs,1747
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeOneSym/Bold/All.js,sha256=ipDCeV8dt_irRbsPnKjNGXa82DKVt6zBNrAfaqrLC5c,907
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeOneSym/Bold/Main.js,sha256=MzIfOSVur-MYvc0f7RHWutNIL61HtBNt0JHkzyw2wA8,1535
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeOneSym/Regular/All.js,sha256=_pDwc_izlbCz-RcPhM3JoM3E5kKOP2HE5g9cnyI8QAo,2254
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeOneSym/Regular/Main.js,sha256=RYVkhM_lFiifgFmjl7P7sG325Tf4yA2bDhpl_M2BJGY,2872
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeThreeSym/Bold/Main.js,sha256=XR-cENwoArb98nC0XWUZuGaDIM4CS7mu-jmG8W0_ZCc,1442
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeThreeSym/Regular/All.js,sha256=Cv0IbkGskpiuqWKObWzB5NaIqt-WGnPbyTPHZGY6iLM,1892
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeThreeSym/Regular/Main.js,sha256=OsliLQCstTwnwhLhNa91H1OnOsc30eiYPjXnEnqykr4,1750
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeTwoSym/Bold/Main.js,sha256=ZXMhtrB0att-21clBbECKRjKkIsLl0WXzWwmYv5p3BA,1422
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeTwoSym/Regular/All.js,sha256=naHwpu2ibjR0bf4pN2I4KYo9UjtSMkaKGcXuX6ByVhs,1925
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/SizeTwoSym/Regular/Main.js,sha256=LK3Ex5QqRrrswry4nLV5h30wBGSbnF7qj33EWJdQa0k,1755
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/Variants/Bold/All.js,sha256=tMuLhnaea38oq5XIHjyVpd95hIeBXJUBkDM5d4ZDX3s,1696
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/Variants/Bold/Main.js,sha256=Na98QBE2unx7xk_vQt0Ua2H6fI1qVC-WgmhrW1_EOYg,1241
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/Variants/Regular/All.js,sha256=A83EXUwFKWdSuhcFkD8suER3KQOzh75lSaA7IyZDIqM,3250
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/Variants/Regular/Main.js,sha256=PubRrpitzIU7jXjTs12NBTYhKs7WX3xifHmT994DaxQ,1336
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/fontdata-1.0.js,sha256=_gRg2S8HsRvQpw_nwTmHou7tuxl9S8V8a6Leo0LXDrw,5155
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/fontdata-beta.js,sha256=aZdJXoJLvabqHTKzy9-WkvTPzpRvx4-1GXcXROplPjo,8394
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/fontdata-extra.js,sha256=bI6p8e5I5ge0fpv_8Km4epzSw-vVOWR34P_aY8VqDPo,7710
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/STIX/fontdata.js,sha256=hcMvCNDQxahlwK-y45zYaHCI1Yi-GZozYOpkhcop3Zg,38364
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/Arrows.js,sha256=VaCdPAsbWJOceonP7mMNLxtW-3ICw98vLhjVlpuXNyQ,1862
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/BBBold.js,sha256=CIwHJlcf8TC4h_bRj-cPpifk1qX22Naqk8-9FfFV1so,1485
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/BoxDrawing.js,sha256=TEepABngEym2NWBcazkKW1QFLJqbTPGzTx8ONqikxNs,1026
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/CombDiacritMarks.js,sha256=1KhJ1BPlpeKwC5mEa6t5T4ir_S6v2Ievpasvt7ELa7Q,936
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/Dingbats.js,sha256=cbh3yPKo2jN1YElipKXT9pK9cX00zIKcjnXDiwD52sg,920
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/EnclosedAlphanum.js,sha256=MxJ4SbbEz7ZJvJODvZWm6WzQptRUM7XgwUyQypjLMU4,909
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/GeneralPunctuation.js,sha256=yLi3Sl-xVNtZ1s31KdTIPUVua8jOhOptXpj25qZNP7c,914
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/GeometricShapes.js,sha256=eLQuqQ5hG0qD5aJaaj-ogjdbwMTi81x_Ty0he7uVgnk,1106
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/GreekAndCoptic.js,sha256=-pXTuESxHNOgDY206VCxS0w2Kw8kHicyC9MEXjCrY5c,928
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/Latin1Supplement.js,sha256=z6OWzNOpyVdppvIK_DHjcT9WPag0cg2zjwmdPlQWdH8,974
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/LatinExtendedA.js,sha256=IQZB1bw1eFKP-TVvhEzgJeoy7Jkp62cCA7ty5awvUDI,904
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/LetterlikeSymbols.js,sha256=8i8OjzCede2HUDIBjJJYlp4Nc9ZyY45eZNk1JZ4g-FI,1062
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/Main.js,sha256=PTmcn_RkMmlpRwFRh3EexVZ3I28dxaUWkvG-vYHBweI,1535
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/MathOperators.js,sha256=qrzPmjVnR3UuqNQNktpD64x0alUroKgGTZCG8rDGN-I,3714
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/MiscMathSymbolsB.js,sha256=xeTANJ4dkkTV4bLcieZKMMB7JevWxgeYibsW2cyZLXU,911
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/MiscSymbols.js,sha256=NxahqpYSnntHgflC0UqYatPeW1IKq-HRlMQQqii4bIY,900
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/MiscTechnical.js,sha256=5dfS62RJmZ0uUv3ZMpow8_h3AIFWFoE_LaIgbfZvDUE,932
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/PUA.js,sha256=NbXrv9_QVAssPM_bvCJjWOBZW6ia8HZRzG7MiemQJxM,1287
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/SpacingModLetters.js,sha256=Hv-iZwJqa3i_XIR8gI0MnWiNeiBMacG7DMGhY7fre0A,942
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/AMS/Regular/SuppMathOperators.js,sha256=nkTHVmp1Y5bZ3VQo1VK17sMeeVttekjjSsIxQ8Eo3O8,1508
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Caligraphic/Bold/Main.js,sha256=i-srckrv1afuKSD73rarQ351qt-j5czJiozx1alGwE8,2189
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Caligraphic/Regular/Main.js,sha256=HYJzwSpJ5rGGc_xG7doWYCeRhozsAeG1aVj6f3FGBz0,2175
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Bold/BasicLatin.js,sha256=FEXuvuq_35Sr5PfM6VRUQY9atXPFcM52wR3f4Dsr3i4,2834
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Bold/Main.js,sha256=bUCV30G54dgMlaa2_H7d0_1uY0Q5qQuGK_Bw2NcFClE,1110
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Bold/Other.js,sha256=CVNKqJjDN2zOYHhSpxQduri--HT7M7rLT999ZXMYCRE,948
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Bold/PUA.js,sha256=JSephgkMsOnlVhlE-up8wrNnYTXd1_U-vKU6rzVCrFs,1059
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Regular/BasicLatin.js,sha256=gL7MPSt6aKIF5cfSP1sIQ8-ly17yij7IqPihQI3x6RM,2829
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Regular/Main.js,sha256=9qSrJzaWMHatlhSn5JOK0OFlYqF0IAsvwpr2vMjezSo,1092
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Regular/Other.js,sha256=6quxNMugicFdznCabD6IC-RwMw7YG731fXALymLrtyU,946
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Fraktur/Regular/PUA.js,sha256=ZUvLMezqF6C-_w4c74fB0YOekFyBwciXzhwsYZd9BhI,1081
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Greek/Bold/Main.js,sha256=ZyJ4PTz79vsebl5A3OyTllB_sZB3yvHrYIpuSw3xgOk,1322
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Greek/BoldItalic/Main.js,sha256=Bj2ge96V2fVqWaDTjDhwkSAh0Pn_xUcSjONNY8fgdcc,2469
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Greek/Italic/Main.js,sha256=uafSFZINAfD85ywh-vfzlq3RRYkHStF2-S8umk4iyC4,2447
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Greek/Regular/Main.js,sha256=7ULqt7OHUvNlCc3NePqcxCeZ_BDx_2Mw-n2wfsoRXEY,1304
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/Arrows.js,sha256=d3q8I8vNXASG9r8HJyaJo3AeScKzXy2OAU-9uBcbGr0,1519
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/CombDiacritMarks.js,sha256=K0sv3LXsYqfQuA0mCkZrHxfX_PpkzglfEUCASUO92Co,1208
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/CombDiactForSymbols.js,sha256=tokKquxfGncGnH0J8Jy5JRvPierHXADHfH6KVs1G9_k,922
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/GeneralPunctuation.js,sha256=rP5mLVfPjC27FizV2tFSV5MwkUCBi0WIrTlpae1YptI,1293
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/GeometricShapes.js,sha256=DPa50HvZPGF_0Mr52pAtKNTKhsOlm0--TmEMR8n1gqI,1018
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/Latin1Supplement.js,sha256=JKe8i1l1Mg6E6SQ3Mg3IQ2H3HhuM9baVMS4Q3faOQuM,1111
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/LatinExtendedA.js,sha256=n7UFYWG7wub47mq6NHaj344I9Srfl_fthuCwYbmukec,908
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/LatinExtendedB.js,sha256=qorXdnPtavTMm8aOKEMvThCkGtwUOZ3fHnIh9JYsclQ,911
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/LetterlikeSymbols.js,sha256=IEwcCiutuP8toMEBZnfgQxr6CFa1MpiL_Cdvx1xsT5U,1039
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/Main.js,sha256=LTYw_3soRerapQJhP29LSYJt2AA4Fr51c1xsdXk682U,4046
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MathOperators.js,sha256=nwZITMJ58R9-PJU6hFP34ZWnZHPBPgSQbuUAmV4vsIA,2606
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MiscMathSymbolsA.js,sha256=0VxIqCYdB_QZopZ_o5JF11szffwYGcSBUMESKkAkg6s,944
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MiscSymbols.js,sha256=TTTWzjcs-LbRSlSw4sCGdQqTU_b4OzFPRV8ep7S0irI,1059
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/MiscTechnical.js,sha256=7gnnPPZWAxLIDjpPgtWgl2dS-l7duBxmDQsEYUP5cGY,1047
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/SpacingModLetters.js,sha256=V60QPCYs90HaK4s8jniB3LBdmq4vFYkA-0PisVSr_qM,1132
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/SuppMathOperators.js,sha256=PdivBX750kPiKupWZpEgKBp33CpPuyCAkUE0lR6yy38,970
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Bold/SupplementalArrowsA.js,sha256=NrwRtcgp24Z6VF5ixrlxwL3OAHQOEwEKswi7k_jRY6U,1091
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Italic/CombDiacritMarks.js,sha256=zV3cW6qjXDSqVjLDbtEac_Dib69dmcnuBP19eG5suGM,1171
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Italic/GeneralPunctuation.js,sha256=yUPqo3WTJ8E9gIRaGlMe84K4Dm-3Uz9eC9Yb4NFr71E,1068
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Italic/Latin1Supplement.js,sha256=G8NxgA1GsNfeZ5DVhSvCNNHdo_BOrN_lVpx1SW6WSIA,913
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Italic/LetterlikeSymbols.js,sha256=Y8qr81mpEZj46JTQXrfkj4s9g0EeH9C_A9Q_VV7TKoM,922
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Italic/Main.js,sha256=hrxpY74ioGox1uecKPPEFagOuR_T_RCVyUY1Dv7gjcQ,3582
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/CombDiacritMarks.js,sha256=wEekK1Rv7ZIOXoBayt49rK9tOWZ10X3AIYd44wSXoJI,1205
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/GeometricShapes.js,sha256=HaaKys83tudimAXhn8daFSsWSVjdkYluiYB5gT7Epdw,1010
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/Main.js,sha256=cGi_rn3NvoqHy5oF_pNYyOgeATUeaTJSaTgpMUn9g_M,7677
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/MiscSymbols.js,sha256=ZxD2_9c75sRQ5xkuD0hpWZpTeGebEbmi6DyCr2ui9n0,1057
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Main/Regular/SpacingModLetters.js,sha256=odXCr4OWq4NipcwHkt8ausvdGst8YyBne0Wa3TvXcho,916
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Math/BoldItalic/Main.js,sha256=qpWMjvy7fj100Jgs-2Lo4ZgxboZblkW7nTEtXPtR9zk,4120
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Math/Italic/Main.js,sha256=GeGscHtnq-qX7kWFtaDgQP_mw2_0ukuOXfmqG1B6i94,4114
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Bold/BasicLatin.js,sha256=dvDvZ8fv6TblJQ59Wx0yo5ovJ-uKNauIr1mDEoorP5s,2934
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Bold/CombDiacritMarks.js,sha256=JsmaQEVmx6vRLH-9ApFIDsgAoMTY5J2s8HFpGHVuHK0,1195
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Bold/Main.js,sha256=ERAk1S-UwDS7f2TFKmqb1W4nmcwESVVK5Dw1uGaBFQw,1137
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Bold/Other.js,sha256=B3nfFEKEldkxcxIFxUrlB6qSAGb71nBN4g_di2awfY8,1366
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Italic/BasicLatin.js,sha256=tCLWD14xbuopFo3P1Bk9zJ6BaG-8RN9RqFsxN9rLcLs,2951
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Italic/CombDiacritMarks.js,sha256=HszGBbJwplWIwSb5WYzARLXRONMJkaK2IYBJNpTxn6E,1187
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Italic/Main.js,sha256=rl-QGYoFhJ4PPcqBuwbYGuBi0-XjFTUaEO0fvGo9B9Q,1148
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Italic/Other.js,sha256=_6Ox6VIoZjLPQM3Zr_jrQyebFhSE0RA2MsQkl18ejPM,1381
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Regular/BasicLatin.js,sha256=iI2qoW1iuIoZsal2QdUV4O9IYsM_yj7R8u2QVZZTE-o,2920
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Regular/CombDiacritMarks.js,sha256=256Vi-ypMKTeVpTAdXUPOe2zme3_EA-WLdHiK0jGyjA,1191
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Regular/Main.js,sha256=Alje5lK1DffUMMLf8IsdntpoZDq4W8bMjX6pmt2A44g,1119
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/SansSerif/Regular/Other.js,sha256=Wa7RFbilH2ZGk6BBuc6--9f5KEkk-cGthHpP1I5RWn0,1363
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Script/Regular/BasicLatin.js,sha256=vmsdEbnJo89Lx3dQZWVj4rXu-mOaSSwNRYRsE8J6LWY,1510
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Script/Regular/Main.js,sha256=U2OeLknh-dGrsKJScteBT8-4YirI_n2JOuoH-67gg-g,1303
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Script/Regular/Other.js,sha256=-LUps4hmI3IuzbMk8GHDztlhbFTtkxdFZU8pUVne4Eo,889
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Size1/Regular/Main.js,sha256=OM4AKBvGPRc4U7Ik04ASQHVQ-Umf-d6zXZzx7cCbYgg,2156
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Size2/Regular/Main.js,sha256=G4vjRC5zL741SMQyg7zPpL1k_lCHwUH6j5XKp1fS0Bo,1999
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Size3/Regular/Main.js,sha256=s9iH1L743FSktjk-3qKNFQ4qYFsx68fssJuvjbDuSAk,1549
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Size4/Regular/Main.js,sha256=Zgjsbv9gWY2suHiYa_yLUnAz1LigokHEVbyvO82BcNo,2291
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Typewriter/Regular/BasicLatin.js,sha256=zo_prYzs07SQgUw23JKNJD2MZjUu-CmOdTUiMwwP1vs,3140
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Typewriter/Regular/CombDiacritMarks.js,sha256=9cjh73ti7Noh0Lrfx4te99-atRvKCrivKpNz9L6s6fo,1143
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Typewriter/Regular/Main.js,sha256=jIdX8x9NxE_7nWTYIw1fmbLwAjg6v6Eh_xivpBWTdD4,1124
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/Typewriter/Regular/Other.js,sha256=oF1ylevlTy8Gw_qEPSn2qALKT7OtJeELLiWCkX8bNdk,1296
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/WinChrome/Regular/Main.js,sha256=D3kNGIT8-OVXdZ13-bRbruWseO2Cg-k90FEk1KD7LmQ,1307
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/WinIE6/Regular/AMS.js,sha256=dLCkO1-CkdcE6gdRN5kmOQisIsZgE-iYoPVCTJQ7wyU,1636
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/WinIE6/Regular/Bold.js,sha256=Nd06I7BnsHLQEBlVVz5DkiiF0GSghUf2cRgOdOwhKOQ,2355
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/WinIE6/Regular/Main.js,sha256=QcLMGb_PHnVRr_3FzGbzLj995V5uFFKgyDO42MHde_Q,3072
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/fontdata-extra.js,sha256=LvGOTCAOepR63kOY-H7oqYQXxzlrSeQKVjbrTwPZi2k,4198
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/fonts/TeX/fontdata.js,sha256=PqgySakbbyGvrJ9hOofrydhXV_bJwgb-aXoARDXmPOU,44346
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/imageFonts.js,sha256=Tf4xEneU4dCHY0yI_AsHbrN1Iso7YVIwyQTOfLekUe4,4637
spyder/plugins/help/utils/js/mathjax/jax/output/HTML-CSS/jax.js,sha256=8yRd5SfwXntIJ3RwpOXBy6dGIL3ME7nsKQozIfQKTI4,82571
spyder/plugins/help/utils/js/mathjax/jax/output/NativeMML/config.js,sha256=kmz9kgrw46PgXfz-lnEhbxL8XY-paole9MTRFg_Bzyk,1140
spyder/plugins/help/utils/js/mathjax/jax/output/NativeMML/jax.js,sha256=2V7-mQAOSx2kP-avq4zeIMLBD3ESeweJECiV4wNQVzE,27559
spyder/plugins/help/utils/js/mathjax/jax/output/PlainSource/config.js,sha256=0WNAavaA_cFTmwLLF4Ev0WSB04KgA-MSOvxHW5-ycR0,1218
spyder/plugins/help/utils/js/mathjax/jax/output/PlainSource/jax.js,sha256=VrGgDOCudcJYm2LiTBIC-UXLESZV0hOz4uV53c_RM_A,3796
spyder/plugins/help/utils/js/mathjax/jax/output/PreviewHTML/config.js,sha256=mpRLmrO7eTnDvGx2LBfe8M1XW9eJwibMXzcFWo_csVc,1149
spyder/plugins/help/utils/js/mathjax/jax/output/PreviewHTML/jax.js,sha256=Vh-NYNgc575bpQgubk-VoTbBKBtn8wnVnGS7ZB1GrvE,25533
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/annotation-xml.js,sha256=m4mQrwlqtgklPyMjeZgGibYNPFWWqru3W7e5DDZFWYo,1986
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/maction.js,sha256=6hWeApmKV4BKqvbJsyAOJYw9P7l441onuRsN7r7X2V0,4229
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/menclose.js,sha256=Nx6KPPjt4agPIxz_hh7tlc4IGtPvJ7cg1c3QJ4p5ygg,5193
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/mglyph.js,sha256=-G6eDIMFYcX8SV3HRrCzk7oTBPYXVVUZ0Ps-0t3tU5U,2697
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/mmultiscripts.js,sha256=5WL8z9ggmTkR7Im3OhGa7MMpekxLKlpP4dPL--JTuQ0,3508
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/ms.js,sha256=a3l4ZDOiIhV6TR_zngF4BIRai8KkuMq1yEo2Kefc0-E,1495
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/mtable.js,sha256=qvusgOmFXlifHl2UK_KrtFLQQnma84O9W8ilhPxx5K4,8068
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/autoload/multiline.js,sha256=ZMQNYzyeNjvRoaM_cBJpSaoEyN7NVBe7oK4fZR641T8,12155
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/config.js,sha256=1oBRit03wNG_UN6jyTpM0qpwPV4dPidIoOAWwmruJTw,1980
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Alphabets/Regular/Main.js,sha256=7HrPRYFQZ-OZtvyfO6BTFjaxw1dtHRy0IbQ3apvU2dg,54334
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Arrows/Regular/Main.js,sha256=U8nDdB_hrU9oBbRmM8q818ASm_XJnJkuoW2kT7FucBQ,32596
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/DoubleStruck/Regular/Main.js,sha256=2me0DCzT4u41_DVtcBuMPhMHvHLFpoL6oZdXRWnY1c4,32855
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Fraktur/Regular/Main.js,sha256=8r_il0HMzy2WFpDoaiLsvuX0_s9EoG1MUq9_YkLfAq0,47123
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Latin/Regular/Main.js,sha256=SazOPqlgQG_4n3FL7wSOEXLggMGzk2zCiKaULriraNE,33724
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Main/Regular/Main.js,sha256=6U8kRkLU097bb4pIuPu1fiFglNeD_YyXhbgdxUIi8J4,119316
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Marks/Regular/Main.js,sha256=plLcTiYzq9W_LllMduw2OQizDWZinpxf8ePEpz5QtNI,11859
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Misc/Regular/Main.js,sha256=vzE7EI4cOqpyxq56rbJn_bZ8OPCsMnLODuG5-X9IXMs,33064
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Monospace/Regular/Main.js,sha256=96cFb97pyikDjUnNU_7Jru-JvS6Wi_7sMp2M3YROnN8,22919
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/NonUnicode/Regular/Main.js,sha256=Jl8XJxLGImkuWkcGk8lfroQ9X11kzIG2H1sALC6V1u4,101705
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Normal/Regular/Main.js,sha256=QMbt-PQzHkv-ViUzUxpaXSK7HkntWszLtePhokuOEpw,122179
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Operators/Regular/Main.js,sha256=hlcjjWRUyR-qRclDugnkMLI1f1Xtj3Ier-SB8UyVjk8,79907
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/SansSerif/Regular/Main.js,sha256=WBD7AepIL1J6YryqBwF9ZnO0ZuqOpRMBjfY7K4Vz6Oo,86849
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Script/Regular/Main.js,sha256=oadzkadDJrsXXvdEqB8jCurT7jM_ySEkB5Y3y-opWP0,57627
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Shapes/Regular/Main.js,sha256=K1YW2MY0XpmqzePK8Dk5dfw_F2nhqoZoBYnERFv7UDk,18658
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Size1/Regular/Main.js,sha256=Pzcx8dJVwdLsOfYFZUS4AVnPVIE58IadBbv5UUEomas,29030
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Size2/Regular/Main.js,sha256=gK8ZNEYRmjsBcegCppB-CC7xqD22eN7uspiBgo3Lo4w,29994
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Size3/Regular/Main.js,sha256=32jUZNQjdhWfdfsnNDcWXh4ojGIRG70msptAEeijEaY,27971
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Size4/Regular/Main.js,sha256=w_NfFIF4tKeYSO66YliOL--i3Xw4rMP0wk9HeIzTVyQ,3610
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Size5/Regular/Main.js,sha256=0S34YbrhbWoT2TkShhezQ4gShx2pVMISsbOmn6MDlJg,1899
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Size6/Regular/Main.js,sha256=z_-rn219HbarS_Y-bOKLHjzi_0t8Ii8J69clnS6fyFM,5746
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Symbols/Regular/Main.js,sha256=mYss1cPPUthm6ERc_gIjfHJVlvW4MhJkBm0M3ApB2Qc,40821
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/Variants/Regular/Main.js,sha256=nk7rLESWmfo5DgHzVVXugNUl5j8m8P5DbB69AfLiJ4s,28689
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/fontdata-extra.js,sha256=6ncKVCfDIns4ngzIWE95rO-ZXlKySn4ZxHyxF0mQJZg,5872
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Asana-Math/fontdata.js,sha256=0CE4PK09R3Qfe7QVj6vtykI3XU77nZeNxs6u9rx6WtU,11507
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Alphabets/Regular/Main.js,sha256=WkkQjEhLA1eQHZlKJO5QpkbRE0HjFs_UkfL017N-96k,7679
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Arrows/Regular/Main.js,sha256=QOvCTzEl__sYw8OSU-bQGGTfvckS6DGEKNbxaBFECRE,11881
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/DoubleStruck/Regular/Main.js,sha256=11O7WkPUcDdZ45zERdDx0Bo0wntSilNwCH9RUmB-qmk,33772
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Fraktur/Regular/Main.js,sha256=X5S0IJK8wpXkvQgD4EJC0Wlv8yBRP_Q-h6-UaLtog2Y,72871
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Latin/Regular/Main.js,sha256=x19avf7IGjCq3Kwmfwu7l_ezFe0ArQ1QEdHtT77ywbA,122158
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Main/Regular/Main.js,sha256=nk5C95uWCXG2JMY9_oi93JxbKI2eiC4qzfmanEq6AgE,157211
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Marks/Regular/Main.js,sha256=ue45g3YcvglOQZihdoxcBTOA-2VzFvGhWqp9sLXmyUk,15053
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Misc/Regular/Main.js,sha256=HmxhO6Kk6Orcneityxuxsr3OTDOVkPyQTr8YEDxf4BY,4887
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Monospace/Regular/Main.js,sha256=xLqaplkP-oooei6_BOEnxyDJQ12tySSKCezvIwStg38,21559
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/NonUnicode/Regular/Main.js,sha256=rImuQOK1g1MTCArhBNtzKqvZ9qhzUZpbl5CgSkMdh7Q,625134
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Normal/Regular/Main.js,sha256=NXEcjhVUUSZFifZwnctT4MQa0wFbRqd3yVDzrlYCuXM,142275
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Operators/Regular/Main.js,sha256=nYDhYgQAMD72C5kzbCQpKhM_BUdjqcCpQXGADK0wGts,37691
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/SansSerif/Regular/Main.js,sha256=JKtv3qIw2dT2RybrF3VdbHfrpYqMRMwmjkLMGgWOUFs,94067
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Script/Regular/Main.js,sha256=7W0fshL5ZfoR-zmljlCU4ihgAT_ymedvTOU1CyH8CEU,56473
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Shapes/Regular/Main.js,sha256=kV2DXM_vtcPJgVmz0FZJJboBmrUUfoWQVtHkuu2teVc,14606
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Size1/Regular/Main.js,sha256=AZCQmDhlPZokrWvcP5MMyAF88sMv1kD69oDi39zvi8s,56429
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Size2/Regular/Main.js,sha256=R_1l1oOzzmErsCuEHLoDJWXC897Re2fiQ4E68weNVeY,8864
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Size3/Regular/Main.js,sha256=v5QAJRuulaSzKgdN5TxS8w1RDvCuV-0GN28oZ9t6O2k,8868
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Size4/Regular/Main.js,sha256=y2WUeS6aQ2CEhEIzCG1_flydfhnKKW333LNOcjCdlzE,8981
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Size5/Regular/Main.js,sha256=eO67eRSCsuwHcL_-RtBVETB_JdAvL9qLj5W9h8C9K9Y,9157
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Size6/Regular/Main.js,sha256=K9pr3OjTJpYnPkxQrGoXQt3AdCyunrT6kOT1TnCQOwQ,54619
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Symbols/Regular/Main.js,sha256=4D0u1odM_Z5HCgpvtfY_xF_zlJRV7baCH-hLIKJm2yo,9838
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/Variants/Regular/Main.js,sha256=bPK7osK-57rhgj8jQB3JGvn7xwOicn6QpY-jzUvWXF0,1947
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/fontdata-extra.js,sha256=N8wmHZJkzksBch6LbKB0jGdW3NwNR6-zObqWk77K9fQ,11244
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Pagella/fontdata.js,sha256=Usbempe0gnFo62CDSkKZwe3qQ0yicbB-aBcE6lzG0Pw,12829
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Alphabets/Regular/Main.js,sha256=iw93Fhi5HmX800WybsiXaEIEoxdkg8Lk1n5B_2u8XJg,6966
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Arrows/Regular/Main.js,sha256=_sRfxmxvJs3D2ikoTlX6pPQ2UHPb5soQY0QFPuv9qfI,6087
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/DoubleStruck/Regular/Main.js,sha256=UsjU6vW4ob9iFDWbmJH2qGEW4WrLi92GRheTX_uNdYI,44323
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Fraktur/Regular/Main.js,sha256=CwrCJGb3NfhbEl5uiNi-dvzGX0c0okWIQ0DKGpvx628,58553
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Latin/Regular/Main.js,sha256=-ejld-PL58h6C8sa7BJB8uBa9lTpwCE8jswyV1qxK5s,121277
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Main/Regular/Main.js,sha256=8b2EMsl4jBFYkACvUQKdUKtvjXy4fASIWZvgEG2fHdY,114044
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Marks/Regular/Main.js,sha256=Vbs8CMNNqlkoD0Pid5qwPwyqkhc02rkOjPh8bBwVWbM,13758
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Misc/Regular/Main.js,sha256=SYPgTkylPgz7Fz3KWsUW0oKZpCkjRleBnbee-c_pnQc,4844
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Monospace/Regular/Main.js,sha256=EjWZxkhDz1LYs-N-AgCoYlKt0i-eS22Q3xkguqLBiaM,20915
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/NonUnicode/Regular/Main.js,sha256=3cIZVaz9tARV1NQztnO4Re70KRwah_WFu3XA7YUEs4g,572305
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Normal/Regular/Main.js,sha256=ihO-g2qbeq_y2dTtpUfsXnyrVDkGxkurv-YcsP4Ibe8,115147
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Operators/Regular/Main.js,sha256=hrsQ7w59gHb3-7ub8QwuU2tnzstXSFJEoVN-GNjEM_M,28396
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/SansSerif/Regular/Main.js,sha256=gke2o3B650xpfR3SSJ7WkNNe2Tnfs4kFCBLOQmOjozc,71987
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Script/Regular/Main.js,sha256=dFDaET1BhCCqsKwGZt8MJaBvNuydavL604T4Zip6U04,72071
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Shapes/Regular/Main.js,sha256=wpTk1Dy9yNu8-Ah1SHoOIUY1MNl4IsRTqRgydJAFOvQ,12049
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Size1/Regular/Main.js,sha256=dQPo7aFrSLN9SyEMAubtaSEAtB710ROlGHjWarD-JOs,34869
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Size2/Regular/Main.js,sha256=0yPr6RrGqdgKYjiup4fcjw7zrVgO1hYmKkSAQ1gopdQ,6686
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Size3/Regular/Main.js,sha256=rcb-0Ii-Y52P2fzO1NYhFAz_oN7n7hWoRdPwWs9ETuk,6648
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Size4/Regular/Main.js,sha256=0VDMyAs4X69ACVe74lgUR7BXTGcgQ-tpot6Ez-ge0Ms,6590
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Size5/Regular/Main.js,sha256=NL_uIen1N9B5TSo1YcT2nTF7OsfzORBv9fOXGbPzA7U,6600
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Size6/Regular/Main.js,sha256=me8cdMNAUQCS7mA1bWATkc13rHAc7gIGR4gjUT03Sfo,34728
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Symbols/Regular/Main.js,sha256=0BTzxNw0Dx5FLkT6-ivV9_Y9WMP7oU3uh5gd-9gn7ro,6846
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/Variants/Regular/Main.js,sha256=2QQczpiEWhF3amYbHR3QNdOwbXctginRFvFrny6P_YU,2531
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/fontdata-extra.js,sha256=TJ2BiE0Kj8d9a29JBcYLmaStyDbfZLOim64mqwfHkuE,11161
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Gyre-Termes/fontdata.js,sha256=NZDUnKp2XfuJf4_DPlDnNZkFSHK6c0q20Pva4ejI7C8,12840
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Alphabets/Regular/Main.js,sha256=37cq98qLQGEH9CQg0RaPo8vGvFs7UDelCJUSvOvgaxc,7586
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Arrows/Regular/Main.js,sha256=eTf6FiIF9m3CttQULQ-lA3SVEi_0RX3swPtu9Cq8rVQ,16231
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/DoubleStruck/Regular/Main.js,sha256=HAiCvBi4JvVR0Xnn-Y0aNuqq4jE4Kl9znr4RS5AmXFE,24559
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Fraktur/Regular/Main.js,sha256=_lHQzh4iHkguBo2FkOf73tZCXr1dPlemA5s0j5vqOi4,72871
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Latin/Regular/Main.js,sha256=-BoC8x2Bwlb2mU5r4yRkPwkvj7wOuxSgHPX2kozFO3I,125460
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Main/Regular/Main.js,sha256=S-myPf5dbG7hFvgaq6E5LOztRhMFNq__cB16Kv3cnpU,176055
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Marks/Regular/Main.js,sha256=e_p9Fjo2XBQFA-p43HBkVmfCQbOGxne-plhV72770qA,18039
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Misc/Regular/Main.js,sha256=dtxH7yzEu_qVPNRYGcQfWaeIYU4itLD29vI7Kq8wl4g,2429
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Monospace/Regular/Main.js,sha256=UnS1pvydWDUZvE7xszJZNjyIxgP4dsmgoo9KIpfUaHY,23303
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/NonUnicode/Regular/Main.js,sha256=eZOajm-IY-NQ8MwmXmc9hzoQROzyerMbZkiLSMezbvA,984414
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Normal/Regular/Main.js,sha256=PDHrFjM1RJtsEN63iDV7-AGVN29ckFeGsS7OPPRypBc,159132
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Operators/Regular/Main.js,sha256=7huPNC9CO1YPmJufAYzhe2uLVRbZ5aL5sDQuibawu7E,38147
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/SansSerif/Regular/Main.js,sha256=Tz23DPiAq2wGRTmUqRPpzSsEMBUBXoEkJsn-NBvtGd8,101048
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Script/Regular/Main.js,sha256=CY-uS-P8envExP-uwEvoXfqerIOmrs4cnkfrhhKNQxY,43819
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Shapes/Regular/Main.js,sha256=c0WXKN6skCyQE02KlSFlH-0pZBgWnIEADyKpUNQmWoU,14525
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Size1/Regular/Main.js,sha256=mQHUmVwDRdhIfWuAu2-9f6P0ZPCnM841Qf2s8k4qhY0,72533
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Size2/Regular/Main.js,sha256=QgZ064eDSGDfxJoZBt6lNtsS4mU7LQlNoxzOOaSIvII,10284
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Size3/Regular/Main.js,sha256=vKrAAhep4Fbq1upOAy7D3EEewNg92JhHHoAnJOkLG2s,10382
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Size4/Regular/Main.js,sha256=0DZvPIFOgWdFNWX4TatVMEyWRyZjKSnmzJb8LKU5aa4,10529
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Size5/Regular/Main.js,sha256=0E47PR_F1zgVp3zrd8dQ4rbxa8L4MwqbCuoNcPF7Jkw,10389
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Size6/Regular/Main.js,sha256=84V-WDzabiVaPpw2ydXNs0jVQpHMDfwsfQTz-jekxEs,10280
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Size7/Regular/Main.js,sha256=_F5e6ugnYv6P3neSdASfb8uD-j8Py9Gb5ucwbbZ0Neo,63683
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Symbols/Regular/Main.js,sha256=7rlSv77e1u5RyyR_mPiKgg8hCbkf5pxbcMqyynRspVk,10592
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/Variants/Regular/Main.js,sha256=t_SpUSXEXiogEYzdEhOYeM8QEc-J0vvrccUgFFZF0gw,2494
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/fontdata-extra.js,sha256=2Gq8x8jbn0OlFmL4J3_ByPUan2jEm1GzieiPn9OFBsA,11245
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Latin-Modern/fontdata.js,sha256=X1m-pbOIUV5xnkBVrcTq_tVckE6XpJLpVUMtrCqRS8M,13571
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Alphabets/Regular/Main.js,sha256=20X4WTztqkAWKIouRSU2jOCSHY1ptJqWCSrq9rw1XpE,1570
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Arrows/Regular/Main.js,sha256=pbTZu2uq9rjyNwUYk2MX_YQE1Ol2wONoylldN_DiskY,1794
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Fraktur/Regular/Main.js,sha256=u52i90R1B_ccDaF82LJqqlBmq-SO9w136b_Sr7L0U_U,74063
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Main/Regular/Main.js,sha256=oDemEul1ollHQ6Jsuu7tAWf2WAJOQ_D6767uCeIq_Ns,104758
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Marks/Regular/Main.js,sha256=jgZPJE3wJ5nczk_rg35cuCTp_VY_qWKvXqbweziqejA,4332
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/NonUnicode/Regular/Main.js,sha256=IqzxvLfnu-jvqAQeOoMKTzHNMxUatsYcsGEj49heyL0,551033
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Normal/Regular/Main.js,sha256=XGXZQ4ohhDvFZkzSA7lpZ5cT-2VkI10z7I1mHdTn8VE,57415
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Operators/Regular/Main.js,sha256=QGRE2UeL5OMty5foF9cSF-g6ie8oGVoEbyV6UOmAH9E,16530
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Script/Regular/Main.js,sha256=UTFqWspq0_MDbsT6Zi_2A_JK6z0lBP-jcBH9B56TVVg,43463
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Shapes/Regular/Main.js,sha256=ytJSjD8Z6J4Hk2cgmgSucX2cqgwCRd3tIqP3jD8Jjhg,2102
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Size1/Regular/Main.js,sha256=sZEAZS2JWj0rd4xj1rD-v48mQJb7kheB56YGZpgVcyg,13621
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Size2/Regular/Main.js,sha256=0xlt6MhAOJ1cYf05t9LrDoUWi_AfnmRo4l9I64lOuew,6241
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Size3/Regular/Main.js,sha256=huDTAltQlckY9TBVdy5V8JU97n792rkR9VHJU7kM9JU,5336
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Size4/Regular/Main.js,sha256=qBox067tTmQCBuoMNEqy4twKkykHZHh5EX3C4bE00Kc,5531
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Size5/Regular/Main.js,sha256=nuqrog0GB186RYxUHtIOUmZ0K36w_6xaDmTr_tA3UN8,4659
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Symbols/Regular/Main.js,sha256=-cqJVi7_-y8aW_lw8RjiDzPUlu2wJHx9ktlJfRUS6rA,4180
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/Variants/Regular/Main.js,sha256=yODFwPjld6e28fA8Mcdv3REKvrLGsrmIFgppYTbiHks,7562
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/fontdata-extra.js,sha256=Klt9Rc9RwkaCLhGrwX4KXnsKufFjfaGmNqC_KN3ZGZ4,2635
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/Neo-Euler/fontdata.js,sha256=oFX-LZ0y6LrfvXyPBfVxuxIdTDV-iHgGXlx_1JKryoo,9653
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Alphabets/Bold/Main.js,sha256=xoExKjRlt2IZk1JM9KA1WXxBOm-wuvqJrMfci5FSu2Y,62081
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Alphabets/BoldItalic/Main.js,sha256=_TEbDEUi5rdIQd0pPJUe9QZvut4oT09JpSMtIkrDERs,77017
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Alphabets/Italic/Main.js,sha256=6SNbS5_pc7g7WA6rUrOGVYxfjnYyWqHba3TVZnI2tE8,76420
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Alphabets/Regular/Main.js,sha256=gtuj1AfrmDfHPDD-8wCwCvUpSt1bLoVh1TWrsHv6FJA,69598
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Arrows/Bold/Main.js,sha256=uC4-o-ZE9POhFvFW1aVcht10jAuECcy92OW_27LK5xA,10017
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Arrows/Regular/Main.js,sha256=pcCbH9ui5OxxxF5tZ2Hw3v1Xec3XLHRAYwvch5p2y9M,72527
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/DoubleStruck/Bold/Main.js,sha256=raMDFNoqdT_v7T8Wd97yAh8eZwAJIdApI-REU6Z46vk,14167
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/DoubleStruck/BoldItalic/Main.js,sha256=2Xmd9rnsb1TF4AxShYMNVjhu36_BwNhw7QKeybMYiEc,5001
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/DoubleStruck/Italic/Main.js,sha256=XjsiGRywFf4F6m0m72Ot03pctWLqyqI0hYRp2J1y0EA,4900
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/DoubleStruck/Regular/Main.js,sha256=ZzcXKjQmCKJiHNnjhjDvpnq_i2fvNjkxyztooLLOiL0,17577
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Fraktur/Bold/Main.js,sha256=HzewIfVdjCBkogHhh2iK9VdVJUGpYD184c8VFU26Z4Q,27932
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Fraktur/Regular/Main.js,sha256=L23MYJXoP-1Se-ylatm6KKLSiIJ2GwKoz8GsFc1AZNU,25634
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Latin/Bold/Main.js,sha256=BIEdqzdSBIsC_1h_cCwj9SSvUMWyiEwDfqL10_vsBVY,95666
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Latin/BoldItalic/Main.js,sha256=BFLf0oLFTMvfFoI5w1cG02xnywhiGkW6HzM_ud1FHRo,109653
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Latin/Italic/Main.js,sha256=nwYLhfVQLN9wS2aIVt1n0GgWbGuLxziVm45xGVoo6-A,111020
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Latin/Regular/Main.js,sha256=BlBxVv6rur293rONnU2qh6xdeCfFIQbwFWSRJcCUZro,101930
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Main/Bold/Main.js,sha256=LEtRTpeYD_iayWtu210FUSFmbHeN2jfN6B5qkrJpB4c,117388
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Main/BoldItalic/Main.js,sha256=nRdMlO09GBGkSp7gWdyLfjGxM86gYf6hY3kAJQL4G3g,61663
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Main/Italic/Main.js,sha256=8a0JYXeqscHCsmgotTyjB8dwe0hTFIUX-a1KT-LsICc,62626
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Main/Regular/Main.js,sha256=qS_KaqubczYPo9ZOXNik7lk4bbs4biKAql22P108J6I,132985
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Marks/Bold/Main.js,sha256=zjE4reOQG0TqWINjfIJeIT-TGyzvApFaJfsNFnoGHVg,32677
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Marks/BoldItalic/Main.js,sha256=6wrYGSePh6BJCQAtdrx3hXvlBud6uoQF8X7_sjul5ls,11389
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Marks/Italic/Main.js,sha256=SkhOoTQNOBwP3QIxxxq8zetA7HKRl4mmusxfWmAu7GI,14110
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Marks/Regular/Main.js,sha256=1-Bn7cIyRtWUh8FvW5BagR8WDWZqRHaJZw7ONDjo-SU,33976
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Misc/Bold/Main.js,sha256=7poQkZkJM9ftT6jpGw9z4HYQvZU4Wnd7BIwxUNzi7CM,71319
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Misc/BoldItalic/Main.js,sha256=YbCSXCHLkOmd9Qas_DHBISVvQac5Z0XT2ul2DHhosfs,70945
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Misc/Italic/Main.js,sha256=bYVPDHUCoMCLSsT9MgYRv24Qh3an8eQwgyK9zCX6fHU,71224
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Misc/Regular/Main.js,sha256=PpWZnkdPz6iRb4lrfVfeVbXX9RzEuT-3upJ1b7RkqUI,102871
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Monospace/Regular/Main.js,sha256=awUfiVgt2cGwonZm3UOoQ83BXxJiWG4ICvuDCtKDlS4,24141
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Normal/Bold/Main.js,sha256=d6d959DZgT1HYysE3HyTZcjJaKn3LWHbslR9eAdA_fw,41094
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Normal/BoldItalic/Main.js,sha256=DkA2TY-j9sE9nXLUdQeoEvJ26JJe2IRnP_iQY859HSM,38110
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Normal/Italic/Main.js,sha256=gsqWuHfplR8Ywgbj5ysBYwCbLf6P7-XK5wqPU4ZSKxg,38925
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Operators/Bold/Main.js,sha256=7ENgvThAVGdcD8NcIvdNQ2v3l_4zHVwvOUsaMSueM6c,47656
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Operators/Regular/Main.js,sha256=7j-pm4M5oheypgj7cPj3hQCA7a3PeLR3ZcMPRu1AxiY,105460
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/SansSerif/Bold/Main.js,sha256=zr-0EE8ken7yf1kq6AvuxMSLdMLOZQklCsNMQVtxeOM,25864
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/SansSerif/BoldItalic/Main.js,sha256=3kBJe-RL2-PiRx2n9vCXIo0V-XhSyHyKio_Z47RyPPM,27828
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/SansSerif/Italic/Main.js,sha256=4OToC_xy1hC4xlSf20rPlGfvajFPnnoV4sKKgmPsMgg,27592
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/SansSerif/Regular/Main.js,sha256=v7CxO-u9VMateF0GDNk6DE_pH7iXIc-DOILbU4NCFn8,25567
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Script/BoldItalic/Main.js,sha256=qnvoINrxwc0QtqISEMisBG5yhBU1YsIovZ0SrJDuZMo,39074
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Script/Italic/Main.js,sha256=6H1yMPOk2rKXHupU1z7IiJsOJRPRrnnSfdpV24qQGw8,31310
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Script/Regular/Main.js,sha256=D0h-toQDDXbXsSFaDfuVAZWlOsUQc9isYb8b_PR6Fs0,31287
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Shapes/Bold/Main.js,sha256=MWVUcxOz60_DDAwLv4PLt_kGj83-0ohEFbtgEdczVvA,1934
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Shapes/BoldItalic/Main.js,sha256=sxXPU6iPt8hFlos6eTT_SwAsR-iueFf2pUAhYknG5kc,1074
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Shapes/Regular/Main.js,sha256=aMNNHiHoM-YPdpsToitKQQe_vhau4qF93os2usMWnaE,91780
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Size1/Regular/Main.js,sha256=lfZJysgR2uDPs6ZBbNy2Lv91O_ZB-MrUZgvAA_geBdY,31171
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Size2/Regular/Main.js,sha256=8k5Z-L-Gv-85do026YEHSUr3S5SOnALbtzmcI5eb8v8,9764
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Size3/Regular/Main.js,sha256=GQsPvTQhoNVk36q9NQzXdvxOg2MhldiY7Q-lA5g2rhI,9673
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Size4/Regular/Main.js,sha256=GR2lXRvyhgJ3NYc2OA2g6cCY0rLhTcxPVv2742txdnU,8805
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Size5/Regular/Main.js,sha256=s9YZZ-bN48I3qaDFtcSelLHz2OhpPwFMIxMCEhkdNUE,12646
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Symbols/Bold/Main.js,sha256=znEp5I2IVPI_mf8JT4fabeYC5NBKr-XgijqIpKHeKD8,4723
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Symbols/Regular/Main.js,sha256=08ELNG_N3fvN1DFPc1miV4vrsNzp_YEnR0V7MuInVT4,48538
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Variants/Bold/Main.js,sha256=wUr3Ygq-0D-A0nnrT-urrKbSiePUqxbmCm1Gsn5-4fA,27354
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Variants/BoldItalic/Main.js,sha256=ZJAGMsHvBmoFiUiczSYjGGIx6kxlowh-3ggjUNuoXF0,13825
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Variants/Italic/Main.js,sha256=XwOYCpJ5GCNA0HstqD2Uup9RaeurXupkxocEsHVb0Js,14147
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/Variants/Regular/Main.js,sha256=zp5R-4-6-ShZkZkLZV4UxYGm7QA_pj_u1_wQtctCEKk,43406
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/fontdata-extra.js,sha256=y5tpuPJV2hPtNB9rLXdCaJXwHBi6IImpbnvvw7jTZV0,12251
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/STIX-Web/fontdata.js,sha256=PaQAgV4tyGXK6J_JbS8Jr5zTxfF2W-Z8HiXKuGMQBVs,15722
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/Arrows.js,sha256=hBkNrsSOMQ6aWIRJ3Fifd92aiImCaLwBaeBvJ34u-YI,19232
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/BoxDrawing.js,sha256=9bKYKSsQr-NeCftptBgiDyi5Ts0u0jQDEayn9MVi6-Y,1943
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/CombDiacritMarks.js,sha256=n8ncPFrfo45MZCh4iKdgUzygeR7R4zuaL-lrhGy66mo,1700
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/Dingbats.js,sha256=T-7UpwR_3ryD3NNaBZJoRWfkgQARy75B5WL7Otsj0eM,1741
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/EnclosedAlphanum.js,sha256=3bmfQngY3FWH7PSouNaHx5TqyFp3Nld9fXlyi7ya-bw,1811
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/GeneralPunctuation.js,sha256=AExH0C1EMh62WH4F11hjPS0eNzn78-eKEvfdWwdHezc,1040
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/GeometricShapes.js,sha256=8Th1SmBanV4vvBsqk8DmZvPGKh5iEZFTj8RZDQ9nuZg,2577
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/GreekAndCoptic.js,sha256=kgLUomNS2K1k1rH2e8VxeAmI8sHNNUPVlcokskhX5eI,1846
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/Latin1Supplement.js,sha256=UvvqdWtLd2DXgo_2yJI21ijIudwf9OwwHlfdrC_OD6s,3958
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/LatinExtendedA.js,sha256=vUDAeuGR52WmjveRz2M5tQBLzS6R2pGV0ouQkhWxThk,1651
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/LetterlikeSymbols.js,sha256=Gzej01ThKq-zTO5gr8L9BvZn6EB2TdjYYCnwRbXcN10,4262
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/Main.js,sha256=G1i08xHkwCxRSrPQrU3SttE8OY4KMiJTZ6leGVswFTo,24217
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/MathOperators.js,sha256=mp91c74Rd2oB96HUFXGXHJ2IG_RMId1Wt_LxNvLi-04,55608
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/MiscMathSymbolsB.js,sha256=ElUkWrJVN1zNC0xxLsYuIe1Jdl3eA9h_7P2QnqZVBEg,1115
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/MiscSymbols.js,sha256=y_fTEvgoedzl7KLK0wSm__s4OXQ_k50m-Zgz-D7clVg,1297
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/MiscTechnical.js,sha256=o2e6A52NrcyDju92S_DGSfbb0QIOH3_iDLc-6bmRUX8,1379
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/PUA.js,sha256=y2tN5k0LFyxTVt6GESt6VIsggNBUML6nD5XpPlDAW9U,11810
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/SpacingModLetters.js,sha256=LLWHrZyWKoFwIAti4o4NUlPr3bhoSK2wG3OfTi1Ydsw,1616
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/AMS/Regular/SuppMathOperators.js,sha256=IImehfI2UTCHOqAR13yapAjbHGl_OZKi9gxLQKFJcBg,18287
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Caligraphic/Bold/Main.js,sha256=4gp_x3q_QNFQbIvh0qi22oQRMjQgP3oQKmHEG_O0Ogs,22630
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Caligraphic/Regular/Main.js,sha256=DlFHzX6hFs0LXNGD2G8m9gckkTz4LXlUwKCeOB7_r1I,22103
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Bold/BasicLatin.js,sha256=geEo_Z3Q1DsuG0EXgT0W0oJKl9cfujXHNUtqGItO5mw,50076
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Bold/Main.js,sha256=5PeDVehWc20xV1Z7XTzvgTOtsiGvu6LoveGxGg7KzeM,980
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Bold/Other.js,sha256=_mKBUxdP5423Nr5Bif8_rBcWMNDNZIgkj4wG9oBLpe0,1219
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Bold/PUA.js,sha256=2ubwNNcHqxaxN9l5Is1-Ov6nfmwF6X6C46D--QQKwc4,5605
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Regular/BasicLatin.js,sha256=IokRLgcyZd6_oLtZsCYFykEMaLVkz6R3MxSY-4bYYuo,48363
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Regular/Main.js,sha256=A6wIX3wC5uZ5PE0tJQIfmgahNOAIsRb5TJIcysDmai0,966
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Regular/Other.js,sha256=6Ki-mj70I_HA8JV8DpYnBrx3aF7UNTLYkh6aHhDAQ0A,1219
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Fraktur/Regular/PUA.js,sha256=VBkg3vCOdTDfkp8Ph39TrKuw8Qe1Y8m6cwzjO5mb2ks,5067
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/Arrows.js,sha256=Okfr6BzX2kWZPfO9JLnJCtv5-qoTBQlfRghs443Kiwo,12752
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/BasicLatin.js,sha256=lrzR1TsiHyp0_RY3dzHk62E8m6WSnONwReioUdRQLUs,18038
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/CombDiacritMarks.js,sha256=vfwCh8rPNHjcCb03nEKMD57vTIU5jqEEr-HAO62-1ok,3717
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/CombDiactForSymbols.js,sha256=UsX8yOfFV5xL9ZcA5JT-qZdxPffj_V_diuEYKi1l8_E,1221
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/GeneralPunctuation.js,sha256=Tyd2S0uh6nqKwDagHbG1YHb2PWoNYZIcFzvEKkFRJFM,4583
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/GeometricShapes.js,sha256=Z5392z6YpbJk71flLnBhSlmZlTWKKbOTr0VHtC2tpOY,2339
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/GreekAndCoptic.js,sha256=nft2s24cWl3OyA0lC5NXrXZDebB9UJajHQemIV64s8k,5239
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/Latin1Supplement.js,sha256=9a1getLZPZeEL9H0Sgtm6S3Td20YCCz7hibnEJ-srFU,3026
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/LatinExtendedA.js,sha256=kh6LoSFnmjfvXcJ1PhUr37NJ5_1l60a12yWdDjq6mS4,1293
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/LatinExtendedB.js,sha256=OjaYd6vsr_bMcJUciTQn769L_EI4Tk-3pmcdjk0UOpc,1317
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/LetterlikeSymbols.js,sha256=BW5OumlTMWENDO0WMngpHaa7y3q9z8URCPEuCAI_hzg,6037
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/Main.js,sha256=AQgIelwrmz0y717eg_YqPwfmym3I67w0DbFf5USxNa0,21842
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/MathOperators.js,sha256=IKR2ru0M5hbP6wAKsBQCZzEEHXGIoPSSkA7mpDWWX8g,27338
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/MiscMathSymbolsA.js,sha256=MY6yhLXsBPSgNQTFehpDLEArnfzpZh8d1Kah5L4d6_Y,1315
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/MiscSymbols.js,sha256=FZQTrbaaWWGJojxgh1D2ZydWDlpTW-dOMv0tp5b9XBQ,4808
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/MiscTechnical.js,sha256=CHYaMB4mA0GoSr-XjpRUWYd96FBpjZIZuNGe91BlVsg,2219
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/SpacingModLetters.js,sha256=FXiOZskiyrwq_dig95n-2T0hKmlw1r7pPO3cw0BrupE,2662
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/SuppMathOperators.js,sha256=lWm2EUuhIBJg3bvdmg2yF89N6wxpG74jUAHyEi1Ql2k,2392
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Bold/SupplementalArrowsA.js,sha256=Goh5lr1u4Z44FELdXanfjYn_QXO-lwRMp3b1xXUNs0U,5283
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/BasicLatin.js,sha256=bKuBnky3Skzp4l9E_-NiFV46qdU5rNzswEBIfXUxDz4,49033
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/CombDiacritMarks.js,sha256=dz1T5BpIOquOg9_C1q1yw0uTGGqRj9dpO46YLVi7cII,3518
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/GeneralPunctuation.js,sha256=agpIsmQrLj_nE3QkW3ek0SMyXh0xPj1SzYyO_h4l1Vg,2708
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/GreekAndCoptic.js,sha256=qHj8-ryt7kMIIcDEhUoQjMbxErDnTTsvUQBCG1a4EPQ,8514
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/LatinExtendedA.js,sha256=P5BmyxjOooYCHyxlvKIh2LA2_hBYi_dHpKVUsqXjaX8,1289
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/LatinExtendedB.js,sha256=9sbNXW3OV8HWRcFDDhnQZCM5oaNvOBY8KcKC-s7FCxI,1314
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/LetterlikeSymbols.js,sha256=-bFRRWTAxwWcEe-J5zQAq7_8VdClGcw9swuU1Z4sqyQ,1703
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/Main.js,sha256=zpk1kPbgASNYFZSdGSQAL2Dplxcy9VrfJS559xLBHL0,1941
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Italic/MathOperators.js,sha256=xMSsRP6RomTMYVW2xTGIxkXGKqo3eHSO5gyihkpD6Xg,895
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/BasicLatin.js,sha256=2QwYJrI1ICPRSQWp0ASxojnFwcEfmDdDJ40XYIYkDig,19906
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/CombDiacritMarks.js,sha256=cXQs9l_IxhG5-DHEpl31_yx_73UcNt3ZjE3NJvMoNEw,3452
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/GeometricShapes.js,sha256=xdb85ZsMaoyamRXoJU2M63npjLf8KLwVvqFC9BkgUj0,2162
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/GreekAndCoptic.js,sha256=WfeuOi0piKC3YirXpsH9l5roDTMpqxZp-UuVR4ZD9M0,5730
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/LatinExtendedA.js,sha256=_y4ixl2kK2Mwy8e3eRIBXFgi5TmaMxzHqtQVQS9oEHY,1135
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/LatinExtendedB.js,sha256=DQmxSLXd0lPwFQ2zhYnYSH-vvRrwAU12AL4hhlkssZ8,1212
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/LetterlikeSymbols.js,sha256=PQu7hCduWiiXeJhMZRxsq6bDS0QAofT0QzkloTRzchg,5960
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/Main.js,sha256=fBnfiPuRjNu6UsK0DmkJ6Jx_aHAU2RjdU27sL8ma5X4,73098
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/MathOperators.js,sha256=3xJnRYiv0UivJMGGV5NmjJS3qaPBCpOhdCYtCdDO1ls,887
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/MiscSymbols.js,sha256=6_zfnVy8UhMobmJrsrUsHn5YuTaT6dg2F5qhrUUYKfg,4513
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/SpacingModLetters.js,sha256=2E0dWDXxz08T_CFWX_srctHiceJXj7PmrPWdhxyeZd8,1170
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Main/Regular/SuppMathOperators.js,sha256=vrHTB18OVHRD4eDIapdLP4cL_Vd9EgiIjJkXOuC1IX8,1477
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Math/BoldItalic/Main.js,sha256=ZCONvmwCjS7bPIi-t8_Koy9nAYq-U21AoUYzf46oORc,58684
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Math/Italic/Main.js,sha256=gLtPm7ZGs-xtilhBteE2wOoJ7fmMJEdPotdMQPcYgs0,58935
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Bold/BasicLatin.js,sha256=gWaO1-TG8L-tlo38IzQINgL-3opSPy_034ivUyxSxq8,36852
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Bold/CombDiacritMarks.js,sha256=KkIrsIskTX-ITS6Dp4qhFC5SsheS3WYofj55tABLSNs,4011
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Bold/Main.js,sha256=v6UxlRwQJ3c4M78_WOEZKnRTKSll91_xUfXBJlK18Q4,997
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Bold/Other.js,sha256=pt6V22kF079d_K4iupoJ9mlbJLYtI4uKPFeumDEXzOI,8151
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Italic/BasicLatin.js,sha256=syCgsV-11V7nJqCLvZyAXOKuWghDsbsr9IlixtCYJZw,30362
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Italic/CombDiacritMarks.js,sha256=3n6gw4PgZFMbvPsP0LyXHTr4e_bAjBgils_fu0PpDj4,2694
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Italic/Main.js,sha256=EgkMm-WD22QMzpE4FjIVBrwC5RhwTO6O5Sh7pVia_08,1006
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Italic/Other.js,sha256=RNj2a8VsF8nZFPTNy8THZDPzwDh-MSFxA5KA0Cwwpbw,6428
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Regular/BasicLatin.js,sha256=JT4C5wtIWiN6U7HSdyTPpOL_EH1DieXANWTDe5AE-_s,24036
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Regular/CombDiacritMarks.js,sha256=5x9AloRw-vJXrsVWsYzBqJvQspNeBeM81iqhzRPKAfo,2547
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Regular/Main.js,sha256=tMVEOEpvY45Lhrmpk_DNjrRPWwBJsEyYAhQcCWno31s,983
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/SansSerif/Regular/Other.js,sha256=slEUdP05DA0t5QOD_moN_AQ3ihyw2BwS0PTPgPVVGgw,4559
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Script/Regular/BasicLatin.js,sha256=bVOdDZltmIcmFnqNOFQd4xmgyYaBTvfpGXe_EFqhH3A,28343
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Script/Regular/Main.js,sha256=wRoa0STbnIbvC7sgGrAv5JqLUj5DxU13nSvIKYRzb_8,1158
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Size1/Regular/Main.js,sha256=PhyDDpwFpi-cr4b54T2SbDjFE2seuo9hcZU-g5zjlB8,17011
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Size2/Regular/Main.js,sha256=97hO0NN_TdlYGJaOfwmG6X71Xg_Y9HSKGTp3u0oEBiQ,15579
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Size3/Regular/Main.js,sha256=wCKlGTO-tx4PM7i3xExJp9P64GWI5P__2Pk8Io10C3w,5608
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Size4/Regular/Main.js,sha256=NU_Qttb3S9TeAnsg4MvMU7YpQ0MEqyJl-BMOJlHxe2Q,11670
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Typewriter/Regular/BasicLatin.js,sha256=qfIzx5p0vtJDtOSFctjez7zQ3-YBqAQkbh1_RK5FBHo,43719
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Typewriter/Regular/CombDiacritMarks.js,sha256=LiGQk_0NerVicuFicIFCnm4f4pzUVKPoVj2hQXhMLfg,3345
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Typewriter/Regular/Main.js,sha256=56RbIxk219izIDF7g65auWEHUAYdCEd6DmIQGI-3MdQ,988
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/Typewriter/Regular/Other.js,sha256=odDmDcJkobf5HJFlbIYXnrx7Km4KYD0xHk_HvVYHXrw,8476
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/fontdata-extra.js,sha256=OTfDZe3KtnPRFoxKUVG3_egwz4tKbEceWa1RfA0B6Jc,4210
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/fonts/TeX/fontdata.js,sha256=wj1L6-MEYQ9heYPq6rf4ql4TpFORMY8wzPJlD7_5OeE,143577
spyder/plugins/help/utils/js/mathjax/jax/output/SVG/jax.js,sha256=OdmpX83nj1YMtfzxG7Q0ylrSNJ10c3txFgyPRzhh-10,54691
spyder/plugins/help/utils/js/move_outline.js,sha256=0nJAoNqnlIxXBjlJLaKviOK3M3_AwmnSB6hij-jFDjo,626
spyder/plugins/help/utils/js/utils.js,sha256=nQWBsLBrNTx9EG1ydeSL5oq-iAzixpNU5r9QeiHZHMk,1455
spyder/plugins/help/utils/sphinxify.py,sha256=JJjLrPFQghIA6PKdUn_gVBdcNdRqhpx-k6Vpei479CU,10197
spyder/plugins/help/utils/sphinxthread.py,sha256=_0TawaNi1MKiQN8RCW0TLdbVe2G1xlc64V4bSTsxk-U,3550
spyder/plugins/help/utils/static/base_css/base.css,sha256=WXJIoPtrL45qsYtsfGGXOXdX-IOoGDiTfg5NC742Lvc,320
spyder/plugins/help/utils/static/css/default.css,sha256=-x3MTgVVrjcWx-_AtM-dL8oqACpKfc7wAugZZwtE4uE,7216
spyder/plugins/help/utils/static/css/pygments.css,sha256=2gY8FHY3tj2739KVUSxJYZp2PkQMGfl43V5nQD3Blw4,3225
spyder/plugins/help/utils/static/dark_css/default.css,sha256=xx5zaa8p5g3VXDD5mT3mXJSS9bIS8pcA6DQ-gFH7qRs,9426
spyder/plugins/help/utils/static/dark_css/pygments.css,sha256=rYGeet3wsmsKlAAt-Xlvcfvkaa33VvGktkvc5JAE9SI,3284
spyder/plugins/help/utils/static/images/collapse_expand.png,sha256=1GEuYJTd9neQtH9Hir-huoJUqVY6DnyFhsyLquxOFTI,295
spyder/plugins/help/utils/static/images/collapse_expand_dark.png,sha256=0sRTEaxIyZd86I8oGt4a5lAobWfk9wBQfUlPlP8SZdc,889
spyder/plugins/help/utils/static/images/debug-continue.png,sha256=lzVl2PI5ZZK5W432K3JypBk36RoFnT9rMJQWssosKXE,251
spyder/plugins/help/utils/static/images/debug-step-in.png,sha256=ehvo_Mq5quhdXKopzKGDpWkN1JXqUONyKA1j9r441rk,227
spyder/plugins/help/utils/static/images/debug-step-out.png,sha256=wB1V3ps7nAZ4RXeKHfqHY2sBdWFJKr2cxKZqT5dEWtk,220
spyder/plugins/help/utils/static/images/debug-step-over.png,sha256=LtbUSst3QJyt8PmHTcPLNvbvdTlzdhQV_iIzJwzHDH4,260
spyder/plugins/help/utils/static/images/down_arrow.png,sha256=AvWCi9Ehgn6LRe6MefIl03kExJHcuNZQVy0L2G4VgiE,165
spyder/plugins/help/utils/static/images/down_arrow_disabled.png,sha256=-t7VfHDbcxgNnxk9YlfVEdjctfQEftQ1DPzHzcnCuNA,166
spyder/plugins/help/utils/static/images/down_arrow_disabled_horizontal.png,sha256=BTPuUkUmyNyZWlOqa11jV3JX2KlVSJ0NEzNZ5FIfZVg,196
spyder/plugins/help/utils/static/images/down_arrow_horizontal.png,sha256=Ed_jqtIkrDqFJ5cNDDekybHCcQaNjtvCV5vBVMy9b2o,196
spyder/plugins/help/utils/static/images/spyder-hello-docstring.png,sha256=R-CJeei8i7_arSj8X4fsbXZXZ8NdRcu0TRPv3cC-ERk,5330
spyder/plugins/help/utils/static/images/spyder-nice-docstring-rendering.png,sha256=C_4sw9H3RJ1oWbAL3SzcCBComjHDdAdn62WcYc_9YQE,31560
spyder/plugins/help/utils/static/images/spyder-sympy-example.png,sha256=S2RndwAEBRWj-5aRuk2HIqYsaNh7kbqZe7PwqVg0Q64,8051
spyder/plugins/help/utils/static/images/up_arrow.png,sha256=96JGHWRw0EfpoNh5xH4XLc5_1J_BSM-vf0hz3WFdoDM,158
spyder/plugins/help/utils/static/images/up_arrow_disabled.png,sha256=6jKCVAKw6EuKJ1WgaHg5W-WlHk-8ckQg44q2Yq-W80A,159
spyder/plugins/help/utils/static/images/up_arrow_disabled_horizontal.png,sha256=2afgY0q5qyVdKNPpz51oAF005uIGnq6EmqSI1wDD-04,193
spyder/plugins/help/utils/static/images/up_arrow_horizontal.png,sha256=6EUETwgqkbn_wbe1inVBhWlIDvGtkoLs_CVTwhd-59I,193
spyder/plugins/help/utils/templates/layout.html,sha256=1j2S0m5B7WI5ia0djVlCfcfcpKoh1yJFHDmwyQSY8dg,2837
spyder/plugins/help/utils/templates/loading.html,sha256=bE_ihmHyNLR5slJungASVvATxxLW1PRQazERaQH9XpI,2709
spyder/plugins/help/utils/templates/usage.html,sha256=gORF2QqU5_jkachfxrJ5YBx0PD1aYNdysMhnCvIYo90,888
spyder/plugins/help/utils/templates/warning.html,sha256=CxSQ4ITF002wgo3-I8tg5Q5mHOtmao_urE5cesHCfPo,607
spyder/plugins/help/utils/tutorial.rst,sha256=MZo-jnBzuEhRDZh3rrxHh3hqz5B5nX4ixJNIERkqtHs,34760
spyder/plugins/help/widgets.py,sha256=FBkoX6W3g7DjSqzQanIRDhel9EAlxdmXK5ialnR7TX0,37908
spyder/plugins/history/__init__.py,sha256=2O0rQakzDLsOvz-0Oo7lpEYFfewpfq0hd_6KZCfeoVY,215
spyder/plugins/history/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/history/__pycache__/api.cpython-312.pyc,,
spyder/plugins/history/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/history/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/history/__pycache__/widgets.cpython-312.pyc,,
spyder/plugins/history/api.py,sha256=gAMvI2Qcu27_utNi9_LgvClQndcXozf2DU7BEwgI8Fg,328
spyder/plugins/history/confpage.py,sha256=MV3WEFMh4LZD0CBKrg44Uv6oA_mL9i_6FaYmToZ1EQQ,1485
spyder/plugins/history/plugin.py,sha256=cTi-8F9levYnnBP3w5fC41HiApCyQ07A60Xo7t0r1w8,4121
spyder/plugins/history/widgets.py,sha256=1nRql_ySeffosw7kwhXNz5_fUsrC3LU3_qFDq2phL8w,10063
spyder/plugins/io_dcm/__init__.py,sha256=E0OZ2bGNLIk3gIqS-3m_hWuhkqfHspalbz6Lmj92PIg,683
spyder/plugins/io_dcm/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/io_dcm/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/io_dcm/plugin.py,sha256=V4lhxb4gyJVJrPCcD-2bF3akG6efXbnDH8GBPW_9luQ,1030
spyder/plugins/io_hdf5/__init__.py,sha256=c6jc2zvTFiidKaqtAggCVBK1nKt3ARPkn7sknvw-Ojk,709
spyder/plugins/io_hdf5/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/io_hdf5/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/io_hdf5/plugin.py,sha256=yGV5cLLM-VRe6iUjF_NBkA495tBP3bAXJ63QeQYM-Pk,2734
spyder/plugins/ipythonconsole/__init__.py,sha256=rQdhfhWGj3Pd6WWCZ7dsbtUrrDGzIYBMvOQaITAu58I,672
spyder/plugins/ipythonconsole/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/ipythonconsole/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/ipythonconsole/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/ipythonconsole/assets/templates/blank.html,sha256=V8pVx_PRA8QUJZacxNRYMYH0h6N-solMv9AHN8uZMgY,495
spyder/plugins/ipythonconsole/assets/templates/kernel_error.html,sha256=rrclWz13UknR7VAnpC3N2bSKlOK_Igv0-y9pX_xMF5M,714
spyder/plugins/ipythonconsole/assets/templates/loading.html,sha256=uPAtj_8JemFYr6vZwk1D5GYqfupwJg0aFOy0frJnKkQ,2709
spyder/plugins/ipythonconsole/comms/__init__.py,sha256=2bPtVv928hSgLw-g6dxXdR7MriUFsu1uTyIoSyH2BUM,433
spyder/plugins/ipythonconsole/comms/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/ipythonconsole/comms/__pycache__/kernelcomm.cpython-312.pyc,,
spyder/plugins/ipythonconsole/comms/kernelcomm.py,sha256=nU9TRT12h8QumiMtm1STfGI5cGXEM0QkC2wZ6Dfu_ko,11274
spyder/plugins/ipythonconsole/confpage.py,sha256=osa6dJyCLy227ytxU6B5HgOAacUY-EJ7qklHtm3Kauw,20324
spyder/plugins/ipythonconsole/plugin.py,sha256=qSKt6cD5FCXh3fEDUhNeswubPbF_5AyUkA79E6ijFSs,29843
spyder/plugins/ipythonconsole/scripts/conda-activate.bat,sha256=2fEhYdYHcIeUAxIWCbkwRWGjqWcw6ZRjc2hpH2PIdNk,769
spyder/plugins/ipythonconsole/scripts/conda-activate.sh,sha256=VW-uvHVcdYll2OvPY26E_qsnld4tKs8Eqmh1uhwrlo8,518
spyder/plugins/ipythonconsole/utils/__init__.py,sha256=t28Qf5HBpjgZYkApVE4PLiBo3rwjFByoH3GXYIpLmVo,369
spyder/plugins/ipythonconsole/utils/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/ipythonconsole/utils/__pycache__/kernelspec.cpython-312.pyc,,
spyder/plugins/ipythonconsole/utils/__pycache__/manager.cpython-312.pyc,,
spyder/plugins/ipythonconsole/utils/__pycache__/ssh.cpython-312.pyc,,
spyder/plugins/ipythonconsole/utils/__pycache__/stdfile.cpython-312.pyc,,
spyder/plugins/ipythonconsole/utils/__pycache__/style.cpython-312.pyc,,
spyder/plugins/ipythonconsole/utils/kernelspec.py,sha256=slxa88z_XsnUg9RC6C2lmxp0_0I5ucpK98h-RHRDIjs,7973
spyder/plugins/ipythonconsole/utils/manager.py,sha256=x4dH-9tnMMnKU2qse3sbrz5Qyn8KNarcJtYNcP3K7hU,4627
spyder/plugins/ipythonconsole/utils/ssh.py,sha256=rr6AyeVgfUXQHYoZgbR9VfglVC5rYFLaHKBV5Qk_5k4,3817
spyder/plugins/ipythonconsole/utils/stdfile.py,sha256=IBMQE2MQDJacg0QNr3ZQqQUCLxe2EqQCV3JHBWNRxlE,3758
spyder/plugins/ipythonconsole/utils/style.py,sha256=0jpPB1b6DnkqTsXwXRjUjp5CJDEGWsOqJEt7C7GalHE,6441
spyder/plugins/ipythonconsole/widgets/__init__.py,sha256=yAxXW-1ZhwHq7bAUEH53pee2338FqwSZCkp1u7BC3dk,947
spyder/plugins/ipythonconsole/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/client.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/control.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/debugging.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/figurebrowser.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/help.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/kernelconnect.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/namespacebrowser.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/restartdialog.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/__pycache__/shell.cpython-312.pyc,,
spyder/plugins/ipythonconsole/widgets/client.py,sha256=bjVZ4m2rkQcP3Wdp7DG04tyS9FFRcpKHq4_YJ4n62cw,38129
spyder/plugins/ipythonconsole/widgets/control.py,sha256=LuF559frUuFlKUuEADoqaKF9GzE2MR1sfDO6dzpnNEc,5198
spyder/plugins/ipythonconsole/widgets/debugging.py,sha256=Hjy5vi_hjdGOPNVVU9lBK8yb0VD_73iDTG0CRMgjPyw,26968
spyder/plugins/ipythonconsole/widgets/figurebrowser.py,sha256=uxDe91QIFIieOkIb_LsnW01iZ892UAH8YzLCBeiZih4,2815
spyder/plugins/ipythonconsole/widgets/help.py,sha256=YJsxhqF895m_ixdu_Y2JEu0ooe-ZcXF0-4k3bMqaCS0,7300
spyder/plugins/ipythonconsole/widgets/kernelconnect.py,sha256=TrA4GVnRawJzht0JH4bux9QRu6YhejvzeP-WY56HEPg,10732
spyder/plugins/ipythonconsole/widgets/main_widget.py,sha256=icPM4XBaJirTCFXkHegWw8t5-McRdEqeAxztZxCB0lk,97080
spyder/plugins/ipythonconsole/widgets/namespacebrowser.py,sha256=elznQWmtheXTmVmfDsK3VFW1o8iZfu8wbf1JYyCmhic,9377
spyder/plugins/ipythonconsole/widgets/restartdialog.py,sha256=hVT8EBPUbPMRH_QcWzrDK9B_9NQ6ZZ6rmw-080o_r3g,4045
spyder/plugins/ipythonconsole/widgets/shell.py,sha256=66RnfqvfujS8O6b8wuqi6VvsU-l5chq165ry_Epm01A,39854
spyder/plugins/layout/__init__.py,sha256=M-GZ4drnfX23JDeC0uCyxkxICTTe9tzGDU91RLCqX38,223
spyder/plugins/layout/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/layout/__pycache__/api.cpython-312.pyc,,
spyder/plugins/layout/__pycache__/container.cpython-312.pyc,,
spyder/plugins/layout/__pycache__/layouts.cpython-312.pyc,,
spyder/plugins/layout/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/layout/api.py,sha256=fw69Os0jqrbkdo3Avjj8Fosbt1yvbXlkJ6tW2IRxXpk,18634
spyder/plugins/layout/container.py,sha256=LTQSmm_OW5hnt_fwRN88pcn1lnkPzCuEGRRGmoPwSIg,15577
spyder/plugins/layout/layouts.py,sha256=GvxK5X2RgLDaHDHn_1DVCJmCD__AT4RH9qCnin13ywo,6800
spyder/plugins/layout/plugin.py,sha256=cWvz3cAuUd77f0dJxONbEFrHjmjkF1qwPZ4VUXQx8Ck,43370
spyder/plugins/layout/widgets/__init__.py,sha256=n5GN0PvibXAiUfAqgYIZ2XJCTaKeuylgSevE76AQgLo,240
spyder/plugins/layout/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/layout/widgets/__pycache__/dialog.cpython-312.pyc,,
spyder/plugins/layout/widgets/dialog.py,sha256=ll466mUCj2mdL80o8lLG05kHbnnnYORSEbtwnACoZ34,14158
spyder/plugins/maininterpreter/__init__.py,sha256=DfmHp9lb5RQM0uXGQV5J5ZWqLdIupLBzNUZmZqJVWZY,251
spyder/plugins/maininterpreter/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/maininterpreter/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/maininterpreter/__pycache__/container.cpython-312.pyc,,
spyder/plugins/maininterpreter/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/maininterpreter/confpage.py,sha256=VecxExsrw_15NIm0KGOG4StGIpyBWrjtzA4GlB8VNyg,10232
spyder/plugins/maininterpreter/container.py,sha256=ZeJLYUqMLlOabbFiCYirrj5CJtBbhFeMiTCzPlskm6M,2547
spyder/plugins/maininterpreter/plugin.py,sha256=nGdRq1OBJPcpKPv_I1nomtkTguW5-4G0dc28qmrXhdA,3967
spyder/plugins/maininterpreter/widgets/__init__.py,sha256=ScltPZn09mVnsvs-8sf0rCVUjWNqj3jNB5Ep74hRI8M,204
spyder/plugins/maininterpreter/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/maininterpreter/widgets/__pycache__/status.cpython-312.pyc,,
spyder/plugins/maininterpreter/widgets/status.py,sha256=jD21iY7d2ZmlCAhGrnTuFp11vRNaLr0-3-7BRgiSb08,6932
spyder/plugins/mainmenu/__init__.py,sha256=XkXsWIIKvSz_VGcko8tWezh2SWDi08RhXpUiABIr_14,230
spyder/plugins/mainmenu/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/mainmenu/__pycache__/api.cpython-312.pyc,,
spyder/plugins/mainmenu/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/mainmenu/api.py,sha256=fpAk8oSGeORcln1cbzK2xLvhgqjeAIOLevpPGm2VGtM,2401
spyder/plugins/mainmenu/plugin.py,sha256=mRZY-im1b3uUdiIAJLS1bLO-Zazt32tM547Zkuya7d4,14326
spyder/plugins/onlinehelp/__init__.py,sha256=FvcIfOO1obeI3wuULYC8l4yge6aKCLXSmtR_X_YPkbM,202
spyder/plugins/onlinehelp/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/onlinehelp/__pycache__/api.cpython-312.pyc,,
spyder/plugins/onlinehelp/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/onlinehelp/__pycache__/pydoc_patch.cpython-312.pyc,,
spyder/plugins/onlinehelp/__pycache__/widgets.cpython-312.pyc,,
spyder/plugins/onlinehelp/api.py,sha256=hwtzioMN1QRPmy84Yuu_2mkkc9F-YIsDSitW39z_pBg,287
spyder/plugins/onlinehelp/plugin.py,sha256=p8TpGQbRRfh6w5q6EaWt0sEhAttRUOXhW37v1SkS1ko,2623
spyder/plugins/onlinehelp/pydoc_patch.py,sha256=DIQ9ahlj70UBS59ehhqF-mfogH37gY-EIIMpuZNFgSc,37910
spyder/plugins/onlinehelp/static/css/dark_pydoc.css,sha256=lM6-Q3l7FQ5N8a0Fye96CJmFgG5sEz8rV1HutiNzmw0,6373
spyder/plugins/onlinehelp/static/css/light_pydoc.css,sha256=DiEyCbNentMYA4XAsEudMHVixZ4nQrZRWOj4Shj2BQ8,2437
spyder/plugins/onlinehelp/widgets.py,sha256=yX8yysXUL-99xbHbqO3UKMGfuvQM_KBG5IcQT-wZ2DQ,14646
spyder/plugins/outlineexplorer/__init__.py,sha256=Xr13PasUKVmUeVDdy-VM8vEscFAaFSQvVwkyfhZ35i8,188
spyder/plugins/outlineexplorer/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/outlineexplorer/__pycache__/api.cpython-312.pyc,,
spyder/plugins/outlineexplorer/__pycache__/editor.cpython-312.pyc,,
spyder/plugins/outlineexplorer/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/outlineexplorer/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/outlineexplorer/__pycache__/widgets.cpython-312.pyc,,
spyder/plugins/outlineexplorer/api.py,sha256=vKkidFOCnRyvWTR0a5FTpzJb6B3YuhjmquEzBzhJMhA,9715
spyder/plugins/outlineexplorer/editor.py,sha256=K40HITqsLYnr8Je6sUYnWDiBmeMu-cQcomG9DuJAKxM,2278
spyder/plugins/outlineexplorer/main_widget.py,sha256=Vq_nNRag0JlVEuygkqmwYCb1DWHQTsC8cb4cfRACWHg,8347
spyder/plugins/outlineexplorer/plugin.py,sha256=yF_uZXvAUc8GVW3tWDrVAOVdze3VtGx9Ln9qpYMKbcc,4513
spyder/plugins/outlineexplorer/widgets.py,sha256=-vzN2v496Im12YCQlPLIdJt10VwD5rllceyLYfP4vMc,32303
spyder/plugins/plots/__init__.py,sha256=zZ2ecN7HetE2u6LYUURUS_Y-sWAizPEimglqOeRwvRs,219
spyder/plugins/plots/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/plots/__pycache__/api.cpython-312.pyc,,
spyder/plugins/plots/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/plots/api.py,sha256=XrG7wsVuWTswj_au4Kf5NgH6QiyGdurG42xdePyKEbg,293
spyder/plugins/plots/plugin.py,sha256=myt8iHBB5W9mLPHr7C17iSRWwV-ArjDJF8vRlOdznsg,2454
spyder/plugins/plots/widgets/__init__.py,sha256=hbfpjN4AXCof_qNam251EBkb4rNRMpUwG9Jit-21NCU,324
spyder/plugins/plots/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/plots/widgets/__pycache__/figurebrowser.cpython-312.pyc,,
spyder/plugins/plots/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/plots/widgets/figurebrowser.py,sha256=cTIFstcHk8DuiQLAjRHqgGd5Iql6L6zvbRCJQ9DnUcQ,40506
spyder/plugins/plots/widgets/main_widget.py,sha256=SgFyIroUZ2cVaPRwc9O0mzsxC58Fwh4A9N6zsTbxtMg,15457
spyder/plugins/preferences/__init__.py,sha256=iBOq0ZyiiyoFEU5KcxhnkuG_MD3n_f43OEK2MUYqKbM,237
spyder/plugins/preferences/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/preferences/__pycache__/api.cpython-312.pyc,,
spyder/plugins/preferences/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/preferences/api.py,sha256=godt3ogN3rgJft94KIEThf_sLxVYHtmhCSNrAwgutj4,37069
spyder/plugins/preferences/plugin.py,sha256=jRiYGTZa6VgMnwvix1iFItLAymnT5LxlsMu7IV8WYFw,13182
spyder/plugins/preferences/widgets/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/plugins/preferences/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/preferences/widgets/__pycache__/configdialog.cpython-312.pyc,,
spyder/plugins/preferences/widgets/__pycache__/container.cpython-312.pyc,,
spyder/plugins/preferences/widgets/configdialog.py,sha256=32ha6cwZOjcO2DovPuqlPHXQYSfWbcdj6hn8bSI5IZg,6580
spyder/plugins/preferences/widgets/container.py,sha256=AENtKPhxaG7IOQ12MHBceJXiiIH3iCu-1U6_bwJ0Dy0,3984
spyder/plugins/profiler/__init__.py,sha256=m2Gc-VwrZLiGvU9PG1gRAMwpf4oYsSnuKfjsJ4nWJUc,399
spyder/plugins/profiler/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/profiler/__pycache__/api.cpython-312.pyc,,
spyder/plugins/profiler/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/profiler/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/profiler/api.py,sha256=qMpoDE00fgGsabywg4VV2izr8pUC7QNtJ7AOqC823AE,445
spyder/plugins/profiler/confpage.py,sha256=WkXZJhi1Ppu73V0dr86jT0TNVzj1APvIFyMEj_Zb1m0,1482
spyder/plugins/profiler/images/profiler.png,sha256=QqxauIvkFaR-Kvb7QnTEfb-D3I635JJbLdGB-IG5sHU,1392
spyder/plugins/profiler/plugin.py,sha256=qYqc-3G8zNHF-WoNO60PfF8gBx9luOWAP8BWGZCw0vU,5215
spyder/plugins/profiler/widgets/__init__.py,sha256=hbfpjN4AXCof_qNam251EBkb4rNRMpUwG9Jit-21NCU,324
spyder/plugins/profiler/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/profiler/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/profiler/widgets/main_widget.py,sha256=ExwNAobq-mj4dtaajDZ-QFfeb9AAYUZSnTiyR1PRnMw,37263
spyder/plugins/projects/__init__.py,sha256=eTnrEwZAG-l1SLPowMgxoYqiCXjDanpOLwmpGPjxnZs,180
spyder/plugins/projects/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/projects/__pycache__/api.cpython-312.pyc,,
spyder/plugins/projects/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/projects/api.py,sha256=kcMw1IeKyygbwI-9fCkLEbtzwWBdhmquePxbgDc2TNs,6397
spyder/plugins/projects/plugin.py,sha256=BMkX5OUaS7pvEn1eMS18gbn9I3_0mYHnZ-W4PzT-U2Q,36827
spyder/plugins/projects/utils/__init__.py,sha256=S4Ox-v2XCyDxVgOm2Hom8D_80sqTHPMv_C9U5p6_dH8,179
spyder/plugins/projects/utils/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/projects/utils/__pycache__/config.cpython-312.pyc,,
spyder/plugins/projects/utils/__pycache__/cookie.cpython-312.pyc,,
spyder/plugins/projects/utils/__pycache__/watcher.cpython-312.pyc,,
spyder/plugins/projects/utils/config.py,sha256=28hlmCptWT4rLueU6Z2X8XsUMAdIg2L7_6KectX7E78,2933
spyder/plugins/projects/utils/cookie.py,sha256=vuOvSnywslpUV_9b6jKdK-6_uB4jxm0Ba7AwD5m3u8s,1422
spyder/plugins/projects/utils/watcher.py,sha256=8lGnzv3H7dvt_WPfMZAdMo_kuUUHbu9tUEncdAdkGfQ,5812
spyder/plugins/projects/widgets/__init__.py,sha256=IgXDB4jIS3CGr8CNyhvEnlk2UJFazrZuwfZ4T3I4mHI,314
spyder/plugins/projects/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/projects/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/projects/widgets/__pycache__/projectdialog.cpython-312.pyc,,
spyder/plugins/projects/widgets/__pycache__/projectexplorer.cpython-312.pyc,,
spyder/plugins/projects/widgets/__pycache__/qcookiecutter.cpython-312.pyc,,
spyder/plugins/projects/widgets/main_widget.py,sha256=NUCaxMnTRoC7PAkEvD5e9CtAb1CUIdrfXonNREsD6c4,4679
spyder/plugins/projects/widgets/projectdialog.py,sha256=GX2LKxh_EDRCJyf3xqk7Kt-yHyEIKriyjRdeXlclrcA,9379
spyder/plugins/projects/widgets/projectexplorer.py,sha256=6UWGXmkP07BOQtK05sSX1XRUx1KuIsZTVQIFzzcbh7Q,11308
spyder/plugins/projects/widgets/qcookiecutter.py,sha256=iBsdkIrxNdUYKayN_MDs6soOTC6692EmJfg-UvSQN3E,13970
spyder/plugins/pylint/__init__.py,sha256=m-v8OJmYiFy_66NRBHd3gsttnMWa8B3hp9aloZOmEb8,223
spyder/plugins/pylint/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/pylint/__pycache__/api.cpython-312.pyc,,
spyder/plugins/pylint/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/pylint/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/pylint/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/pylint/__pycache__/utils.cpython-312.pyc,,
spyder/plugins/pylint/api.py,sha256=0JOu47rgEY53owLhpQQxSqNT8Y6ZKwWoT4cg4aUDxyk,474
spyder/plugins/pylint/confpage.py,sha256=RN7rw3WyVE3Q_zdAybh3hJmFLzwska1QJk0Nq7jKqMY,2567
spyder/plugins/pylint/images/pylint.png,sha256=Zgau6jCVwxurcJgaNb7gTznsMPiaSHSdBLgVbJAY6N0,1134
spyder/plugins/pylint/main_widget.py,sha256=f3mjihMtkzd4jvRy--Ugt8MwbWsZNq8c4JuXc14x6fU,34114
spyder/plugins/pylint/plugin.py,sha256=Wyt_Zyd8w-FFt0NanS58qfpx5f0DoxQFJQW1KAWNYcw,7356
spyder/plugins/pylint/utils.py,sha256=YLCDPJ4C7p9nEUCF4gDpaAlwe0acOFR5OZDAIkzwXy0,1808
spyder/plugins/pythonpath/__init__.py,sha256=-Do2nGrZQUm-gw2GcJWnye8juJ60tRT7Pm6cvAV2vaw,243
spyder/plugins/pythonpath/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/pythonpath/__pycache__/api.cpython-312.pyc,,
spyder/plugins/pythonpath/__pycache__/container.cpython-312.pyc,,
spyder/plugins/pythonpath/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/pythonpath/__pycache__/utils.cpython-312.pyc,,
spyder/plugins/pythonpath/api.py,sha256=7SgtTijeOeQX14BjO2-giPnCL6JnPr8oFZDI5zdRbf8,262
spyder/plugins/pythonpath/container.py,sha256=ZjzWfxRo-nUy4-yZ8Nti-8P240uL0NsHfAxxEilZ_m8,8733
spyder/plugins/pythonpath/plugin.py,sha256=AvWtBBDSTiTmis-ytfeTQBn3DTj0gYMcFkN0X-_7gL0,4962
spyder/plugins/pythonpath/utils.py,sha256=sHST2QZkeoTMxC-f0e4Pivape64ArHiA57NOa-T2CZ8,1151
spyder/plugins/pythonpath/widgets/__init__.py,sha256=Rq8v7eef9zvhq7rhOjCJDKM5aMk4bVfy6wxgRV5fvBs,191
spyder/plugins/pythonpath/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/pythonpath/widgets/__pycache__/pathmanager.cpython-312.pyc,,
spyder/plugins/pythonpath/widgets/pathmanager.py,sha256=ai6oav8kXfQtBK8M4RgjH31405LBZ3fiowvRDPSiXwk,21300
spyder/plugins/run/__init__.py,sha256=w0JEPntn_oW0wD62tDCvw9z3TnW-PzjvKM-GvdfiExw,214
spyder/plugins/run/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/run/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/run/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/run/__pycache__/widgets.cpython-312.pyc,,
spyder/plugins/run/confpage.py,sha256=h86tz9yt5BaKtYGFOGlXcvgqLKq_IpWYe2wgHkyJ20U,5746
spyder/plugins/run/plugin.py,sha256=n4gZiAm0H8norSn4D_feaYMavYf8XMwF5dcA0LfVaQk,1873
spyder/plugins/run/widgets.py,sha256=MHvx-gFXqMM5faupLTvS_YOJtK7tGQKzAvnfZ5bSP9Y,20318
spyder/plugins/shortcuts/__init__.py,sha256=Bd-3TO7a87b6_7EKiZnYjfLMl8i6IpPYce7Pv-KLqVA,232
spyder/plugins/shortcuts/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/shortcuts/__pycache__/api.cpython-312.pyc,,
spyder/plugins/shortcuts/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/shortcuts/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/shortcuts/api.py,sha256=1CN-ciOzYjFYoXy6dTL08Dxf8pZ2BxGbeg0KlQ5Znas,175
spyder/plugins/shortcuts/confpage.py,sha256=aEq3lmuNzRyMrLep4JIi-13-YqSFC3ACiEGQQShkKE8,3137
spyder/plugins/shortcuts/plugin.py,sha256=svaTJuTs3c-xLObictovCnnkKL_6S2b7i28dqcbB7Ko,8726
spyder/plugins/shortcuts/widgets/__init__.py,sha256=ZQDrtFHGsiXrHybUNTtMmWv82p2iD0UqS-0db594T6I,164
spyder/plugins/shortcuts/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/shortcuts/widgets/__pycache__/summary.cpython-312.pyc,,
spyder/plugins/shortcuts/widgets/__pycache__/table.cpython-312.pyc,,
spyder/plugins/shortcuts/widgets/summary.py,sha256=Y-zxDS-1j3bD7Oas5fFSII4VHh0gh5gbNp1KhS_Ci7o,5311
spyder/plugins/shortcuts/widgets/table.py,sha256=dHf41VXLjBqnuUrX7_GCUNGg6uoDbrOsuwS1MYGyn7Q,34920
spyder/plugins/statusbar/__init__.py,sha256=G6CsGGiGEmRUp8S1N7j-xTwA1iC9lgsCmkAdkS58T-Y,292
spyder/plugins/statusbar/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/statusbar/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/statusbar/__pycache__/container.cpython-312.pyc,,
spyder/plugins/statusbar/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/statusbar/confpage.py,sha256=uk01TbrcYpb5SvPx7KNDvRAxvME8kzJ6JCuUNxHK7HI,2021
spyder/plugins/statusbar/container.py,sha256=82MHNw0TOufn9YNKxgPDm8WfcexwuY1zFxxHbVIaJ7Q,1854
spyder/plugins/statusbar/plugin.py,sha256=L-KLcLRTzxNQt1-PcvuFACKCH2g4UIn7xZ-uRrcs0e0,8135
spyder/plugins/statusbar/widgets/__init__.py,sha256=l8DFuaBu6wzEhI0RmU3Yp7-3JRuLRYcJcJr0si1_CwE,250
spyder/plugins/statusbar/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/statusbar/widgets/__pycache__/status.cpython-312.pyc,,
spyder/plugins/statusbar/widgets/status.py,sha256=5PbgwVC1d3acpzDJb6JPDVWLbGWlGQ09e4G1I6FvI-E,2088
spyder/plugins/toolbar/__init__.py,sha256=2EFuWgN_XJjjO3Iy9e0aXQi_8Bpj1sI0SqYn_KXmXuQ,226
spyder/plugins/toolbar/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/toolbar/__pycache__/api.cpython-312.pyc,,
spyder/plugins/toolbar/__pycache__/container.cpython-312.pyc,,
spyder/plugins/toolbar/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/toolbar/api.py,sha256=aW9lZV-j8lBGnhMNDpAtM_telMcsz7xUw_pY_aAtZCA,609
spyder/plugins/toolbar/container.py,sha256=yv3wmZB2QphJAsNYzNf8flzNlG0tLiay2I3eOhxCz_Q,13649
spyder/plugins/toolbar/plugin.py,sha256=MQg7zODBjQQYGp0W4j2zyCLPnZmlVuQ6Yfsr70XS1eQ,9089
spyder/plugins/tours/__init__.py,sha256=3gNSt3S-p4GA_Mm6rJgfCohdkXYpIio2Pa1KPmcMPLQ,221
spyder/plugins/tours/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/tours/__pycache__/api.cpython-312.pyc,,
spyder/plugins/tours/__pycache__/container.cpython-312.pyc,,
spyder/plugins/tours/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/tours/__pycache__/tours.cpython-312.pyc,,
spyder/plugins/tours/__pycache__/widgets.cpython-312.pyc,,
spyder/plugins/tours/api.py,sha256=x-2DnoJvMldc23Ht0mTprGOsvmIfkdBemJwwhRmoPzQ,1236
spyder/plugins/tours/container.py,sha256=QG63yExBXT-BjPKRebnqX8DdblMVPoqvv3UkpSVIhzo,3341
spyder/plugins/tours/plugin.py,sha256=mMPrJ3T560jOYCenCQDSZVCiInBBcQt1wVIs8b6eoZ8,3771
spyder/plugins/tours/tours.py,sha256=9r1UFVBcEAEa2R5bfvYs7WuqKfyOsmxGE-1hH5b_OJ8,9273
spyder/plugins/tours/widgets.py,sha256=7MZycyxjN45YOaBjtdWMJmotmr2o9x2D7k1AcL6SsAo,45975
spyder/plugins/variableexplorer/__init__.py,sha256=-11urTiDTtmBw62FgVY2SDs2a4-uX6xbxSC4QlgtOTc,281
spyder/plugins/variableexplorer/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/variableexplorer/__pycache__/api.cpython-312.pyc,,
spyder/plugins/variableexplorer/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/variableexplorer/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/variableexplorer/api.py,sha256=qDUE-2Av6YGDH18UP4Jr2s3SZxqQAmuVJC1h20yxe9A,430
spyder/plugins/variableexplorer/confpage.py,sha256=h7BVZGMnZkXhmiAfsSTFtXLvnZF5KP6y97zxc7SQMvQ,1787
spyder/plugins/variableexplorer/plugin.py,sha256=8nTbhEzvsOlP4DcFnVrj0muLUzDgRKUHEiC9bqX--cM,2500
spyder/plugins/variableexplorer/widgets/__init__.py,sha256=ylleALLS8FhWt9tZajcDePFBOfvapl6WLxRx4dyt0NE,450
spyder/plugins/variableexplorer/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/arrayeditor.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/basedialog.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/collectionsdelegate.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/dataframeeditor.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/importwizard.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/main_widget.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/namespacebrowser.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/objecteditor.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/__pycache__/texteditor.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/arrayeditor.py,sha256=NUowIk9POTlabGhfxjDBAFwe0eaFX1k1bP7H-RdJfvE,35985
spyder/plugins/variableexplorer/widgets/basedialog.py,sha256=0FBOdh6h0jWeb_pfV_F1zTzFh7Ps6tyCS1E5of4Sztg,1643
spyder/plugins/variableexplorer/widgets/collectionsdelegate.py,sha256=o8AYLkYPOowW9EX3crEx2gOJ9c0CWnUrLOnEXPsmu1I,25050
spyder/plugins/variableexplorer/widgets/dataframeeditor.py,sha256=z1hOKBLgDZSIYhcSEk7x5k0LyraCDQftmMGqfqtgt9U,56606
spyder/plugins/variableexplorer/widgets/importwizard.py,sha256=Qf8KTgsi4fmCjW0rt29hhgLkCyxypoFvGuH4e1ScYak,24520
spyder/plugins/variableexplorer/widgets/main_widget.py,sha256=4T6DFnTq0nqjn8cMf-TNVpqAT-AG1swLzlm2c26Utw0,22159
spyder/plugins/variableexplorer/widgets/namespacebrowser.py,sha256=P6v5EZ-b9FYQ2gi1HxdV-jJQ16bT5OoKQW6nRW2gpWg,12370
spyder/plugins/variableexplorer/widgets/objecteditor.py,sha256=hrAVVN2d2hZB5P7YzBWNwvjJN_e4JlAFcjwrzbly1Zc,5818
spyder/plugins/variableexplorer/widgets/objectexplorer/__init__.py,sha256=_jOwsj7AREM9DUixQsMmxexIEna8gWsqLWM6ukmVVGQ,855
spyder/plugins/variableexplorer/widgets/objectexplorer/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/objectexplorer/__pycache__/attribute_model.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/objectexplorer/__pycache__/objectexplorer.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/objectexplorer/__pycache__/toggle_column_mixin.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/objectexplorer/__pycache__/tree_item.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/objectexplorer/__pycache__/tree_model.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/objectexplorer/__pycache__/utils.cpython-312.pyc,,
spyder/plugins/variableexplorer/widgets/objectexplorer/attribute_model.py,sha256=pStIZgBq0mcU45y82eQhgaxhyZUWBoYpHcTSz9fcD_M,14708
spyder/plugins/variableexplorer/widgets/objectexplorer/objectexplorer.py,sha256=NyasGjqrP1cbm5UeKL4BJLaozeu9kwvcVDBZxCOEd4A,19076
spyder/plugins/variableexplorer/widgets/objectexplorer/toggle_column_mixin.py,sha256=g_fzqjU0PU733N_IEshnZzYWbK0GU1VJ7h9U7FWPiIo,7003
spyder/plugins/variableexplorer/widgets/objectexplorer/tree_item.py,sha256=D9-KZNiNBLhbGdCZrxSZH_RzC7_wBLsQteqd8EWLquE,3600
spyder/plugins/variableexplorer/widgets/objectexplorer/tree_model.py,sha256=5qt_fsCkwjtDk1q8liZKN7PjNJauMH1KbUrj0zc2afU,23252
spyder/plugins/variableexplorer/widgets/objectexplorer/utils.py,sha256=zqPJ_oTIHdiGC0DP3RBESOPcDswJGpiWxt2PRfknlO8,853
spyder/plugins/variableexplorer/widgets/texteditor.py,sha256=s462z3bw0NplPgscsM_tY-V2vK__AU2GwCZYton4dxU,4866
spyder/plugins/workingdirectory/__init__.py,sha256=im5-Xg-NFyeda5y3_SfHcbnPsClylcMFKwDX8pYrJLs,261
spyder/plugins/workingdirectory/__pycache__/__init__.cpython-312.pyc,,
spyder/plugins/workingdirectory/__pycache__/confpage.cpython-312.pyc,,
spyder/plugins/workingdirectory/__pycache__/container.cpython-312.pyc,,
spyder/plugins/workingdirectory/__pycache__/plugin.cpython-312.pyc,,
spyder/plugins/workingdirectory/confpage.py,sha256=uARiRQRRITXOPR2Jw7Z5VeLidLoRZpUKkJqN0BpKzhA,4601
spyder/plugins/workingdirectory/container.py,sha256=KU51DkYBb5sFJBK6baxsqP6_EBPN-7OCaH8F7cFd1EU,10426
spyder/plugins/workingdirectory/plugin.py,sha256=CUxShVmhG2SHlREJDYp19y8u3ahktOxSfedVmQPmU64,9042
spyder/py3compat.py,sha256=T8eQyFew4LObmO4x594XMxuOJuK4XPE83qmYi3mxBzM,2876
spyder/pyplot.py,sha256=lGwErbIdVN-JGUCgtfFmetuQwPvH9c7jyvYB8LpjHfI,481
spyder/requirements.py,sha256=y1GAUoilVP7-lrfLefJwJ8sxMyxQMzoSINzrsd7at9U,1811
spyder/utils/__init__.py,sha256=iBGJVAcqtEVinqiarhjGfG_hRV164ID0LRKdvvaEPXk,379
spyder/utils/__pycache__/__init__.cpython-312.pyc,,
spyder/utils/__pycache__/bsdsocket.cpython-312.pyc,,
spyder/utils/__pycache__/clipboard_helper.cpython-312.pyc,,
spyder/utils/__pycache__/color_system.cpython-312.pyc,,
spyder/utils/__pycache__/conda.cpython-312.pyc,,
spyder/utils/__pycache__/debug.cpython-312.pyc,,
spyder/utils/__pycache__/encoding.cpython-312.pyc,,
spyder/utils/__pycache__/environ.cpython-312.pyc,,
spyder/utils/__pycache__/fixtures.cpython-312.pyc,,
spyder/utils/__pycache__/icon_manager.cpython-312.pyc,,
spyder/utils/__pycache__/image_path_manager.cpython-312.pyc,,
spyder/utils/__pycache__/installers.cpython-312.pyc,,
spyder/utils/__pycache__/misc.cpython-312.pyc,,
spyder/utils/__pycache__/palette.cpython-312.pyc,,
spyder/utils/__pycache__/programs.cpython-312.pyc,,
spyder/utils/__pycache__/pyenv.cpython-312.pyc,,
spyder/utils/__pycache__/qstringhelpers.cpython-312.pyc,,
spyder/utils/__pycache__/qthelpers.cpython-312.pyc,,
spyder/utils/__pycache__/registries.cpython-312.pyc,,
spyder/utils/__pycache__/sourcecode.cpython-312.pyc,,
spyder/utils/__pycache__/stringmatching.cpython-312.pyc,,
spyder/utils/__pycache__/stylesheet.cpython-312.pyc,,
spyder/utils/__pycache__/switcher.cpython-312.pyc,,
spyder/utils/__pycache__/syntaxhighlighters.cpython-312.pyc,,
spyder/utils/__pycache__/system.cpython-312.pyc,,
spyder/utils/__pycache__/test.cpython-312.pyc,,
spyder/utils/__pycache__/vcs.cpython-312.pyc,,
spyder/utils/__pycache__/windows.cpython-312.pyc,,
spyder/utils/__pycache__/workers.cpython-312.pyc,,
spyder/utils/bsdsocket.py,sha256=L3TnLaNQbMOPId6rD2Hpmznh9ASTa65-sAKnEk_RZgk,5920
spyder/utils/check-git.sh,sha256=Jl0xnAy4qRQS2Of-g00x2NVhw0l2yTpbjekBz07oh1M,215
spyder/utils/clipboard_helper.py,sha256=SwW2dOcKrmnSfpd6OBehKtvIpWMbxRQTtsnbKFjz77I,1744
spyder/utils/color_system.py,sha256=xeaX4EuUtVIG5YdIZiZaRxhK3b26LoMZw3NTJ53VIKs,2173
spyder/utils/conda.py,sha256=MzWfk-ZrNtXxAawcFZ4FTK35kFduXcSulDSjSgtLvis,6306
spyder/utils/debug.py,sha256=Du4pCjmfO502ZpDaSJg8ctS16FM198_5RAZVIHTnUDk,4413
spyder/utils/encoding.py,sha256=dzlwZQTFDzqRtu4eX82nG_DqcAFFJyiJoZONvBON5YI,10641
spyder/utils/environ.py,sha256=YVcKDKujUT4qqvPFiJzipZr91r5rv_Y-fTSZeZTCRSg,8205
spyder/utils/external/LICENSE.txt,sha256=1Ca5j-pSBSnDK5bySIrCqYdRvD47gYp5ROJ17feG3es,1244
spyder/utils/external/README.rst,sha256=KCYhWJTrL7WwhvLC9y_urF3DYvmCCnVD-1itSO2Jupw,2586
spyder/utils/external/__init__.py,sha256=_ooPOqQTT8_2IzUNLI1twGB270uuiU6mvZNojPiHj5M,992
spyder/utils/external/__pycache__/__init__.cpython-312.pyc,,
spyder/utils/external/__pycache__/github.cpython-312.pyc,,
spyder/utils/external/__pycache__/lockfile.cpython-312.pyc,,
spyder/utils/external/binaryornot/LICENSE.txt,sha256=zbvaLXOCez61U0AibxXrevlrmqrBl-OVHDVgG36s-Yc,1479
spyder/utils/external/binaryornot/__init__.py,sha256=yviraT4Zk4cE0KsPPytEIvzpSkf0DrhuV1-EXTstRLA,599
spyder/utils/external/binaryornot/__pycache__/__init__.cpython-312.pyc,,
spyder/utils/external/binaryornot/__pycache__/check.cpython-312.pyc,,
spyder/utils/external/binaryornot/__pycache__/helpers.cpython-312.pyc,,
spyder/utils/external/binaryornot/check.py,sha256=ABfxg3Y8iXcqg_pSChXN5c__EQ-Q6muEDbWEurjw6CI,1256
spyder/utils/external/binaryornot/helpers.py,sha256=oXmzM82w0wtHdmDgXRijs56hm4uowEn9jCNLjLV-CZ8,5125
spyder/utils/external/dafsa/__init__.py,sha256=hHRlvEuEPUv21ftcdw65cfMMArCCOaS7CdYIUGXaxvs,592
spyder/utils/external/dafsa/__pycache__/__init__.cpython-312.pyc,,
spyder/utils/external/dafsa/__pycache__/dafsa.cpython-312.pyc,,
spyder/utils/external/dafsa/dafsa.py,sha256=n4Qjr4y6cZhJPlNbh01mfkhjWnxdwkxyflioOvSZREo,31793
spyder/utils/external/github.py,sha256=lnlM7ZCxPOY7FDzwJ3_UbO_n1fyX1RxXbcNSz3MzaQ8,9885
spyder/utils/external/lockfile.py,sha256=kikp4I94Bz9yoTq7MZPgOf4lwrbDhIledsl4IPRKDCU,8316
spyder/utils/external/pybloom_pyqt/__init__.py,sha256=xHLr7fouqPrUCJdppvFXJc8kqkxVasZqDtXz2wM_BEU,71
spyder/utils/external/pybloom_pyqt/__pycache__/__init__.cpython-312.pyc,,
spyder/utils/external/pybloom_pyqt/__pycache__/pybloom.cpython-312.pyc,,
spyder/utils/external/pybloom_pyqt/__pycache__/utils.cpython-312.pyc,,
spyder/utils/external/pybloom_pyqt/pybloom.py,sha256=IbGpNSozxovFGWOM9igTZzxYSO33hgSBi4Ev4bkg4Gk,14802
spyder/utils/external/pybloom_pyqt/utils.py,sha256=mc36yCyRaZ6WEC5DNzMbuH2jD5JasLObGTBgVkgopLo,641
spyder/utils/fixtures.py,sha256=qcccxcceiHZxr6yIu_odBCA_Ypou9em7ib0BIez_qqs,869
spyder/utils/icon_manager.py,sha256=0R1G47DnTDJfiDWpLVqwGvkDR0zq9Z1VMClMCZCAvvo,38156
spyder/utils/image_path_manager.py,sha256=k8qfA2e1foZMYhbau8s0Lt30DfWVNTGxXio3DgaoCKM,2184
spyder/utils/installers.py,sha256=SnhJ02DhuEB24peozWQwRd2AQR8eYQJK63jgCfvnwvg,2148
spyder/utils/introspection/__init__.py,sha256=w-iu78JrF_IBkja5n3aqEnBuK8xY8Ra2jtp2qHmRzxE,374
spyder/utils/introspection/__pycache__/__init__.cpython-312.pyc,,
spyder/utils/introspection/__pycache__/module_completion.cpython-312.pyc,,
spyder/utils/introspection/__pycache__/rope_patch.cpython-312.pyc,,
spyder/utils/introspection/module_completion.py,sha256=Se3aYXQF-sbqg4NytukJ5cRz1S_V4MlJySJN1UlacHk,2316
spyder/utils/introspection/rope_patch.py,sha256=zMKA4SrA7VSLJZtxkcmjj_l323EP1xqWSqchupRVIWg,9396
spyder/utils/misc.py,sha256=WCJV3aCDxpDUM80fSEBd6h0LHpoO3LpPAhKj2wdp5o8,9280
spyder/utils/palette.py,sha256=bmiz-G2aD336hc4K94KbYFKg0ADdvk_jRsVOloHUBeo,4150
spyder/utils/programs.py,sha256=AT44f7A008PHoCXDNw6TVWptY935DQKNYyTE_hYvbdQ,36395
spyder/utils/pyenv.py,sha256=K-Qn5GfsyP9tHggjhlfgVL-1oPD463oLyFxKh9Rsgko,1670
spyder/utils/qstringhelpers.py,sha256=cbMmhH2eABXODwkdmMstTUYvO1EXYD9xvQAODs0BZF4,566
spyder/utils/qthelpers.py,sha256=Vszhi12VUKTTV1AfPb9YiQyho75yeq2-KpJv2dwFrIA,28012
spyder/utils/registries.py,sha256=oS5Ft1L1StOkYXXOHlEQL5-NZHWZaWJv8eImNvII3Go,7139
spyder/utils/snippets/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder/utils/snippets/__pycache__/__init__.cpython-312.pyc,,
spyder/utils/snippets/__pycache__/ast.cpython-312.pyc,,
spyder/utils/snippets/__pycache__/lexer.cpython-312.pyc,,
spyder/utils/snippets/__pycache__/nodes.cpython-312.pyc,,
spyder/utils/snippets/__pycache__/parser.cpython-312.pyc,,
spyder/utils/snippets/ast.py,sha256=2g460bC1E2U4aX8SS3iFjBb8tdGQZXy7MQ6wOUGYGB8,6160
spyder/utils/snippets/lexer.py,sha256=liBzje00-RSs75JkR4S4paUaB-9c2wrB9cEKTP7Pvj4,2991
spyder/utils/snippets/nodes.py,sha256=dlyJoVpammLBcXRZBD2oS-Nobrss1f1cTS9nnp8oJzo,16370
spyder/utils/snippets/parser.py,sha256=ahqgKySb4Y5R-B4Ilv9mOxGn7Cw-FOsTUQkoOb6vESU,7462
spyder/utils/sourcecode.py,sha256=VwfA44YsQJcw2DsVHKrk7XCEG7-ckaooHyG1kttQ3Y0,7871
spyder/utils/stringmatching.py,sha256=riSl1EzuSMB3kvd_-Zzcb1p7HiG16vx21AL-Gcyqydw,10423
spyder/utils/stylesheet.py,sha256=ZhNtBZ0M-hohf0YgFrtOhNMYtsgvZPjeUB5nXzvBz4A,12232
spyder/utils/switcher.py,sha256=zAi6kIuKrRzMnjyO450kqEPf4pjN18uoOHX0WwncbHA,5099
spyder/utils/syntaxhighlighters.py,sha256=d4LU2Xl4KmZX4zQqOC_4_SHssvgoNcGoaV2Wz56mRgU,83862
spyder/utils/system.py,sha256=bGXuWkYOaD7iL7c7KC5O18mT2fIZc5sAUEK0j2-py-g,2119
spyder/utils/test.py,sha256=JnTusOJ3RCkAhrheRgJqadQC658jFcGtG3cHnIcOpTI,735
spyder/utils/vcs.py,sha256=Ev4jYWSK0UnvYhL3ItANwZiX30j7TkBc_5JC-TVMtGQ,6939
spyder/utils/windows.py,sha256=UV3Ob4a0Bzeucscr8nFztymeDf64jQ0WaUwWStcs3h4,1463
spyder/utils/workers.py,sha256=RY8qdmR4CN6G70413pk4x_VS3ArNteE0nC7VnS1F89o,11649
spyder/widgets/__init__.py,sha256=SrjPh7NsV0-La5XIyvv7ucnV39metEfYTpXEXt43TIM,524
spyder/widgets/__pycache__/__init__.cpython-312.pyc,,
spyder/widgets/__pycache__/about.cpython-312.pyc,,
spyder/widgets/__pycache__/arraybuilder.cpython-312.pyc,,
spyder/widgets/__pycache__/browser.cpython-312.pyc,,
spyder/widgets/__pycache__/calltip.cpython-312.pyc,,
spyder/widgets/__pycache__/collectionseditor.cpython-312.pyc,,
spyder/widgets/__pycache__/colors.cpython-312.pyc,,
spyder/widgets/__pycache__/comboboxes.cpython-312.pyc,,
spyder/widgets/__pycache__/dependencies.cpython-312.pyc,,
spyder/widgets/__pycache__/dock.cpython-312.pyc,,
spyder/widgets/__pycache__/findreplace.cpython-312.pyc,,
spyder/widgets/__pycache__/helperwidgets.cpython-312.pyc,,
spyder/widgets/__pycache__/mixins.cpython-312.pyc,,
spyder/widgets/__pycache__/onecolumntree.cpython-312.pyc,,
spyder/widgets/__pycache__/reporterror.cpython-312.pyc,,
spyder/widgets/__pycache__/simplecodeeditor.cpython-312.pyc,,
spyder/widgets/__pycache__/switcher.cpython-312.pyc,,
spyder/widgets/__pycache__/tabs.cpython-312.pyc,,
spyder/widgets/__pycache__/waitingspinner.cpython-312.pyc,,
spyder/widgets/about.py,sha256=lw7udBNYOY0gE8ZsxAACI9Ax9gAhiuMjFBixwErWPzg,10763
spyder/widgets/arraybuilder.py,sha256=2ghDTNyTyAvS-MI-WOFc1lGBQrAUh1tx9o0x5k5_kyk,13284
spyder/widgets/browser.py,sha256=GmphPdTCV6LJuz9hSCMVgfK66K_O4flbXQXEQVOLrfU,20226
spyder/widgets/calltip.py,sha256=Wo9taLmPTiczxkZyElLgruyOmBVFy-Jtk2bp9Hnjx_A,21276
spyder/widgets/collectionseditor.py,sha256=83qXZcfyGZ1WrtZfBhQTKIRXKqMNEnSAJ526tx9GRf8,70135
spyder/widgets/colors.py,sha256=L3wMhFDpw1veXvJBvTwj2pOO2xcHI7XKYxYW4vH9tGM,2907
spyder/widgets/comboboxes.py,sha256=cfSA6-EJy6n5sznjCE3xPRjfd7_dn58NBpXy02V_Dc4,15093
spyder/widgets/dependencies.py,sha256=HFEBW3UD-JRCj9uPNhYBT_2ca7YNATzw2256XB2j3_s,5389
spyder/widgets/dock.py,sha256=QuD72sol3ewEeurEGF3VuZ5MPohM-eqONYi_dHK0Zfo,10883
spyder/widgets/findreplace.py,sha256=e9zrdBLU3DdYXEUcZI_M7FlP3QRf3x2jSsN9it52Cqo,31681
spyder/widgets/github/LICENSE.txt,sha256=NPcnQWLECysVWgvg5QNS41xQA_gK84CWYeRkimgX39E,1163
spyder/widgets/github/__init__.py,sha256=gfywY_014yHfvCIhzPRZBAv9imzkUFWo0Ry0gJYx3Tw,555
spyder/widgets/github/__pycache__/__init__.cpython-312.pyc,,
spyder/widgets/github/__pycache__/backend.cpython-312.pyc,,
spyder/widgets/github/__pycache__/gh_login.cpython-312.pyc,,
spyder/widgets/github/backend.py,sha256=AHrblrC7agnOOzEM124yir4ddjwvORx5ay2uRsXI4Ak,9225
spyder/widgets/github/gh_login.py,sha256=JR9mtr9K7iX1iLAqMn4MlhIbZbz4VRER10xkv6cVRpI,6040
spyder/widgets/helperwidgets.py,sha256=gg98aJh14QLGvOC2C6Xx9-ARz2q3oYsDhI39clOnEPc,13957
spyder/widgets/mixins.py,sha256=oC03QW7QY5TYgaY3vTY9fgZoEg_I7xUg_rs-7XZluuI,64129
spyder/widgets/onecolumntree.py,sha256=rocprfGbqoMts_E55QohZgDzmrGYnzryMYoICHbVlFY,10438
spyder/widgets/reporterror.py,sha256=8NO8c92NiqbVbIrQcz0Cx6Lzm1MrVgaK-_eSGETkcSM,17572
spyder/widgets/simplecodeeditor.py,sha256=aYEUj15WcQqb-173Q-l6LDi_b55O6doq0N-jQLa-4hA,18096
spyder/widgets/switcher.py,sha256=A_bzT8_ik0pf57wLh70DXItaw1Pzn2LLUbpAVCOBYns,34216
spyder/widgets/tabs.py,sha256=SOs9tvdHpZttDsRDzZcWI3dtchQJaXDgEeC26l5noQI,18610
spyder/widgets/waitingspinner.py,sha256=f-a5owu_aUx2hNc8yowUSYV1DqlNQRzGRLKfPjBYHoQ,8983
spyder/windows/spyder.ico,sha256=Ug7AXEO55uUnaRmuuzoJVOwZpeB6_tE1vMzp0F49rr4,100763
spyder/windows/spyder_light.ico,sha256=LhU-cleWAGpALh-Zffwuxg88ngJlvKSVQU0E2bfNs7k,99495
spyder/workers/__init__.py,sha256=hbfpjN4AXCof_qNam251EBkb4rNRMpUwG9Jit-21NCU,324
spyder/workers/__pycache__/__init__.cpython-312.pyc,,
spyder/workers/__pycache__/updates.cpython-312.pyc,,
spyder/workers/updates.py,sha256=38Gmj-tJk7c2Dwiu0dZ6mujbH2J--NWvcEUvAGIbz5w,10749
