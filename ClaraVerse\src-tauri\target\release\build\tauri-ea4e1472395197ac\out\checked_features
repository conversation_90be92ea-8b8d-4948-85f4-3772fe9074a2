custom-protocol,custom-protocol,updater,api-all,fs-all,fs-read-file,fs-write-file,fs-read-dir,fs-copy-file,fs-create-dir,fs-remove-dir,fs-remove-file,fs-rename-file,fs-exists,window-all,window-create,window-center,window-request-user-attention,window-set-resizable,window-set-maximizable,window-set-minimizable,window-set-closable,window-set-title,window-maximize,window-unmaximize,window-minimize,window-unminimize,window-show,window-hide,window-close,window-set-decorations,window-set-always-on-top,window-set-content-protected,window-set-size,window-set-min-size,window-set-max-size,window-set-position,window-set-fullscreen,window-set-focus,window-set-icon,window-set-skip-taskbar,window-set-cursor-grab,window-set-cursor-visible,window-set-cursor-icon,window-set-cursor-position,window-set-ignore-cursor-events,window-start-dragging,window-print,shell-all,shell-execute,shell-sidecar,shell-open,shell-execute,shell-open-api,dialog-all,dialog-open,dialog-save,dialog-message,dialog-ask,dialog-confirm,http-all,http-request,cli,notification-all,global-shortcut-all,os-all,path-all,protocol-all,protocol-asset,process-all,process-relaunch,process-exit,clipboard-all,clipboard-write-text,clipboard-read-text,app-all,app-show,app-hide