Included Software, Images and Icons
###################################


General Information
===================


Spyder incorporates code and image assets from various external sources,
each of which is covered by its own license and copyright notice.
These files are listed here with their corresponding authors,
permission notices, disclaimers, and copyright statements; these are also
included in the files' directory and/or their headers, as appropriate.

Any file not listed here is covered by <PERSON>der's MIT (Expat) license,
described in the LICENSE.txt file in this repository and Spyder's __init__.py.

Images, icons and font gyphs that include trademarked elements are used for
informational purposes only, and do not necessarily constitute any endorsement,
association or ownership by or of the marks or their respective owners.

All trademarks are the property of their respective owners.


-------------------------------------------------------------------------------



Instructions for Adding a New Entry
-----------------------------------


All files or groups of files, including source code, images, icons, and other
assets, that originate from projects outside of the Spyder organization
(regardless of the license), must be documented here.
Please see the Contributing Guide, CONTRIBUTING.md, for full details on the
criteria for inclusion and other steps you need to take to ensure transparency,
legal clarity and license compliance.
Always check with the Spyder team (on Github, Gitter or the Google Group)
before adding content from an external project, and only do so when necessary.

Add one entry per code repo, icon set or font face, or other cohesive
unit within which the source material is organized and distributed as.
New entries for icons should be in order of the number of icons used;
other entries should be in order of inclusion.

Pay careful attention to the number of blank lines before and after headings,
horizontal rules, sections, and other elements.
This helps keep this document semi-machine-readable and helps avoid conflicts.
Break this document at 79 characters, and at periods (sentences) unless the
text is copied directly from an external file (license, header, etc).
Follow the project standards here and for added files (see CONTRIBUTING.md).

Please check the list below to ensure your project's license is on it.
If not, you'll need to add it (but double-check it with us first).
Add its name here, and copy the plain text (broken at > 80 characters) in the
final section at the end of this document, at the end of the list.


Source code licenses:

* MIT (Expat) License
* 3-Clause ("Modified") BSD License
* Python Software Foundation License Version 2
* Apache License 2.0


Additional asset licenses:

* GNU LGPL 2.1 (with GPL 2.0)
* GNU LGPL 3.0 (with GPL 3.0)
* Creative Commons Attribution 3.0
* SIL Open Font License 1.1


-------------------------------------------------------------------------------



New Entry Template
------------------


# Fields that should be replaced are marked with <this-is-a-field>.
# Some fields are optional, indicated with [<option-one> OR <two>]
# Comments, indicated with # (like this one) are only for reference and should
# not be included in the final product.

# Beginning of template #


[<Project-Name> OR <Named-Subcomponent> from <Project-Name>] <version-used>
-------------------------------------------------------------------------


Copyright <year(s)> <author-name> [(<email-or-website>) IF provided]
# Use the author-provided form, if given, correcting obvious syntax errors
# (e.g. remove commas, false "All rights reserved" statements, etc)
# List additional copyright lines here, if provided
# If modified by Spyder contributors after adding, additionally list:
[Copyright (c) <current-year>- Spyder Project Contributors (see AUTHORS.txt)]


Author: <author-name> | <author-email> | <author-website>
Site: <main-project-website-other-than-github>  # Unless no site exists
[Source: <main-page-of-github-etc-repository>]
[Download: <page-to-download-icons-or-assets>]  # If not the same as above
License: <full-name-of-license-including-version>
<canonical-url-to-authoritative-license-text>

[No modifications made. OR Modifications made to [each file] to <purpose>.]


<license-summary-text-and-notices-from-original-project>
# If the project includes a NOTICE.txt file, copy its contents verbatim here.
# Additionally, include any non-canonical text in the project's license file
# (i.e. that not part of the actual license itself, as reproduced below)
# Any text from an AUTHORS file, any file header text aside from the copyright,
# Any legal-related text in the readme (credits, acknowledgments, license, etc)
# and any other legal text, disclaimer, notice, attribution etc. present.
# Trim all line breaks to no more than one consecutive blank line


[<project-name> is used in Spyder under the terms of the <specific-licensed>.
See below for the full text of the <specific-license>.]  # If dual-licensed
# Pick the license closest to Spyder's, or otherwise the most permissive

# Include any additional custom explanatory text describing usage of the
# project/component/asset with Spyder here

See below for the full text of the <full-name-of-license-including-version>.

The current <project-name> license can be viewed at:
<direct-link-to-license-file-on-github-etc>

[Later versions of the <full-name-of-license] can be viewed at:
<link-to-license-creator-site>]  # For "any later version" licenses, like LGPL

[The current license summary can be viewed at:
<link-to-license-summary-on-project-or-license-creator-page>]  # If present

[The current version of the original file[s] can be viewed at:
<link-to-github-etc-of-original-file>]  # If only specific file(s) used
<links-to-further-files-if-applicable]


Files covered:

<path/to/file/in/spyder/repo.py>  # Should always be relative to the repo root


-------------------------------------------------------------------------------

# End of template #


===============================================================================




Source Code
===========

Electrum
--------


Copyright (C) 2018 The Electrum Developers


Author: Thomas Voegtlin and others | <EMAIL>
https://github.com/spesmilo/electrum/graphs/contributors
Site: https://electrum.org/
Source: https://github.com/spesmilo/electrum
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to add ellipsis to displayed text in a QLineEdit.


See below for the full text of the MIT License.

The current Electrum license can be viewed at:
https://github.com/spesmilo/electrum/blob/master/LICENCE

The current versions of the original files can be viewed at:
https://github.com/spesmilo/electrum/blob/master/electrum/gui/qt/amountedit.py


Files covered:

spyder/widgets/helperwidgets.py


-------------------------------------------------------------------------------



QDarkStyleSheet
---------------


Copyright (c) 2013- Colin Duquesnoy and others (see QDarkStyleSheet/AUTHORS.rst)


Author: Colin Duquesnoy and others | <EMAIL>
https://github.com/ColinDuquesnoy/QDarkStyleSheet/graphs/contributors
Site: https://github.com/ColinDuquesnoy/QDarkStyleSheet/
Source: https://github.com/ColinDuquesnoy/QDarkStyleSheet
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to adapt each file to Spyder.


See below for the full text of the MIT License.

The current QDarkStyleSheet license can be viewed at:
https://github.com/ColinDuquesnoy/QDarkStyleSheet/blob/master/LICENSE.rst

The current versions of the original files can be viewed at:
https://github.com/ColinDuquesnoy/QDarkStyleSheet/blob/master/qdarkstyle/__init__.py


Files covered:

spyder/app/utils.py


-------------------------------------------------------------------------------



MathJax 2.7.5
-------------


Copyright (c) 2009-2012 Design Science, Inc.


Author: The MathJax Consortium | Sponsored by the AMS, SIAM and Design Science
Site: https://www.mathjax.org/
Source: https://github.com/mathjax/MathJax/
License: Apache License 2.0 | https://www.apache.org/licenses/LICENSE-2.0

No individual files modified; subset of original files included.


Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.


See below for the full text of the Apache License 2.0.

The current MathJax license can be viewed at
https://github.com/mathjax/MathJax/blob/master/LICENSE


Files covered:

spyder/plugins/help/utils/js/mathjax/*


-------------------------------------------------------------------------------



jQuery Javascript Library v1.7.2
--------------------------------


Copyright (c) 2011 John Resig, https://jquery.com/


Author: John Resig and contributors | jQuery Foundation
Site: https://jquery.com/
Source: https://github.com/jquery/jquery/
License: Dual licensed under the
    * MIT (Expat) License | https://opensource.org/licenses/MIT
    * GNU General Public License Version 2
      https://www.gnu.org/licenses/old-licenses/gpl-2.0.en.html

No modifications made.


https://jquery.org/license

Includes Sizzle.js (see below)
https://sizzlejs.com/

Date: Wed Mar 21 12:46:34 2012 -0700

This software consists of voluntary contributions made by many
individuals. For exact contribution history, see the revision history
available at https://github.com/jquery/jquery/

The MIT License is simple and easy to understand and it places almost
no restrictions on what you can do with the Project.

You are free to use the Project in any other project (even commercial projects)
as long as the copyright header is left intact.


jQuery is used in Spyder under the terms of the MIT (Expat) license.
See below for the full text of the MIT (Expat) license.

A copy of the GNU General Public License Version 2 is included for reference.

The current license summary can be viewed at:
https://jquery.org/license

The present license text can be referenced at:
https://github.com/jquery/jquery/blob/master/LICENSE.txt


Files covered:

spyder/plugins/help/utils/js/jquery.js


-------------------------------------------------------------------------------



Sizzle CSS Selector Engine 1.7.2
--------------------------------


Copyright (c) 2011 The Dojo Foundation and other contributors


Author: John Resig and contributors | jQuery Foundation
Site: https://sizzlejs.com/
Source: https://github.com/jquery/sizzle/
License: Triple licensed under the
    * MIT (Expat) License | https://opensource.org/licenses/MIT
    * BSD (3-clause) License | https://opensource.org/licenses/BSD-3-Clause
    * GNU General Public License Version 2
      https://www.gnu.org/licenses/old-licenses/gpl-2.0.en.html

No modifications made.


More information: https://sizzlejs.com/

Date: Wed Mar 21 12:46:34 2012 -0700

This software consists of voluntary contributions made by many
individuals. For exact contribution history, see the revision history
available at https://github.com/jquery/sizzle

The MIT License is simple and easy to understand and it places almost
no restrictions on what you can do with the Project.

You are free to use the Project in any other project (even commercial projects)
as long as the copyright header is left intact.


Sizzle is used in Spyder under the terms of the MIT (Expat) license.
See below for the full text of the MIT (Expat) license.

The GNU General Public License Version 2 and BSD 3-clause license are below.

The current JS Foundation license summary can be viewed at:
https://jquery.org/license

The present license text can be referenced at:
https://github.com/jquery/sizzle/blob/master/LICENSE.txt


Files covered:

spyder/plugins/help/utils/js/jquery.js


-------------------------------------------------------------------------------



Modules from PyQode 2.11.0
--------------------------


Copyright (c) 2013-2016 Colin Duquesnoy and others (see pyqode/AUTHORS.rst)
Copyright (c) 2016- Spyder Project Contributors (see AUTHORS.txt)


Author: Colin Duquesnoy and others | <EMAIL>
https://github.com/pyQode/pyqode.core/graphs/contributors
Site: https://github.com/pyQode/pyQode
Source: https://github.com/pyQode/pyqode.core
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to adapt each file to Spyder.


Most of the pyqode packages are licensed under the MIT license.

Main Authors: <AUTHORS>

Contributors:

Andres Granada (@ogranada)
Fynn Mazurkiewicz <FynnMazurkiewicz@'Googles-Email-Service'.com>


See below for the full text of the MIT License.

The current PyQode license can be viewed at:
https://github.com/pyQode/pyqode.core/blob/master/LICENSE

The current versions of the original files can be viewed at:
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/api/folding.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/api/decoration.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/api/manager.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/api/mode.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/api/panel.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/api/utils.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/managers/decorations.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/managers/modes.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/managers/panels.py
https://github.com/pyQode/pyqode.core/blob/master/pyqode/core/panels/folding.py


Files covered:

spyder/api/editorextension.py
spyder/api/manager.py
spyder/api/panel.py
spyder/api/widgets/mixins.py
spyder/plugins/editor/api/decoration.py
spyder/plugins/editor/extensions/manager.py
spyder/plugins/editor/panels/codefolding.py
spyder/plugins/editor/panels/manager.py
spyder/plugins/editor/utils/decoration.py
spyder/plugins/editor/utils/editor.py


-------------------------------------------------------------------------------



QCrash 0.2.0
------------


Copyright (c) 2016 Colin Duquesnoy
Copyright (c) 2018- Spyder Project Contributors (see AUTHORS.txt)

Author: Colin Duquesnoy | https://github.com/ColinDuquesnoy
Source: https://github.com/ColinDuquesnoy/QCrash
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to each file adapt QCrash to Spyder and improve it further.


QCrash is licensed under the MIT license.


Also includes an unmodified copy of Githubpy, listed below.

See below for the full text of the MIT (Expat) license.

The current QCrash license can be viewed at:
https://github.com/ColinDuquesnoy/QCrash/blob/master/LICENSE


Files covered:

spyder/widgets/github/backend.py
spyder/widgets/github/gh_login.py
spyder/widgets/github/tests/test_github_backend.py


-------------------------------------------------------------------------------



Githubpy 1.1.1
--------------


Copyright 2014 Michael Liao (<EMAIL>)


Author: Micheal Liao | <EMAIL> | https://www.liaoxuefeng.com/
Site: https://github.liaoxuefeng.com/githubpy/
Source: https://github.com/michaelliao/githubpy
License: Apache License Version 2.0
https://www.apache.org/licenses/LICENSE-2.0

No modifications made.


githubpy is distributed under Apache License 2.0. Enjoy!


Also included unmodified as part of QCrash, listed above.

See below for the full text of the Apache License Version 2.0.

The current Githubpy license can be viewed at:
https://github.com/michaelliao/githubpy/blob/master/LICENSE.txt


Files covered:

spyder/utils/external/github.py


-------------------------------------------------------------------------------



Sphinxify (from the Sage Project)
---------------------------------


Copyright (C) 2009 Tim Dumol <<EMAIL>>
Copyright (C) 2010- Spyder Project Contributors (see AUTHORS.txt)


Author: Tim Joseph Dumol | <EMAIL> | https://www.timdumol.com/
Site: https://www.sagemath.org/
Source: https://github.com/sagemath/sagenb
License: BSD (3-clause) License | https://opensource.org/licenses/BSD-3-Clause

Modifications made to each file adapt Sphinxify to Spyder and fix bugs.


Distributed under the terms of the BSD License.


See below for the full text of the BSD (3-clause) License.

The current version of the original file can be viewed at:
https://github.com/sagemath/sagenb/blob/master/sagenb/misc/sphinxify.py


Files covered:

spyder/plugins/help/utils/conf.py
spyder/plugins/help/utils/sphinxify.py
spyder/plugins/help/utils/templates/layout.html


-------------------------------------------------------------------------------



IPython
-------


Copyright (c) 2001 Nathaniel Gray <<EMAIL>>
Copyright (c) 2001 Janko Hauser <<EMAIL>>
Copyright (c) 2001-2007 Fernando Perez <<EMAIL>>
Copyright (c) 2008- IPython Development Team


Author: IPython Development Team (see above) | https://ipython.org/
Site: https://ipython.readthedocs.io/en/stable/
Source: https://github.com/ipython/ipython
License: BSD (3-clause) License | https://opensource.org/licenses/BSD-3-Clause

Modifications made to below files integrate with Spyder/QtPy and fix bugs.


IPython is licensed under the terms of the Modified BSD License (also known as
New or Revised or 3-Clause BSD).

Fernando Perez began IPython in 2001 based on code from Janko Hauser
<<EMAIL>> and Nathaniel Gray <<EMAIL>>.  Fernando is still
the project lead.

The IPython Development Team is the set of all contributors to the IPython
project. This includes all of the IPython subprojects.

The core team that coordinates development on GitHub can be found here:
https://github.com/ipython/.

IPython uses a shared copyright model. Each contributor maintains copyright
over their contributions to IPython. But, it is important to note that these
contributions are typically only changes to the repositories. Thus, the IPython
source code, in its entirety is not the copyright of any single person or
institution.  Instead, it is the collective copyright of the entire IPython
Development Team.  If individual contributors want to maintain a record of what
changes/contributions they have specific copyright on, they should indicate
their copyright in the commit message of the change, when they commit the
change to one of the IPython repositories.


Spyder's Console is ultimately a frontend to the IPython interpreter,
which is used to run all interactive commands, scripts and executable code.

IPython frontend components were developed into Jupyter QtConsole; see below.

In addition to the list below, modified IPython code snippits included in:

setup.py
spyder/plugins/editor/extensions/closequotes.py
spyder/plugins/ipythonconsole/plugin.py
spyder/utils/programs.py

See below for the full text of the Modified BSD (3-clause) License.

The current IPython license can be viewed at:
https://github.com/ipython/ipython/blob/master/LICENSE

The current IPython license summary can be viewed at:
https://github.com/ipython/ipython/blob/master/COPYING.rst


Files covered:

spyder/utils/introspection/module_completion.py
spyder/widgets/calltip.py


-------------------------------------------------------------------------------



Jupyter QtConsole
-----------------


Copyright (c) 2001-2015 IPython Development Team
Copyright (c) 2015- Jupyter Development Team


Author: IPython/Project Jupyter Development Team | https://jupyter.org/
Site: https://qtconsole.readthedocs.io/en/stable/
Source: https://github.com/jupyter/qtconsole/
License: BSD (3-clause) License | https://opensource.org/licenses/BSD-3-Clause

Modifications made to below files to integrate with Spyder and migrate to QtPy.


This project is licensed under the terms of the Modified BSD License
(also known as New or Revised or 3-Clause BSD).

The Jupyter Development Team is the set of all contributors to the
Jupyter project. This includes all of the Jupyter subprojects.

The core team that coordinates development on GitHub can be found here:
https://github.com/jupyter/.

Jupyter uses a shared copyright model. Each contributor maintains copyright
over their contributions to Jupyter. But, it is important to note that
these contributions are typically only changes to the repositories.
Thus, the Jupyter source code, in its entirety is not the copyright of any
single person or institution. Instead, it is the collective copyright of the
entire Jupyter Development Team. If individual contributors want to maintain
a record of what changes/contributions they have specific copyright on,
they should indicate their copyright in the commit message of the change,
when they commit the change to one of the Jupyter repositories.


Spyder also relies on the QtConsole Python package to provide its Console UI.

Jupyter QtConsole was originally developed as part of IPython; see above.

In addition to the list below, modified QtConsole code snippits included in:

spyder/plugins/ipythonconsole/widgets/client.py
spyder/plugins/ipythonconsole/widgets/shell.py

See below for the full text of the Modified BSD (3-clause) License.

The current QtConsole license can be viewed at:
https://github.com/jupyter/qtconsole/blob/master/LICENSE


Files covered:

spyder/plugins/editor/utils/kill_ring.py


-------------------------------------------------------------------------------



BinaryOrNot 0.4.0
-----------------


Copyright (c) 2013 Audrey Roy


Author: Audrey Roy Greenfeld and contributors | <EMAIL> |
https://github.com/audreyr/binaryornot/blob/master/AUTHORS.rst
Site: https://binaryornot.readthedocs.io/en/latest/
Source: https://github.com/audreyr/binaryornot
License: BSD (3-clause) License | https://opensource.org/licenses/BSD-3-Clause

Minor modifications made to below files to integrate with Spyder.


Free software: BSD license

Development Lead:

Audrey Roy Greenfeld (https://github.com/audreyr)

Contributors:

* Nick Coghlan (https://github.com/ncoghlan)
* Ville Skyttä (https://github.com/scop)
* Vincent Bernat (https://github.com/vincentbernat)
* Daniel Roy Greenfeld (https://github.com/pydanny)
* Philippe Ombredanne (https://github.com/pombredanne)
* Aaron Meurer (https://github.com/asmeurer)
* David R. MacIver (https://github.com/DRMacIver)
* Raphael Pierzina (https://github.com/hackebrot)
* Johannes (https://github.com/johtso)

Special thanks to Eli Bendersky (@eliben) for his writeup explaining
the heuristic and his implementation, which this is largely based on.

Source code from the portion of Perl's pp_fttext that checks for textiness:
https://github.com/Perl/perl5/blob/v5.23.1/pp_sys.c#L3527-L3587


See below for the full text of the Modified BSD (3-clause) License.

The current BinaryOrNot license can be viewed at:
https://github.com/audreyr/binaryornot/blob/master/LICENSE


Files covered:

spyder/utils/external/binaryornot/__init__.py
spyder/utils/external/binaryornot/check.py
spyder/utils/external/binaryornot/helpers.py


-------------------------------------------------------------------------------



Lockfile Module from Twisted 12.3.0
-----------------------------------


Copyright (c) 2005 Divmod, Inc.
Copyright (c) 2008-2011 Twisted Matrix Laboratories
Copyright (c) 2012- Spyder Project Contributors


Author: Twisted Matrix Laboratories | https://github.com/ColinDuquesnoy
Site: https://www.twistedmatrix.com/
Source: https://github.com/twisted/twisted
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to adapt/improve the code for Spyder (see file for details).


All of the code in this distribution is
Copyright (c) 2001-2012 Twisted Matrix Laboratories.

Twisted is made available under the MIT license.

Copyright (c) 2001-2012
Allen Short
Andy Gayton
Andrew Bennetts
Antoine Pitrou
Apple Computer, Inc.
Benjamin Bruheim
Bob Ippolito
Canonical Limited
Christopher Armstrong
David Reid
Donovan Preston
Eric Mangold
Eyal Lotem
Itamar Turner-Trauring
James Knight
Jason A. Mobarak
Jean-Paul Calderone
Jessica McKellar
Jonathan Jacobs
Jonathan Lange
Jonathan D. Simms
Jürgen Hermann
Kevin Horn
Kevin Turner
Mary Gardiner
Matthew Lefkowitz
Massachusetts Institute of Technology
Moshe Zadka
Paul Swartz
Pavel Pergamenshchik
Ralph Meijer
Sean Riley
Software Freedom Conservancy
Travis B. Hartwell
Thijs Triemstra
Thomas Herve
Timothy Allen


See below for the full text of the MIT (Expat) license.

The current Twisted license can be viewed at:
https://github.com/twisted/twisted/blob/trunk/LICENSE

The current version of the original file can be viewed at:
https://github.com/twisted/twisted/blob/trunk/src/twisted/python/lockfile.py


Files covered:

spyder/utils/external/lockfile.py


-------------------------------------------------------------------------------



Toggleable Sections from the Cloud Sphinx Theme 1.6
---------------------------------------------------


Copyright (c) 2011-2012 Assurance Technologies
Copyright (c) 2014- Spyder Project Contributors


Author: Assurance Technologies | https://www.assurancetechnologies.com
Site/Source: https://bitbucket.org/ecollins/cloud_sptheme/
License: BSD (3-clause) License | https://opensource.org/licenses/BSD-3-Clause

Modifications made to extract and adapt a specific section of code for Spyder.


cloud_sptheme is released under the BSD license,
and is (c) `Assurance Technologies <https://www.assurancetechnologies.com>`_

The "cloud_sptheme" python package and artwork is
Copyright (c) 2010-2012 by Assurance Technologies, LLC.


See below for the full text of the 3-clause BSD license.

The current Cloud Sphinx Theme license can be viewed at:
https://bitbucket.org/ecollins/cloud_sptheme/src/default/LICENSE

The current version of the original file can be viewed at:
https://bitbucket.org/ecollins/cloud_sptheme/src/default/cloud_sptheme/themes/cloud/static/cloud.js_t


Files covered:

spyder/plugins/help/utils/js/collapse_sections.js


-------------------------------------------------------------------------------



Copy Button from the Python Docs Theme
--------------------------------------


Copyright (c) 2012 Python Software Foundation
Copyright (c) 2012- Spyder Project Contributors


Author: Python Software Foundation | https://www.python.org/
Site: https://docs.python.org/3/
Source: https://github.com/python/python-docs-theme
License: Python Software Foundation License Version 2
https://docs.python.org/3/license.html

Minor modifications made to style.


Note that when adopting this theme, you're also borrowing an element of the
trust and credibility established by the CPython core developers over the
years. That's fine, and you're welcome to do so for other Python community
projects if you so choose, but please keep in mind that in doing so you're also
choosing to accept some of the responsibility for maintaining that collective
trust.

The Python name and logo is a trademark of the Python Software Foundation.


See below for the full text of the PSF License Version 2.

The current Cloud Sphinx Theme license can be viewed at:
https://bitbucket.org/ecollins/cloud_sptheme/src/default/LICENSE

The current version of the original file can be viewed at:
https://github.com/python/python-docs-theme/blob/master/python_docs_theme/static/copybutton.js

The original file is used online at:
https://docs.python.org/_static/copybutton.js


Files covered:

spyder/plugins/help/utils/js/copy_button.js


-------------------------------------------------------------------------------



QtWaitingSpinner
----------------


Copyright (c) 2012-2014 Alexander Turkin
Copyright (c) 2014 William Hallatt
Copyright (c) 2015 Jacob Dawid
Copyright (c) 2016 Luca Weiss
Copyright (c) 2017- Spyder Project Contributors


Author: Alex Turkin, Luca Weiss and Contributors
https://github.com/z3ntu/QtWaitingSpinner/graphs/contributors
Site: https://github.com/snowwlex/QtWaitingSpinner
Source: https://github.com/z3ntu/QtWaitingSpinner
License: MIT (Expat) License | https://opensource.org/licenses/MIT

A trivial modification was made to ensure independent function with Spyder.


Original Work Copyright (c) 2012-2015 Alexander Turkin
Modified 2014 by William Hallatt

Modified for Python by Luca Weiss (@z3ntu)
https://github.com/z3ntu

Thanks to @snowwlex for the base of my port (and the README)!
https://github.com/snowwlex

QtWaitingSpinner was inspired by the spin.js project.
https://spin.js.org/


See below for the full text of the MIT (Expat) License.

The current Waiting Spinner license can be viewed at:
https://github.com/z3ntu/QtWaitingSpinner/blob/master/LICENSE.md


Files covered:

spyder/widgets/waitingspinner.py


-------------------------------------------------------------------------------



DataFrameModel from QtPandas (Pandas 0.13.1)
--------------------------------------------


Copyright (c) 2008-2011 AQR Capital Management, LLC
Copyright (c) 2011-2012 Lambda Foundry, Inc. and PyData Development Team
Copyright (c) 2013 Jev Kuznetsov and contributors
Copyright (c) 2014- Spyder Project Contributors


Author: Jev Kuznetsov and pandas contributors | https://github.com/sjev
Site: https://pandas.pydata.org/
Source: https://github.com/draperjames/qtpandas
License: BSD (3-clause) License | https://opensource.org/licenses/BSD-3-Clause

Extensive adaptation, modification and improvements made to the base class.


pandas is distributed under a 3-clause ("New") BSD license.

AQR Capital Management began pandas development in 2008. Development was
led by Wes McKinney. AQR released the source under this license in 2009.
Wes is now an employee of Lambda Foundry, and remains the pandas project
lead.

The PyData Development Team is the collection of developers of the PyData
project. This includes all of the PyData sub-projects, including pandas. The
core team that coordinates development on GitHub can be found here:
https://github.com/pydata.

Full credits for pandas contributors can be found in the documentation.

PyData uses a shared copyright model. Each contributor maintains copyright
over their contributions to PyData. However, it is important to note that
these contributions are typically only changes to the repositories. Thus,
the PyData source code, in its entirety, is not the copyright of any single
person or institution. Instead, it is the collective copyright of the
entire PyData Development Team. If individual contributors want to maintain
a record of what changes/contributions they have specific copyright on,
they should indicate their copyright in the commit message of the change
when they commit the change to one of the PyData repositories.


See below for the full text of the BSD (3-clause) license.

The current pandas license can be viewed at:
https://github.com/pandas-dev/pandas/blob/master/LICENSE

The current QtPandas license can be viewed at:
https://github.com/draperjames/qtpandas/blob/master/LICENSE

The original version of the original file can be viewed at:
https://github.com/pandas-dev/pandas/blob/d10a658673b7d2c2e7a346461c9a4bfc5233d7e1/pandas/sandbox/qtpandas.py

The current version of the original file can be viewed at:
https://github.com/draperjames/qtpandas/blob/master/qtpandas/models/DataFrameModel.py


Files covered:

spyder/plugins/variableexplorer/widgets/dataframeeditor.py


-------------------------------------------------------------------------------



Classes from Gtabview 0.8
-------------------------


Copyright (c) 2014-2015 Scott Hansen <<EMAIL>>
Copyright (c) 2014-2016 Yuri D'Elia "wave++" <<EMAIL>>
Copyright (c) 2016- Spyder Project Contributors (see AUTHORS.txt)


Author: <author-name> | <author-email> | <author-website>
Site/Source: https://github.com/TabViewer/gtabview
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to each class to adapt them to Spyder's needs.


gtabview is distributed under the MIT license.


DataFrameHeaderModel and DataFrameLevelModel are based on the classes
Header4ExtModel and Level4ExtModel from the gtabview project.
DataFrameModel is based on the classes ExtDataModel and ExtFrameModel, and
DataFrameEditor is based on gtExtTableView from the same project.

See below for the full text of the MIT (Expat) license.

The current gtabview license can be viewed at:
https://github.com/TabViewer/gtabview/blob/master/LICENSE.txt

The current version of the original files can be viewed at:
https://github.com/TabViewer/gtabview/blob/master/gtabview/viewer.py
https://github.com/TabViewer/gtabview/blob/master/gtabview/models.py


Files covered:

spyder/plugins/variableexplorer/widgets/dataframeeditor.py


-------------------------------------------------------------------------------



Classes from objbrowser 1.2.1
-----------------------------


Copyright (c) 2016 Pepijn Kenter


Author: Pepijn Kenter | <EMAIL> | https://github.com/titusjan
Site/Source: https://github.com/titusjan/objbrowser
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to each class to adapt them to Spyder's needs.


objbrowser is distributed under the MIT license.


Use the core functionality of the project, mainly changing the code to use
the upstream version of qtpy project (since it is already a dependency of
Spyder).

See below for the full text of the MIT (Expat) license.

The current objbrowser license can be viewed at:
https://github.com/titusjan/objbrowser/blob/master/LICENSE.txt

The current version of the original files can be viewed at:
https://github.com/titusjan/objbrowser/tree/master/objbrowser


Files covered:

spyder/plugins/variableexplorer/widgets/objectexplorer/attribute_model.py
spyder/plugins/variableexplorer/widgets/objectexplorer/objectexplorer.py
spyder/plugins/variableexplorer/widgets/objectexplorer/toggle_column_mixin.py
spyder/plugins/variableexplorer/widgets/objectexplorer/tree_item.py
spyder/plugins/variableexplorer/widgets/objectexplorer/tree_model.py
spyder/plugins/variableexplorer/widgets/objectexplorer/utils.py


-------------------------------------------------------------------------------



Classes from mod_pydoc
-----------------------------


Copyright (c) 2018 André Roberge


Author: André Roberge | <EMAIL> | https://github.com/aroberge
Site/Source: https://github.com/aroberge/mod_pydoc
License: MIT (Expat) License | https://opensource.org/licenses/MIT

Modifications made to pydoc module to adapt it to Spyder's needs.


mod_pydoc is distributed under the MIT license.


Use the core functionality of the project, mainly changing the code to set
a font programmatically and change the way handling of the errors is made as
well as the use of a custom CSS taking into account the current theme
(light or dark).

See below for the full text of the MIT (Expat) license.

The current mod_pydoc license can be viewed at:
https://github.com/aroberge/mod_pydoc/blob/master/LICENSE

The current version of the original files can be viewed at:
https://github.com/aroberge/mod_pydoc/


Files covered:

spyder/plugins/onlinehelp/pydoc_patch.py


===============================================================================




External Fonts Used for Icons
=============================


Font Awesome 4.7 (via QtAwesome)
--------------------------------


Copyright 2016 Dave Gandy


Author: Dave Gandy/FontAwesome | @fontawesome
Site: https://fontawesome.com/
Source: https://github.com/FortAwesome/Font-Awesome
License: SIL Open Font License 1.1 | https://scripts.sil.org/OFL

No modifications made.


Font Awesome Free is free, open source, and GPL friendly.
You can use it for commercial projects, open source projects,
or really almost whatever you want.

In the Font Awesome Free download, the SIL OLF license applies to all icons
packaged as web and desktop font files.

Attribution is required by MIT, SIL OLF, and CC BY licenses.
Downloaded Font Awesome Free files already contain embedded comments
with sufficient attribution, so you shouldn't need to do anything additional
when using these files normally.

We've kept attribution comments terse, so we ask that you do not
actively work to remove them from files, especially code.
They're a great way for folks to learn about Font Awesome.

All brand icons are trademarks of their respective owners.
The use of these trademarks does not indicate endorsement of the
trademark holder by Font Awesome, nor vice versa.
Brand icons should only be used to represent the
company or product to which they refer.
**Please do not use brand logos for any purpose except
to represent the company, product, or service to which they refer.**


See below for the full text of the SIL Open Font License 1.1.

The FontAwesome 4.7.0 license summary can be viewed at:
https://fontawesome.com/v4.7.0/license/

The current FontAwesome Free license summary can be viewed at:
https://fontawesome.com/license/free


Assets covered:

Most icons in the Spyder 3 theme; used via Spyder's own QtAwesome package.

See spyder/utils/icon_manager.py for a complete list.

In addition:

spyder/images/folding.arrow_down_off.png
spyder/images/folding.arrow_down_on.png
spyder/images/folding.arrow_right_off.png
spyder/images/folding.arrow_right_on.png


-------------------------------------------------------------------------------


Material Design (via QtAwesome)
--------------------------------


Copyright (c) 2014, Austin Andrews (http://materialdesignicons.com/),
with Reserved Font Name Material Design Icons.
Copyright (c) 2014, Google (http://www.google.com/design/)
uses the license at https://github.com/google/material-design-icons/blob/master/LICENSE


Author: Austin Andrews/MaterialDesign
Site: http://materialdesignicons.com/
Source: https://github.com/Templarian/MaterialDesign
License: SIL Open Font License 1.1 | https://scripts.sil.org/OFL

No modifications made.


This Font Software is licensed under the SIL Open Font License, Version 1.1.
This license is copied below, and is also available with a FAQ at:
http://scripts.sil.org/OFL

See below for the full text of the SIL Open Font License 1.1.

The current MaterialDesign license summary can be viewed at:
https://github.com/Templarian/MaterialDesign/blob/master/LICENSE


Assets covered:

Some icons in the Spyder 3 theme; used via Spyder's own QtAwesome package.

See spyder/utils/icon_manager.py for a complete list.


===============================================================================




Icons under spyder/images
=========================


The following lists the source, license and accompanying details for every
image and icon under the spyder/images directory, including those made by
the Spyder project in-house. All images and icons in any other directory
of the repository or package are an original creation of the Spyder team
and are covered by Spyder's overall MIT (Expat) license as described above.


-------------------------------------------------------------------------------



Spyder's Bespoke Icons
----------------------


Copyright (c) 2009- Spyder Project Contributors (see AUTHORS.txt)


Author: Spyder Project Contributors | AUTHORS.txt
https://github.com/spyder-ide/spyder/graphs/contributors
Site: https://www.spyder-ide.org/
Source: https://github.com/spyder-ide/spyder
License: MIT (Expat) License | https://opensource.org/licenses/MIT


Licensed under the terms of the MIT License.

These icons were created by Spyder's own contributors specifically for the IDE.
They may implicitly be used in other projects compatible with the same license,
presuming its terms are followed in full.

The Spyder logo and related marks
may be subject to trademark; however, unmodified usage that does not risk any
confusion or mislead as to association with or endorsement by Spyder or its
contributors without prior consultation is expressly encouraged. If you have
any questions, please do not hesitate to contact us.


See below for the full text of the MIT (Expat) License.

The current Spyder repo license can be viewed at:
https://github.com/spyder-ide/spyder/blob/master/LICENSE.txt


Files covered:

spyder/images/actions/hist.png
spyder/images/actions/window_nofullscreen.png
spyder/images/console/ipython_console.png
spyder/images/editor/attribute.png
spyder/images/editor/blockcomment.png
spyder/images/editor/cell.png
spyder/images/editor/filelist.png
spyder/images/editor/function.png
spyder/images/editor/method.png
spyder/images/editor/module.png
spyder/images/editor/no_match.png
spyder/images/editor/private1.png
spyder/images/editor/private2.png
spyder/images/editor/refactor.png
spyder/images/old_svg/options.svg
spyder/images/spyder.svg
spyder/images/spyder2_icon_theme/dictedit.png
spyder/images/spyder2_icon_theme/not_found.png
spyder/images/spyder2_icon_theme/whole_words.png
spyder/images/spyder2_icon_theme/win_env.png


-------------------------------------------------------------------------------



Crystal Project Icons
---------------------


Copyright (c) 2006-2007 Everaldo Coelho


Author: Everaldo Coelho | <EMAIL>
Site: https://www.everaldo.com
Source: https://store.kde.org/p/1002590/
Download (mirror): https://github.com/thecodingmachine/crystal-project
License: GNU Lesser General Public License 2.1 or later
https://www.gnu.org/licenses/old-licenses/lgpl-2.1.html

No modifications made.


All Icons are free software; you can redistribute it and/or modify it under the
terms of the GNU Lesser General Public License as published by the Free
Software Foundation; either version 2.1 of the License, or (at your option) any
later version. This library is distributed in the hope that it will be useful,
but without any warranty; without even the implied warranty of merchantability
or fitness for a particular purpose.


See below for full text of the GNU LGPL 2.1.

Later versions of the GNU LGPL may be found at:
https://www.gnu.org/licenses/lgpl.html


Files covered:

spyder/images/actions/1downarrow.png
spyder/images/actions/1uparrow.png
spyder/images/actions/2downarrow.png
spyder/images/actions/2uparrow.png
spyder/images/actions/browse_tab.png
spyder/images/actions/check.png
spyder/images/actions/configure.png
spyder/images/actions/copywop.png
spyder/images/actions/delete.png
spyder/images/actions/edit.png
spyder/images/actions/edit24.png
spyder/images/actions/edit_add.png
spyder/images/actions/edit_remove.png
spyder/images/actions/editcopy.png
spyder/images/actions/editcut.png
spyder/images/actions/editpaste.png
spyder/images/actions/eraser.png
spyder/images/actions/exit.png
spyder/images/actions/filter.png
spyder/images/actions/find.png
spyder/images/actions/findf.png
spyder/images/actions/folder_new.png
spyder/images/actions/hide.png
spyder/images/actions/home.png
spyder/images/actions/imshow.png
spyder/images/actions/insert.png
spyder/images/actions/lock.png
spyder/images/actions/lock_open.png
spyder/images/actions/magnifier.png
spyder/images/actions/next.png
spyder/images/actions/options_less.png
spyder/images/actions/options_more.png
spyder/images/actions/plot.png
spyder/images/actions/previous.png
spyder/images/actions/redo.png
spyder/images/actions/rename.png
spyder/images/actions/replace.png
spyder/images/actions/show.png
spyder/images/actions/special_paste.png
spyder/images/actions/synchronize.png
spyder/images/actions/tooloptions.png
spyder/images/actions/undo.png
spyder/images/actions/up.png
spyder/images/console/console.png
spyder/images/console/history.png
spyder/images/console/kill.png
spyder/images/console/prompt.png
spyder/images/console/restart.png
spyder/images/editor/bug.png
spyder/images/editor/close_panel.png
spyder/images/editor/comment.png
spyder/images/editor/error.png
spyder/images/editor/file.png
spyder/images/editor/fromcursor.png
spyder/images/editor/gotoline.png
spyder/images/editor/highlight.png
spyder/images/editor/indent.png
spyder/images/editor/newwindow.png
spyder/images/editor/next_cursor.png
spyder/images/editor/next_wng.png
spyder/images/editor/outline_explorer.png
spyder/images/editor/outline_explorer_vis.png
spyder/images/editor/prev_cursor.png
spyder/images/editor/prev_wng.png
spyder/images/editor/select.png
spyder/images/editor/selectall.png
spyder/images/editor/todo_list.png
spyder/images/editor/uncomment.png
spyder/images/editor/unindent.png
spyder/images/editor/wng_list.png
spyder/images/file/fileclose.png
spyder/images/file/fileimport.png
spyder/images/file/filenew.png
spyder/images/file/fileopen.png
spyder/images/file/filesave.png
spyder/images/file/filesaveas.png
spyder/images/file/print.png
spyder/images/file/save_all.png
spyder/images/projects/add_to_path.png
spyder/images/projects/folder.png
spyder/images/projects/package.png
spyder/images/projects/pp_folder.png
spyder/images/projects/pp_package.png
spyder/images/projects/pp_project.png
spyder/images/projects/project.png
spyder/images/projects/project_closed.png
spyder/images/projects/pythonpath.png
spyder/images/projects/remove_from_path.png
spyder/images/projects/show_all.png
spyder/images/spyder2_icon_theme/advanced.png
spyder/images/spyder2_icon_theme/help.png
spyder/images/spyder2_icon_theme/italic.png
spyder/images/spyder2_icon_theme/vcs_browse.png
spyder/images/spyder2_icon_theme/vcs_commit.png


-------------------------------------------------------------------------------



Yusuke Kamiyamane Icons
-----------------------


Copyright (C) 2013 Yusuke Kamiyamane


Author: Yusuke Kamiyamane | <EMAIL>
Source/Download: http://p.yusukekamiyamane.com/
License: Creative Commons Attribution 3.0 International
https://creativecommons.org/licenses/by/3.0/

No modifications made.


These icons are licensed under a Creative Commons
Attribution 3.0 License.
<https://creativecommons.org/licenses/by/3.0/>

If you can't or don't want to provide attribution, please
purchase a royalty-free license.
<http://p.yusukekamiyamane.com/>

I'm unavailable for custom icon design work. But your
suggestions are always welcome!
<mailto:<EMAIL>>


See below for the full text of the CC-BY 3.0 International license.

The license summary and text can be found online at:
https://creativecommons.org/licenses/by/3.0/


Files covered:

spyder/images/actions/arrow-continue.png
spyder/images/actions/arrow-step-in.png
spyder/images/actions/arrow-step-out.png
spyder/images/actions/arrow-step-over.png
spyder/images/actions/stop.png
spyder/images/actions/stop_debug.png
spyder/images/actions/unmaximize.png
spyder/images/actions/window_fullscreen.png
spyder/images/editor/breakpoint_big.png
spyder/images/editor/breakpoint_cond_big.png
spyder/images/editor/breakpoint_cond_small.png
spyder/images/editor/breakpoint_small.png
spyder/images/editor/debug.png


-------------------------------------------------------------------------------



FamFamFam Silk Icons 1.3
------------------------


Copyright 2006 Mark James


Author: Mark James | <EMAIL>
Site: http://www.famfamfam.com/
Source/Download: http://www.famfamfam.com/lab/icons/silk/
License: Creative Commons Attribution 2.5 Generic
https://creativecommons.org/licenses/by/2.5/
or
Creative Commons Attribution 3.0 International
https://creativecommons.org/licenses/by/3.0/

No modifications made.


I also love to hear of my work being used, feel encouraged to send an email
with a link or screenshot of the icons in their new <NAME_EMAIL>.
This work is licensed under a Creative Commons Attribution 2.5 License.
This means you may use it for any purpose, and make any changes you like.
All I ask is that you include a link back to this page in your credits
(although a giant link on every page of your website really isn't needed,
contact me to discuss specifics).

The icons can also be used under Creative Commons Attribution 3.0 License
(Hi Debian folks!) with the following requirements:

    As an author, I would appreciate a reference to my authorship of the Silk
    icon set contents within a readme file or equivalent documentation for the
    software which includes the set or a subset of the icons contained within.


See below for the full text of the CC-BY-3.0 license.

The license summaries and text can be found online at:
https://creativecommons.org/licenses/by/2.5/
https://creativecommons.org/licenses/by/3.0/


Files covered:

spyder/images/actions/collapse.png
spyder/images/actions/collapse_selection.png
spyder/images/actions/expand.png
spyder/images/actions/expand_selection.png
spyder/images/actions/restore.png
spyder/images/editor/class.png
spyder/images/editor/convention.png
spyder/images/editor/todo.png
spyder/images/editor/warning.png


-------------------------------------------------------------------------------



The Oxygen Icon Theme
---------------------


Copyright (C) 2007 Nuno Pinheiro <<EMAIL>>
Copyright (C) 2007 David Vignoni <<EMAIL>>
Copyright (C) 2007 David Miller <<EMAIL>>
Copyright (C) 2007 Johann Ollivier Lapeyre <<EMAIL>>
Copyright (C) 2007 Kenneth Wimer <<EMAIL>>
Copyright (C) 2007 Riccardo Iaconelli <<EMAIL>>

and others.


Author: KDE Artists (see above) | <EMAIL> | https://www.kde.org/
Site: https://techbase.kde.org/Projects/Oxygen
Source: https://cgit.kde.org/oxygen-icons5.git/
Download (official mirror): https://github.com/KDE/oxygen-icons
License: GNU Lesser General Public License 3.0 or later
https://www.gnu.org/licenses/lgpl.html

Modifications made: Assets combined to make new icons as listed below.


This library is free software; you can redistribute it and/or
modify it under the terms of the GNU Lesser General Public
License as published by the Free Software Foundation; either
version 3 of the License, or (at your option) any later version.

This library is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public
License along with this library. If not, see <https://www.gnu.org/licenses/>.

Clarification:

The GNU Lesser General Public License or LGPL is written for
software libraries in the first place. We expressly want the LGPL to
be valid for this artwork library too.

KDE Oxygen theme icons is a special kind of software library, it is an
artwork library, it's elements can be used in a Graphical User Interface, or
GUI.

Source code, for this library means:
- where they exist, SVG;
- otherwise, if applicable, the multi-layered formats xcf or psd, or
otherwise png.

The LGPL in some sections obliges you to make the files carry
notices. With images this is in some cases impossible or hardly useful.

With this library a notice is placed at a prominent place in the directory
containing the elements. You may follow this practice.

The exception in section 5 of the GNU Lesser General Public License covers
the use of elements of this art library in a GUI.


See below for the full text of the GNU LGPL 3.0, along with the GPL 3.0.

Later versions of the GNU LGPL may be found at:
https://www.gnu.org/licenses/lgpl.html

SVG sources for the assets used located in img_src/Oxygen_Icon_Set directory.


Assets used:

img_src/arrow-right.svgz
img_src/configure.svgz
img_src/edit-undo.svgz


Files covered:

spyder/images/console/run_small.png
spyder/images/editor/run.png


-------------------------------------------------------------------------------



Python Logo
-----------


Copyright (c) 2008 Python Software Foundation


Author: Python Software Foundation | https://www.python.org/
Source/Download: https://www.python.org/community/logos/
License: Python Software Foundation License Version 2
https://docs.python.org/3/license.html
Trademark: Python Software Foundation Trademark Usage Policy
https://www.python.org/psf/trademarks/

No modifications made.


The Python logo is a trademark of the Python Software Foundation,
which is responsible for defending against
any damaging or confusing uses of the trademark.

See the PSF Trademark Usage Policy: https://www.python.org/psf/trademarks/

In general, we want the logo to be used as widely as possible to indicate
use of Python or suitability for Python. However, please ask first when using
a derived version of the logo or when in doubt.

Projects and companies that use Python are encouraged to incorporate the
Python logo on their websites, brochures, packaging, and elsewhere to indicate
suitability for use with Python or implementation in Python. Use of the
"two snakes" logo element alone, without the accompanying wordmark is permitted
on the same terms as the combined logo.


See below for the full text of the PSF License Version 2.

The license history and text can be found online at:
https://docs.python.org/3/license.html


Files covered:

spyder/images/console/python.png


-------------------------------------------------------------------------------



Notebook Logo
-------------


Copyright (c) 2015 Project Jupyter Contributors


Author: Project Jupyter Contributors | https://github.com/jupyterlab/jupyterlab
Site: https://github.com/jupyterlab/jupyterlab
Source: https://github.com/jupyterlab/jupyterlab
License: 3-Clause ("Modified") BSD License
https://opensource.org/licenses/BSD-3-Clause

The icon color was changed to black.


Distributed under the terms of the BSD License.


See below for the full text of the BSD (3-clause) License.

The current version of the original file can be viewed at:
https://github.com/jupyterlab/jupyterlab/blob/0.35.x/packages/theme-light-extension/style/icons/jupyter/book_selected.svg


Files covered:

spyder/images/dark/notebook.svg
spyder/images/light/notebook.svg


-------------------------------------------------------------------------------



Icons for Visual Studio Code
----------------------------


Copyright (c) 2016 Roberto Huertas


Author: Roberto Huertas | https://github.com/vscode-icons/vscode-icons
Site: https://github.com/vscode-icons
Source: https://github.com/vscode-icons
License: The MIT License (MIT)
https://opensource.org/licenses/MIT


Distributed under the terms of The MIT License (MIT)


See below for the full text of The MIT License (MIT).

The current version of the original files can be viewed at:
https://github.com/vscode-icons/vscode-icons/blob/master/icons


Files covered:

spyder/images/dark/binary.svg
spyder/images/dark/file_type_tex.svg
spyder/images/light/binary.svg
spyder/images/light/file_type_tex.svg


-------------------------------------------------------------------------------


Icons for Kite Completions Plugin
---------------------------------


Copyright (c) 2019 Kite


Author: Kite | https://kite.com
Site: https://kite.com
Source: https://kite.com
License: Creative Commons Attribution 4.0 International
https://creativecommons.org/licenses/by/4.0/


Licensed under the terms of the Creative Commons Attribution International 4.0.


See below for the full text of the Creative Commons Attribution International 4.0 License.


Files covered:

spyder/images/dark/kite.svg
spyder/images/kite/kite_copilot.png
spyder/images/light/kite.svg


===============================================================================



Plugin Assets
=============


Images from QDarkStyle
----------------------


Copyright (c) 2013-2018 Colin Duquesnoy <<EMAIL>> - original author.
Copyright (c) 2018 Daniel Pizetta <<EMAIL>> - improvements and bug fixes.


Author: QDarkStyle Contributors | See AUTHORS.md
https://github.com/ColinDuquesnoy/QDarkStyleSheet/blob/master/AUTHORS.md
Source: https://github.com/ColinDuquesnoy/QDarkStyleSheet
License: Creative Commons Attribution International 4.0
https://creativecommons.org/licenses/by/4.0/


Licensed under the terms of the Creative Commons Attribution International 4.0.


See below for the full text of the Creative Commons Attribution International 4.0 License.

The current QDarkStyle repo license can be viewed at:
https://github.com/ColinDuquesnoy/QDarkStyleSheet/blob/master/LICENSE.md


Files covered:

spyder/plugins/help/utils/static/images/down_arrow.png
spyder/plugins/help/utils/static/images/down_arrow_disabled.png
spyder/plugins/help/utils/static/images/up_arrow.png
spyder/plugins/help/utils/static/images/up_arrow_disabled.png


===============================================================================



pyqt-bloomfilter 3.0.0
-------------------------------------------------------------------------


Copyright (c) 2011 Jay Baird and Bob Ippolito
Copyright (c) 2019 - Spyder Project Contributors (see AUTHORS.txt)


Author: Jay Baird and Bob Ippolito
Source: https://github.com/kiteco/pyqt-bloomfilter
License: MIT (expat)
https://opensource.org/licenses/MIT

No modifications made.


pyqt-bloomfilter is used in Spyder under the terms of the MIT.
See below for the full text of the MIT.

spyder/utils/external/pybloom_pyqt is used in Spyder
to implement fast lookup of the availability of Kite completions.

See below for the full text of the MIT (expat) license.

The current pyqt-bloomfilter license can be viewed at:
https://github.com/kiteco/pyqt-bloomfilter/blob/master/LICENSE.txt

The current version of the original file[s] can be viewed at:
https://github.com/kiteco/pyqt-bloomfilter


Files covered:

spyder/utils/external/pybloom_pyqt/__init__.py
spyder/utils/external/pybloom_pyqt/pybloom.py
spyder/utils/external/pybloom_pyqt/utils.py


-------------------------------------------------------------------------------




Full License Text
=================


MIT (Expat) License
-------------------


Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


-------------------------------------------------------------------------------



3-Clause ("Modified") BSD License
---------------------------------


Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
    * Neither the name of the <organization> nor the
      names of its contributors may be used to endorse or promote products
      derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.


-------------------------------------------------------------------------------



PSF License Version 2
---------------------


PYTHON SOFTWARE FOUNDATION LICENSE VERSION 2
--------------------------------------------

1. This LICENSE AGREEMENT is between the Python Software Foundation
("PSF"), and the Individual or Organization ("Licensee") accessing and
otherwise using this software ("Python") in source or binary form and
its associated documentation.

2. Subject to the terms and conditions of this License Agreement, PSF hereby
grants Licensee a nonexclusive, royalty-free, world-wide license to reproduce,
analyze, test, perform and/or display publicly, prepare derivative works,
distribute, and otherwise use Python alone or in any derivative version,
provided, however, that PSF's License Agreement and PSF's notice of copyright,
i.e., "Copyright (c) 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010,
2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018 Python Software Foundation; All
Rights Reserved" are retained in Python alone or in any derivative version
prepared by Licensee.

3. In the event Licensee prepares a derivative work that is based on
or incorporates Python or any part thereof, and wants to make
the derivative work available to others as provided herein, then
Licensee hereby agrees to include in any such work a brief summary of
the changes made to Python.

4. PSF is making Python available to Licensee on an "AS IS"
basis.  PSF MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR
IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, PSF MAKES NO AND
DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS
FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF PYTHON WILL NOT
INFRINGE ANY THIRD PARTY RIGHTS.

5. PSF SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF PYTHON
FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR LOSS AS
A RESULT OF MODIFYING, DISTRIBUTING, OR OTHERWISE USING PYTHON,
OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF THE POSSIBILITY THEREOF.

6. This License Agreement will automatically terminate upon a material
breach of its terms and conditions.

7. Nothing in this License Agreement shall be deemed to create any
relationship of agency, partnership, or joint venture between PSF and
Licensee.  This License Agreement does not grant permission to use PSF
trademarks or trade name in a trademark sense to endorse or promote
products or services of Licensee, or any third party.

8. By copying, installing or otherwise using Python, Licensee
agrees to be bound by the terms and conditions of this License
Agreement.


-------------------------------------------------------------------------------



GNU General Public License Version 2
------------------------------------


                    GNU GENERAL PUBLIC LICENSE
                       Version 2, June 1991

 Copyright (C) 1989, 1991 Free Software Foundation, Inc.,
 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.

                            Preamble

  The licenses for most software are designed to take away your
freedom to share and change it.  By contrast, the GNU General Public
License is intended to guarantee your freedom to share and change free
software--to make sure the software is free for all its users.  This
General Public License applies to most of the Free Software
Foundation's software and to any other program whose authors commit to
using it.  (Some other Free Software Foundation software is covered by
the GNU Lesser General Public License instead.)  You can apply it to
your programs, too.

  When we speak of free software, we are referring to freedom, not
price.  Our General Public Licenses are designed to make sure that you
have the freedom to distribute copies of free software (and charge for
this service if you wish), that you receive source code or can get it
if you want it, that you can change the software or use pieces of it
in new free programs; and that you know you can do these things.

  To protect your rights, we need to make restrictions that forbid
anyone to deny you these rights or to ask you to surrender the rights.
These restrictions translate to certain responsibilities for you if you
distribute copies of the software, or if you modify it.

  For example, if you distribute copies of such a program, whether
gratis or for a fee, you must give the recipients all the rights that
you have.  You must make sure that they, too, receive or can get the
source code.  And you must show them these terms so they know their
rights.

  We protect your rights with two steps: (1) copyright the software, and
(2) offer you this license which gives you legal permission to copy,
distribute and/or modify the software.

  Also, for each author's protection and ours, we want to make certain
that everyone understands that there is no warranty for this free
software.  If the software is modified by someone else and passed on, we
want its recipients to know that what they have is not the original, so
that any problems introduced by others will not reflect on the original
authors' reputations.

  Finally, any free program is threatened constantly by software
patents.  We wish to avoid the danger that redistributors of a free
program will individually obtain patent licenses, in effect making the
program proprietary.  To prevent this, we have made it clear that any
patent must be licensed for everyone's free use or not licensed at all.

  The precise terms and conditions for copying, distribution and
modification follow.

                    GNU GENERAL PUBLIC LICENSE
   TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

  0. This License applies to any program or other work which contains
a notice placed by the copyright holder saying it may be distributed
under the terms of this General Public License.  The "Program", below,
refers to any such program or work, and a "work based on the Program"
means either the Program or any derivative work under copyright law:
that is to say, a work containing the Program or a portion of it,
either verbatim or with modifications and/or translated into another
language.  (Hereinafter, translation is included without limitation in
the term "modification".)  Each licensee is addressed as "you".

Activities other than copying, distribution and modification are not
covered by this License; they are outside its scope.  The act of
running the Program is not restricted, and the output from the Program
is covered only if its contents constitute a work based on the
Program (independent of having been made by running the Program).
Whether that is true depends on what the Program does.

  1. You may copy and distribute verbatim copies of the Program's
source code as you receive it, in any medium, provided that you
conspicuously and appropriately publish on each copy an appropriate
copyright notice and disclaimer of warranty; keep intact all the
notices that refer to this License and to the absence of any warranty;
and give any other recipients of the Program a copy of this License
along with the Program.

You may charge a fee for the physical act of transferring a copy, and
you may at your option offer warranty protection in exchange for a fee.

  2. You may modify your copy or copies of the Program or any portion
of it, thus forming a work based on the Program, and copy and
distribute such modifications or work under the terms of Section 1
above, provided that you also meet all of these conditions:

    a) You must cause the modified files to carry prominent notices
    stating that you changed the files and the date of any change.

    b) You must cause any work that you distribute or publish, that in
    whole or in part contains or is derived from the Program or any
    part thereof, to be licensed as a whole at no charge to all third
    parties under the terms of this License.

    c) If the modified program normally reads commands interactively
    when run, you must cause it, when started running for such
    interactive use in the most ordinary way, to print or display an
    announcement including an appropriate copyright notice and a
    notice that there is no warranty (or else, saying that you provide
    a warranty) and that users may redistribute the program under
    these conditions, and telling the user how to view a copy of this
    License.  (Exception: if the Program itself is interactive but
    does not normally print such an announcement, your work based on
    the Program is not required to print an announcement.)

These requirements apply to the modified work as a whole.  If
identifiable sections of that work are not derived from the Program,
and can be reasonably considered independent and separate works in
themselves, then this License, and its terms, do not apply to those
sections when you distribute them as separate works.  But when you
distribute the same sections as part of a whole which is a work based
on the Program, the distribution of the whole must be on the terms of
this License, whose permissions for other licensees extend to the
entire whole, and thus to each and every part regardless of who wrote it.

Thus, it is not the intent of this section to claim rights or contest
your rights to work written entirely by you; rather, the intent is to
exercise the right to control the distribution of derivative or
collective works based on the Program.

In addition, mere aggregation of another work not based on the Program
with the Program (or with a work based on the Program) on a volume of
a storage or distribution medium does not bring the other work under
the scope of this License.

  3. You may copy and distribute the Program (or a work based on it,
under Section 2) in object code or executable form under the terms of
Sections 1 and 2 above provided that you also do one of the following:

    a) Accompany it with the complete corresponding machine-readable
    source code, which must be distributed under the terms of Sections
    1 and 2 above on a medium customarily used for software interchange; or,

    b) Accompany it with a written offer, valid for at least three
    years, to give any third party, for a charge no more than your
    cost of physically performing source distribution, a complete
    machine-readable copy of the corresponding source code, to be
    distributed under the terms of Sections 1 and 2 above on a medium
    customarily used for software interchange; or,

    c) Accompany it with the information you received as to the offer
    to distribute corresponding source code.  (This alternative is
    allowed only for noncommercial distribution and only if you
    received the program in object code or executable form with such
    an offer, in accord with Subsection b above.)

The source code for a work means the preferred form of the work for
making modifications to it.  For an executable work, complete source
code means all the source code for all modules it contains, plus any
associated interface definition files, plus the scripts used to
control compilation and installation of the executable.  However, as a
special exception, the source code distributed need not include
anything that is normally distributed (in either source or binary
form) with the major components (compiler, kernel, and so on) of the
operating system on which the executable runs, unless that component
itself accompanies the executable.

If distribution of executable or object code is made by offering
access to copy from a designated place, then offering equivalent
access to copy the source code from the same place counts as
distribution of the source code, even though third parties are not
compelled to copy the source along with the object code.

  4. You may not copy, modify, sublicense, or distribute the Program
except as expressly provided under this License.  Any attempt
otherwise to copy, modify, sublicense or distribute the Program is
void, and will automatically terminate your rights under this License.
However, parties who have received copies, or rights, from you under
this License will not have their licenses terminated so long as such
parties remain in full compliance.

  5. You are not required to accept this License, since you have not
signed it.  However, nothing else grants you permission to modify or
distribute the Program or its derivative works.  These actions are
prohibited by law if you do not accept this License.  Therefore, by
modifying or distributing the Program (or any work based on the
Program), you indicate your acceptance of this License to do so, and
all its terms and conditions for copying, distributing or modifying
the Program or works based on it.

  6. Each time you redistribute the Program (or any work based on the
Program), the recipient automatically receives a license from the
original licensor to copy, distribute or modify the Program subject to
these terms and conditions.  You may not impose any further
restrictions on the recipients' exercise of the rights granted herein.
You are not responsible for enforcing compliance by third parties to
this License.

  7. If, as a consequence of a court judgment or allegation of patent
infringement or for any other reason (not limited to patent issues),
conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License.  If you cannot
distribute so as to satisfy simultaneously your obligations under this
License and any other pertinent obligations, then as a consequence you
may not distribute the Program at all.  For example, if a patent
license would not permit royalty-free redistribution of the Program by
all those who receive copies directly or indirectly through you, then
the only way you could satisfy both it and this License would be to
refrain entirely from distribution of the Program.

If any portion of this section is held invalid or unenforceable under
any particular circumstance, the balance of the section is intended to
apply and the section as a whole is intended to apply in other
circumstances.

It is not the purpose of this section to induce you to infringe any
patents or other property right claims or to contest validity of any
such claims; this section has the sole purpose of protecting the
integrity of the free software distribution system, which is
implemented by public license practices.  Many people have made
generous contributions to the wide range of software distributed
through that system in reliance on consistent application of that
system; it is up to the author/donor to decide if he or she is willing
to distribute software through any other system and a licensee cannot
impose that choice.

This section is intended to make thoroughly clear what is believed to
be a consequence of the rest of this License.

  8. If the distribution and/or use of the Program is restricted in
certain countries either by patents or by copyrighted interfaces, the
original copyright holder who places the Program under this License
may add an explicit geographical distribution limitation excluding
those countries, so that distribution is permitted only in or among
countries not thus excluded.  In such case, this License incorporates
the limitation as if written in the body of this License.

  9. The Free Software Foundation may publish revised and/or new versions
of the General Public License from time to time.  Such new versions will
be similar in spirit to the present version, but may differ in detail to
address new problems or concerns.

Each version is given a distinguishing version number.  If the Program
specifies a version number of this License which applies to it and "any
later version", you have the option of following the terms and conditions
either of that version or of any later version published by the Free
Software Foundation.  If the Program does not specify a version number of
this License, you may choose any version ever published by the Free Software
Foundation.

  10. If you wish to incorporate parts of the Program into other free
programs whose distribution conditions are different, write to the author
to ask for permission.  For software which is copyrighted by the Free
Software Foundation, write to the Free Software Foundation; we sometimes
make exceptions for this.  Our decision will be guided by the two goals
of preserving the free status of all derivatives of our free software and
of promoting the sharing and reuse of software generally.

                            NO WARRANTY

  11. BECAUSE THE PROGRAM IS LICENSED FREE OF CHARGE, THERE IS NO WARRANTY
FOR THE PROGRAM, TO THE EXTENT PERMITTED BY APPLICABLE LAW.  EXCEPT WHEN
OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR OTHER PARTIES
PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED
OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.  THE ENTIRE RISK AS
TO THE QUALITY AND PERFORMANCE OF THE PROGRAM IS WITH YOU.  SHOULD THE
PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL NECESSARY SERVICING,
REPAIR OR CORRECTION.

  12. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING
WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY AND/OR
REDISTRIBUTE THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES,
INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING
OUT OF THE USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED
TO LOSS OF DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY
YOU OR THIRD PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER
PROGRAMS), EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE
POSSIBILITY OF SUCH DAMAGES.

                     END OF TERMS AND CONDITIONS

            How to Apply These Terms to Your New Programs

  If you develop a new program, and you want it to be of the greatest
possible use to the public, the best way to achieve this is to make it
free software which everyone can redistribute and change under these terms.

  To do so, attach the following notices to the program.  It is safest
to attach them to the start of each source file to most effectively
convey the exclusion of warranty; and each file should have at least
the "copyright" line and a pointer to where the full notice is found.

    <one line to give the program's name and a brief idea of what it does.>
    Copyright (C) <year>  <name of author>

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License along
    with this program; if not, write to the Free Software Foundation, Inc.,
    51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.

Also add information on how to contact you by electronic and paper mail.

If the program is interactive, make it output a short notice like this
when it starts in an interactive mode:

    Gnomovision version 69, Copyright (C) year name of author
    Gnomovision comes with ABSOLUTELY NO WARRANTY; for details type `show w'.
    This is free software, and you are welcome to redistribute it
    under certain conditions; type `show c' for details.

The hypothetical commands `show w' and `show c' should show the appropriate
parts of the General Public License.  Of course, the commands you use may
be called something other than `show w' and `show c'; they could even be
mouse-clicks or menu items--whatever suits your program.

You should also get your employer (if you work as a programmer) or your
school, if any, to sign a "copyright disclaimer" for the program, if
necessary.  Here is a sample; alter the names:

  Yoyodyne, Inc., hereby disclaims all copyright interest in the program
  `Gnomovision' (which makes passes at compilers) written by James Hacker.

  <signature of Ty Coon>, 1 April 1989
  Ty Coon, President of Vice

This General Public License does not permit incorporating your program into
proprietary programs.  If your program is a subroutine library, you may
consider it more useful to permit linking proprietary applications with the
library.  If this is what you want to do, use the GNU Lesser General
Public License instead of this License.


-------------------------------------------------------------------------------



GNU Lesser General Public License 2.1
-------------------------------------


                  GNU LESSER GENERAL PUBLIC LICENSE
                       Version 2.1, February 1999

 Copyright (C) 1991, 1999 Free Software Foundation, Inc.
 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.

[This is the first released version of the Lesser GPL.  It also counts
 as the successor of the GNU Library Public License, version 2, hence
 the version number 2.1.]

                            Preamble

  The licenses for most software are designed to take away your
freedom to share and change it.  By contrast, the GNU General Public
Licenses are intended to guarantee your freedom to share and change
free software--to make sure the software is free for all its users.

  This license, the Lesser General Public License, applies to some
specially designated software packages--typically libraries--of the
Free Software Foundation and other authors who decide to use it.  You
can use it too, but we suggest you first think carefully about whether
this license or the ordinary General Public License is the better
strategy to use in any particular case, based on the explanations below.

  When we speak of free software, we are referring to freedom of use,
not price.  Our General Public Licenses are designed to make sure that
you have the freedom to distribute copies of free software (and charge
for this service if you wish); that you receive source code or can get
it if you want it; that you can change the software and use pieces of
it in new free programs; and that you are informed that you can do
these things.

  To protect your rights, we need to make restrictions that forbid
distributors to deny you these rights or to ask you to surrender these
rights.  These restrictions translate to certain responsibilities for
you if you distribute copies of the library or if you modify it.

  For example, if you distribute copies of the library, whether gratis
or for a fee, you must give the recipients all the rights that we gave
you.  You must make sure that they, too, receive or can get the source
code.  If you link other code with the library, you must provide
complete object files to the recipients, so that they can relink them
with the library after making changes to the library and recompiling
it.  And you must show them these terms so they know their rights.

  We protect your rights with a two-step method: (1) we copyright the
library, and (2) we offer you this license, which gives you legal
permission to copy, distribute and/or modify the library.

  To protect each distributor, we want to make it very clear that
there is no warranty for the free library.  Also, if the library is
modified by someone else and passed on, the recipients should know
that what they have is not the original version, so that the original
author's reputation will not be affected by problems that might be
introduced by others.

  Finally, software patents pose a constant threat to the existence of
any free program.  We wish to make sure that a company cannot
effectively restrict the users of a free program by obtaining a
restrictive license from a patent holder.  Therefore, we insist that
any patent license obtained for a version of the library must be
consistent with the full freedom of use specified in this license.

  Most GNU software, including some libraries, is covered by the
ordinary GNU General Public License.  This license, the GNU Lesser
General Public License, applies to certain designated libraries, and
is quite different from the ordinary General Public License.  We use
this license for certain libraries in order to permit linking those
libraries into non-free programs.

  When a program is linked with a library, whether statically or using
a shared library, the combination of the two is legally speaking a
combined work, a derivative of the original library.  The ordinary
General Public License therefore permits such linking only if the
entire combination fits its criteria of freedom.  The Lesser General
Public License permits more lax criteria for linking other code with
the library.

  We call this license the "Lesser" General Public License because it
does Less to protect the user's freedom than the ordinary General
Public License.  It also provides other free software developers Less
of an advantage over competing non-free programs.  These disadvantages
are the reason we use the ordinary General Public License for many
libraries.  However, the Lesser license provides advantages in certain
special circumstances.

  For example, on rare occasions, there may be a special need to
encourage the widest possible use of a certain library, so that it becomes
a de-facto standard.  To achieve this, non-free programs must be
allowed to use the library.  A more frequent case is that a free
library does the same job as widely used non-free libraries.  In this
case, there is little to gain by limiting the free library to free
software only, so we use the Lesser General Public License.

  In other cases, permission to use a particular library in non-free
programs enables a greater number of people to use a large body of
free software.  For example, permission to use the GNU C Library in
non-free programs enables many more people to use the whole GNU
operating system, as well as its variant, the GNU/Linux operating
system.

  Although the Lesser General Public License is Less protective of the
users' freedom, it does ensure that the user of a program that is
linked with the Library has the freedom and the wherewithal to run
that program using a modified version of the Library.

  The precise terms and conditions for copying, distribution and
modification follow.  Pay close attention to the difference between a
"work based on the library" and a "work that uses the library".  The
former contains code derived from the library, whereas the latter must
be combined with the library in order to run.

                  GNU LESSER GENERAL PUBLIC LICENSE
   TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

  0. This License Agreement applies to any software library or other
program which contains a notice placed by the copyright holder or
other authorized party saying it may be distributed under the terms of
this Lesser General Public License (also called "this License").
Each licensee is addressed as "you".

  A "library" means a collection of software functions and/or data
prepared so as to be conveniently linked with application programs
(which use some of those functions and data) to form executables.

  The "Library", below, refers to any such software library or work
which has been distributed under these terms.  A "work based on the
Library" means either the Library or any derivative work under
copyright law: that is to say, a work containing the Library or a
portion of it, either verbatim or with modifications and/or translated
straightforwardly into another language.  (Hereinafter, translation is
included without limitation in the term "modification".)

  "Source code" for a work means the preferred form of the work for
making modifications to it.  For a library, complete source code means
all the source code for all modules it contains, plus any associated
interface definition files, plus the scripts used to control compilation
and installation of the library.

  Activities other than copying, distribution and modification are not
covered by this License; they are outside its scope.  The act of
running a program using the Library is not restricted, and output from
such a program is covered only if its contents constitute a work based
on the Library (independent of the use of the Library in a tool for
writing it).  Whether that is true depends on what the Library does
and what the program that uses the Library does.

  1. You may copy and distribute verbatim copies of the Library's
complete source code as you receive it, in any medium, provided that
you conspicuously and appropriately publish on each copy an
appropriate copyright notice and disclaimer of warranty; keep intact
all the notices that refer to this License and to the absence of any
warranty; and distribute a copy of this License along with the
Library.

  You may charge a fee for the physical act of transferring a copy,
and you may at your option offer warranty protection in exchange for a
fee.

  2. You may modify your copy or copies of the Library or any portion
of it, thus forming a work based on the Library, and copy and
distribute such modifications or work under the terms of Section 1
above, provided that you also meet all of these conditions:

    a) The modified work must itself be a software library.

    b) You must cause the files modified to carry prominent notices
    stating that you changed the files and the date of any change.

    c) You must cause the whole of the work to be licensed at no
    charge to all third parties under the terms of this License.

    d) If a facility in the modified Library refers to a function or a
    table of data to be supplied by an application program that uses
    the facility, other than as an argument passed when the facility
    is invoked, then you must make a good faith effort to ensure that,
    in the event an application does not supply such function or
    table, the facility still operates, and performs whatever part of
    its purpose remains meaningful.

    (For example, a function in a library to compute square roots has
    a purpose that is entirely well-defined independent of the
    application.  Therefore, Subsection 2d requires that any
    application-supplied function or table used by this function must
    be optional: if the application does not supply it, the square
    root function must still compute square roots.)

These requirements apply to the modified work as a whole.  If
identifiable sections of that work are not derived from the Library,
and can be reasonably considered independent and separate works in
themselves, then this License, and its terms, do not apply to those
sections when you distribute them as separate works.  But when you
distribute the same sections as part of a whole which is a work based
on the Library, the distribution of the whole must be on the terms of
this License, whose permissions for other licensees extend to the
entire whole, and thus to each and every part regardless of who wrote
it.

Thus, it is not the intent of this section to claim rights or contest
your rights to work written entirely by you; rather, the intent is to
exercise the right to control the distribution of derivative or
collective works based on the Library.

In addition, mere aggregation of another work not based on the Library
with the Library (or with a work based on the Library) on a volume of
a storage or distribution medium does not bring the other work under
the scope of this License.

  3. You may opt to apply the terms of the ordinary GNU General Public
License instead of this License to a given copy of the Library.  To do
this, you must alter all the notices that refer to this License, so
that they refer to the ordinary GNU General Public License, version 2,
instead of to this License.  (If a newer version than version 2 of the
ordinary GNU General Public License has appeared, then you can specify
that version instead if you wish.)  Do not make any other change in
these notices.

  Once this change is made in a given copy, it is irreversible for
that copy, so the ordinary GNU General Public License applies to all
subsequent copies and derivative works made from that copy.

  This option is useful when you wish to copy part of the code of
the Library into a program that is not a library.

  4. You may copy and distribute the Library (or a portion or
derivative of it, under Section 2) in object code or executable form
under the terms of Sections 1 and 2 above provided that you accompany
it with the complete corresponding machine-readable source code, which
must be distributed under the terms of Sections 1 and 2 above on a
medium customarily used for software interchange.

  If distribution of object code is made by offering access to copy
from a designated place, then offering equivalent access to copy the
source code from the same place satisfies the requirement to
distribute the source code, even though third parties are not
compelled to copy the source along with the object code.

  5. A program that contains no derivative of any portion of the
Library, but is designed to work with the Library by being compiled or
linked with it, is called a "work that uses the Library".  Such a
work, in isolation, is not a derivative work of the Library, and
therefore falls outside the scope of this License.

  However, linking a "work that uses the Library" with the Library
creates an executable that is a derivative of the Library (because it
contains portions of the Library), rather than a "work that uses the
library".  The executable is therefore covered by this License.
Section 6 states terms for distribution of such executables.

  When a "work that uses the Library" uses material from a header file
that is part of the Library, the object code for the work may be a
derivative work of the Library even though the source code is not.
Whether this is true is especially significant if the work can be
linked without the Library, or if the work is itself a library.  The
threshold for this to be true is not precisely defined by law.

  If such an object file uses only numerical parameters, data
structure layouts and accessors, and small macros and small inline
functions (ten lines or less in length), then the use of the object
file is unrestricted, regardless of whether it is legally a derivative
work.  (Executables containing this object code plus portions of the
Library will still fall under Section 6.)

  Otherwise, if the work is a derivative of the Library, you may
distribute the object code for the work under the terms of Section 6.
Any executables containing that work also fall under Section 6,
whether or not they are linked directly with the Library itself.

  6. As an exception to the Sections above, you may also combine or
link a "work that uses the Library" with the Library to produce a
work containing portions of the Library, and distribute that work
under terms of your choice, provided that the terms permit
modification of the work for the customer's own use and reverse
engineering for debugging such modifications.

  You must give prominent notice with each copy of the work that the
Library is used in it and that the Library and its use are covered by
this License.  You must supply a copy of this License.  If the work
during execution displays copyright notices, you must include the
copyright notice for the Library among them, as well as a reference
directing the user to the copy of this License.  Also, you must do one
of these things:

    a) Accompany the work with the complete corresponding
    machine-readable source code for the Library including whatever
    changes were used in the work (which must be distributed under
    Sections 1 and 2 above); and, if the work is an executable linked
    with the Library, with the complete machine-readable "work that
    uses the Library", as object code and/or source code, so that the
    user can modify the Library and then relink to produce a modified
    executable containing the modified Library.  (It is understood
    that the user who changes the contents of definitions files in the
    Library will not necessarily be able to recompile the application
    to use the modified definitions.)

    b) Use a suitable shared library mechanism for linking with the
    Library.  A suitable mechanism is one that (1) uses at run time a
    copy of the library already present on the user's computer system,
    rather than copying library functions into the executable, and (2)
    will operate properly with a modified version of the library, if
    the user installs one, as long as the modified version is
    interface-compatible with the version that the work was made with.

    c) Accompany the work with a written offer, valid for at
    least three years, to give the same user the materials
    specified in Subsection 6a, above, for a charge no more
    than the cost of performing this distribution.

    d) If distribution of the work is made by offering access to copy
    from a designated place, offer equivalent access to copy the above
    specified materials from the same place.

    e) Verify that the user has already received a copy of these
    materials or that you have already sent this user a copy.

  For an executable, the required form of the "work that uses the
Library" must include any data and utility programs needed for
reproducing the executable from it.  However, as a special exception,
the materials to be distributed need not include anything that is
normally distributed (in either source or binary form) with the major
components (compiler, kernel, and so on) of the operating system on
which the executable runs, unless that component itself accompanies
the executable.

  It may happen that this requirement contradicts the license
restrictions of other proprietary libraries that do not normally
accompany the operating system.  Such a contradiction means you cannot
use both them and the Library together in an executable that you
distribute.

  7. You may place library facilities that are a work based on the
Library side-by-side in a single library together with other library
facilities not covered by this License, and distribute such a combined
library, provided that the separate distribution of the work based on
the Library and of the other library facilities is otherwise
permitted, and provided that you do these two things:

    a) Accompany the combined library with a copy of the same work
    based on the Library, uncombined with any other library
    facilities.  This must be distributed under the terms of the
    Sections above.

    b) Give prominent notice with the combined library of the fact
    that part of it is a work based on the Library, and explaining
    where to find the accompanying uncombined form of the same work.

  8. You may not copy, modify, sublicense, link with, or distribute
the Library except as expressly provided under this License.  Any
attempt otherwise to copy, modify, sublicense, link with, or
distribute the Library is void, and will automatically terminate your
rights under this License.  However, parties who have received copies,
or rights, from you under this License will not have their licenses
terminated so long as such parties remain in full compliance.

  9. You are not required to accept this License, since you have not
signed it.  However, nothing else grants you permission to modify or
distribute the Library or its derivative works.  These actions are
prohibited by law if you do not accept this License.  Therefore, by
modifying or distributing the Library (or any work based on the
Library), you indicate your acceptance of this License to do so, and
all its terms and conditions for copying, distributing or modifying
the Library or works based on it.

  10. Each time you redistribute the Library (or any work based on the
Library), the recipient automatically receives a license from the
original licensor to copy, distribute, link with or modify the Library
subject to these terms and conditions.  You may not impose any further
restrictions on the recipients' exercise of the rights granted herein.
You are not responsible for enforcing compliance by third parties with
this License.

  11. If, as a consequence of a court judgment or allegation of patent
infringement or for any other reason (not limited to patent issues),
conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License.  If you cannot
distribute so as to satisfy simultaneously your obligations under this
License and any other pertinent obligations, then as a consequence you
may not distribute the Library at all.  For example, if a patent
license would not permit royalty-free redistribution of the Library by
all those who receive copies directly or indirectly through you, then
the only way you could satisfy both it and this License would be to
refrain entirely from distribution of the Library.

If any portion of this section is held invalid or unenforceable under any
particular circumstance, the balance of the section is intended to apply,
and the section as a whole is intended to apply in other circumstances.

It is not the purpose of this section to induce you to infringe any
patents or other property right claims or to contest validity of any
such claims; this section has the sole purpose of protecting the
integrity of the free software distribution system which is
implemented by public license practices.  Many people have made
generous contributions to the wide range of software distributed
through that system in reliance on consistent application of that
system; it is up to the author/donor to decide if he or she is willing
to distribute software through any other system and a licensee cannot
impose that choice.

This section is intended to make thoroughly clear what is believed to
be a consequence of the rest of this License.

  12. If the distribution and/or use of the Library is restricted in
certain countries either by patents or by copyrighted interfaces, the
original copyright holder who places the Library under this License may add
an explicit geographical distribution limitation excluding those countries,
so that distribution is permitted only in or among countries not thus
excluded.  In such case, this License incorporates the limitation as if
written in the body of this License.

  13. The Free Software Foundation may publish revised and/or new
versions of the Lesser General Public License from time to time.
Such new versions will be similar in spirit to the present version,
but may differ in detail to address new problems or concerns.

Each version is given a distinguishing version number.  If the Library
specifies a version number of this License which applies to it and
"any later version", you have the option of following the terms and
conditions either of that version or of any later version published by
the Free Software Foundation.  If the Library does not specify a
license version number, you may choose any version ever published by
the Free Software Foundation.

  14. If you wish to incorporate parts of the Library into other free
programs whose distribution conditions are incompatible with these,
write to the author to ask for permission.  For software which is
copyrighted by the Free Software Foundation, write to the Free
Software Foundation; we sometimes make exceptions for this.  Our
decision will be guided by the two goals of preserving the free status
of all derivatives of our free software and of promoting the sharing
and reuse of software generally.

                            NO WARRANTY

  15. BECAUSE THE LIBRARY IS LICENSED FREE OF CHARGE, THERE IS NO
WARRANTY FOR THE LIBRARY, TO THE EXTENT PERMITTED BY APPLICABLE LAW.
EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
OTHER PARTIES PROVIDE THE LIBRARY "AS IS" WITHOUT WARRANTY OF ANY
KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE
LIBRARY IS WITH YOU.  SHOULD THE LIBRARY PROVE DEFECTIVE, YOU ASSUME
THE COST OF ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

  16. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
AND/OR REDISTRIBUTE THE LIBRARY AS PERMITTED ABOVE, BE LIABLE TO YOU
FOR DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR
CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE
LIBRARY (INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING
RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A
FAILURE OF THE LIBRARY TO OPERATE WITH ANY OTHER SOFTWARE), EVEN IF
SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
DAMAGES.

                     END OF TERMS AND CONDITIONS

           How to Apply These Terms to Your New Libraries

  If you develop a new library, and you want it to be of the greatest
possible use to the public, we recommend making it free software that
everyone can redistribute and change.  You can do so by permitting
redistribution under these terms (or, alternatively, under the terms of the
ordinary General Public License).

  To apply these terms, attach the following notices to the library.  It is
safest to attach them to the start of each source file to most effectively
convey the exclusion of warranty; and each file should have at least the
"copyright" line and a pointer to where the full notice is found.

    <one line to give the library's name and a brief idea of what it does.>
    Copyright (C) <year>  <name of author>

    This library is free software; you can redistribute it and/or
    modify it under the terms of the GNU Lesser General Public
    License as published by the Free Software Foundation; either
    version 2.1 of the License, or (at your option) any later version.

    This library is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
    Lesser General Public License for more details.

    You should have received a copy of the GNU Lesser General Public
    License along with this library; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA

Also add information on how to contact you by electronic and paper mail.

You should also get your employer (if you work as a programmer) or your
school, if any, to sign a "copyright disclaimer" for the library, if
necessary.  Here is a sample; alter the names:

  Yoyodyne, Inc., hereby disclaims all copyright interest in the
  library `Frob' (a library for tweaking knobs) written by James Random Hacker.

  <signature of Ty Coon>, 1 April 1990
  Ty Coon, President of Vice

That's all there is to it!


-------------------------------------------------------------------------------



GNU Lesser General Public License 3.0 (with GNU GPL 3.0)
--------------------------------------------------------


                   GNU LESSER GENERAL PUBLIC LICENSE
                       Version 3, 29 June 2007

 Copyright (C) 2007 Free Software Foundation, Inc. <https://fsf.org/>
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.


  This version of the GNU Lesser General Public License incorporates
the terms and conditions of version 3 of the GNU General Public
License, supplemented by the additional permissions listed below.

  0. Additional Definitions.

  As used herein, "this License" refers to version 3 of the GNU Lesser
General Public License, and the "GNU GPL" refers to version 3 of the GNU
General Public License.

  "The Library" refers to a covered work governed by this License,
other than an Application or a Combined Work as defined below.

  An "Application" is any work that makes use of an interface provided
by the Library, but which is not otherwise based on the Library.
Defining a subclass of a class defined by the Library is deemed a mode
of using an interface provided by the Library.

  A "Combined Work" is a work produced by combining or linking an
Application with the Library.  The particular version of the Library
with which the Combined Work was made is also called the "Linked
Version".

  The "Minimal Corresponding Source" for a Combined Work means the
Corresponding Source for the Combined Work, excluding any source code
for portions of the Combined Work that, considered in isolation, are
based on the Application, and not on the Linked Version.

  The "Corresponding Application Code" for a Combined Work means the
object code and/or source code for the Application, including any data
and utility programs needed for reproducing the Combined Work from the
Application, but excluding the System Libraries of the Combined Work.

  1. Exception to Section 3 of the GNU GPL.

  You may convey a covered work under sections 3 and 4 of this License
without being bound by section 3 of the GNU GPL.

  2. Conveying Modified Versions.

  If you modify a copy of the Library, and, in your modifications, a
facility refers to a function or data to be supplied by an Application
that uses the facility (other than as an argument passed when the
facility is invoked), then you may convey a copy of the modified
version:

   a) under this License, provided that you make a good faith effort to
   ensure that, in the event an Application does not supply the
   function or data, the facility still operates, and performs
   whatever part of its purpose remains meaningful, or

   b) under the GNU GPL, with none of the additional permissions of
   this License applicable to that copy.

  3. Object Code Incorporating Material from Library Header Files.

  The object code form of an Application may incorporate material from
a header file that is part of the Library.  You may convey such object
code under terms of your choice, provided that, if the incorporated
material is not limited to numerical parameters, data structure
layouts and accessors, or small macros, inline functions and templates
(ten or fewer lines in length), you do both of the following:

   a) Give prominent notice with each copy of the object code that the
   Library is used in it and that the Library and its use are
   covered by this License.

   b) Accompany the object code with a copy of the GNU GPL and this license
   document.

  4. Combined Works.

  You may convey a Combined Work under terms of your choice that,
taken together, effectively do not restrict modification of the
portions of the Library contained in the Combined Work and reverse
engineering for debugging such modifications, if you also do each of
the following:

   a) Give prominent notice with each copy of the Combined Work that
   the Library is used in it and that the Library and its use are
   covered by this License.

   b) Accompany the Combined Work with a copy of the GNU GPL and this license
   document.

   c) For a Combined Work that displays copyright notices during
   execution, include the copyright notice for the Library among
   these notices, as well as a reference directing the user to the
   copies of the GNU GPL and this license document.

   d) Do one of the following:

       0) Convey the Minimal Corresponding Source under the terms of this
       License, and the Corresponding Application Code in a form
       suitable for, and under terms that permit, the user to
       recombine or relink the Application with a modified version of
       the Linked Version to produce a modified Combined Work, in the
       manner specified by section 6 of the GNU GPL for conveying
       Corresponding Source.

       1) Use a suitable shared library mechanism for linking with the
       Library.  A suitable mechanism is one that (a) uses at run time
       a copy of the Library already present on the user's computer
       system, and (b) will operate properly with a modified version
       of the Library that is interface-compatible with the Linked
       Version.

   e) Provide Installation Information, but only if you would otherwise
   be required to provide such information under section 6 of the
   GNU GPL, and only to the extent that such information is
   necessary to install and execute a modified version of the
   Combined Work produced by recombining or relinking the
   Application with a modified version of the Linked Version. (If
   you use option 4d0, the Installation Information must accompany
   the Minimal Corresponding Source and Corresponding Application
   Code. If you use option 4d1, you must provide the Installation
   Information in the manner specified by section 6 of the GNU GPL
   for conveying Corresponding Source.)

  5. Combined Libraries.

  You may place library facilities that are a work based on the
Library side by side in a single library together with other library
facilities that are not Applications and are not covered by this
License, and convey such a combined library under terms of your
choice, if you do both of the following:

   a) Accompany the combined library with a copy of the same work based
   on the Library, uncombined with any other library facilities,
   conveyed under the terms of this License.

   b) Give prominent notice with the combined library that part of it
   is a work based on the Library, and explaining where to find the
   accompanying uncombined form of the same work.

  6. Revised Versions of the GNU Lesser General Public License.

  The Free Software Foundation may publish revised and/or new versions
of the GNU Lesser General Public License from time to time. Such new
versions will be similar in spirit to the present version, but may
differ in detail to address new problems or concerns.

  Each version is given a distinguishing version number. If the
Library as you received it specifies that a certain numbered version
of the GNU Lesser General Public License "or any later version"
applies to it, you have the option of following the terms and
conditions either of that published version or of any later version
published by the Free Software Foundation. If the Library as you
received it does not specify a version number of the GNU Lesser
General Public License, you may choose any version of the GNU Lesser
General Public License ever published by the Free Software Foundation.

  If the Library as you received it specifies that a proxy can decide
whether future versions of the GNU Lesser General Public License shall
apply, that proxy's public statement of acceptance of any version is
permanent authorization for you to choose that version for the
Library.


                    GNU GENERAL PUBLIC LICENSE
                       Version 3, 29 June 2007

 Copyright (C) 2007 Free Software Foundation, Inc. <https://fsf.org/>
 Everyone is permitted to copy and distribute verbatim copies
 of this license document, but changing it is not allowed.

                            Preamble

  The GNU General Public License is a free, copyleft license for
software and other kinds of works.

  The licenses for most software and other practical works are designed
to take away your freedom to share and change the works.  By contrast,
the GNU General Public License is intended to guarantee your freedom to
share and change all versions of a program--to make sure it remains free
software for all its users.  We, the Free Software Foundation, use the
GNU General Public License for most of our software; it applies also to
any other work released this way by its authors.  You can apply it to
your programs, too.

  When we speak of free software, we are referring to freedom, not
price.  Our General Public Licenses are designed to make sure that you
have the freedom to distribute copies of free software (and charge for
them if you wish), that you receive source code or can get it if you
want it, that you can change the software or use pieces of it in new
free programs, and that you know you can do these things.

  To protect your rights, we need to prevent others from denying you
these rights or asking you to surrender the rights.  Therefore, you have
certain responsibilities if you distribute copies of the software, or if
you modify it: responsibilities to respect the freedom of others.

  For example, if you distribute copies of such a program, whether
gratis or for a fee, you must pass on to the recipients the same
freedoms that you received.  You must make sure that they, too, receive
or can get the source code.  And you must show them these terms so they
know their rights.

  Developers that use the GNU GPL protect your rights with two steps:
(1) assert copyright on the software, and (2) offer you this License
giving you legal permission to copy, distribute and/or modify it.

  For the developers' and authors' protection, the GPL clearly explains
that there is no warranty for this free software.  For both users' and
authors' sake, the GPL requires that modified versions be marked as
changed, so that their problems will not be attributed erroneously to
authors of previous versions.

  Some devices are designed to deny users access to install or run
modified versions of the software inside them, although the manufacturer
can do so.  This is fundamentally incompatible with the aim of
protecting users' freedom to change the software.  The systematic
pattern of such abuse occurs in the area of products for individuals to
use, which is precisely where it is most unacceptable.  Therefore, we
have designed this version of the GPL to prohibit the practice for those
products.  If such problems arise substantially in other domains, we
stand ready to extend this provision to those domains in future versions
of the GPL, as needed to protect the freedom of users.

  Finally, every program is threatened constantly by software patents.
States should not allow patents to restrict development and use of
software on general-purpose computers, but in those that do, we wish to
avoid the special danger that patents applied to a free program could
make it effectively proprietary.  To prevent this, the GPL assures that
patents cannot be used to render the program non-free.

  The precise terms and conditions for copying, distribution and
modification follow.

                       TERMS AND CONDITIONS

  0. Definitions.

  "This License" refers to version 3 of the GNU General Public License.

  "Copyright" also means copyright-like laws that apply to other kinds of
works, such as semiconductor masks.

  "The Program" refers to any copyrightable work licensed under this
License.  Each licensee is addressed as "you".  "Licensees" and
"recipients" may be individuals or organizations.

  To "modify" a work means to copy from or adapt all or part of the work
in a fashion requiring copyright permission, other than the making of an
exact copy.  The resulting work is called a "modified version" of the
earlier work or a work "based on" the earlier work.

  A "covered work" means either the unmodified Program or a work based
on the Program.

  To "propagate" a work means to do anything with it that, without
permission, would make you directly or secondarily liable for
infringement under applicable copyright law, except executing it on a
computer or modifying a private copy.  Propagation includes copying,
distribution (with or without modification), making available to the
public, and in some countries other activities as well.

  To "convey" a work means any kind of propagation that enables other
parties to make or receive copies.  Mere interaction with a user through
a computer network, with no transfer of a copy, is not conveying.

  An interactive user interface displays "Appropriate Legal Notices"
to the extent that it includes a convenient and prominently visible
feature that (1) displays an appropriate copyright notice, and (2)
tells the user that there is no warranty for the work (except to the
extent that warranties are provided), that licensees may convey the
work under this License, and how to view a copy of this License.  If
the interface presents a list of user commands or options, such as a
menu, a prominent item in the list meets this criterion.

  1. Source Code.

  The "source code" for a work means the preferred form of the work
for making modifications to it.  "Object code" means any non-source
form of a work.

  A "Standard Interface" means an interface that either is an official
standard defined by a recognized standards body, or, in the case of
interfaces specified for a particular programming language, one that
is widely used among developers working in that language.

  The "System Libraries" of an executable work include anything, other
than the work as a whole, that (a) is included in the normal form of
packaging a Major Component, but which is not part of that Major
Component, and (b) serves only to enable use of the work with that
Major Component, or to implement a Standard Interface for which an
implementation is available to the public in source code form.  A
"Major Component", in this context, means a major essential component
(kernel, window system, and so on) of the specific operating system
(if any) on which the executable work runs, or a compiler used to
produce the work, or an object code interpreter used to run it.

  The "Corresponding Source" for a work in object code form means all
the source code needed to generate, install, and (for an executable
work) run the object code and to modify the work, including scripts to
control those activities.  However, it does not include the work's
System Libraries, or general-purpose tools or generally available free
programs which are used unmodified in performing those activities but
which are not part of the work.  For example, Corresponding Source
includes interface definition files associated with source files for
the work, and the source code for shared libraries and dynamically
linked subprograms that the work is specifically designed to require,
such as by intimate data communication or control flow between those
subprograms and other parts of the work.

  The Corresponding Source need not include anything that users
can regenerate automatically from other parts of the Corresponding
Source.

  The Corresponding Source for a work in source code form is that
same work.

  2. Basic Permissions.

  All rights granted under this License are granted for the term of
copyright on the Program, and are irrevocable provided the stated
conditions are met.  This License explicitly affirms your unlimited
permission to run the unmodified Program.  The output from running a
covered work is covered by this License only if the output, given its
content, constitutes a covered work.  This License acknowledges your
rights of fair use or other equivalent, as provided by copyright law.

  You may make, run and propagate covered works that you do not
convey, without conditions so long as your license otherwise remains
in force.  You may convey covered works to others for the sole purpose
of having them make modifications exclusively for you, or provide you
with facilities for running those works, provided that you comply with
the terms of this License in conveying all material for which you do
not control copyright.  Those thus making or running the covered works
for you must do so exclusively on your behalf, under your direction
and control, on terms that prohibit them from making any copies of
your copyrighted material outside their relationship with you.

  Conveying under any other circumstances is permitted solely under
the conditions stated below.  Sublicensing is not allowed; section 10
makes it unnecessary.

  3. Protecting Users' Legal Rights From Anti-Circumvention Law.

  No covered work shall be deemed part of an effective technological
measure under any applicable law fulfilling obligations under article
11 of the WIPO copyright treaty adopted on 20 December 1996, or
similar laws prohibiting or restricting circumvention of such
measures.

  When you convey a covered work, you waive any legal power to forbid
circumvention of technological measures to the extent such circumvention
is effected by exercising rights under this License with respect to
the covered work, and you disclaim any intention to limit operation or
modification of the work as a means of enforcing, against the work's
users, your or third parties' legal rights to forbid circumvention of
technological measures.

  4. Conveying Verbatim Copies.

  You may convey verbatim copies of the Program's source code as you
receive it, in any medium, provided that you conspicuously and
appropriately publish on each copy an appropriate copyright notice;
keep intact all notices stating that this License and any
non-permissive terms added in accord with section 7 apply to the code;
keep intact all notices of the absence of any warranty; and give all
recipients a copy of this License along with the Program.

  You may charge any price or no price for each copy that you convey,
and you may offer support or warranty protection for a fee.

  5. Conveying Modified Source Versions.

  You may convey a work based on the Program, or the modifications to
produce it from the Program, in the form of source code under the
terms of section 4, provided that you also meet all of these conditions:

    a) The work must carry prominent notices stating that you modified
    it, and giving a relevant date.

    b) The work must carry prominent notices stating that it is
    released under this License and any conditions added under section
    7.  This requirement modifies the requirement in section 4 to
    "keep intact all notices".

    c) You must license the entire work, as a whole, under this
    License to anyone who comes into possession of a copy.  This
    License will therefore apply, along with any applicable section 7
    additional terms, to the whole of the work, and all its parts,
    regardless of how they are packaged.  This License gives no
    permission to license the work in any other way, but it does not
    invalidate such permission if you have separately received it.

    d) If the work has interactive user interfaces, each must display
    Appropriate Legal Notices; however, if the Program has interactive
    interfaces that do not display Appropriate Legal Notices, your
    work need not make them do so.

  A compilation of a covered work with other separate and independent
works, which are not by their nature extensions of the covered work,
and which are not combined with it such as to form a larger program,
in or on a volume of a storage or distribution medium, is called an
"aggregate" if the compilation and its resulting copyright are not
used to limit the access or legal rights of the compilation's users
beyond what the individual works permit.  Inclusion of a covered work
in an aggregate does not cause this License to apply to the other
parts of the aggregate.

  6. Conveying Non-Source Forms.

  You may convey a covered work in object code form under the terms
of sections 4 and 5, provided that you also convey the
machine-readable Corresponding Source under the terms of this License,
in one of these ways:

    a) Convey the object code in, or embodied in, a physical product
    (including a physical distribution medium), accompanied by the
    Corresponding Source fixed on a durable physical medium
    customarily used for software interchange.

    b) Convey the object code in, or embodied in, a physical product
    (including a physical distribution medium), accompanied by a
    written offer, valid for at least three years and valid for as
    long as you offer spare parts or customer support for that product
    model, to give anyone who possesses the object code either (1) a
    copy of the Corresponding Source for all the software in the
    product that is covered by this License, on a durable physical
    medium customarily used for software interchange, for a price no
    more than your reasonable cost of physically performing this
    conveying of source, or (2) access to copy the
    Corresponding Source from a network server at no charge.

    c) Convey individual copies of the object code with a copy of the
    written offer to provide the Corresponding Source.  This
    alternative is allowed only occasionally and noncommercially, and
    only if you received the object code with such an offer, in accord
    with subsection 6b.

    d) Convey the object code by offering access from a designated
    place (gratis or for a charge), and offer equivalent access to the
    Corresponding Source in the same way through the same place at no
    further charge.  You need not require recipients to copy the
    Corresponding Source along with the object code.  If the place to
    copy the object code is a network server, the Corresponding Source
    may be on a different server (operated by you or a third party)
    that supports equivalent copying facilities, provided you maintain
    clear directions next to the object code saying where to find the
    Corresponding Source.  Regardless of what server hosts the
    Corresponding Source, you remain obligated to ensure that it is
    available for as long as needed to satisfy these requirements.

    e) Convey the object code using peer-to-peer transmission, provided
    you inform other peers where the object code and Corresponding
    Source of the work are being offered to the general public at no
    charge under subsection 6d.

  A separable portion of the object code, whose source code is excluded
from the Corresponding Source as a System Library, need not be
included in conveying the object code work.

  A "User Product" is either (1) a "consumer product", which means any
tangible personal property which is normally used for personal, family,
or household purposes, or (2) anything designed or sold for incorporation
into a dwelling.  In determining whether a product is a consumer product,
doubtful cases shall be resolved in favor of coverage.  For a particular
product received by a particular user, "normally used" refers to a
typical or common use of that class of product, regardless of the status
of the particular user or of the way in which the particular user
actually uses, or expects or is expected to use, the product.  A product
is a consumer product regardless of whether the product has substantial
commercial, industrial or non-consumer uses, unless such uses represent
the only significant mode of use of the product.

  "Installation Information" for a User Product means any methods,
procedures, authorization keys, or other information required to install
and execute modified versions of a covered work in that User Product from
a modified version of its Corresponding Source.  The information must
suffice to ensure that the continued functioning of the modified object
code is in no case prevented or interfered with solely because
modification has been made.

  If you convey an object code work under this section in, or with, or
specifically for use in, a User Product, and the conveying occurs as
part of a transaction in which the right of possession and use of the
User Product is transferred to the recipient in perpetuity or for a
fixed term (regardless of how the transaction is characterized), the
Corresponding Source conveyed under this section must be accompanied
by the Installation Information.  But this requirement does not apply
if neither you nor any third party retains the ability to install
modified object code on the User Product (for example, the work has
been installed in ROM).

  The requirement to provide Installation Information does not include a
requirement to continue to provide support service, warranty, or updates
for a work that has been modified or installed by the recipient, or for
the User Product in which it has been modified or installed.  Access to a
network may be denied when the modification itself materially and
adversely affects the operation of the network or violates the rules and
protocols for communication across the network.

  Corresponding Source conveyed, and Installation Information provided,
in accord with this section must be in a format that is publicly
documented (and with an implementation available to the public in
source code form), and must require no special password or key for
unpacking, reading or copying.

  7. Additional Terms.

  "Additional permissions" are terms that supplement the terms of this
License by making exceptions from one or more of its conditions.
Additional permissions that are applicable to the entire Program shall
be treated as though they were included in this License, to the extent
that they are valid under applicable law.  If additional permissions
apply only to part of the Program, that part may be used separately
under those permissions, but the entire Program remains governed by
this License without regard to the additional permissions.

  When you convey a copy of a covered work, you may at your option
remove any additional permissions from that copy, or from any part of
it.  (Additional permissions may be written to require their own
removal in certain cases when you modify the work.)  You may place
additional permissions on material, added by you to a covered work,
for which you have or can give appropriate copyright permission.

  Notwithstanding any other provision of this License, for material you
add to a covered work, you may (if authorized by the copyright holders of
that material) supplement the terms of this License with terms:

    a) Disclaiming warranty or limiting liability differently from the
    terms of sections 15 and 16 of this License; or

    b) Requiring preservation of specified reasonable legal notices or
    author attributions in that material or in the Appropriate Legal
    Notices displayed by works containing it; or

    c) Prohibiting misrepresentation of the origin of that material, or
    requiring that modified versions of such material be marked in
    reasonable ways as different from the original version; or

    d) Limiting the use for publicity purposes of names of licensors or
    authors of the material; or

    e) Declining to grant rights under trademark law for use of some
    trade names, trademarks, or service marks; or

    f) Requiring indemnification of licensors and authors of that
    material by anyone who conveys the material (or modified versions of
    it) with contractual assumptions of liability to the recipient, for
    any liability that these contractual assumptions directly impose on
    those licensors and authors.

  All other non-permissive additional terms are considered "further
restrictions" within the meaning of section 10.  If the Program as you
received it, or any part of it, contains a notice stating that it is
governed by this License along with a term that is a further
restriction, you may remove that term.  If a license document contains
a further restriction but permits relicensing or conveying under this
License, you may add to a covered work material governed by the terms
of that license document, provided that the further restriction does
not survive such relicensing or conveying.

  If you add terms to a covered work in accord with this section, you
must place, in the relevant source files, a statement of the
additional terms that apply to those files, or a notice indicating
where to find the applicable terms.

  Additional terms, permissive or non-permissive, may be stated in the
form of a separately written license, or stated as exceptions;
the above requirements apply either way.

  8. Termination.

  You may not propagate or modify a covered work except as expressly
provided under this License.  Any attempt otherwise to propagate or
modify it is void, and will automatically terminate your rights under
this License (including any patent licenses granted under the third
paragraph of section 11).

  However, if you cease all violation of this License, then your
license from a particular copyright holder is reinstated (a)
provisionally, unless and until the copyright holder explicitly and
finally terminates your license, and (b) permanently, if the copyright
holder fails to notify you of the violation by some reasonable means
prior to 60 days after the cessation.

  Moreover, your license from a particular copyright holder is
reinstated permanently if the copyright holder notifies you of the
violation by some reasonable means, this is the first time you have
received notice of violation of this License (for any work) from that
copyright holder, and you cure the violation prior to 30 days after
your receipt of the notice.

  Termination of your rights under this section does not terminate the
licenses of parties who have received copies or rights from you under
this License.  If your rights have been terminated and not permanently
reinstated, you do not qualify to receive new licenses for the same
material under section 10.

  9. Acceptance Not Required for Having Copies.

  You are not required to accept this License in order to receive or
run a copy of the Program.  Ancillary propagation of a covered work
occurring solely as a consequence of using peer-to-peer transmission
to receive a copy likewise does not require acceptance.  However,
nothing other than this License grants you permission to propagate or
modify any covered work.  These actions infringe copyright if you do
not accept this License.  Therefore, by modifying or propagating a
covered work, you indicate your acceptance of this License to do so.

  10. Automatic Licensing of Downstream Recipients.

  Each time you convey a covered work, the recipient automatically
receives a license from the original licensors, to run, modify and
propagate that work, subject to this License.  You are not responsible
for enforcing compliance by third parties with this License.

  An "entity transaction" is a transaction transferring control of an
organization, or substantially all assets of one, or subdividing an
organization, or merging organizations.  If propagation of a covered
work results from an entity transaction, each party to that
transaction who receives a copy of the work also receives whatever
licenses to the work the party's predecessor in interest had or could
give under the previous paragraph, plus a right to possession of the
Corresponding Source of the work from the predecessor in interest, if
the predecessor has it or can get it with reasonable efforts.

  You may not impose any further restrictions on the exercise of the
rights granted or affirmed under this License.  For example, you may
not impose a license fee, royalty, or other charge for exercise of
rights granted under this License, and you may not initiate litigation
(including a cross-claim or counterclaim in a lawsuit) alleging that
any patent claim is infringed by making, using, selling, offering for
sale, or importing the Program or any portion of it.

  11. Patents.

  A "contributor" is a copyright holder who authorizes use under this
License of the Program or a work on which the Program is based.  The
work thus licensed is called the contributor's "contributor version".

  A contributor's "essential patent claims" are all patent claims
owned or controlled by the contributor, whether already acquired or
hereafter acquired, that would be infringed by some manner, permitted
by this License, of making, using, or selling its contributor version,
but do not include claims that would be infringed only as a
consequence of further modification of the contributor version.  For
purposes of this definition, "control" includes the right to grant
patent sublicenses in a manner consistent with the requirements of
this License.

  Each contributor grants you a non-exclusive, worldwide, royalty-free
patent license under the contributor's essential patent claims, to
make, use, sell, offer for sale, import and otherwise run, modify and
propagate the contents of its contributor version.

  In the following three paragraphs, a "patent license" is any express
agreement or commitment, however denominated, not to enforce a patent
(such as an express permission to practice a patent or covenant not to
sue for patent infringement).  To "grant" such a patent license to a
party means to make such an agreement or commitment not to enforce a
patent against the party.

  If you convey a covered work, knowingly relying on a patent license,
and the Corresponding Source of the work is not available for anyone
to copy, free of charge and under the terms of this License, through a
publicly available network server or other readily accessible means,
then you must either (1) cause the Corresponding Source to be so
available, or (2) arrange to deprive yourself of the benefit of the
patent license for this particular work, or (3) arrange, in a manner
consistent with the requirements of this License, to extend the patent
license to downstream recipients.  "Knowingly relying" means you have
actual knowledge that, but for the patent license, your conveying the
covered work in a country, or your recipient's use of the covered work
in a country, would infringe one or more identifiable patents in that
country that you have reason to believe are valid.

  If, pursuant to or in connection with a single transaction or
arrangement, you convey, or propagate by procuring conveyance of, a
covered work, and grant a patent license to some of the parties
receiving the covered work authorizing them to use, propagate, modify
or convey a specific copy of the covered work, then the patent license
you grant is automatically extended to all recipients of the covered
work and works based on it.

  A patent license is "discriminatory" if it does not include within
the scope of its coverage, prohibits the exercise of, or is
conditioned on the non-exercise of one or more of the rights that are
specifically granted under this License.  You may not convey a covered
work if you are a party to an arrangement with a third party that is
in the business of distributing software, under which you make payment
to the third party based on the extent of your activity of conveying
the work, and under which the third party grants, to any of the
parties who would receive the covered work from you, a discriminatory
patent license (a) in connection with copies of the covered work
conveyed by you (or copies made from those copies), or (b) primarily
for and in connection with specific products or compilations that
contain the covered work, unless you entered into that arrangement,
or that patent license was granted, prior to 28 March 2007.

  Nothing in this License shall be construed as excluding or limiting
any implied license or other defenses to infringement that may
otherwise be available to you under applicable patent law.

  12. No Surrender of Others' Freedom.

  If conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License.  If you cannot convey a
covered work so as to satisfy simultaneously your obligations under this
License and any other pertinent obligations, then as a consequence you may
not convey it at all.  For example, if you agree to terms that obligate you
to collect a royalty for further conveying from those to whom you convey
the Program, the only way you could satisfy both those terms and this
License would be to refrain entirely from conveying the Program.

  13. Use with the GNU Affero General Public License.

  Notwithstanding any other provision of this License, you have
permission to link or combine any covered work with a work licensed
under version 3 of the GNU Affero General Public License into a single
combined work, and to convey the resulting work.  The terms of this
License will continue to apply to the part which is the covered work,
but the special requirements of the GNU Affero General Public License,
section 13, concerning interaction through a network will apply to the
combination as such.

  14. Revised Versions of this License.

  The Free Software Foundation may publish revised and/or new versions of
the GNU General Public License from time to time.  Such new versions will
be similar in spirit to the present version, but may differ in detail to
address new problems or concerns.

  Each version is given a distinguishing version number.  If the
Program specifies that a certain numbered version of the GNU General
Public License "or any later version" applies to it, you have the
option of following the terms and conditions either of that numbered
version or of any later version published by the Free Software
Foundation.  If the Program does not specify a version number of the
GNU General Public License, you may choose any version ever published
by the Free Software Foundation.

  If the Program specifies that a proxy can decide which future
versions of the GNU General Public License can be used, that proxy's
public statement of acceptance of a version permanently authorizes you
to choose that version for the Program.

  Later license versions may give you additional or different
permissions.  However, no additional obligations are imposed on any
author or copyright holder as a result of your choosing to follow a
later version.

  15. Disclaimer of Warranty.

  THERE IS NO WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY
APPLICABLE LAW.  EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT
HOLDERS AND/OR OTHER PARTIES PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY
OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO,
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE PROGRAM
IS WITH YOU.  SHOULD THE PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF
ALL NECESSARY SERVICING, REPAIR OR CORRECTION.

  16. Limitation of Liability.

  IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING
WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MODIFIES AND/OR CONVEYS
THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY
GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE
USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS OF
DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD
PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER PROGRAMS),
EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF
SUCH DAMAGES.

  17. Interpretation of Sections 15 and 16.

  If the disclaimer of warranty and limitation of liability provided
above cannot be given local legal effect according to their terms,
reviewing courts shall apply local law that most closely approximates
an absolute waiver of all civil liability in connection with the
Program, unless a warranty or assumption of liability accompanies a
copy of the Program in return for a fee.

                     END OF TERMS AND CONDITIONS

            How to Apply These Terms to Your New Programs

  If you develop a new program, and you want it to be of the greatest
possible use to the public, the best way to achieve this is to make it
free software which everyone can redistribute and change under these terms.

  To do so, attach the following notices to the program.  It is safest
to attach them to the start of each source file to most effectively
state the exclusion of warranty; and each file should have at least
the "copyright" line and a pointer to where the full notice is found.

    <one line to give the program's name and a brief idea of what it does.>
    Copyright (C) <year>  <name of author>

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

Also add information on how to contact you by electronic and paper mail.

  If the program does terminal interaction, make it output a short
notice like this when it starts in an interactive mode:

    <program>  Copyright (C) <year>  <name of author>
    This program comes with ABSOLUTELY NO WARRANTY; for details type `show w'.
    This is free software, and you are welcome to redistribute it
    under certain conditions; type `show c' for details.

The hypothetical commands `show w' and `show c' should show the appropriate
parts of the General Public License.  Of course, your program's commands
might be different; for a GUI interface, you would use an "about box".

  You should also get your employer (if you work as a programmer) or school,
if any, to sign a "copyright disclaimer" for the program, if necessary.
For more information on this, and how to apply and follow the GNU GPL, see
<https://www.gnu.org/licenses/>.

  The GNU General Public License does not permit incorporating your program
into proprietary programs.  If your program is a subroutine library, you
may consider it more useful to permit linking proprietary applications with
the library.  If this is what you want to do, use the GNU Lesser General
Public License instead of this License.  But first, please read
<https://www.gnu.org/licenses/why-not-lgpl.html>.


-------------------------------------------------------------------------------



Creative Commons Attribution 3.0 International (CC-BY 3.0)
----------------------------------------------------------


Creative Commons Legal Code

Attribution 3.0 Unported

    CREATIVE COMMONS CORPORATION IS NOT A LAW FIRM AND DOES NOT PROVIDE
    LEGAL SERVICES. DISTRIBUTION OF THIS LICENSE DOES NOT CREATE AN
    ATTORNEY-CLIENT RELATIONSHIP. CREATIVE COMMONS PROVIDES THIS
    INFORMATION ON AN "AS-IS" BASIS. CREATIVE COMMONS MAKES NO WARRANTIES
    REGARDING THE INFORMATION PROVIDED, AND DISCLAIMS LIABILITY FOR
    DAMAGES RESULTING FROM ITS USE.

License

THE WORK (AS DEFINED BELOW) IS PROVIDED UNDER THE TERMS OF THIS CREATIVE
COMMONS PUBLIC LICENSE ("CCPL" OR "LICENSE"). THE WORK IS PROTECTED BY
COPYRIGHT AND/OR OTHER APPLICABLE LAW. ANY USE OF THE WORK OTHER THAN AS
AUTHORIZED UNDER THIS LICENSE OR COPYRIGHT LAW IS PROHIBITED.

BY EXERCISING ANY RIGHTS TO THE WORK PROVIDED HERE, YOU ACCEPT AND AGREE
TO BE BOUND BY THE TERMS OF THIS LICENSE. TO THE EXTENT THIS LICENSE MAY
BE CONSIDERED TO BE A CONTRACT, THE LICENSOR GRANTS YOU THE RIGHTS
CONTAINED HERE IN CONSIDERATION OF YOUR ACCEPTANCE OF SUCH TERMS AND
CONDITIONS.

1. Definitions

 a. "Adaptation" means a work based upon the Work, or upon the Work and
    other pre-existing works, such as a translation, adaptation,
    derivative work, arrangement of music or other alterations of a
    literary or artistic work, or phonogram or performance and includes
    cinematographic adaptations or any other form in which the Work may be
    recast, transformed, or adapted including in any form recognizably
    derived from the original, except that a work that constitutes a
    Collection will not be considered an Adaptation for the purpose of
    this License. For the avoidance of doubt, where the Work is a musical
    work, performance or phonogram, the synchronization of the Work in
    timed-relation with a moving image ("synching") will be considered an
    Adaptation for the purpose of this License.
 b. "Collection" means a collection of literary or artistic works, such as
    encyclopedias and anthologies, or performances, phonograms or
    broadcasts, or other works or subject matter other than works listed
    in Section 1(f) below, which, by reason of the selection and
    arrangement of their contents, constitute intellectual creations, in
    which the Work is included in its entirety in unmodified form along
    with one or more other contributions, each constituting separate and
    independent works in themselves, which together are assembled into a
    collective whole. A work that constitutes a Collection will not be
    considered an Adaptation (as defined above) for the purposes of this
    License.
 c. "Distribute" means to make available to the public the original and
    copies of the Work or Adaptation, as appropriate, through sale or
    other transfer of ownership.
 d. "Licensor" means the individual, individuals, entity or entities that
    offer(s) the Work under the terms of this License.
 e. "Original Author" means, in the case of a literary or artistic work,
    the individual, individuals, entity or entities who created the Work
    or if no individual or entity can be identified, the publisher; and in
    addition (i) in the case of a performance the actors, singers,
    musicians, dancers, and other persons who act, sing, deliver, declaim,
    play in, interpret or otherwise perform literary or artistic works or
    expressions of folklore; (ii) in the case of a phonogram the producer
    being the person or legal entity who first fixes the sounds of a
    performance or other sounds; and, (iii) in the case of broadcasts, the
    organization that transmits the broadcast.
 f. "Work" means the literary and/or artistic work offered under the terms
    of this License including without limitation any production in the
    literary, scientific and artistic domain, whatever may be the mode or
    form of its expression including digital form, such as a book,
    pamphlet and other writing; a lecture, address, sermon or other work
    of the same nature; a dramatic or dramatico-musical work; a
    choreographic work or entertainment in dumb show; a musical
    composition with or without words; a cinematographic work to which are
    assimilated works expressed by a process analogous to cinematography;
    a work of drawing, painting, architecture, sculpture, engraving or
    lithography; a photographic work to which are assimilated works
    expressed by a process analogous to photography; a work of applied
    art; an illustration, map, plan, sketch or three-dimensional work
    relative to geography, topography, architecture or science; a
    performance; a broadcast; a phonogram; a compilation of data to the
    extent it is protected as a copyrightable work; or a work performed by
    a variety or circus performer to the extent it is not otherwise
    considered a literary or artistic work.
 g. "You" means an individual or entity exercising rights under this
    License who has not previously violated the terms of this License with
    respect to the Work, or who has received express permission from the
    Licensor to exercise rights under this License despite a previous
    violation.
 h. "Publicly Perform" means to perform public recitations of the Work and
    to communicate to the public those public recitations, by any means or
    process, including by wire or wireless means or public digital
    performances; to make available to the public Works in such a way that
    members of the public may access these Works from a place and at a
    place individually chosen by them; to perform the Work to the public
    by any means or process and the communication to the public of the
    performances of the Work, including by public digital performance; to
    broadcast and rebroadcast the Work by any means including signs,
    sounds or images.
 i. "Reproduce" means to make copies of the Work by any means including
    without limitation by sound or visual recordings and the right of
    fixation and reproducing fixations of the Work, including storage of a
    protected performance or phonogram in digital form or other electronic
    medium.

2. Fair Dealing Rights. Nothing in this License is intended to reduce,
limit, or restrict any uses free from copyright or rights arising from
limitations or exceptions that are provided for in connection with the
copyright protection under copyright law or other applicable laws.

3. License Grant. Subject to the terms and conditions of this License,
Licensor hereby grants You a worldwide, royalty-free, non-exclusive,
perpetual (for the duration of the applicable copyright) license to
exercise the rights in the Work as stated below:

 a. to Reproduce the Work, to incorporate the Work into one or more
    Collections, and to Reproduce the Work as incorporated in the
    Collections;
 b. to create and Reproduce Adaptations provided that any such Adaptation,
    including any translation in any medium, takes reasonable steps to
    clearly label, demarcate or otherwise identify that changes were made
    to the original Work. For example, a translation could be marked "The
    original work was translated from English to Spanish," or a
    modification could indicate "The original work has been modified.";
 c. to Distribute and Publicly Perform the Work including as incorporated
    in Collections; and,
 d. to Distribute and Publicly Perform Adaptations.
 e. For the avoidance of doubt:

     i. Non-waivable Compulsory License Schemes. In those jurisdictions in
        which the right to collect royalties through any statutory or
        compulsory licensing scheme cannot be waived, the Licensor
        reserves the exclusive right to collect such royalties for any
        exercise by You of the rights granted under this License;
    ii. Waivable Compulsory License Schemes. In those jurisdictions in
        which the right to collect royalties through any statutory or
        compulsory licensing scheme can be waived, the Licensor waives the
        exclusive right to collect such royalties for any exercise by You
        of the rights granted under this License; and,
   iii. Voluntary License Schemes. The Licensor waives the right to
        collect royalties, whether individually or, in the event that the
        Licensor is a member of a collecting society that administers
        voluntary licensing schemes, via that society, from any exercise
        by You of the rights granted under this License.

The above rights may be exercised in all media and formats whether now
known or hereafter devised. The above rights include the right to make
such modifications as are technically necessary to exercise the rights in
other media and formats. Subject to Section 8(f), all rights not expressly
granted by Licensor are hereby reserved.

4. Restrictions. The license granted in Section 3 above is expressly made
subject to and limited by the following restrictions:

 a. You may Distribute or Publicly Perform the Work only under the terms
    of this License. You must include a copy of, or the Uniform Resource
    Identifier (URI) for, this License with every copy of the Work You
    Distribute or Publicly Perform. You may not offer or impose any terms
    on the Work that restrict the terms of this License or the ability of
    the recipient of the Work to exercise the rights granted to that
    recipient under the terms of the License. You may not sublicense the
    Work. You must keep intact all notices that refer to this License and
    to the disclaimer of warranties with every copy of the Work You
    Distribute or Publicly Perform. When You Distribute or Publicly
    Perform the Work, You may not impose any effective technological
    measures on the Work that restrict the ability of a recipient of the
    Work from You to exercise the rights granted to that recipient under
    the terms of the License. This Section 4(a) applies to the Work as
    incorporated in a Collection, but this does not require the Collection
    apart from the Work itself to be made subject to the terms of this
    License. If You create a Collection, upon notice from any Licensor You
    must, to the extent practicable, remove from the Collection any credit
    as required by Section 4(b), as requested. If You create an
    Adaptation, upon notice from any Licensor You must, to the extent
    practicable, remove from the Adaptation any credit as required by
    Section 4(b), as requested.
 b. If You Distribute, or Publicly Perform the Work or any Adaptations or
    Collections, You must, unless a request has been made pursuant to
    Section 4(a), keep intact all copyright notices for the Work and
    provide, reasonable to the medium or means You are utilizing: (i) the
    name of the Original Author (or pseudonym, if applicable) if supplied,
    and/or if the Original Author and/or Licensor designate another party
    or parties (e.g., a sponsor institute, publishing entity, journal) for
    attribution ("Attribution Parties") in Licensor's copyright notice,
    terms of service or by other reasonable means, the name of such party
    or parties; (ii) the title of the Work if supplied; (iii) to the
    extent reasonably practicable, the URI, if any, that Licensor
    specifies to be associated with the Work, unless such URI does not
    refer to the copyright notice or licensing information for the Work;
    and (iv) , consistent with Section 3(b), in the case of an Adaptation,
    a credit identifying the use of the Work in the Adaptation (e.g.,
    "French translation of the Work by Original Author," or "Screenplay
    based on original Work by Original Author"). The credit required by
    this Section 4 (b) may be implemented in any reasonable manner;
    provided, however, that in the case of a Adaptation or Collection, at
    a minimum such credit will appear, if a credit for all contributing
    authors of the Adaptation or Collection appears, then as part of these
    credits and in a manner at least as prominent as the credits for the
    other contributing authors. For the avoidance of doubt, You may only
    use the credit required by this Section for the purpose of attribution
    in the manner set out above and, by exercising Your rights under this
    License, You may not implicitly or explicitly assert or imply any
    connection with, sponsorship or endorsement by the Original Author,
    Licensor and/or Attribution Parties, as appropriate, of You or Your
    use of the Work, without the separate, express prior written
    permission of the Original Author, Licensor and/or Attribution
    Parties.
 c. Except as otherwise agreed in writing by the Licensor or as may be
    otherwise permitted by applicable law, if You Reproduce, Distribute or
    Publicly Perform the Work either by itself or as part of any
    Adaptations or Collections, You must not distort, mutilate, modify or
    take other derogatory action in relation to the Work which would be
    prejudicial to the Original Author's honor or reputation. Licensor
    agrees that in those jurisdictions (e.g. Japan), in which any exercise
    of the right granted in Section 3(b) of this License (the right to
    make Adaptations) would be deemed to be a distortion, mutilation,
    modification or other derogatory action prejudicial to the Original
    Author's honor and reputation, the Licensor will waive or not assert,
    as appropriate, this Section, to the fullest extent permitted by the
    applicable national law, to enable You to reasonably exercise Your
    right under Section 3(b) of this License (right to make Adaptations)
    but not otherwise.

5. Representations, Warranties and Disclaimer

UNLESS OTHERWISE MUTUALLY AGREED TO BY THE PARTIES IN WRITING, LICENSOR
OFFERS THE WORK AS-IS AND MAKES NO REPRESENTATIONS OR WARRANTIES OF ANY
KIND CONCERNING THE WORK, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE,
INCLUDING, WITHOUT LIMITATION, WARRANTIES OF TITLE, MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, NONINFRINGEMENT, OR THE ABSENCE OF
LATENT OR OTHER DEFECTS, ACCURACY, OR THE PRESENCE OF ABSENCE OF ERRORS,
WHETHER OR NOT DISCOVERABLE. SOME JURISDICTIONS DO NOT ALLOW THE EXCLUSION
OF IMPLIED WARRANTIES, SO SUCH EXCLUSION MAY NOT APPLY TO YOU.

6. Limitation on Liability. EXCEPT TO THE EXTENT REQUIRED BY APPLICABLE
LAW, IN NO EVENT WILL LICENSOR BE LIABLE TO YOU ON ANY LEGAL THEORY FOR
ANY SPECIAL, INCIDENTAL, CONSEQUENTIAL, PUNITIVE OR EXEMPLARY DAMAGES
ARISING OUT OF THIS LICENSE OR THE USE OF THE WORK, EVEN IF LICENSOR HAS
BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

7. Termination

 a. This License and the rights granted hereunder will terminate
    automatically upon any breach by You of the terms of this License.
    Individuals or entities who have received Adaptations or Collections
    from You under this License, however, will not have their licenses
    terminated provided such individuals or entities remain in full
    compliance with those licenses. Sections 1, 2, 5, 6, 7, and 8 will
    survive any termination of this License.
 b. Subject to the above terms and conditions, the license granted here is
    perpetual (for the duration of the applicable copyright in the Work).
    Notwithstanding the above, Licensor reserves the right to release the
    Work under different license terms or to stop distributing the Work at
    any time; provided, however that any such election will not serve to
    withdraw this License (or any other license that has been, or is
    required to be, granted under the terms of this License), and this
    License will continue in full force and effect unless terminated as
    stated above.

8. Miscellaneous

 a. Each time You Distribute or Publicly Perform the Work or a Collection,
    the Licensor offers to the recipient a license to the Work on the same
    terms and conditions as the license granted to You under this License.
 b. Each time You Distribute or Publicly Perform an Adaptation, Licensor
    offers to the recipient a license to the original Work on the same
    terms and conditions as the license granted to You under this License.
 c. If any provision of this License is invalid or unenforceable under
    applicable law, it shall not affect the validity or enforceability of
    the remainder of the terms of this License, and without further action
    by the parties to this agreement, such provision shall be reformed to
    the minimum extent necessary to make such provision valid and
    enforceable.
 d. No term or provision of this License shall be deemed waived and no
    breach consented to unless such waiver or consent shall be in writing
    and signed by the party to be charged with such waiver or consent.
 e. This License constitutes the entire agreement between the parties with
    respect to the Work licensed here. There are no understandings,
    agreements or representations with respect to the Work not specified
    here. Licensor shall not be bound by any additional provisions that
    may appear in any communication from You. This License may not be
    modified without the mutual written agreement of the Licensor and You.
 f. The rights granted under, and the subject matter referenced, in this
    License were drafted utilizing the terminology of the Berne Convention
    for the Protection of Literary and Artistic Works (as amended on
    September 28, 1979), the Rome Convention of 1961, the WIPO Copyright
    Treaty of 1996, the WIPO Performances and Phonograms Treaty of 1996
    and the Universal Copyright Convention (as revised on July 24, 1971).
    These rights and subject matter take effect in the relevant
    jurisdiction in which the License terms are sought to be enforced
    according to the corresponding provisions of the implementation of
    those treaty provisions in the applicable national law. If the
    standard suite of rights granted under applicable copyright law
    includes additional rights not granted under this License, such
    additional rights are deemed to be included in the License; this
    License is not intended to restrict the license of any rights under
    applicable law.


Creative Commons Notice

    Creative Commons is not a party to this License, and makes no warranty
    whatsoever in connection with the Work. Creative Commons will not be
    liable to You or any party on any legal theory for any damages
    whatsoever, including without limitation any general, special,
    incidental or consequential damages arising in connection to this
    license. Notwithstanding the foregoing two (2) sentences, if Creative
    Commons has expressly identified itself as the Licensor hereunder, it
    shall have all rights and obligations of Licensor.

    Except for the limited purpose of indicating to the public that the
    Work is licensed under the CCPL, Creative Commons does not authorize
    the use by either party of the trademark "Creative Commons" or any
    related trademark or logo of Creative Commons without the prior
    written consent of Creative Commons. Any permitted use will be in
    compliance with Creative Commons' then-current trademark usage
    guidelines, as may be published on its website or otherwise made
    available upon request from time to time. For the avoidance of doubt,
    this trademark restriction does not form part of this License.

    Creative Commons may be contacted at https://creativecommons.org/.


-------------------------------------------------------------------------------



Creative Commons Attribution 4.0 International Public License
-------------------------------------------------------------


By exercising the Licensed Rights (defined below), You accept and agree to be
bound by the terms and conditions of this Creative Commons Attribution 4.0
International Public License ("Public License"). To the extent this Public
License may be interpreted as a contract, You are granted the Licensed Rights
in consideration of Your acceptance of these terms and conditions, and the
Licensor grants You such rights in consideration of benefits the Licensor
receives from making the Licensed Material available under these terms and
conditions.

Section 1 – Definitions

a. Adapted Material means material subject to Copyright and Similar Rights that
is derived from or based upon the Licensed Material and in which the Licensed
Material is translated, altered, arranged, transformed, or otherwise modified
in a manner requiring permission under the Copyright and Similar Rights held by
the Licensor. For purposes of this Public License, where the Licensed Material
is a musical work, performance, or sound recording, Adapted Material is always
produced where the Licensed Material is synched in timed relation with a moving
image.

b. Adapter's License means the license You apply to Your Copyright and Similar
Rights in Your contributions to Adapted Material in accordance with the terms
and conditions of this Public License.

c. Copyright and Similar Rights means copyright and/or similar rights closely
related to copyright including, without limitation, performance, broadcast,
sound recording, and Sui Generis Database Rights, without regard to how the
rights are labeled or categorized. For purposes of this Public License, the
rights specified in Section 2(b)(1)-(2) are not Copyright and Similar Rights.

d. Effective Technological Measures means those measures that, in the absence
of proper authority, may not be circumvented under laws fulfilling obligations
under Article 11 of the WIPO Copyright Treaty adopted on December 20, 1996,
and/or similar international agreements.

e. Exceptions and Limitations means fair use, fair dealing, and/or any other
exception or limitation to Copyright and Similar Rights that applies to Your
use of the Licensed Material.

f. Licensed Material means the artistic or literary work, database, or other
material to which the Licensor applied this Public License.

g. Licensed Rights means the rights granted to You subject to the terms and
conditions of this Public License, which are limited to all Copyright and
Similar Rights that apply to Your use of the Licensed Material and that the
Licensor has authority to license.

h. Licensor means the individual(s) or entity(ies) granting rights under this
Public License.

i. Share means to provide material to the public by any means or process that
requires permission under the Licensed Rights, such as reproduction, public
display, public performance, distribution, dissemination, communication, or
importation, and to make material available to the public including in ways
that members of the public may access the material from a place and at a time
individually chosen by them.

j. Sui Generis Database Rights means rights other than copyright resulting from
Directive 96/9/EC of the European Parliament and of the Council of 11 March
1996 on the legal protection of databases, as amended and/or succeeded, as well
as other essentially equivalent rights anywhere in the world.

k. You means the individual or entity exercising the Licensed Rights under this
Public License. Your has a corresponding meaning.

Section 2 – Scope

a. License grant.

    Subject to the terms and conditions of this Public License, the Licensor
    hereby grants You a worldwide, royalty-free, non-sublicensable,
    non-exclusive, irrevocable license to exercise the Licensed Rights in the
    Licensed Material to:

    A. reproduce and Share the Licensed Material, in whole or in part; and

    B. produce, reproduce, and Share Adapted Material.

    Exceptions and Limitations. For the avoidance of doubt, where Exceptions
    and Limitations apply to Your use, this Public License does not apply, and
    You do not need to comply with its terms and conditions.

    Term. The term of this Public License is specified in Section 6(a).

    Media and formats; technical modifications allowed. The Licensor authorizes
    You to exercise the Licensed Rights in all media and formats whether now
    known or hereafter created, and to make technical modifications necessary
    to do so. The Licensor waives and/or agrees not to assert any right or
    authority to forbid You from making technical modifications necessary to
    exercise the Licensed Rights, including technical modifications necessary
    to circumvent Effective Technological Measures. For purposes of this Public
    License, simply making modifications authorized by this Section 2(a)(4)
    never produces Adapted Material.

    Downstream recipients.

    A. Offer from the Licensor – Licensed Material. Every recipient of the
    Licensed Material automatically receives an offer from the Licensor to
    exercise the Licensed Rights under the terms and conditions of this Public
    License.

    B. No downstream restrictions. You may not offer or impose any additional
    or different terms or conditions on, or apply any Effective Technological
    Measures to, the Licensed Material if doing so restricts exercise of the
    Licensed Rights by any recipient of the Licensed Material.

    No endorsement. Nothing in this Public License constitutes or may be
    construed as permission to assert or imply that You are, or that Your use
    of the Licensed Material is, connected with, or sponsored, endorsed, or
    granted official status by, the Licensor or others designated to receive
    attribution as provided in Section 3(a)(1)(A)(i).

b. Other rights.

    Moral rights, such as the right of integrity, are not licensed under this
    Public License, nor are publicity, privacy, and/or other similar
    personality rights; however, to the extent possible, the Licensor waives
    and/or agrees not to assert any such rights held by the Licensor to the
    limited extent necessary to allow You to exercise the Licensed Rights,
    but not otherwise.

    Patent and trademark rights are not licensed under this Public License.

    To the extent possible, the Licensor waives any right to collect royalties
    from You for the exercise of the Licensed Rights, whether directly or
    through a collecting society under any voluntary or waivable statutory or
    compulsory licensing scheme. In all other cases the Licensor expressly
    reserves any right to collect such royalties.

Section 3 – License Conditions

Your exercise of the Licensed Rights is expressly made subject to the following
conditions.

a. Attribution.

    If You Share the Licensed Material (including in modified form), You must:

    A. retain the following if it is supplied by the Licensor with the Licensed
    Material:

    i. identification of the creator(s) of the Licensed Material and any others
    designated to receive attribution, in any reasonable manner requested by
    the Licensor (including by pseudonym if designated);

    ii. a copyright notice;

    iii. a notice that refers to this Public License;

    iv. a notice that refers to the disclaimer of warranties;

    v. a URI or hyperlink to the Licensed Material to the extent reasonably
    practicable;

    B. indicate if You modified the Licensed Material and retain an indication
    of any previous modifications; and

    C. indicate the Licensed Material is licensed under this Public License,
    and include the text of, or the URI or hyperlink to, this Public License.

    You may satisfy the conditions in Section 3(a)(1) in any reasonable manner
    based on the medium, means, and context in which You Share the Licensed
    Material. For example, it may be reasonable to satisfy the conditions by
    providing a URI or hyperlink to a resource that includes the required
    information.

    If requested by the Licensor, You must remove any of the information
    required by Section 3(a)(1)(A) to the extent reasonably practicable.

    If You Share Adapted Material You produce, the Adapter's License You apply
    must not prevent recipients of the Adapted Material from complying with
    this Public License.

Section 4 – Sui Generis Database Rights

Where the Licensed Rights include Sui Generis Database Rights that apply to
Your use of the Licensed Material:

a. for the avoidance of doubt, Section 2(a)(1) grants You the right to extract,
reuse, reproduce, and Share all or a substantial portion of the contents of the
database;

b. if You include all or a substantial portion of the database contents in a
database in which You have Sui Generis Database Rights, then the database in
which You have Sui Generis Database Rights (but not its individual contents)
is Adapted Material; and

c. You must comply with the conditions in Section 3(a) if You Share all or a
substantial portion of the contents of the database.

For the avoidance of doubt, this Section 4 supplements and does not replace
Your obligations under this Public License where the Licensed Rights include
other Copyright and Similar Rights.

Section 5 – Disclaimer of Warranties and Limitation of Liability

a. Unless otherwise separately undertaken by the Licensor, to the extent
possible, the Licensor offers the Licensed Material as-is and as-available,
and makes no representations or warranties of any kind concerning the Licensed
Material, whether express, implied, statutory, or other. This includes, without
limitation, warranties of title, merchantability, fitness for a particular
purpose, non-infringement, absence of latent or other defects, accuracy, or the
presence or absence of errors, whether or not known or discoverable. Where
disclaimers of warranties are not allowed in full or in part, this disclaimer
may not apply to You.

b. To the extent possible, in no event will the Licensor be liable to You on
any legal theory (including, without limitation, negligence) or otherwise for
any direct, special, indirect, incidental, consequential, punitive, exemplary,
or other losses, costs, expenses, or damages arising out of this Public License
or use of the Licensed Material, even if the Licensor has been advised of the
possibility of such losses, costs, expenses, or damages. Where a limitation of
liability is not allowed in full or in part, this limitation may not apply to
You.

c. The disclaimer of warranties and limitation of liability provided above
shall be interpreted in a manner that, to the extent possible, most closely
approximates an absolute disclaimer and waiver of all liability.

Section 6 – Term and Termination

a. This Public License applies for the term of the Copyright and Similar Rights
licensed here. However, if You fail to comply with this Public License, then
Your rights under this Public License terminate automatically.

b. Where Your right to use the Licensed Material has terminated under Section
6(a), it reinstates:

    automatically as of the date the violation is cured, provided it is cured
    within 30 days of Your discovery of the violation; or

    upon express reinstatement by the Licensor.

For the avoidance of doubt, this Section 6(b) does not affect any right the
Licensor may have to seek remedies for Your violations of this Public License.

c. For the avoidance of doubt, the Licensor may also offer the Licensed
Material under separate terms or conditions or stop distributing the Licensed
Material at any time; however, doing so will not terminate this Public License.

d. Sections 1, 5, 6, 7, and 8 survive termination of this Public License.

Section 7 – Other Terms and Conditions

a. The Licensor shall not be bound by any additional or different terms or
conditions communicated by You unless expressly agreed.

b. Any arrangements, understandings, or agreements regarding the Licensed
Material not stated herein are separate from and independent of the terms and
conditions of this Public License.

Section 8 – Interpretation

a. For the avoidance of doubt, this Public License does not, and shall not be
interpreted to, reduce, limit, restrict, or impose conditions on any use of the
Licensed Material that could lawfully be made without permission under this
Public License.

b. To the extent possible, if any provision of this Public License is deemed
unenforceable, it shall be automatically reformed to the minimum extent
necessary to make it enforceable. If the provision cannot be reformed, it shall
be severed from this Public License without affecting the enforceability of
the remaining terms and conditions.

c. No term or condition of this Public License will be waived and no failure to
comply consented to unless expressly agreed to by the Licensor.

d. Nothing in this Public License constitutes or may be interpreted as a
limitation upon, or waiver of, any privileges and immunities that apply to the
Licensor or You, including from the legal processes of any jurisdiction or
authority.

    Creative Commons is not a party to its public licenses. Notwithstanding,
    Creative Commons may elect to apply one of its public licenses to material
    it publishes and in those instances will be considered the “Licensor.”
    Except for the limited purpose of indicating that material is shared under
    a Creative Commons public license or as otherwise permitted by the Creative
    Commons policies published at creativecommons.org/policies, Creative
    Commons does not authorize the use of the trademark “Creative Commons” or
    any other trademark or logo of Creative Commons without its prior written
    consent including, without limitation, in connection with any unauthorized
    modifications to any of its public licenses or any other arrangements,
    understandings, or agreements concerning use of licensed material. For the
    avoidance of doubt, this paragraph does not form part of the public
    licenses.

    Creative Commons may be contacted at creativecommons.org


-------------------------------------------------------------------------------



SIL Open Font License 1.1
-------------------------


This Font Software is licensed under the SIL Open Font License, Version 1.1.
This license is copied below, and is also available with a FAQ at:
https://scripts.sil.org/OFL


-----------------------------------------------------------
SIL OPEN FONT LICENSE Version 1.1 - 26 February 2007
-----------------------------------------------------------

PREAMBLE
The goals of the Open Font License (OFL) are to stimulate worldwide
development of collaborative font projects, to support the font creation
efforts of academic and linguistic communities, and to provide a free and
open framework in which fonts may be shared and improved in partnership
with others.

The OFL allows the licensed fonts to be used, studied, modified and
redistributed freely as long as they are not sold by themselves. The
fonts, including any derivative works, can be bundled, embedded,
redistributed and/or sold with any software provided that any reserved
names are not used by derivative works. The fonts and derivatives,
however, cannot be released under any other type of license. The
requirement for fonts to remain under this license does not apply
to any document created using the fonts or their derivatives.

DEFINITIONS
"Font Software" refers to the set of files released by the Copyright
Holder(s) under this license and clearly marked as such. This may
include source files, build scripts and documentation.

"Reserved Font Name" refers to any names specified as such after the
copyright statement(s).

"Original Version" refers to the collection of Font Software components as
distributed by the Copyright Holder(s).

"Modified Version" refers to any derivative made by adding to, deleting,
or substituting -- in part or in whole -- any of the components of the
Original Version, by changing formats or by porting the Font Software to a
new environment.

"Author" refers to any designer, engineer, programmer, technical
writer or other person who contributed to the Font Software.

PERMISSION & CONDITIONS
Permission is hereby granted, free of charge, to any person obtaining
a copy of the Font Software, to use, study, copy, merge, embed, modify,
redistribute, and sell modified and unmodified copies of the Font
Software, subject to the following conditions:

1) Neither the Font Software nor any of its individual components,
in Original or Modified Versions, may be sold by itself.

2) Original or Modified Versions of the Font Software may be bundled,
redistributed and/or sold with any software, provided that each copy
contains the above copyright notice and this license. These can be
included either as stand-alone text files, human-readable headers or
in the appropriate machine-readable metadata fields within text or
binary files as long as those fields can be easily viewed by the user.

3) No Modified Version of the Font Software may use the Reserved Font
Name(s) unless explicit written permission is granted by the corresponding
Copyright Holder. This restriction only applies to the primary font name as
presented to the users.

4) The name(s) of the Copyright Holder(s) or the Author(s) of the Font
Software shall not be used to promote, endorse or advertise any
Modified Version, except to acknowledge the contribution(s) of the
Copyright Holder(s) and the Author(s) or with their explicit written
permission.

5) The Font Software, modified or unmodified, in part or in whole,
must be distributed entirely under this license, and must not be
distributed under any other license. The requirement for fonts to
remain under this license does not apply to any document created
using the Font Software.

TERMINATION
This license becomes null and void if any of the above conditions are
not met.

DISCLAIMER
THE FONT SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT
OF COPYRIGHT, PATENT, TRADEMARK, OR OTHER RIGHT. IN NO EVENT SHALL THE
COPYRIGHT HOLDER BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
INCLUDING ANY GENERAL, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL
DAMAGES, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF THE USE OR INABILITY TO USE THE FONT SOFTWARE OR FROM
OTHER DEALINGS IN THE FONT SOFTWARE.


-------------------------------------------------------------------------------



Apache License 2.0
------------------



                                 Apache License
                           Version 2.0, January 2004
                        https://www.apache.org/licenses/

   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION

   1. Definitions.

      "License" shall mean the terms and conditions for use, reproduction,
      and distribution as defined by Sections 1 through 9 of this document.

      "Licensor" shall mean the copyright owner or entity authorized by
      the copyright owner that is granting the License.

      "Legal Entity" shall mean the union of the acting entity and all
      other entities that control, are controlled by, or are under common
      control with that entity. For the purposes of this definition,
      "control" means (i) the power, direct or indirect, to cause the
      direction or management of such entity, whether by contract or
      otherwise, or (ii) ownership of fifty percent (50%) or more of the
      outstanding shares, or (iii) beneficial ownership of such entity.

      "You" (or "Your") shall mean an individual or Legal Entity
      exercising permissions granted by this License.

      "Source" form shall mean the preferred form for making modifications,
      including but not limited to software source code, documentation
      source, and configuration files.

      "Object" form shall mean any form resulting from mechanical
      transformation or translation of a Source form, including but
      not limited to compiled object code, generated documentation,
      and conversions to other media types.

      "Work" shall mean the work of authorship, whether in Source or
      Object form, made available under the License, as indicated by a
      copyright notice that is included in or attached to the work
      (an example is provided in the Appendix below).

      "Derivative Works" shall mean any work, whether in Source or Object
      form, that is based on (or derived from) the Work and for which the
      editorial revisions, annotations, elaborations, or other modifications
      represent, as a whole, an original work of authorship. For the purposes
      of this License, Derivative Works shall not include works that remain
      separable from, or merely link (or bind by name) to the interfaces of,
      the Work and Derivative Works thereof.

      "Contribution" shall mean any work of authorship, including
      the original version of the Work and any modifications or additions
      to that Work or Derivative Works thereof, that is intentionally
      submitted to Licensor for inclusion in the Work by the copyright owner
      or by an individual or Legal Entity authorized to submit on behalf of
      the copyright owner. For the purposes of this definition, "submitted"
      means any form of electronic, verbal, or written communication sent
      to the Licensor or its representatives, including but not limited to
      communication on electronic mailing lists, source code control systems,
      and issue tracking systems that are managed by, or on behalf of, the
      Licensor for the purpose of discussing and improving the Work, but
      excluding communication that is conspicuously marked or otherwise
      designated in writing by the copyright owner as "Not a Contribution."

      "Contributor" shall mean Licensor and any individual or Legal Entity
      on behalf of whom a Contribution has been received by Licensor and
      subsequently incorporated within the Work.

   2. Grant of Copyright License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      copyright license to reproduce, prepare Derivative Works of,
      publicly display, publicly perform, sublicense, and distribute the
      Work and such Derivative Works in Source or Object form.

   3. Grant of Patent License. Subject to the terms and conditions of
      this License, each Contributor hereby grants to You a perpetual,
      worldwide, non-exclusive, no-charge, royalty-free, irrevocable
      (except as stated in this section) patent license to make, have made,
      use, offer to sell, sell, import, and otherwise transfer the Work,
      where such license applies only to those patent claims licensable
      by such Contributor that are necessarily infringed by their
      Contribution(s) alone or by combination of their Contribution(s)
      with the Work to which such Contribution(s) was submitted. If You
      institute patent litigation against any entity (including a
      cross-claim or counterclaim in a lawsuit) alleging that the Work
      or a Contribution incorporated within the Work constitutes direct
      or contributory patent infringement, then any patent licenses
      granted to You under this License for that Work shall terminate
      as of the date such litigation is filed.

   4. Redistribution. You may reproduce and distribute copies of the
      Work or Derivative Works thereof in any medium, with or without
      modifications, and in Source or Object form, provided that You
      meet the following conditions:

      (a) You must give any other recipients of the Work or
          Derivative Works a copy of this License; and

      (b) You must cause any modified files to carry prominent notices
          stating that You changed the files; and

      (c) You must retain, in the Source form of any Derivative Works
          that You distribute, all copyright, patent, trademark, and
          attribution notices from the Source form of the Work,
          excluding those notices that do not pertain to any part of
          the Derivative Works; and

      (d) If the Work includes a "NOTICE" text file as part of its
          distribution, then any Derivative Works that You distribute must
          include a readable copy of the attribution notices contained
          within such NOTICE file, excluding those notices that do not
          pertain to any part of the Derivative Works, in at least one
          of the following places: within a NOTICE text file distributed
          as part of the Derivative Works; within the Source form or
          documentation, if provided along with the Derivative Works; or,
          within a display generated by the Derivative Works, if and
          wherever such third-party notices normally appear. The contents
          of the NOTICE file are for informational purposes only and
          do not modify the License. You may add Your own attribution
          notices within Derivative Works that You distribute, alongside
          or as an addendum to the NOTICE text from the Work, provided
          that such additional attribution notices cannot be construed
          as modifying the License.

      You may add Your own copyright statement to Your modifications and
      may provide additional or different license terms and conditions
      for use, reproduction, or distribution of Your modifications, or
      for any such Derivative Works as a whole, provided Your use,
      reproduction, and distribution of the Work otherwise complies with
      the conditions stated in this License.

   5. Submission of Contributions. Unless You explicitly state otherwise,
      any Contribution intentionally submitted for inclusion in the Work
      by You to the Licensor shall be under the terms and conditions of
      this License, without any additional terms or conditions.
      Notwithstanding the above, nothing herein shall supersede or modify
      the terms of any separate license agreement you may have executed
      with Licensor regarding such Contributions.

   6. Trademarks. This License does not grant permission to use the trade
      names, trademarks, service marks, or product names of the Licensor,
      except as required for reasonable and customary use in describing the
      origin of the Work and reproducing the content of the NOTICE file.

   7. Disclaimer of Warranty. Unless required by applicable law or
      agreed to in writing, Licensor provides the Work (and each
      Contributor provides its Contributions) on an "AS IS" BASIS,
      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
      implied, including, without limitation, any warranties or conditions
      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
      PARTICULAR PURPOSE. You are solely responsible for determining the
      appropriateness of using or redistributing the Work and assume any
      risks associated with Your exercise of permissions under this License.

   8. Limitation of Liability. In no event and under no legal theory,
      whether in tort (including negligence), contract, or otherwise,
      unless required by applicable law (such as deliberate and grossly
      negligent acts) or agreed to in writing, shall any Contributor be
      liable to You for damages, including any direct, indirect, special,
      incidental, or consequential damages of any character arising as a
      result of this License or out of the use or inability to use the
      Work (including but not limited to damages for loss of goodwill,
      work stoppage, computer failure or malfunction, or any and all
      other commercial damages or losses), even if such Contributor
      has been advised of the possibility of such damages.

   9. Accepting Warranty or Additional Liability. While redistributing
      the Work or Derivative Works thereof, You may choose to offer,
      and charge a fee for, acceptance of support, warranty, indemnity,
      or other liability obligations and/or rights consistent with this
      License. However, in accepting such obligations, You may act only
      on Your own behalf and on Your sole responsibility, not on behalf
      of any other Contributor, and only if You agree to indemnify,
      defend, and hold each Contributor harmless for any liability
      incurred by, or claims asserted against, such Contributor by reason
      of your accepting any such warranty or additional liability.

   END OF TERMS AND CONDITIONS

   APPENDIX: How to apply the Apache License to your work.

      To apply the Apache License to your work, attach the following
      boilerplate notice, with the fields enclosed by brackets "[]"
      replaced with your own identifying information. (Don't include
      the brackets!)  The text should be enclosed in the appropriate
      comment syntax for the file format. We also recommend that a
      file or class name and description of purpose be included on the
      same "printed page" as the copyright notice for easier
      identification within third-party archives.

   Copyright [yyyy] [name of copyright owner]

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
