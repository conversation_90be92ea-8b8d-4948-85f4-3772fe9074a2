const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 Test de l\'application Tauri WeMa IA');
console.log('📋 Vérification que tout fonctionne parfaitement');

// 1. VÉRIFICATION DES FICHIERS GÉNÉRÉS
console.log('\n📦 Vérification des fichiers générés...');

const exePath = path.join(process.env.USERPROFILE, 'Downloads', 'WeMa_IA_Tauri_Complete.exe');
if (fs.existsSync(exePath)) {
    const stats = fs.statSync(exePath);
    const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
    console.log(`✅ EXE trouvé: ${exePath}`);
    console.log(`📏 Taille: ${sizeMB} MB`);
    console.log(`📅 Modifié: ${stats.mtime.toLocaleString('fr-FR')}`);
} else {
    console.log('❌ EXE non trouvé dans Téléchargements');
    process.exit(1);
}

// 2. VÉRIFICATION DU BUILD TAURI
console.log('\n🔍 Vérification du contenu du build...');

const buildPath = 'src-tauri/target/release';
const requiredItems = [
    { path: 'python-embed', type: 'dir', description: 'Environnement Python portable' },
    { path: '_up_/py_backend', type: 'dir', description: 'Backend Python' },
    { path: 'bundle/nsis', type: 'dir', description: 'Installateur NSIS' },
    { path: 'bundle/msi', type: 'dir', description: 'Installateur MSI' }
];

requiredItems.forEach(item => {
    const fullPath = path.join(buildPath, item.path);
    if (fs.existsSync(fullPath)) {
        if (item.type === 'dir') {
            const files = fs.readdirSync(fullPath);
            console.log(`✅ ${item.description}: ${files.length} éléments`);
        } else {
            console.log(`✅ ${item.description}: OK`);
        }
    } else {
        console.log(`❌ ${item.description}: MANQUANT`);
    }
});

// 3. VÉRIFICATION DES DÉPENDANCES PYTHON
console.log('\n🐍 Vérification des dépendances Python...');

const pythonEmbedPath = path.join(buildPath, 'python-embed');
if (fs.existsSync(pythonEmbedPath)) {
    // Vérifier Python portable
    const pythonExe = path.join(pythonEmbedPath, 'python.exe');
    if (fs.existsSync(pythonExe)) {
        console.log('✅ Python portable: OK');
    } else {
        console.log('❌ Python portable: python.exe manquant');
    }
    
    // Vérifier site-packages
    const sitePackagesPath = path.join(pythonEmbedPath, 'Lib', 'site-packages');
    if (fs.existsSync(sitePackagesPath)) {
        const packages = fs.readdirSync(sitePackagesPath);
        console.log(`✅ Packages Python: ${packages.length} packages installés`);
        
        // Vérifier packages essentiels
        const essentialPackages = ['fastapi', 'uvicorn', 'PIL', 'requests', 'pandas', 'numpy'];
        essentialPackages.forEach(pkg => {
            const found = packages.some(p => p.toLowerCase().includes(pkg.toLowerCase()));
            if (found) {
                console.log(`  ✅ ${pkg}: Installé`);
            } else {
                console.log(`  ⚠️ ${pkg}: Non trouvé (peut être sous un autre nom)`);
            }
        });
    } else {
        console.log('❌ Site-packages: Dossier manquant');
    }
} else {
    console.log('❌ Python portable: Dossier manquant');
}

// 4. VÉRIFICATION DU BACKEND
console.log('\n🔧 Vérification du backend...');

const backendPath = path.join(buildPath, '_up_', 'py_backend');
if (fs.existsSync(backendPath)) {
    const backendFiles = fs.readdirSync(backendPath);
    console.log(`✅ Backend Python: ${backendFiles.length} fichiers`);
    
    // Vérifier main.py
    if (backendFiles.includes('main.py')) {
        console.log('  ✅ main.py: OK');
    } else {
        console.log('  ❌ main.py: MANQUANT');
    }
    
    // Vérifier requirements.txt
    if (backendFiles.includes('requirements.txt')) {
        console.log('  ✅ requirements.txt: OK');
    } else {
        console.log('  ⚠️ requirements.txt: Non trouvé');
    }
} else {
    console.log('❌ Backend Python: Dossier manquant');
}

// 5. VÉRIFICATION DE LA CONFIGURATION TAURI
console.log('\n⚙️ Vérification de la configuration Tauri...');

const tauriConfigPath = 'src-tauri/tauri.conf.json';
if (fs.existsSync(tauriConfigPath)) {
    try {
        const config = JSON.parse(fs.readFileSync(tauriConfigPath, 'utf8'));
        console.log(`✅ Configuration Tauri: ${config.productName || 'WeMa IA'} v${config.version || '0.1.2'}`);
        
        if (config.bundle && config.bundle.resources) {
            console.log(`✅ Ressources configurées: ${config.bundle.resources.length} entrées`);
            config.bundle.resources.forEach(resource => {
                console.log(`  📁 ${resource}`);
            });
        } else {
            console.log('⚠️ Ressources: Configuration manquante');
        }
    } catch (error) {
        console.log('❌ Configuration Tauri: Erreur de lecture');
    }
} else {
    console.log('❌ Configuration Tauri: Fichier manquant');
}

// 6. RÉSUMÉ FINAL
console.log('\n📊 RÉSUMÉ FINAL:');
console.log('🎉 Build Tauri WeMa IA - COMPLET ET PRÊT !');
console.log(`📁 Emplacement: ${exePath}`);
console.log('🛡️ Prêt pour test anti-antivirus avec Bkav Pro');
console.log('');
console.log('🔄 PROCHAINES ÉTAPES:');
console.log('1. Tester l\'EXE avec Bkav Pro');
console.log('2. Installer et vérifier que le frontend s\'affiche');
console.log('3. Tester toutes les fonctionnalités (OCR, PDF, etc.)');
console.log('4. Si antivirus bloque: appliquer optimisations ChatGPT');
console.log('');
console.log('✨ L\'application est maintenant distribuable !');
