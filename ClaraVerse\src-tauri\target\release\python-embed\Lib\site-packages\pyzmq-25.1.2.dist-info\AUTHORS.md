## Authors

This project was started and continues to be led by <PERSON>
(ellisonbg AT gmail DOT com). <PERSON> (benjaminrk AT gmail DOT
com) is the primary developer of pyzmq at this time.

The following people have contributed to the project:

- <PERSON> (alexander DOT else AT team DOT telstra DOT com)
- <PERSON> (apyhalov AT gmail DOT com)
- <PERSON><PERSON><PERSON> (frvzmb AT gmail DOT com)
- <PERSON><PERSON> (amr AT ledgerx DOT com)
- <PERSON> (andre DOT l DOT caron AT gmail DOT com)
- <PERSON> (andrea DOT crotti DOT 0 AT gmail DOT com)
- <PERSON> (git AT apgwoz DOT com)
- <PERSON> (baptiste DOT lepilleur AT gmail DOT com)
- <PERSON><PERSON><PERSON> (bwhite AT dappervision DOT com)
- <PERSON> (ellisonbg AT gmail DOT com)
- <PERSON> (hoffman_brian AT bah DOT com)
- <PERSON> (carlos DOT rocha AT gmail DOT com)
- <PERSON> (clawsicus AT gmail DOT com)
- <PERSON> (christian AT bu DOT mp)
- <PERSON> (cgohlke AT uci DOT edu)
- <PERSON> (curtis AT tinbrain DOT net)
- <PERSON>ck (cyril DOT holweck AT free DOT fr)
- Dan Colish (dcolish AT gmail DOT com)
- Daniel Lundin (dln AT eintr DOT org)
- Daniel Truemper (truemped AT googlemail DOT com)
- Douglas Creager (douglas DOT creager AT redjack DOT com)
- <PERSON> Stalinho (eduardooc DOT 86 AT gmail DOT com)
- Eren Güven (erenguven0 AT gmail DOT com)
- Erick Tryzelaar (erick DOT tryzelaar AT gmail DOT com)
- Erik Tollerud (erik DOT tollerud AT gmail DOT com)
- FELD Boris (lothiraldan AT gmail DOT com)
- Fantix King (fantix DOT king AT gmail DOT com)
- Felipe Cruz (felipecruz AT loogica DOT net)
- Fernando Perez (Fernando DOT Perez AT berkeley DOT edu)
- Frank Wiles (frank AT revsys DOT com)
- Félix-Antoine Fortin (felix DOT antoine DOT fortin AT gmail DOT com)
- Gavrie Philipson (gavriep AT il DOT ibm DOT com)
- Godefroid Chapelle (gotcha AT bubblenet DOT be)
- Greg Banks (gbanks AT mybasis DOT com)
- Greg Ward (greg AT gerg DOT ca)
- Guido Goldstein (github AT a-nugget DOT de)
- Ian Lee (IanLee1521 AT gmail DOT com)
- Ionuț Arțăriși (ionut AT artarisi DOT eu)
- Ivo Danihelka (ivo AT danihelka DOT net)
- Iyed (iyed DOT bennour AT gmail DOT com)
- Jim Garrison (jim AT garrison DOT cc)
- John Gallagher (johnkgallagher AT gmail DOT com)
- Julian Taylor (jtaylor DOT debian AT googlemail DOT com)
- Justin Bronder (jsbronder AT gmail DOT com)
- Justin Riley (justin DOT t DOT riley AT gmail DOT com)
- Marc Abramowitz (marc AT marc-abramowitz DOT com)
- Matthew Aburn (mattja6 AT gmail DOT com)
- Michel Pelletier (pelletier DOT michel AT gmail DOT com)
- Michel Zou (xantares09 AT hotmail DOT com)
- Min Ragan-Kelley (benjaminrk AT gmail DOT com)
- Nell Hardcastle (nell AT dev-nell DOT com)
- Nicholas Pilkington (nicholas DOT pilkington AT gmail DOT com)
- Nicholas Piël (nicholas AT nichol DOT as)
- Nick Pellegrino (npellegrino AT mozilla DOT com)
- Nicolas Delaby (nicolas DOT delaby AT ezeep DOT com)
- Ondrej Certik (ondrej AT certik DOT cz)
- Paul Colomiets (paul AT colomiets DOT name)
- Pawel Jasinski (pawel DOT jasinski AT gmail DOT com)
- Phus Lu (phus DOT lu AT gmail DOT com)
- Robert Buchholz (rbu AT goodpoint DOT de)
- Robert Jordens (jordens AT gmail DOT com)
- Ryan Cox (ryan DOT a DOT cox AT gmail DOT com)
- Ryan Kelly (ryan AT rfk DOT id DOT au)
- Scott Maxwell (scott AT codecobblers DOT com)
- Scott Sadler (github AT mashi DOT org)
- Simon Knight (simon DOT knight AT gmail DOT com)
- Stefan Friesel (sf AT cloudcontrol DOT de)
- Stefan van der Walt (stefan AT sun DOT ac DOT za)
- Stephen Diehl (stephen DOT m DOT diehl AT gmail DOT com)
- Sylvain Corlay (scorlay AT bloomberg DOT net)
- Thomas Kluyver (takowl AT gmail DOT com)
- Thomas Spura (tomspur AT fedoraproject DOT org)
- Tigger Bear (Tigger AT Tiggers-Mac-mini DOT local)
- Torsten Landschoff (torsten DOT landschoff AT dynamore DOT de)
- Vadim Markovtsev (v DOT markovtsev AT samsung DOT com)
- Yannick Hold (yannickhold AT gmail DOT com)
- Zbigniew Jędrzejewski-Szmek (zbyszek AT in DOT waw DOT pl)
- hugo shi (hugoshi AT bleb2 DOT (none))
- jdgleeson (jdgleeson AT mac DOT com)
- kyledj (kyle AT bucebuce DOT com)
- spez (steve AT hipmunk DOT com)
- stu (stuart DOT axon AT jpcreative DOT co DOT uk)
- xantares (xantares AT fujitsu-l64 DOT (none))

as reported by:

```
git log --all --format='- %aN (%aE)' | sort -u | sed 's/@/ AT /1' | sed -e 's/\.\([^ ]\)/ DOT \1/g'
```

with some adjustments.

### Not in git log

- Brandon Craig-Rhodes (brandon AT rhodesmill DOT org)
- Eugene Chernyshov (chernyshov DOT eugene AT gmail DOT com)
- Craig Austin (craig DOT austin AT gmail DOT com)

### gevent_zeromq, now zmq.green

- Travis Cline (travis DOT cline AT gmail DOT com)
- Ryan Kelly (ryan AT rfk DOT id DOT au)
- Zachary Voase (z AT zacharyvoase DOT com)
