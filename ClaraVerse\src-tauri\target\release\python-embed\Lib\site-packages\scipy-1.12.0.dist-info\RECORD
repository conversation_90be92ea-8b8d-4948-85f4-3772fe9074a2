scipy-1.12.0-cp312-cp312-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.12.0.dist-info/DELVEWHEEL,sha256=qKagiXAlSGonRVNOWCWXXUyoZw1A3nXn-Og2dJf0lDI,444
scipy-1.12.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.12.0.dist-info/LICENSE.txt,sha256=UlkCiee0Uvv9lJ2s9Q4oE-NhKrt8EW5cegwnpJa5xe8,47742
scipy-1.12.0.dist-info/METADATA,sha256=eMfJCIVnFZyVm5U2vC9bHFympF7m4y4LSrOF4MdlaN0,60444
scipy-1.12.0.dist-info/RECORD,,
scipy-1.12.0.dist-info/WHEEL,sha256=50PeAbplA6PkI0hYOYoeacB9US1R6EguyfOnsccH0WU,85
scipy.libs/libopenblas_v0.3.20-571-g3dec11c6-gcc_10_3_0-c2315440d6b6cef5037bad648efc8c59.dll,sha256=KPsyRkaXuLjtheb2qxII1vf_QQ7c_p0oaehKJeL_eRs,35938219
scipy/__config__.py,sha256=I-Ybt5VWZopoykFT7dmMFbAYF2gCcwrlgXIn7oB-bNA,5310
scipy/__init__.py,sha256=F3yE6C7HcIATlTvTt9Drkn4_ZCHGQ-JwpyeXBtlyJLU,4616
scipy/__pycache__/__config__.cpython-312.pyc,,
scipy/__pycache__/__init__.cpython-312.pyc,,
scipy/__pycache__/_distributor_init.cpython-312.pyc,,
scipy/__pycache__/conftest.cpython-312.pyc,,
scipy/__pycache__/version.cpython-312.pyc,,
scipy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
scipy/_lib/__init__.py,sha256=lcHbxe9IkuoUZjXA8VX6-SYYCREBLq1cqoPo2mR6V1w,367
scipy/_lib/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/__pycache__/_array_api.cpython-312.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-312.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-312.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-312.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-312.pyc,,
scipy/_lib/__pycache__/_finite_differences.cpython-312.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-312.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-312.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-312.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-312.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-312.pyc,,
scipy/_lib/__pycache__/_util.cpython-312.pyc,,
scipy/_lib/__pycache__/decorator.cpython-312.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-312.pyc,,
scipy/_lib/__pycache__/doccer.cpython-312.pyc,,
scipy/_lib/__pycache__/uarray.cpython-312.pyc,,
scipy/_lib/_array_api.py,sha256=0RPQNUeo_eol7g5rJph_9VkfrdBtj97Q4Gn_3flawDM,13022
scipy/_lib/_bunch.py,sha256=wQK9j5N61P1eXt73dEZnUcEc8bS5Y59qOgp_av6eGPc,8345
scipy/_lib/_ccallback.py,sha256=RDz5WUY_jgPtUlEgtm-VGxcub9nvF4laqIsyExK8Nzk,7338
scipy/_lib/_ccallback_c.cp312-win_amd64.dll.a,sha256=W8U59Cx08g8bP3uoMBfAZKPAONUZwXyJVqDWCH-V_6c,1608
scipy/_lib/_ccallback_c.cp312-win_amd64.pyd,sha256=OlXbXGCzzEqG69-okzUVP68fbwV-5IuUM9ezV_8G4k8,85504
scipy/_lib/_disjoint_set.py,sha256=3EIkZl2z9UajnPRVjSD5H8JszidKURpxHCO580OJGC8,6414
scipy/_lib/_docscrape.py,sha256=5dCdfJJwgOz_Utb7OrSkiEmeCDxCm-Xs6kYLbQu_fEQ,22177
scipy/_lib/_finite_differences.py,sha256=Uu28sJ1PNvgW0Y7ESs0voon5AVYtj0QRrbqsVPjoL70,4317
scipy/_lib/_fpumode.cp312-win_amd64.dll.a,sha256=DAqffKvS3-DYOXCe1QDIhF3Fa3ov4Git4JmT9RIs4TQ,1560
scipy/_lib/_fpumode.cp312-win_amd64.pyd,sha256=rexFtkLB_WQ4INnRpuQAPkQXrjoGTT03z-us6JmDDdM,15872
scipy/_lib/_gcutils.py,sha256=ULLIREutjgyo8dLsvU2hhI0lDGeu5LqX6557X60xPi0,2774
scipy/_lib/_pep440.py,sha256=xSupJHSD_X9EOOUla1iKcNZ9lwxW6qraE3cbHHLunBY,14492
scipy/_lib/_test_ccallback.cp312-win_amd64.dll.a,sha256=H-CPez3baSwa-nPF6qIDa-P86Xg_NkQd3oEDuMu0AHY,1640
scipy/_lib/_test_ccallback.cp312-win_amd64.pyd,sha256=2TsJZiIuZSt6TxBgcl0_rgLA-EruQbHGrxC16QCxbA8,52224
scipy/_lib/_test_deprecation_call.cp312-win_amd64.dll.a,sha256=_PZ2s-ZQsqb172_YI2spyddzEfpwt5I6T8_YzFWCpUg,1724
scipy/_lib/_test_deprecation_call.cp312-win_amd64.pyd,sha256=PEOti0H-Li4oui4i0C-TKC9S45KgXSqe0GRpsVAKbU0,35328
scipy/_lib/_test_deprecation_def.cp312-win_amd64.dll.a,sha256=VT80bkmU6sx7oP_5w1uEX-xZQE8DIBBv532cT5urni8,1712
scipy/_lib/_test_deprecation_def.cp312-win_amd64.pyd,sha256=lHdoMYIH-5lVQGe_2WAhA7hKdsQmHQgzisyInD4bxWg,27648
scipy/_lib/_testutils.py,sha256=WBfjFTE5oX_6_Co7EU6KwL_y9ArM3eAtNkdyMQVNhGY,8551
scipy/_lib/_threadsafety.py,sha256=2dkby9bQV_BrdlThHUfiqZJsQq-Mte5R28_ueFpNikA,1513
scipy/_lib/_tmpdirs.py,sha256=r8sz8dcfBYiPAYondvpGHVVFB2_rB5sajkTeV1gt1fw,2460
scipy/_lib/_uarray/LICENSE,sha256=5MSswx6ChwZtfJldJuPvkCyhl37hh7JgsRwng2ZQOBE,1543
scipy/_lib/_uarray/__init__.py,sha256=pex73GgY7YUWARVgHzYw_Ky2fdAH7M9BcuDMif_hm38,4609
scipy/_lib/_uarray/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-312.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=bKJqAjPtPrnHUpph4Po4rSvN7V9cRKRZVVOsExkV9x0,21136
scipy/_lib/_uarray/_uarray.cp312-win_amd64.dll.a,sha256=eZjjRdMfC-9ZKFl3ZJJbdrkQhgj5-0tTyq7rM31uelE,1544
scipy/_lib/_uarray/_uarray.cp312-win_amd64.pyd,sha256=-elV-X2_xOLHvKgvo83-6BY4qXYt4gjswutupw2hS3Q,235520
scipy/_lib/_util.py,sha256=9bEwKRHmvVD76Witsr6w3nPfnWo2gBg2L1gZbsu0v6E,28593
scipy/_lib/array_api_compat/array_api_compat/__init__.py,sha256=IA2WuIPcgWVJ3vYzhau3Fx-YG4jpj7PFlIujrDE_47g,966
scipy/_lib/array_api_compat/array_api_compat/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/__pycache__/_internal.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/_internal.py,sha256=BC0giq7U-zElzbzEtrSmtrJKo61duyrC6RHi4QL64yc,1030
scipy/_lib/array_api_compat/array_api_compat/common/__init__.py,sha256=IOoccfR-BZ_F-CEN37M9ss_hT4nWMKqQLNbkUq6CHkM,25
scipy/_lib/array_api_compat/array_api_compat/common/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/common/__pycache__/_aliases.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/common/__pycache__/_helpers.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/common/__pycache__/_linalg.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/common/__pycache__/_typing.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/common/_aliases.py,sha256=U28ZG6K3WEkCPIBJOFOOZnRRtUT8pAf9ufViIS0IaS8,16531
scipy/_lib/array_api_compat/array_api_compat/common/_helpers.py,sha256=PxpMc2cbl98x8H5mFdV-iXB3aHK88G0W1zAC2JS-3Ec,8546
scipy/_lib/array_api_compat/array_api_compat/common/_linalg.py,sha256=nM2-F-rG9UKQ-2OQy4_VZ1m7r5fp4D1cMUPTB8Rha9I,6345
scipy/_lib/array_api_compat/array_api_compat/common/_typing.py,sha256=_ZLNGpqmAzvUAscbROUlJt03MHvG_xdMX13r4GAqTPI,408
scipy/_lib/array_api_compat/array_api_compat/cupy/__init__.py,sha256=i1de5OVYzS7-17NyRs9Ede0x_U7giHk7-iGNoOvKeAI,413
scipy/_lib/array_api_compat/array_api_compat/cupy/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/cupy/__pycache__/_aliases.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/cupy/__pycache__/_typing.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/cupy/__pycache__/linalg.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/cupy/_aliases.py,sha256=qh75Km9jyKLhWU2fzYLH-J0B_28dIW5uF24ceP8Duxo,2366
scipy/_lib/array_api_compat/array_api_compat/cupy/_typing.py,sha256=VhPA4g6G5Y8acUHqd5XG_vABH_M5mHUajSqFFfesBgM,663
scipy/_lib/array_api_compat/array_api_compat/cupy/linalg.py,sha256=ZnBGEBy7u-3ZkHT6djDpJvKjPoji201XUxwJEdqV83E,1166
scipy/_lib/array_api_compat/array_api_compat/numpy/__init__.py,sha256=avpnV0rdoayyE6lAmOiD1S-VHfOT47Q8M1pm26_v9XA,618
scipy/_lib/array_api_compat/array_api_compat/numpy/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/numpy/__pycache__/_aliases.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/numpy/__pycache__/_typing.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/numpy/__pycache__/linalg.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/numpy/_aliases.py,sha256=FmQYDiV2M6EWlxCyt1pl1HoqnzSDFOzu3L2LdWSEW-0,2370
scipy/_lib/array_api_compat/array_api_compat/numpy/_typing.py,sha256=m3A_ClFAyC17Hbsm77fe-eSfXbOFpx5h9_WggniIN5A,664
scipy/_lib/array_api_compat/array_api_compat/numpy/linalg.py,sha256=ZexqLyxOY67cSsL2_Codnfv13pfA5q3THjfKkSjAEVE,990
scipy/_lib/array_api_compat/array_api_compat/torch/__init__.py,sha256=4-LLJdmr5h5HIeCBNXPmxfViYReQD5QamtJ-FnzN3Fc,540
scipy/_lib/array_api_compat/array_api_compat/torch/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/torch/__pycache__/_aliases.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/torch/__pycache__/linalg.cpython-312.pyc,,
scipy/_lib/array_api_compat/array_api_compat/torch/_aliases.py,sha256=EtQWHDFrnJuKm2I0kZDMoGoaP5TEnaD6Yo3YbU62FeU,27371
scipy/_lib/array_api_compat/array_api_compat/torch/linalg.py,sha256=hoeFtlOD4ji18_FuP5kFDg6XgyU57Jd9705xUZOQXp4,2154
scipy/_lib/decorator.py,sha256=JoJOywg9I_xfdfasNhrwiasJEnNTvV3SYGv6GtC1Mak,15439
scipy/_lib/deprecation.py,sha256=L6_eIt28ZIJf3vlVMRXJl4cmbUhdMVZhwz00Ty_Tqq0,8313
scipy/_lib/doccer.py,sha256=9Ins8j58b2YQkD-vWCH1nMJ7pklhhRWqjELTeiJ-a_w,8637
scipy/_lib/messagestream.cp312-win_amd64.dll.a,sha256=eyJ3i1lvX1m5lIGQpexiY8P17j_oFbAfTHkEcG4lqBA,1616
scipy/_lib/messagestream.cp312-win_amd64.pyd,sha256=HpbKpvO9lJwfSnsHl-BGaCxOO_1VB9txDKmVdwIRTBw,68096
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_array_api.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-312.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-312.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=EeeSFQ1MTmcgxcDD5yI16UUAfESjSx40n3prcogOX30,3517
scipy/_lib/tests/test__pep440.py,sha256=4AS6UhkhxfVqvZfZmxM0pgIOvQVbAjPdWN3YtgfXWkY,2344
scipy/_lib/tests/test__testutils.py,sha256=cI6oLinJsZDXIi_pmPMTVAYTvepddvhYVGEMd2MlW2Q,832
scipy/_lib/tests/test__threadsafety.py,sha256=nnAypNwYK4YNe2s3xcYfGslLKXqdcrOeza73EeybiAQ,1373
scipy/_lib/tests/test__util.py,sha256=XlNa1i1e3DpusGZKY5TTtOUcUVXNnq2CS20MynhCsts,14887
scipy/_lib/tests/test_array_api.py,sha256=lfSbM1-5v49USbhy51E9bh4afgka24CLwcKBILYTmf4,4171
scipy/_lib/tests/test_bunch.py,sha256=yZmGHnJ-qBIMP-8TmnyvK7OGiXRzHzbRHCpMwIsmsk0,6330
scipy/_lib/tests/test_ccallback.py,sha256=klbEMLd28QAUyzGp6Z89Fr6FzV6jKnmuQumzpe4bXco,6379
scipy/_lib/tests/test_deprecation.py,sha256=NGUuuv24fSTGyTUZWd2saZkapR2NOpIpx0tijRjBQ7Y,374
scipy/_lib/tests/test_import_cycles.py,sha256=pKC7H1Tsu1Cuun_moQFZrRPYrdeuAT0s8dFAeZMeyZI,514
scipy/_lib/tests/test_public_api.py,sha256=9Yl8hF8C-pPTM4ZIs7UVHU12CJhPFmd1S-R6_9xMIMk,18789
scipy/_lib/tests/test_scipy_version.py,sha256=j-i3VewqD2gfdZZiJQCdlB3x_4EVekZkND1IFWszqhc,624
scipy/_lib/tests/test_tmpdirs.py,sha256=jY1yJyn2NN6l_BX_7u5HOuohD9K_SlU08W_yaoR15ek,1282
scipy/_lib/tests/test_warnings.py,sha256=dadYbf2-awQJwteEeGApq4mtXCQtKx7DFgk38BMqfPk,4634
scipy/_lib/uarray.py,sha256=qXvvUluJiQJwLybyo5ZZtGGWa_o2T0mSvojeu8t_IkQ,846
scipy/cluster/__init__.py,sha256=ck3TgyUyHOG1-MiymZd04JoIvkWrBaFL56fM-LS-tK8,907
scipy/cluster/__pycache__/__init__.cpython-312.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-312.pyc,,
scipy/cluster/__pycache__/vq.cpython-312.pyc,,
scipy/cluster/_hierarchy.cp312-win_amd64.dll.a,sha256=GvkA-Nw_SK2eMYPR_k5SP8NzaRL9sUVdoEeNKzXGR3g,1580
scipy/cluster/_hierarchy.cp312-win_amd64.pyd,sha256=wMbaEbuyf4aU14Ko-fJbTBk6eJgRyFgNcbRZRexMWx0,390656
scipy/cluster/_optimal_leaf_ordering.cp312-win_amd64.dll.a,sha256=nAu9dpW_uYvDzMZUQ6xnC_VEkYEvuE4U4NCSleU8GkU,1724
scipy/cluster/_optimal_leaf_ordering.cp312-win_amd64.pyd,sha256=WEl8_gHbT8RucDGPoykVHhB9674FuYarKP2rFRcDWFE,320512
scipy/cluster/_vq.cp312-win_amd64.dll.a,sha256=0C1oqqov3LYvv3bVUIUb_Uw2QGBB_B-17AxHu3-JPI0,1496
scipy/cluster/_vq.cp312-win_amd64.pyd,sha256=GYMfAUzVtbGMlwSzKWAeFYqaoadxLCZrjcJIGkFwSoo,112128
scipy/cluster/hierarchy.py,sha256=FchxpdMJ_EX4PRFFbUtxuVr47XGckJBFhTEP-VtD9Rg,152706
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-312.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-312.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=lvVMk2L5iEKlhX32aFRBJ3clecx6AXLYw75Epl07OBo,6995
scipy/cluster/tests/test_disjoint_set.py,sha256=fczIL8hK_ICE23j54dwEdn9KBOipPnpxAhWK7xj8X4o,5727
scipy/cluster/tests/test_hierarchy.py,sha256=SEqFgQ2cNy3AOMCagcChuL1Pxrb9t6gFvp_8gpFoTcw,52545
scipy/cluster/tests/test_vq.py,sha256=tZ6t3Ke1ypEe9rCOnASgkmpRUy9OOG7soi57FmY1Wfc,16832
scipy/cluster/vq.py,sha256=dW-8BZiFpEwJu5JSHKd3RMAYBzXI795k47FTQakw-K0,31190
scipy/conftest.py,sha256=dMPPk1V4MhhD4NjLEn__oziP-LzRgWrUNtG6XuZ1vn0,8490
scipy/constants/__init__.py,sha256=21Fu1jN-LvN-EbuCnzxGTQgAiv33VwvKM2PcDM7WeHc,12784
scipy/constants/__pycache__/__init__.cpython-312.pyc,,
scipy/constants/__pycache__/_codata.cpython-312.pyc,,
scipy/constants/__pycache__/_constants.cpython-312.pyc,,
scipy/constants/__pycache__/codata.cpython-312.pyc,,
scipy/constants/__pycache__/constants.cpython-312.pyc,,
scipy/constants/_codata.py,sha256=R3T8P1TLrAnQV9WCYF8c9OriLHtGmtp0vQ15PmT45cI,157383
scipy/constants/_constants.py,sha256=DYar8r1j0djolADiEz1ab8St1oBLcYfMVYsf4mUjqWI,10738
scipy/constants/codata.py,sha256=HSemkal4KFVqUurlalWfb7kzHuUBtWZ8gOgrYi5cNS0,818
scipy/constants/constants.py,sha256=vdC_jDTQZWQlrkYZY0Uj7NKO2AtBwH5mc28Jhy7Ur9s,2303
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-312.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-312.pyc,,
scipy/constants/tests/test_codata.py,sha256=0p4cAPH0UMK3JWHGI2Ha05dQpxBOzNSme3_pWcIVTDw,2016
scipy/constants/tests/test_constants.py,sha256=oZy6J36DLygy5IVdcNY_NFJlbvuTTFoAt3EM4JGCIkM,1667
scipy/datasets/__init__.py,sha256=9FDldqGVaM-sobI1bN43qJeNppyBd-W48hpEEjnCHqg,2892
scipy/datasets/__pycache__/__init__.cpython-312.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-312.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-312.pyc,,
scipy/datasets/__pycache__/_registry.cpython-312.pyc,,
scipy/datasets/__pycache__/_utils.cpython-312.pyc,,
scipy/datasets/_download_all.py,sha256=i-fMQWgAfvOgCEoEabsd68iUYDki-4OUfPjjcGLHRDI,1758
scipy/datasets/_fetchers.py,sha256=Vt5-ngD7ZXuLRUy_iYKS04eBk48ffXa2KAlCbwJLzJQ,6980
scipy/datasets/_registry.py,sha256=3_ZNYpe3lCXvUGalpNsrhRwKcnFA2P6yWV7MFdKb2Hw,1098
scipy/datasets/_utils.py,sha256=7bK_NVCoURt-HDGe7I0iBGLEdcrLIU5eJuSjJqAkgJg,3048
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-312.pyc,,
scipy/datasets/tests/test_data.py,sha256=loTZrDHWZEQjIihEcSYi6S6HlzltygW3HeRQp61SdVA,4187
scipy/fft/__init__.py,sha256=tTo8pSk5GeJaL2pHakUVxMByC78MMcDPtGtcgtBElp4,3624
scipy/fft/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/__pycache__/_backend.cpython-312.pyc,,
scipy/fft/__pycache__/_basic.cpython-312.pyc,,
scipy/fft/__pycache__/_basic_backend.cpython-312.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-312.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-312.pyc,,
scipy/fft/__pycache__/_fftlog_backend.cpython-312.pyc,,
scipy/fft/__pycache__/_helper.cpython-312.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-312.pyc,,
scipy/fft/__pycache__/_realtransforms_backend.cpython-312.pyc,,
scipy/fft/_backend.py,sha256=ySd0v6IodaTh-_kjfEOogdlfCsJ3rdcRLqv-ZU8NkTM,6740
scipy/fft/_basic.py,sha256=pSqks7ZcODqlyWVorO1pg3C_GyfcQxVdgUSHvNTTHmg,64627
scipy/fft/_basic_backend.py,sha256=ZIIZd7dfAD8G5h0uhslcqiCB69sWt-pDdLotnfjcAS8,6722
scipy/fft/_debug_backends.py,sha256=R0zCc_CgDubYnaGbQGjk0GTmd5HlYFpBLw7WSlqljpQ,620
scipy/fft/_fftlog.py,sha256=8xIIGkpizRzuClOnpd3UGjWWCdExE85b_F9n9pysIys,7728
scipy/fft/_fftlog_backend.py,sha256=NUrIgnZUV1EX4OmrUIRvpZiaMcAzgFyVHohuOCWwXEY,5434
scipy/fft/_helper.py,sha256=Awkb-ysHw_11YGooOZGW3O2MG-jR6blzbgg5bUxH-4I,10197
scipy/fft/_pocketfft/LICENSE.md,sha256=wEZhaFz_nYA1_hxMOrNX5_djPK_AanPp2Qw8X-3oqGo,1523
scipy/fft/_pocketfft/__init__.py,sha256=WKXb59wx9DK9zXq9DwKchp-zg1SuJIynTQ_POQrFXxo,216
scipy/fft/_pocketfft/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-312.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-312.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-312.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=dST8PBFhoiOR1Kj1j3CcjMC0p7KFxTA9qDr1oN2YrFw,8389
scipy/fft/_pocketfft/helper.py,sha256=-GPG0cgvXmawKVOCyBym428NFXsRnEbYblvN--xfJws,5949
scipy/fft/_pocketfft/pypocketfft.cp312-win_amd64.dll.a,sha256=CqkmARS1Hit3qhQcEtb0Gj3EA_PPSHpiw5T26_XmPOs,1592
scipy/fft/_pocketfft/pypocketfft.cp312-win_amd64.pyd,sha256=Q5MINr0SrL1_kE1z5v_sHFLEFmXZbv1V_zql8Qf42kI,1075200
scipy/fft/_pocketfft/realtransforms.py,sha256=nk1e31laxa9920AeBgZgb1vZSXIpoILwWqSI028HCyQ,3453
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-312.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=aSYik24MfNp1aKe9zzT33bJbz9D0PzoapK_Rvw1ezrY,36338
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=EBq5FEvMEVSyczizYsXnPBNIlPALwmVSAPzaUAmZss4,17150
scipy/fft/_realtransforms.py,sha256=T0ASVUXymqi_PXtsaP2xDUO0UX0hPLirWM-PG3_C90M,25975
scipy/fft/_realtransforms_backend.py,sha256=hJ3LkFOvggibVF_E36fM9R3ZqAJHHicYrGUceH8B81g,2452
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-312.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-312.pyc,,
scipy/fft/tests/mock_backend.py,sha256=jlJZ-GOwaAmSjp6g_riFY9X_86a2ngqmhOt-mUF0SGg,2646
scipy/fft/tests/test_backend.py,sha256=dJehAFjzYlIsYqpknfnd_sikp2qI_ndyiL2BkM5cD28,4354
scipy/fft/tests/test_basic.py,sha256=ntlAT65iQYnmcZo3iwZ0_uYnsNwpk820mExgvnZms4k,20270
scipy/fft/tests/test_fftlog.py,sha256=o04qsoN7MTMBX8Zigtp3Afzmbo8gxeu9Vik5RZZ7pgw,6397
scipy/fft/tests/test_helper.py,sha256=T8IGRCvk_aRRA1R-disGMzCkHxQeFCBAfarrr6MCdn8,16752
scipy/fft/tests/test_multithreading.py,sha256=AD3naMjpD9aQ0sJwLs8n7ln3IzeCzPm5Y6QV8su0YgI,2215
scipy/fft/tests/test_real_transforms.py,sha256=1P0Mm-Q-Hj8Tptt-CX0ZosfuhGhN10Tmi0PSh4YQwyU,8623
scipy/fftpack/__init__.py,sha256=NKSnRJ6EMDP3nDxGSJMyd2PJBB2ytMAO01Kq884NXXo,3258
scipy/fftpack/__pycache__/__init__.cpython-312.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-312.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-312.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-312.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-312.pyc,,
scipy/fftpack/__pycache__/basic.cpython-312.pyc,,
scipy/fftpack/__pycache__/helper.cpython-312.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-312.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-312.pyc,,
scipy/fftpack/_basic.py,sha256=6AFPL2JeBvZsIZV-C3kAmvG3ybQ8XtiRLSJQ9Go9L2E,13526
scipy/fftpack/_helper.py,sha256=KTS-_cTl2lNu8PXdopJRG80EbwBI3HnhvvziiWRT2tE,3465
scipy/fftpack/_pseudo_diffs.py,sha256=5IZuPf96AqFK17zyOv3PJUICEd5qrzQPqW7Aq-B-Pa8,14751
scipy/fftpack/_realtransforms.py,sha256=gG8Q39JAiO1Y3k8neJR1rfXLNhb9IvfBn7w8URM-Ndw,19812
scipy/fftpack/basic.py,sha256=FFn2KxrsmC6IsOQdjcoVr8Nvrlng2FRiv7gNeT1ZrY4,597
scipy/fftpack/convolve.cp312-win_amd64.dll.a,sha256=qiVstcrqSri4-A1bFGpbQusgRoEj433odndfZG8Vm00,1560
scipy/fftpack/convolve.cp312-win_amd64.pyd,sha256=2QwgruS_3z9d3yXA_UZquG_XcXJtCp4yCtEaD9k7brU,243200
scipy/fftpack/helper.py,sha256=1b1b278FWyTc2MeAjeLFB8eyV76pRxOigGtBUvCp_lo,599
scipy/fftpack/pseudo_diffs.py,sha256=OccZOhf76stov4474cq8Juy4rcQQ37pQnuxUsG7X_Nc,696
scipy/fftpack/realtransforms.py,sha256=oUJXNb5KAyS4k8xubnE7hGE9BpLCcdkk_iiReyB8OOE,614
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-312.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-312.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=Y42dJm5Gcs5CvVhCB1Zbb9-qfUzL3SKH5IfC7SVUMbg,31157
scipy/fftpack/tests/test_helper.py,sha256=HuCAP5esSm4lMnw9f9i-ByJj1DURntgC64s2fX5RVIg,1729
scipy/fftpack/tests/test_import.py,sha256=96z6hsyxt6iMH0Xn1xHd2T-kTyYM3obvlf0xniF73Cw,1151
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=5wmGeVj5QXbFkxrRG7Tl162v4pFSxiMSONtm4Uoy-AQ,13769
scipy/fftpack/tests/test_real_transforms.py,sha256=iaJJV0JFnFKLSXmfrA2Y4qTltkG0HW0sjMPo_c4ON3M,24786
scipy/integrate/__init__.py,sha256=QAHaKXYrkhDhF44xiF9szNiRqLoHmCzC0ePjuEHlJ_U,4346
scipy/integrate/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-312.pyc,,
scipy/integrate/__pycache__/_ode.cpython-312.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-312.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-312.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-312.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-312.pyc,,
scipy/integrate/__pycache__/_tanhsinh.cpython-312.pyc,,
scipy/integrate/__pycache__/dop.cpython-312.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-312.pyc,,
scipy/integrate/__pycache__/odepack.cpython-312.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-312.pyc,,
scipy/integrate/__pycache__/vode.cpython-312.pyc,,
scipy/integrate/_bvp.py,sha256=mi8S-exWGnFTlopJti0UDTDnUPjrH6ewrDZYRtJyuzE,42082
scipy/integrate/_dop.cp312-win_amd64.dll.a,sha256=HLUNS4RZnDUZLXs7IOa-N4HkV__YL8-WLo5y-vtBkqI,1512
scipy/integrate/_dop.cp312-win_amd64.pyd,sha256=-_XS7nU-kLC9asE96TEhYYWH41wdjPIiV2gH3XMP4hE,433664
scipy/integrate/_ivp/__init__.py,sha256=XVoxnj-1q1Xm4StWijukIoIrqc6Ny1Dxi625GurInWs,264
scipy/integrate/_ivp/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-312.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-312.pyc,,
scipy/integrate/_ivp/base.py,sha256=efUVvU8k5dFgglCta7f5ZiCMyTU4XN38JMfiKLQcYFQ,10585
scipy/integrate/_ivp/bdf.py,sha256=TYyHuFvF-eKxe-5PMtzkPs192A9HzkEw7adDazq0QDY,18001
scipy/integrate/_ivp/common.py,sha256=YXx_X0y6OjycqQb3FVZK20pj8npSkLVcbFhhMZkTilc,15714
scipy/integrate/_ivp/dop853_coefficients.py,sha256=4oMhmg8eXyzDeSSrlh2dJJMvaabWn-aO44S3wx8Ig5o,7430
scipy/integrate/_ivp/ivp.py,sha256=K9LY9tVm0exB8HzZ-0AICzlZlPjhCZZSAs0Du0tlHjU,31628
scipy/integrate/_ivp/lsoda.py,sha256=95NTm0zyHR5zFH5IeKo9kEWtDkdR3Mdfrbubnsf4Awo,10151
scipy/integrate/_ivp/radau.py,sha256=I33zwaSN9IJzaGiaRy3cU0SS56TyaCpBEiDPCo7IWXs,20317
scipy/integrate/_ivp/rk.py,sha256=bEgsvVyu4xwfhfLWGInrwbKsX-zZy3VOQibnn3wfCvE,23382
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-312.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-312.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=y6SHGW-L8T-pClrlYpKwWckfxEztHzKWRQjOaJ89zMY,37117
scipy/integrate/_ivp/tests/test_rk.py,sha256=_8nQKtotVvR218br-0mNSQSEZ-ST4L2Tgaz5xKXHgJw,1363
scipy/integrate/_lsoda.cp312-win_amd64.dll.a,sha256=IjITmQ8IcHuqe6sew_cH14WWiUF8KmEQrWN6rCwrfSg,1532
scipy/integrate/_lsoda.cp312-win_amd64.pyd,sha256=TFUSPvphzvjmiT4l4nDvivKoffWa30It8KCrAvm-WVs,429568
scipy/integrate/_ode.py,sha256=Ot4fEYgvhX3eUNIW3d_iwSXyIbZ0EQiPrLeyo4uXsFM,49450
scipy/integrate/_odepack.cp312-win_amd64.dll.a,sha256=lViZOdNDv1nzktRtyt-o17GkkGR8TQ2_aVTUZmKgGeI,1560
scipy/integrate/_odepack.cp312-win_amd64.pyd,sha256=PFmlh5YzPsf-rgTGTtrv65kxZvRplRYe1VvO0_PfAMg,410112
scipy/integrate/_odepack_py.py,sha256=VjytB7LLxOuwql5CSSdIEqjR8lSHTqq1k8DUR08smso,11174
scipy/integrate/_quad_vec.py,sha256=YNSgy0aVBB5TQ5_vtWVqprwBB_XHOUkz33RdDUYEuaA,21890
scipy/integrate/_quadpack.cp312-win_amd64.dll.a,sha256=VtWs6VoACC1sQqlG829pLm3F91I6zQV0v5a3FSOUK8Y,1568
scipy/integrate/_quadpack.cp312-win_amd64.pyd,sha256=1--7NgfbOTIKQY_H0FlyiLesf2IGzjsi1hoq-iTiejY,446976
scipy/integrate/_quadpack_py.py,sha256=iPHtbxdH2v4R-fANmpfyGa8twaEcIY8scjm_qIq9WPc,54913
scipy/integrate/_quadrature.py,sha256=ynYFxfo4xMdfrlbDUUsDhrehEpoqJxGr4MbSaRjzt-w,66885
scipy/integrate/_tanhsinh.py,sha256=Ty3ZK9bssfSuE2E6vPeMZ3vJBsYjYonr9H_hBz_2oCA,34026
scipy/integrate/_test_multivariate.cp312-win_amd64.dll.a,sha256=pHC_WI6fuzu0yjv80DxMfkLgUKwPnm6Ftq0fxsH2LIU,1676
scipy/integrate/_test_multivariate.cp312-win_amd64.pyd,sha256=NcVYjbJt9bxOml6kLApcumOC7zSesEgD9nC4PqnCoMw,17920
scipy/integrate/_test_odeint_banded.cp312-win_amd64.dll.a,sha256=-jTgMweTNZ7eQJsdmM0FnMzx0EV4efpY1CSv281C4Mw,1688
scipy/integrate/_test_odeint_banded.cp312-win_amd64.pyd,sha256=SImqWPJcwrz0gKurME1atRLzBA1cC5nrzDy0Aw3Rnwo,430080
scipy/integrate/_vode.cp312-win_amd64.dll.a,sha256=xUHF8NX7Occp7g2ITDIR6weK2BjXO_qU1GZ1Q2Y2Zq0,1520
scipy/integrate/_vode.cp312-win_amd64.pyd,sha256=Fh-uqR3C-YYvwSm-DKLk1E3X1hMwg1NqfyXiRoDOGCc,491520
scipy/integrate/dop.py,sha256=ZgmvSd-IxtG5kvMIQajBvUyi-T6JjeE5LDtHZaK2K1w,471
scipy/integrate/lsoda.py,sha256=KtndEiRbVPejH0aNBaj3mSHWayXP-wqXSoMZJRPWhAg,451
scipy/integrate/odepack.py,sha256=TxsXidD25atNMK5kKXD-FyNCLXnzrwCdVB1hP4aLSQ8,562
scipy/integrate/quadpack.py,sha256=cPEsDzMW-ghFwSDKI_ow1d4E08coR1vmrw9GBftE_UM,641
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-312.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-312.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=JHj_lZSbWKWc_oFLBShigs8Xgft9AkVFFXQ2j_h07UA,6493
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=erjGeCJNAVCaiulMn698i6ZjMqUFFr0SCZcmo2jJZsM,6905
scipy/integrate/tests/test_bvp.py,sha256=V4jOddeW6ukG3U4jgNinE5hR08ihZ0bZEWOlxe5B6rA,20892
scipy/integrate/tests/test_integrate.py,sha256=AQeUxBCo_LJsPIB7ba0e5GMwc9GLHz7xszGXRJvKW0A,25234
scipy/integrate/tests/test_odeint_jac.py,sha256=XedvNm_tVVuiQhUiL_5_OfPe5nDOEc821vsqiLZtm8c,1890
scipy/integrate/tests/test_quadpack.py,sha256=VZQGbIMf6eiQ6cSlNrtdqf6T8MZiEHfOJm3Inh-g7wM,28660
scipy/integrate/tests/test_quadrature.py,sha256=KSWynuWrHkqykxHxU2JBBAUNsd0_FV6lRL3q8td-IpE,53540
scipy/integrate/vode.py,sha256=txo8KXcwm0NGuFF8Oah4CszUPq5jxLfu5phYqH82e8U,471
scipy/interpolate/__init__.py,sha256=Qo38iQ5KFhyz2V1gj1BjpUFA92Vr_WrZyX3FeiUZsxY,3731
scipy/interpolate/__pycache__/__init__.cpython-312.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-312.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-312.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-312.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-312.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-312.pyc,,
scipy/interpolate/__pycache__/_interpnd_info.cpython-312.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-312.pyc,,
scipy/interpolate/__pycache__/_ndbspline.cpython-312.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-312.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-312.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-312.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-312.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-312.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-312.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-312.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-312.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-312.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-312.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-312.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-312.pyc,,
scipy/interpolate/_bspl.cp312-win_amd64.dll.a,sha256=grpBNNBQTFihZayhy3rfL7KXRii1zW24XmdODV-FtzY,1520
scipy/interpolate/_bspl.cp312-win_amd64.pyd,sha256=oiQg1JUStzadylJQCIuoQGlq079omWqygTNzkveKjKk,456192
scipy/interpolate/_bsplines.py,sha256=n30f2G_x31i01Pf_aPj3myhdWKJNkJUaskyOgqA9vYo,71433
scipy/interpolate/_cubic.py,sha256=cZmwXCB6WgzFWGyw71e59GMaPPXEwCXM4COhmPHiwhY,34769
scipy/interpolate/_fitpack.cp312-win_amd64.dll.a,sha256=-XaciAewfZBNg4AW61NhWZDo91xgF_H_upvW6_KtIR0,1560
scipy/interpolate/_fitpack.cp312-win_amd64.pyd,sha256=YQ5m6S9JTJXoa55RWTAQmjkv4OnTJWOWjY1J_NtkwS4,420864
scipy/interpolate/_fitpack2.py,sha256=UwFgtMivM8bzG1wY6JNj5NT2pFq7WPQwQa_SYR66puY,91534
scipy/interpolate/_fitpack_impl.py,sha256=rdW2bjiiMs4luM86-w_n2lRULhozh0G4x_DwD8v04NQ,29474
scipy/interpolate/_fitpack_py.py,sha256=zYUa--ZSkmKUjRclG9DDHIB_pOjgfobSbhDmFYOFkRs,28314
scipy/interpolate/_interpnd_info.py,sha256=GYonUhHHH1g1O9TdINaSgOIv1bSzcWDeLCskackRJMM,906
scipy/interpolate/_interpolate.py,sha256=J_8lVuWs2taqV1dudERyLXQR0rzwYCOKWGk7-7PjUZk,90665
scipy/interpolate/_ndbspline.py,sha256=XAmw1_kbFoAwFkHvCdCxbjm0fCIRkXYJvAj5djZuRN8,7618
scipy/interpolate/_ndgriddata.py,sha256=gG8EHsX2cqpEBLoAV-grw7J3gKmysKmYqNcx2BpY9_A,12424
scipy/interpolate/_pade.py,sha256=Phydd4f5oVM_-B3_iwe7H3t66FoCjbfsK_TagWLz9Jo,1894
scipy/interpolate/_polyint.py,sha256=tzpv5wZB16Vb8dwVwXnAlzJfWeU4GYWoNgNQm1__rLY,35921
scipy/interpolate/_ppoly.cp312-win_amd64.dll.a,sha256=gMJptldXYPUo3QDK-pAX0rnwxQeg8g91YehkyOUw4n0,1532
scipy/interpolate/_ppoly.cp312-win_amd64.pyd,sha256=3VC0603kMckRD2UM_CCS8457AKFxjNGTPIF4HfzSd-Q,441344
scipy/interpolate/_rbf.py,sha256=U7QwA94uCzHjxnFFYOApzqHJ6J6LoncTLs4cJEi_Mng,11964
scipy/interpolate/_rbfinterp.py,sha256=fZTvIamJeWkoyg4szpdZORKMTwyH-5hFxzSLJ1AKsNE,20147
scipy/interpolate/_rbfinterp_pythran.cp312-win_amd64.dll.a,sha256=E1Vuwlu1MRPPW3B0bnBEjn8MJae3XMz3es5FiGhRvKc,1676
scipy/interpolate/_rbfinterp_pythran.cp312-win_amd64.pyd,sha256=kLWC8w6CIxjJTB2quX-RPTcshMbvGhXEoa9ouRC0c30,1167872
scipy/interpolate/_rgi.py,sha256=A6nAZGh1eLGBYPbCeLpmBSXZtDqPdnDOMKKCVouxNh8,27578
scipy/interpolate/_rgi_cython.cp312-win_amd64.dll.a,sha256=6bsNCOUDhFih3l4RcNrhxnj9-4r-_oaC2CvMB1hK6JQ,1592
scipy/interpolate/_rgi_cython.cp312-win_amd64.pyd,sha256=Mrf_C90Gzs_jVlSPsbu1t-BGd9MrFai_c05R1W39Wwc,269312
scipy/interpolate/dfitpack.cp312-win_amd64.dll.a,sha256=Z5e8cu7YKwJXKxMwHFAYqmH5c3Yv9dxwEl-E-w9FKOM,1560
scipy/interpolate/dfitpack.cp312-win_amd64.pyd,sha256=TBxeEkj9Bp9nAIeoJtgV-wY4lSOmTuMITYROqXtaK3A,662016
scipy/interpolate/fitpack.py,sha256=FjYeE_uNfnCAdJjGdoIL93tDEh_4cSWS75wU7q6t2t0,748
scipy/interpolate/fitpack2.py,sha256=OADrx9ZTP7Tdc_mXi9n0wXpRQA8qGWVg4od37gf9018,1002
scipy/interpolate/interpnd.cp312-win_amd64.dll.a,sha256=-cCc_25hjbUMOr7N3NMQw7tVX36MMucWFsEFUH6pP_g,1560
scipy/interpolate/interpnd.cp312-win_amd64.pyd,sha256=uima79oW6SCkmdKE9ZIJkN8CQAWOXrUqI_1emJGASuU,429056
scipy/interpolate/interpolate.py,sha256=1-IYFTuGl8ueDaf93h2hHRltX9mS5dXgfRvw5F8Yayg,1007
scipy/interpolate/ndgriddata.py,sha256=7uNTwKpcjIF5pV-XLf3bdhZ-xmE1TErNqRB4JbXNUSI,702
scipy/interpolate/polyint.py,sha256=T5CNYqyuCvgEqVi6tGLCPAI3jgNqTxS3x__15aGWd5g,738
scipy/interpolate/rbf.py,sha256=JxNFnTsH_NI0QOCBt7qovGDEWBubxkN97lXUEkF2d98,622
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-312.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-312.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bsplines.py,sha256=kXmvY6DL2vRjMivFij7uZBiWE7JrbR-jf3aHYn9fdVs,79577
scipy/interpolate/tests/test_fitpack.py,sha256=ti8RjSO9twixgzz75klXxagYUwHiusRDIi0VBj_fgYI,16514
scipy/interpolate/tests/test_fitpack2.py,sha256=BrtNhvSn2XNE1JZlPniBXSWWyFn48tRfbT7aHyogj2U,60081
scipy/interpolate/tests/test_gil.py,sha256=U7P1Jo7ztDrrkjDRv_2AZb7I2f6hv-c6WyP8UeeNVVE,1939
scipy/interpolate/tests/test_interpnd.py,sha256=dUrvp9JawMiT640P8goeiBEfLRVueubldAGDnIJOvEw,14062
scipy/interpolate/tests/test_interpolate.py,sha256=0E_YlURkmLN7GrdEGgruAGjyJ2WHydIbVTv1ZocRFAc,98393
scipy/interpolate/tests/test_ndgriddata.py,sha256=G2DrnpFWvHdqy3Hzsd0kwFz35OwstHhmceMboGBkdZ8,11264
scipy/interpolate/tests/test_pade.py,sha256=hyxMyYpWDds6pUN9LaweoZAov32b8dIVrXacdoDOibE,3912
scipy/interpolate/tests/test_polyint.py,sha256=BnUOnBN--REziwJkWSmxzmu8H7Dkz3_6cj1wHuXLmAk,36651
scipy/interpolate/tests/test_rbf.py,sha256=p4TlyxPEMCcKaVKbFpl8foncvs8LCyyBrMkYAj2lYfg,6781
scipy/interpolate/tests/test_rbfinterp.py,sha256=daBY-1p-L20iF29e8MHVh3AENI77iZk0_XZ9eP8kF3s,18635
scipy/interpolate/tests/test_rgi.py,sha256=0wNTAfnb0AmFv-4aVkb4W9Wm7wHuuHQWfDRyoWsBM44,42891
scipy/io/__init__.py,sha256=3ETOeDKr2QQL-Ty2qrrzxAzqX09sfJzsKaPm5r8tZyA,2851
scipy/io/__pycache__/__init__.cpython-312.pyc,,
scipy/io/__pycache__/_fortran.cpython-312.pyc,,
scipy/io/__pycache__/_idl.cpython-312.pyc,,
scipy/io/__pycache__/_mmio.cpython-312.pyc,,
scipy/io/__pycache__/_netcdf.cpython-312.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-312.pyc,,
scipy/io/__pycache__/idl.cpython-312.pyc,,
scipy/io/__pycache__/mmio.cpython-312.pyc,,
scipy/io/__pycache__/netcdf.cpython-312.pyc,,
scipy/io/__pycache__/wavfile.cpython-312.pyc,,
scipy/io/_fast_matrix_market/__init__.py,sha256=JNjYiv7_ojzemjmyFSvyblDQOEMC5Kit6fIUKv5nfBY,17470
scipy/io/_fast_matrix_market/__pycache__/__init__.cpython-312.pyc,,
scipy/io/_fast_matrix_market/_fmm_core.cp312-win_amd64.dll.a,sha256=Bf0VcBG-cwKcns7DMOsZdjHgX5_cZBh9yRYy3rT4xQQ,1568
scipy/io/_fast_matrix_market/_fmm_core.cp312-win_amd64.pyd,sha256=sCaJa9U9BJCMfADUL04OUcZwmSzIXgSIFIPSrZKCgew,2740224
scipy/io/_fortran.py,sha256=vaaYYQPTMA8TiFob56TKborkigS6wnuZl2xGnpJO9ws,11249
scipy/io/_harwell_boeing/__init__.py,sha256=YnsHlxg7HQ-WxR5OOELtfv1JrwYrSkojji4_8hpppBs,591
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-312.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-312.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-312.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=1bJDeg-occK-jF_eneTCRc6nj9yWQU1QGPweRNMkflw,9226
scipy/io/_harwell_boeing/hb.py,sha256=4Lvcgr_BdskwmAVliLhk3F6yomsmR9o-RL0xVYgSLgk,19748
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-312.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-312.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=oBT5mfOa0sthUlb4qo_lJnP5BYFj3h8a6HqUZPMTXrk,2434
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=BCfVyKAE3O0ABcfeGfxtGquwhehBGMPKGJl95oC3EIo,2349
scipy/io/_idl.py,sha256=Ym0tUIyqMhZkx9Sr7_A9HTWXXIHVD1D61FVAFbsnMPY,28020
scipy/io/_mmio.py,sha256=_V9VYFI084fqhAo0bbBN9FtrVfyLVeGR2dqwOeh_lkc,32833
scipy/io/_netcdf.py,sha256=kM4993iMVHNPtqJ8X1W1HOPERpsPzzu9xPX2zqW30ow,40358
scipy/io/_test_fortran.cp312-win_amd64.dll.a,sha256=iJq3OLWaJ8BRggnPW6weH9U0t2QJRPBCprh999dOOYk,1616
scipy/io/_test_fortran.cp312-win_amd64.pyd,sha256=DChXqElxE9l3-o8v1bRcn5uyH_XxPhuwMI3HPqCAUJQ,380416
scipy/io/arff/__init__.py,sha256=3F0yvqizJT7Hq5gygpmZw2WMuy0r9aCmb6G2yNNn8-I,833
scipy/io/arff/__pycache__/__init__.cpython-312.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-312.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-312.pyc,,
scipy/io/arff/_arffread.py,sha256=KAVr3DpekgNZXXM7zJfDyUT02kxGK5xBeLIKRxpgnjI,27467
scipy/io/arff/arffread.py,sha256=7ddVqvI1JkdbeYccQQS7TyNU8kKBtxHlbjEyk823ANg,1173
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-312.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=gzevjIJmmJLocLjOEnQMkZVZuIpfPJLocJdIG4RYHIk,7711
scipy/io/arff/tests/data/missing.arff,sha256=m53l1Himyx0TtVgNLucoz1go8-9BmSE70Ta9bm6dt_k,128
scipy/io/arff/tests/data/nodata.arff,sha256=6oAHQ7CUQEG8h5glXKpCaXH0UABOCEoxs5j50WYPAKU,240
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=rc8IS9thME1NM9tAR-C4IUixLrSpw64YNeHkIRMS6Ng,299
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=NbE6wwE0HkJDomErLPHgPGO6iF2HA1bLpmcamzx3F58,305
scipy/io/arff/tests/data/test1.arff,sha256=NV86lzIKWCwq3UNZmMGt1XK0yurcYmcp0KS9c5rKLkE,201
scipy/io/arff/tests/data/test10.arff,sha256=GbDWnG6LzjsbjYxiarYG4Ft3WlK1rvuM6w3proNFD58,199016
scipy/io/arff/tests/data/test11.arff,sha256=RFWNoWrx2LHJ0izc1bJdmGA2M2L8NT58MwKQN2B-3AI,252
scipy/io/arff/tests/data/test2.arff,sha256=zk5hNFTEcphFXkon2At_FD9ia_lAyzOcSFj4xnJTVPs,315
scipy/io/arff/tests/data/test3.arff,sha256=R2zwCzapweGqylmLD-LXx7GAS957NNJ8emM48hLTwEY,78
scipy/io/arff/tests/data/test4.arff,sha256=KtyokEnupbYZVi-fR7GJtr-hHefP54xgV2l26Nz3YZU,249
scipy/io/arff/tests/data/test5.arff,sha256=_1-QhpmflJKp1polaKwjkeJZsHeho7RzM3_ic2JJykA,391
scipy/io/arff/tests/data/test6.arff,sha256=WOCPN8QRdxCUSfzs-Y-OlJKiUmgLKuDrfy6wsWIFCJU,207
scipy/io/arff/tests/data/test7.arff,sha256=AQdPXYEKEhb10Odd_SDrPcv_gHNR0ymFKnD_91zjFc8,573
scipy/io/arff/tests/data/test8.arff,sha256=YlCbztVn-7x4w41HLIP852BqkKvWBrO9N8Nb0LSR0ZM,440
scipy/io/arff/tests/data/test9.arff,sha256=X0fH-_mz5ScbEvPJV7LJH-7Kbr2RTOlamf2_x18ydY0,324
scipy/io/arff/tests/test_arffread.py,sha256=YFtoh4I83UWxVdMBhLhGIkRRG1_U01e3EzKFcDZyzFw,13525
scipy/io/harwell_boeing.py,sha256=l_sl2xco4bRk70iaN5-oeCFlsXb6vHPV7PDms2Nmk2E,703
scipy/io/idl.py,sha256=RvSPSnoqWHFi4ECsQYdN8XgUBK6qyzX8Su60Olv8218,619
scipy/io/matlab/__init__.py,sha256=7N61Ssi3RyZbcXWAX5SHdfCsDkj3TIy1cHQkdC6csQY,2091
scipy/io/matlab/__pycache__/__init__.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-312.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-312.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-312.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-312.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-312.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-312.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=tVJ4_1yhVsey9Yaabn9mjl7srPkkdEWJ5YjRP3LLapw,2060
scipy/io/matlab/_mio.py,sha256=xqSXoT67Nh-ulbm8e0nW1NipfC1v9dxPsFc1Hg7k7ZA,13192
scipy/io/matlab/_mio4.py,sha256=FC9liz3_LcQB9lDUyGHgRbhrFZua_0gL-9ofqUN6Ugc,21244
scipy/io/matlab/_mio5.py,sha256=eAijMJ-PErmMjyVfwiJwl3mHJlrPx5uzo1w3U3VRa_Q,34472
scipy/io/matlab/_mio5_params.py,sha256=f5HyjzbAwkN4zNRwn0N_n01LadTbsmUFGWjflumbxeU,8482
scipy/io/matlab/_mio5_utils.cp312-win_amd64.dll.a,sha256=x_NfZTpsE871LPG59Z23CjtKPkfjJpGr9rH4KV0AsNs,1592
scipy/io/matlab/_mio5_utils.cp312-win_amd64.pyd,sha256=7QYPnItE6OlPaV0sZU-t-PkIheYBDfcBaie_qOgygdU,205824
scipy/io/matlab/_mio_utils.cp312-win_amd64.dll.a,sha256=mux48Ber5hyWTU1tLN2oL7QQUNmewHyqKdcxvx91PYc,1580
scipy/io/matlab/_mio_utils.cp312-win_amd64.pyd,sha256=x5KaijRbHFEOAMFSAmkz2v3K9sNkHvBHJ6IjaGQEdpA,60416
scipy/io/matlab/_miobase.py,sha256=f1fu5PDL99ULHRQspDSm9wry9gze_3cumKJ9ch07NXo,13391
scipy/io/matlab/_streams.cp312-win_amd64.dll.a,sha256=0loSL8mf2eUTX5VC7VgAMe-7qARfRcdxSBMvX1-4hEg,1560
scipy/io/matlab/_streams.cp312-win_amd64.pyd,sha256=paRoDug4lD0NWmOjRlLKbWlnc9oakzwk88fQnWOPw9U,116224
scipy/io/matlab/byteordercodes.py,sha256=IihL2pGCaxGfzTo4YEEUtCq2DCMIfSwVK7sA948DoQo,631
scipy/io/matlab/mio.py,sha256=1-lmQbo4alZNFgqQvEMHyMRWljZVfO1w1zKGrJKCsho,698
scipy/io/matlab/mio4.py,sha256=S3Ma9Vh8tOuikSzzYQlByem3Dni5dohucB5ngTF7mQM,1007
scipy/io/matlab/mio5.py,sha256=an0HVQlCzJ_spApJ0qei_bty8a2eZ3KUC9l2X0jNWCU,1245
scipy/io/matlab/mio5_params.py,sha256=ZuW_moknJgk7Q43CnzqN-XzFIJQueNmhjn5Kwtb3CGs,1322
scipy/io/matlab/mio5_utils.py,sha256=8_MdWfraDwWiOg-F39QbgEZ3SDHnEga-mg_irE8mZA8,680
scipy/io/matlab/mio_utils.py,sha256=wo_7kYRsLRZca_ROx9GOqFAhesSBdVp8WnJUY26_bYI,575
scipy/io/matlab/miobase.py,sha256=cf1KQ7eDsVhySKdCYj19_NYGzJh010BV_j4YIfqsui8,786
scipy/io/matlab/streams.py,sha256=dNiZsOftjoMdRSLzMRFL4-qpVwulW_ft9CUnYbLvS9I,603
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-312.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-312.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=YKPAsE36MRPDi3Jsh2QrinvLOEvDPmsBylkwUzqoDTg,967
scipy/io/matlab/tests/test_mio.py,sha256=88XRZpkxxcyX8QaWV1qb3PrcAq65QKlXTHp8tMd5vns,45840
scipy/io/matlab/tests/test_mio5_utils.py,sha256=Z1Byr0AJMkdlidOETqIsAJdCZCx0TGqR4OyFI4SDBaY,5568
scipy/io/matlab/tests/test_mio_funcs.py,sha256=JoLN42aZLd-f-kju-kTgRFQjQmEy4VtJlKgJ5EgnVFI,1443
scipy/io/matlab/tests/test_mio_utils.py,sha256=I4_z8My_JDy6jverX4bm-GeDNnnC6jX71hprsGSnCF8,1639
scipy/io/matlab/tests/test_miobase.py,sha256=G3Xm9Q14vWR37Wt7uxxgIBnLvmSkJ86GVABDhlJMflU,1496
scipy/io/matlab/tests/test_pathological.py,sha256=8lcveXzzknQH_009kNTvrf4aAr-qgatrXPEuRZtxQ4w,1088
scipy/io/matlab/tests/test_streams.py,sha256=3T0aC_eSvutWlfNsKOL4JCUUqg9jYEhGbPgJ4ZdynpM,7638
scipy/io/mmio.py,sha256=JUd3o5FazDpeJ7RFY1EMIQcPvtaeWqR6rEwf2L6IeyM,589
scipy/io/netcdf.py,sha256=Z2OaZi2xfYuSSEaiXNZ4cRTZw3vVZRM67_lmzAW64TM,905
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-312.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-312.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=3Ligma7QN6U0hMJFE9xJZC_xV03bbSKHsp0rz2g7PwM,7769
scipy/io/tests/test_idl.py,sha256=hy3ER4-qVpni6T9rvhkra80FqoGRUaQGDLgU_2US9TQ,20992
scipy/io/tests/test_mmio.py,sha256=2PWDz3tc03WLh9uMyjdojm2YJ_BKBeW893O3Mx4lrBU,28676
scipy/io/tests/test_netcdf.py,sha256=izzpjJbXiIWPAy_xL5Mo2x5-b65LJtjxJR6P8cgnGiQ,19950
scipy/io/tests/test_paths.py,sha256=yfqQtS6YQkg2SFa4_YbGBqZlIhd-brGut6jSRDtv4yc,3271
scipy/io/tests/test_wavfile.py,sha256=TnsS-_8CoY0ZRFjR7N6E9UYK5A2lCHD5SQH1LOc8d10,15719
scipy/io/wavfile.py,sha256=OdMGD7q592u6m7BtYWhzhjys-R3cTTJVW3wvww1sDAc,27447
scipy/linalg.pxd,sha256=SUm9fRHyF3s5-nhG0PWlenOlEEXtMRBEW_5X2q0ufGs,54
scipy/linalg/__init__.py,sha256=EbuO2u-OCvaqh2VavmBAo3p1s_h7wFI7RPpKN5KB-p4,7972
scipy/linalg/__pycache__/__init__.cpython-312.pyc,,
scipy/linalg/__pycache__/_basic.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-312.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-312.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-312.pyc,,
scipy/linalg/__pycache__/_flinalg_py.cpython-312.pyc,,
scipy/linalg/__pycache__/_interpolative_backend.cpython-312.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-312.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-312.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-312.pyc,,
scipy/linalg/__pycache__/_misc.cpython-312.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-312.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-312.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-312.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-312.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-312.pyc,,
scipy/linalg/__pycache__/basic.cpython-312.pyc,,
scipy/linalg/__pycache__/blas.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-312.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-312.pyc,,
scipy/linalg/__pycache__/flinalg.cpython-312.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-312.pyc,,
scipy/linalg/__pycache__/lapack.cpython-312.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-312.pyc,,
scipy/linalg/__pycache__/misc.cpython-312.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-312.pyc,,
scipy/linalg/_basic.py,sha256=WHDtkKYd0O26nirK7gKuUrN5vCHrwKT6DotMWzSIHW0,71417
scipy/linalg/_blas_subroutine_wrappers.f,sha256=xCDifo7qRixvt11wrQR6BvQf3-l6rtH1H4cl1Snhz3U,8219
scipy/linalg/_blas_subroutines.h,sha256=z_4wvAsR2qygLTOQiqbv7PEMc8a1dpVnULvFKLP_VoY,19234
scipy/linalg/_cythonized_array_utils.cp312-win_amd64.dll.a,sha256=x6j-YZArL6xmJCAFPMYphBT02P8zeKzXWpRRGK7BZjE,1736
scipy/linalg/_cythonized_array_utils.cp312-win_amd64.pyd,sha256=yiIgStS2I9bUSE0AWKc_yAbsULs5DmcvnW5DfCCX7ik,568832
scipy/linalg/_cythonized_array_utils.pxd,sha256=fyp2eQgNSXX88dHrIV5qUuZ-fTEU-8JdXKOTLagAeK8,930
scipy/linalg/_cythonized_array_utils.pyi,sha256=scRj6ew53DtnyIry4gVV3IsZlbwiNZJeptv_0icKE2g,356
scipy/linalg/_decomp.py,sha256=zawwm5FIkOjBWvLn4yztavYL5r584dywCELIRmbIoO4,63340
scipy/linalg/_decomp_cholesky.py,sha256=FwQTaP-ryuEzWSebXj7w-qBS-FKVsQr_-8iT09vIEeM,12171
scipy/linalg/_decomp_cossin.py,sha256=_faz8WS0SWtl-U4WxVcSc6QpEsLfsK5ROBkyd8PaDBg,9164
scipy/linalg/_decomp_ldl.py,sha256=fx4qmHmF_JuJ3R_r9aqcn7UZG4jTui6Vy5SqmIiD2Ns,12888
scipy/linalg/_decomp_lu.py,sha256=kSaNwL0D1jhwO7Z8TguZGOsGvKsjcxenSGUDphMyjIM,13088
scipy/linalg/_decomp_lu_cython.cp312-win_amd64.dll.a,sha256=irHuU4Lo5G-UOMvjDQLyINn_BVvZJEb_fofy6f9OmcE,1664
scipy/linalg/_decomp_lu_cython.cp312-win_amd64.pyd,sha256=t0gk1BrRUjW32mgNZseBgRduVddFehi5M6pHKUSSRPs,248832
scipy/linalg/_decomp_lu_cython.pyi,sha256=bwyBF7nl-jGGrmj_D16QSXx7MBXAVtU3k4eswqKohCA,297
scipy/linalg/_decomp_polar.py,sha256=4KAJQAgKoN6W_onEM-m3oGqfwCK3__ASC7UFMFdKcac,3689
scipy/linalg/_decomp_qr.py,sha256=hGxmQIIQiVHw1D1-jfhf5aB4jAvvuVndUxDxZAyUH6I,14199
scipy/linalg/_decomp_qz.py,sha256=RMxX7pLyNuFhvTKwRnuaR0NraFwuZhvC8DBlIPc8I4Q,16779
scipy/linalg/_decomp_schur.py,sha256=xTwP4ZtSOfOBN12_k3xzxoqvfgpYW-sbCCjM1Tep0oE,10616
scipy/linalg/_decomp_svd.py,sha256=zMA1JP6yD2gY2Zq7e3mtUdncbATZBRziabt5Q8TdQck,15419
scipy/linalg/_decomp_update.cp312-win_amd64.dll.a,sha256=jtekta3yToBDIEM-ZO-H8CDUkicrXEvZnbBnh4Og3G0,1628
scipy/linalg/_decomp_update.cp312-win_amd64.pyd,sha256=eoGOKBzivLJpmMo60zqxyPSU20uIR4bsyLikyiRdacY,342528
scipy/linalg/_expm_frechet.py,sha256=m4h3Ibq3Jlh2LMuHNoRsB_LsGHDmTo5XyCdPcudx3Zs,12741
scipy/linalg/_fblas.cp312-win_amd64.dll.a,sha256=1sMCWd3lAorL4LgLCbMnDB30p8SodRMDpq96MwvreS8,1532
scipy/linalg/_fblas.cp312-win_amd64.pyd,sha256=puu4dBl8ublHh6c6lNOY_o_ezxHzP61sLTCL1l-XLwM,596480
scipy/linalg/_flapack.cp312-win_amd64.dll.a,sha256=TO0xh55B3hHArFANeVwcMcFTbnd71uBwVv_KhLso1Bo,1560
scipy/linalg/_flapack.cp312-win_amd64.pyd,sha256=tkU6Zd4JpeDQnUOvZ4WORaAcGD_kt5n1uY1PLsenM2U,1897984
scipy/linalg/_flinalg.cp312-win_amd64.dll.a,sha256=tVuJ3KfTeQWCiV6XEo2C0zY9wK_Y45r7MkmVrJ8TOdE,1560
scipy/linalg/_flinalg.cp312-win_amd64.pyd,sha256=N2yeXbqp0cADd0qYOCyUKk9y3P7t1vA7fgkPogoUyW4,88576
scipy/linalg/_flinalg_py.py,sha256=c1IdcsS9Vj_dd23d96bJMaEZKgK-9-yDmMjoVvplUDM,1569
scipy/linalg/_interpolative.cp312-win_amd64.dll.a,sha256=geuOIG6q_PZfBgaU_dEaNGm9YYJ_HTsBNd1hXKWtCdI,1628
scipy/linalg/_interpolative.cp312-win_amd64.pyd,sha256=yvFmfnRpZxwPLwNzSYY2iRHKVcRw4AE5fHfFjaBXALk,758272
scipy/linalg/_interpolative_backend.py,sha256=mwdx_kKIlNkWDBeLqx7WnD2ce4UEo2nXqCSyKHny-9s,46873
scipy/linalg/_lapack_subroutine_wrappers.f,sha256=p9h3y4oiQiOBihKeoF08B9R45dV5-MBeUsUFeREkyOg,36415
scipy/linalg/_lapack_subroutines.h,sha256=jzZsEPEbMC3r7z1TqeBDyWxaI1YSk4OXkc_Qt1l4yeM,248359
scipy/linalg/_matfuncs.py,sha256=YozFy73vtQd9K_i7uHs-yAlsHHkdeNl59cbxYjPUtuI,26004
scipy/linalg/_matfuncs_expm.cp312-win_amd64.dll.a,sha256=c_wBjTtEktq7hfDkFNXUPRGalAZNS0iXpSENSC2Puro,1628
scipy/linalg/_matfuncs_expm.cp312-win_amd64.pyd,sha256=f3-lGVyB_L0auw90YUsQJNSb-hvSRJs5LQMw8EjdYBE,439296
scipy/linalg/_matfuncs_expm.pyi,sha256=J4OGtavgQPeZXtpBbWDQMH08jKqrdM028ZpWzyEoDfI,193
scipy/linalg/_matfuncs_inv_ssq.py,sha256=9O4ImZRTV9pbiBzbYMwir_C3_3kFLu2RIa9fPHkuOAE,28945
scipy/linalg/_matfuncs_sqrtm.py,sha256=7rO5jRYAf90U2aIvWvcdtvDIm1Sa0Y1Sc7iYy0bEn1w,6870
scipy/linalg/_matfuncs_sqrtm_triu.cp312-win_amd64.dll.a,sha256=qVZm9F79vayX6k23ytnDxDgNUXwMwJ616Sjv7qI10XY,1704
scipy/linalg/_matfuncs_sqrtm_triu.cp312-win_amd64.pyd,sha256=zIDEBO_DGEpzHyKwnrZ7kI-BDaa7eFcFnFKFXmXWjJ8,251392
scipy/linalg/_misc.py,sha256=GuAl0DgvKf35a2oyvYgcud84O0e3DCQLSUdR2AzXE3k,6474
scipy/linalg/_procrustes.py,sha256=5s-CF7-cmMR7NRJTqHn1FgozjMKLpmEOMImID7wc7o4,2853
scipy/linalg/_sketches.py,sha256=nVwWE2o7wZw69rFbEDoosoZoNm31jW1iM9fxhdz5BnU,6324
scipy/linalg/_solve_toeplitz.cp312-win_amd64.dll.a,sha256=SrB8w2zwklWA72J0O1XgR_cfi5tx6hX5jRAKsFvyeMA,1640
scipy/linalg/_solve_toeplitz.cp312-win_amd64.pyd,sha256=yOBn8aGakzuGlozR42KRA2EyKo7daUMeeBH_mUrfi1c,269824
scipy/linalg/_solvers.py,sha256=XpplZPY3JHyR0prhu1pK3_eMFwMsDEvUQC7uGWcEUM4,29204
scipy/linalg/_special_matrices.py,sha256=hG-E0Djklp4u1DDI0OeoMevdvocG7x_9dSCUbtzHT14,42119
scipy/linalg/_testutils.py,sha256=ufSOQpa_ELeIj5xOKNcmuJPnr4KbcpisDaUm8sj-JuE,1793
scipy/linalg/basic.py,sha256=fvCfA0cB6qWyhCvSX9ijOhcBmrdRFFQ8va7_qqrLZhc,841
scipy/linalg/blas.py,sha256=9VNxn8qpwCwDI3yHdqp9rdxUfJQmei9sFH8xkVpS1kg,12181
scipy/linalg/cython_blas.cp312-win_amd64.dll.a,sha256=LMJRPeEAvkwCO7SiTbMjyi6tFM4bX7siT1WUleK_koc,1592
scipy/linalg/cython_blas.cp312-win_amd64.pyd,sha256=5pQrOuBgqJDwIjAh1nGaCHiVs6KNJEjL1q8h3t74DNg,277504
scipy/linalg/cython_blas.pxd,sha256=iuRww_s-VX6vBgzRU2R8kEt1n5TJ2hfYcK5-UZ8XM1Y,16049
scipy/linalg/cython_blas.pyx,sha256=OOg3Ego-6MJDGoDjM4C_K4yFM8zpMg_ELfkzqGIaZi8,65931
scipy/linalg/cython_lapack.cp312-win_amd64.dll.a,sha256=u8jGlTQOpuNYwBMIESeJyAQF1j6DP0WdklRkX2jcB6g,1616
scipy/linalg/cython_lapack.cp312-win_amd64.pyd,sha256=kNk9fD1buAmORRvT8lcvtgJj8WJySqu9fYmWadMO4-k,527872
scipy/linalg/cython_lapack.pxd,sha256=uPXUcQJqwGew9houpmqn1dnffXSUKU04hqt4ss9GbbA,209064
scipy/linalg/cython_lapack.pyx,sha256=LxZVRhD2mCDWcnAj27V1jzf-glDHc_IFpMJ_s8QUS60,710917
scipy/linalg/decomp.py,sha256=K3PKvW3LDX3IfONdWAt763BOU-40ic479y4yVnoWtjQ,863
scipy/linalg/decomp_cholesky.py,sha256=rx2bt5Xk3TslWMHIQQTTDlflCBgKeVMhZxG9yFg5DBg,710
scipy/linalg/decomp_lu.py,sha256=hWt9zkoMJdkLWdVhx8-1ox1om-RJuPeoucNHONUDFCs,662
scipy/linalg/decomp_qr.py,sha256=uBMdbPI3zbwJg6V9wSJc4jGUWRZBz8JrSYajVWHg0cY,599
scipy/linalg/decomp_schur.py,sha256=X_z3WQXWzAYCSfXM0tm0bpFzTry6ruOtSmfSOR4pF04,682
scipy/linalg/decomp_svd.py,sha256=6vTAujJiNDCu1-UTkPcPmWkT9s2BXSi9H9d_o-RZCow,652
scipy/linalg/flinalg.py,sha256=0mA4TgzU2wLAaHSeQLRuCg3A_R0cXCQSiLyM4H3xQGY,495
scipy/linalg/interpolative.py,sha256=a33573E5xJCAsYQltbzEC2WJi2QyF3Lowk0khyp3wRk,33266
scipy/linalg/lapack.py,sha256=qdxxtL5Gjc8qLjlnzgUbUE5xYn7eINDw_hdRBgHahDM,16706
scipy/linalg/matfuncs.py,sha256=yha-BcBUNQ5P9r_Ieb6jOsPOLS3INFr9NwFVZj4oj1o,908
scipy/linalg/misc.py,sha256=SY3ronlv05KREUi0vhxT3ruSPUg6MZmRoZWgXJh-7ZQ,613
scipy/linalg/special_matrices.py,sha256=uffrhazqL86i0JQNErcjSLiTB9b7c3E90x-g9trMD0s,816
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_misc.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-312.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-312.pyc,,
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=msR1CLmUFTacycBbzhn0XN17t1Y9wahAM8TA_lkEM04,71768
scipy/linalg/tests/test_blas.py,sha256=I3IhqtV-2oZrJnF5V1ZcJByXg8qGfF9EQ2oiOH8rN5s,41372
scipy/linalg/tests/test_cython_blas.py,sha256=w-Xg6YtJKzlhfD5EKninnVnvmwJuDYnffM-kzotXoCE,4205
scipy/linalg/tests/test_cython_lapack.py,sha256=L92_DkwZQyPDplzVwrdbMDLCHaXu5WPACwG-dwACGC0,591
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=uGVyBm288GVIieUXpc0Iu81z9u8Vc6Q6neIN2aCnIvE,3961
scipy/linalg/tests/test_decomp.py,sha256=SRPYRH0y1WZawzjltIQkHuDm5hScvxl28GvpzRYagZ4,107201
scipy/linalg/tests/test_decomp_cholesky.py,sha256=FtAiYQKLuej7DyViryuIMOTfmlnYoW3sT0zqSfgTP4I,7467
scipy/linalg/tests/test_decomp_cossin.py,sha256=2hK8yN25BPcsjX6sI_Sb9Fiie203iRBdmKGAFWETO78,5927
scipy/linalg/tests/test_decomp_ldl.py,sha256=Qt5tWnNSs6k3dCTWAKRuWDt5U2A8ibo1J-_BF_nJISI,5080
scipy/linalg/tests/test_decomp_lu.py,sha256=5SDUfPF-VUpUYEs604H4JxIQKkC5MtYOhXeIGXjpVRA,11453
scipy/linalg/tests/test_decomp_polar.py,sha256=dx9ubrGOCBZW2IVPBveUt6v6wWldGUOrh0MzFlB6h7w,2736
scipy/linalg/tests/test_decomp_update.py,sha256=xIhcW4BRZWWRX4hPZsGiEaISxpU7MGMjTKdbiltgabs,70190
scipy/linalg/tests/test_fblas.py,sha256=FoEZpLFpdle1scpFXl5CeVTEH31c5GBxxW5FNPx_Usg,19294
scipy/linalg/tests/test_interpolative.py,sha256=feCEd-BNhbvJV0HJpCmfDQt0uaQhk1LK6t3waSpbyYI,9210
scipy/linalg/tests/test_lapack.py,sha256=GpChthYxQZfBRkuLmb8BtSqkFIhSdtqfDAwRPXs5FFo,132748
scipy/linalg/tests/test_matfuncs.py,sha256=Tc_aCfpmo0lyOM34v3ypH7UuacIAWKsaTHN7iz1d4Ww,39823
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=jrzob-NZ16k_1cYe-7xqI5L8ic7VMYxVlqG7IyiMTy4,3995
scipy/linalg/tests/test_misc.py,sha256=k0264gAdjSMDmE4KRZcMztRKkkwWuzblNOrAD4K17Ec,81
scipy/linalg/tests/test_procrustes.py,sha256=JalCsDisOMEY2lD9RSHfSU6X_W4wVmev_9BZpn8VAko,6949
scipy/linalg/tests/test_sketches.py,sha256=RMUKNRQTj1o08CGHGf68bsES-jzLuN0AswGG5aNXsk0,4078
scipy/linalg/tests/test_solve_toeplitz.py,sha256=1y0Vec8ZOK1tmjclVMXVxCJJ8ZgO_s4TguYRFawrKcU,4131
scipy/linalg/tests/test_solvers.py,sha256=p4RkivhRqc1fvcIKPwBvI4ExoswMyefP0L04CKCXEsc,32332
scipy/linalg/tests/test_special_matrices.py,sha256=UflM-fIkzmALMcV0buNwq50INpvF-F24PZ-uf3jGo7E,27749
scipy/misc/__init__.py,sha256=yGB5XvVr2KLtK1e40nBG1TTne-7oxFrwSYufByVeUDE,1793
scipy/misc/__pycache__/__init__.cpython-312.pyc,,
scipy/misc/__pycache__/_common.cpython-312.pyc,,
scipy/misc/__pycache__/common.cpython-312.pyc,,
scipy/misc/__pycache__/doccer.cpython-312.pyc,,
scipy/misc/_common.py,sha256=6UkM80cLptkn_NdOuhU09nGYQB9Ns7wYRPSWWb3_GPY,11497
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=zhZRCBTlj4XhdPSo0wr8RQ77vAzRfM1fPLbNoBkGemw,639
scipy/misc/doccer.py,sha256=NQvWWhsiS5fZXEUjbdWlj1OMjqfJyhZdzUe4DhJJNe8,1503
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/misc/tests/__pycache__/test_common.cpython-312.pyc,,
scipy/misc/tests/__pycache__/test_config.cpython-312.pyc,,
scipy/misc/tests/__pycache__/test_doccer.cpython-312.pyc,,
scipy/misc/tests/test_common.py,sha256=rfcxQnBUc22G95MDYoYRmIciDjL8Pgmrf0fbMp-pqGs,859
scipy/misc/tests/test_config.py,sha256=Ma6jMr-KuIuDdFFz88ZHmFcawmOugzfJnspZv9Aqda4,1288
scipy/misc/tests/test_doccer.py,sha256=CWV_zZfJCz88jyPJ-pcIk8hMXxmXh_f58LgJjjBbJzg,3872
scipy/ndimage/__init__.py,sha256=Oo-CeM7DPYAGMsDW39xOMPXINXdq0tSRaASScCIJj1g,5158
scipy/ndimage/__pycache__/__init__.cpython-312.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-312.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-312.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-312.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-312.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-312.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-312.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-312.pyc,,
scipy/ndimage/__pycache__/filters.cpython-312.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-312.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-312.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-312.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-312.pyc,,
scipy/ndimage/_ctest.cp312-win_amd64.dll.a,sha256=pSyGDDmrk45v1tzYFoUcOea4jdyP5PWMLHnyOttKl5w,1532
scipy/ndimage/_ctest.cp312-win_amd64.pyd,sha256=wPxFAkL618l_yUF6gjRYcg74KaSD81e15AZTL-sE_GY,16896
scipy/ndimage/_cytest.cp312-win_amd64.dll.a,sha256=MAjT_UW63KCQHjaVAWvEwStbxfK5ouW1aDw06wYciJU,1544
scipy/ndimage/_cytest.cp312-win_amd64.pyd,sha256=6bDd3aKSeF3WLhxob7_Dw8tdJ5_Pos3c2_CNqQoubYQ,77824
scipy/ndimage/_filters.py,sha256=XLD2lAi0x6cqwiPWlBY54NuVR9wipNfU1utbSFb8cxQ,67547
scipy/ndimage/_fourier.py,sha256=qp5zfKy385ESHgKsnGRdLqo_06cPXykO5ubU0cKnxp8,11692
scipy/ndimage/_interpolation.py,sha256=KPIA3rYBpX2pdFAnUoThxEQoS_BqGCEEmusFaTAHyXw,38033
scipy/ndimage/_measurements.py,sha256=MzUt7beMMhDnGr7-zrRYDxqhy0N-oKy-MFZ-AhxdhY4,57693
scipy/ndimage/_morphology.py,sha256=WEw0GY88mopiU4qFAN0J8F33qEvPM1_PxvQxvOM5nxI,97433
scipy/ndimage/_nd_image.cp312-win_amd64.dll.a,sha256=RBS18cxJEh03BXuM1GJiyQkkpjql4TnnZr0kdipWy0A,1568
scipy/ndimage/_nd_image.cp312-win_amd64.pyd,sha256=AjTtuQ6mCNKmfr9b66RQt8jOgsmkPSeTtCIUmS23C1Y,186368
scipy/ndimage/_ni_docstrings.py,sha256=gUp13eFtd2wqZwlpI7O6SqDIlY7bGikdujvOAntqwf8,8713
scipy/ndimage/_ni_label.cp312-win_amd64.dll.a,sha256=cmHRmcOqjpM7H_PS2NklJjBmFTkOUbiRaLVMZyhEzKU,1568
scipy/ndimage/_ni_label.cp312-win_amd64.pyd,sha256=f6806OARAs1RenP9GcZ0-WCjnx3H6h-X0AZ43qdJwFY,406528
scipy/ndimage/_ni_support.py,sha256=p4-oDUktjLKW6429fcX1QiAmO7THuRoJuDkOQ-AlM4c,4884
scipy/ndimage/filters.py,sha256=pBSTBZeUY3XAJCIFdL1UZmB3OM6KJld2jhzPVWHpOYs,1003
scipy/ndimage/fourier.py,sha256=Mg8ym6fd2BXBFSrUiwrW3GUoeDTYXbdOqQe75EJiKYw,620
scipy/ndimage/interpolation.py,sha256=7VM2PUpSqDH6hFvaF4QIIsArLrF5Eml_Vq3ADrmv6Do,703
scipy/ndimage/measurements.py,sha256=NNTrZLZSbU6hO42Tj49me-S1A-V-pnODbpxkm4ehLOI,812
scipy/ndimage/morphology.py,sha256=6axnW-p_Fp3Bm-21zYoaaz-1SdNxEPYCl8SoeRXwDQQ,992
scipy/ndimage/tests/__init__.py,sha256=FNT5j4JCFxTapyNfZrM6cxnn7WlOGq0ZvCwhUZerX54,408
scipy/ndimage/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-312.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-312.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=oBKOjlyOqkDERFefuyjlsPeVfGRyfmDc-uiawuMey4A,315
scipy/ndimage/tests/data/label_results.txt,sha256=hNPE8YOPm5MZ169I4c5JnG2cWxleOlKddG1VdA-Fn3Y,4603
scipy/ndimage/tests/data/label_strels.txt,sha256=fLjLCCb72QdX55kKssZdwg262SaV5NgdIG_Bn_gPHSg,294
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=ii8Y7KA9O4TJEosO_bQ8yN3jBS9QvqnmM4r2KlEU0fs,3832
scipy/ndimage/tests/test_datatypes.py,sha256=4qhH8s06BlCG0Jbw5gps_7RDFNuw_D7r4fFtdV8tYqQ,2893
scipy/ndimage/tests/test_filters.py,sha256=OjHaFpPmVvseKomPHP4HNrSvoF0zOYKl_t_zn209KgA,95513
scipy/ndimage/tests/test_fourier.py,sha256=YI64WKLr4dJKXMT_o0J5C0C90Ojdbm9HGTx6AhE2zl8,6815
scipy/ndimage/tests/test_interpolation.py,sha256=kgFq2LAiZINPmVqSgL69MFB9rqt2gSJqueqg7ZPR3U0,56098
scipy/ndimage/tests/test_measurements.py,sha256=ACaoikLaLLfMjhXRv6Tm7IGO7MIA2RxlbeEansAjHo4,49590
scipy/ndimage/tests/test_morphology.py,sha256=lLyaza5WhYKwX-UxAOKH6-ZKjRusfQREOdvnx-S0w5k,109082
scipy/ndimage/tests/test_splines.py,sha256=r1rRYS7GD0ZsQWvuobXIBBC1Mh1MdHusr68vZLdhXBU,2264
scipy/odr/__init__.py,sha256=gHT9GMAwFz2ewCC0B1kTenNLG6g8CHNm__5z6vtF_zc,4456
scipy/odr/__odrpack.cp312-win_amd64.dll.a,sha256=IyD4bMeVDZVvmvPpqRRovLFG-9mcMvI1TCeTsTGeIMw,1568
scipy/odr/__odrpack.cp312-win_amd64.pyd,sha256=dey9S5Np4OI2RvAsXwUR70EbvXXKqOlyLjkEWeiZ6Mc,565760
scipy/odr/__pycache__/__init__.cpython-312.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-312.pyc,,
scipy/odr/__pycache__/_models.cpython-312.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-312.pyc,,
scipy/odr/__pycache__/models.cpython-312.pyc,,
scipy/odr/__pycache__/odrpack.cpython-312.pyc,,
scipy/odr/_add_newdocs.py,sha256=nquKKPO9q-4oOImnO766H3wnLIN8dBZJfqPh8BgKJ_8,1162
scipy/odr/_models.py,sha256=Mi8SuWoWXIFqGi3BfgMSc_eu1GQfGRtXSvkeI9fblWQ,8115
scipy/odr/_odrpack.py,sha256=vjMaJwARP-JUkLnVowpi3eLUSdE_jlpUwKER6AY_jDA,43551
scipy/odr/models.py,sha256=_7pQbo0FThkV9yo2NvXC21_SMbNqUiBjeUsxnd9PerM,610
scipy/odr/odrpack.py,sha256=NqRd2QtNcw1-gq4KpkbkddvMF3G78DxKGSFlJ8Day4Q,653
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-312.pyc,,
scipy/odr/tests/test_odr.py,sha256=gplFVJp6YtaKvcbpUSF7bzYMwpWW95hB2rwkhHGm9BQ,21576
scipy/optimize.pxd,sha256=Go47l_Tk8YvXQxnuJNN6UmDEApEKgBUrplitjO4tsUM,40
scipy/optimize/README,sha256=_CUBWHgG5xW7HG5La9gsHQ2Cr7cRidzPOexd4rg3oIA,3297
scipy/optimize/__init__.py,sha256=d7MHq3s2bdMFMMm9_lCbTRsICtc74gx8KW3vi30dUPU,13480
scipy/optimize/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-312.pyc,,
scipy/optimize/__pycache__/_chandrupatla.cpython-312.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-312.pyc,,
scipy/optimize/__pycache__/_dcsrch.cpython-312.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-312.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-312.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-312.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-312.pyc,,
scipy/optimize/__pycache__/_isotonic.cpython-312.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-312.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-312.pyc,,
scipy/optimize/__pycache__/_milp.cpython-312.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-312.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-312.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-312.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-312.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-312.pyc,,
scipy/optimize/__pycache__/_qap.cpython-312.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-312.pyc,,
scipy/optimize/__pycache__/_root.cpython-312.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-312.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-312.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-312.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-312.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-312.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-312.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-312.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-312.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-312.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-312.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-312.pyc,,
scipy/optimize/__pycache__/minpack.cpython-312.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-312.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-312.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-312.pyc,,
scipy/optimize/__pycache__/optimize.cpython-312.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-312.pyc,,
scipy/optimize/__pycache__/tnc.cpython-312.pyc,,
scipy/optimize/__pycache__/zeros.cpython-312.pyc,,
scipy/optimize/_basinhopping.py,sha256=ZhU0A8eDSNOLuOmxDnumhzQtM4E0XolgRTcHogtCgWw,31444
scipy/optimize/_bglu_dense.cp312-win_amd64.dll.a,sha256=zqWS0qnVl80O6-ceY8B91QarjLf9FsnCt-FsyD7-VSk,1592
scipy/optimize/_bglu_dense.cp312-win_amd64.pyd,sha256=uqRnKFBslIHWIYkNSpu3nlflo5RmH10BI3Ozeo-c-I4,313344
scipy/optimize/_chandrupatla.py,sha256=5xIvPShRwI3QPdazy4hvpqe0fJ34eCrEXJxVn1VsDGk,13441
scipy/optimize/_cobyla.cp312-win_amd64.dll.a,sha256=nT11tWzNX5hUf2oaPNBiKGZ1kvkokaHSigJ6sK3KdWY,1544
scipy/optimize/_cobyla.cp312-win_amd64.pyd,sha256=jtBrz7BCTajXsbqq8VHvianv82cuXk_Lwq7tW_FURzA,418304
scipy/optimize/_cobyla_py.py,sha256=Ycqnv7D9YqcDeM1pl-TNS0tMnAAJK9wEsY7KnCbxwUc,11185
scipy/optimize/_constraints.py,sha256=ejG1EP0e6MTOR9sPaeXhAvqeGQtGhORT0dbbap3xq_U,23444
scipy/optimize/_dcsrch.py,sha256=udxXF-mxNpriHIcdA6h_ct33oZudRJZj2ktTURLF_Gk,25967
scipy/optimize/_differentiable_functions.py,sha256=eXjmu5Vzm84Cm7BhuEbs1SizBGfUfbTstQ0MXaHmaDE,24311
scipy/optimize/_differentialevolution.py,sha256=1P7oCaebzefn6trgvM9m4R77ApsthD8UMjsLFyPEu68,84380
scipy/optimize/_direct.cp312-win_amd64.dll.a,sha256=siz5pk-vcmQlXiHa1yPKf4t26BeBWy4nB3CgulqM_oo,1544
scipy/optimize/_direct.cp312-win_amd64.pyd,sha256=n38WoSosNRY4OsPLjY8qDSZWNqlfmWKlLYxggyGoWFI,69632
scipy/optimize/_direct_py.py,sha256=kRtd45Akip2Z-lzRzoLdOkJoxAkr9wTLVJArnpfq8bY,12076
scipy/optimize/_dual_annealing.py,sha256=IYKMG5gYR0zOUN8uTVErpIuF_YiLk1zRa2394go_ffw,31061
scipy/optimize/_group_columns.cp312-win_amd64.dll.a,sha256=W--HdU88uqDAMdNxFAQVCa26SdXCgAIXc17kGKTTTbM,1628
scipy/optimize/_group_columns.cp312-win_amd64.pyd,sha256=w89sfuqkYcYvlwxVndaMjJVhoO4x2DbAFZgHuq6K5XQ,1028608
scipy/optimize/_hessian_update_strategy.py,sha256=ELbQBvWV3bcHOynxIO4Febymi6m2zj-15Q9hROBWJsg,16292
scipy/optimize/_highs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highs/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_highs/_highs_constants.cp312-win_amd64.dll.a,sha256=91J4O_oNHpTr_xgdpsoRMXTj9gbg82Jnp8zZL1VPQJo,1656
scipy/optimize/_highs/_highs_constants.cp312-win_amd64.pyd,sha256=JVAJWJ996Lnd8BJdL0aGGA8OA5kXR7sZEUUSXo-XVq0,949248
scipy/optimize/_highs/_highs_wrapper.cp312-win_amd64.dll.a,sha256=9x5_4Iqt3rCwWGRct4V5w8b6m5qzhOeZ6koqvKpFC1A,1628
scipy/optimize/_highs/_highs_wrapper.cp312-win_amd64.pyd,sha256=m3G9icGGs98SConulYpyJE6ETtPpwIHFT0vHLRK0N3o,4536320
scipy/optimize/_highs/src/cython/HConst.pxd,sha256=L15CusfL00lWvNnDBadRg4cfyFQ9OBZjx7DnuWTiQAM,5617
scipy/optimize/_highs/src/cython/Highs.pxd,sha256=DEAT3kbjSV8yG4ODe3pf13WxXXTrtxdXd3iPZB1C-lc,2203
scipy/optimize/_highs/src/cython/HighsIO.pxd,sha256=cyJqwFONXeG4JCUM732mCNtjBqpwlu-NdZTEGNZeWDc,725
scipy/optimize/_highs/src/cython/HighsInfo.pxd,sha256=UVxVCOiJv01N9sdMx7ZJYEWr4Sb3v2JsTyf1oqwli4I,757
scipy/optimize/_highs/src/cython/HighsLp.pxd,sha256=vlYC8YlR4B_nve_g7zc8V7XxDhojrdIl21cAQIwIdfI,1152
scipy/optimize/_highs/src/cython/HighsLpUtils.pxd,sha256=X7RWGM_jqH9iomrdE82vJ3FiQTlwCxOK0dFWwyhBcvk,298
scipy/optimize/_highs/src/cython/HighsModelUtils.pxd,sha256=_wdfARiJz-1uVj4-DCDQFL4qlxtwfSGCEcPENTxMa98,345
scipy/optimize/_highs/src/cython/HighsOptions.pxd,sha256=Q9qcpapCKmpUPQzvnSi8ikl5AwQ1evrw3UilgXO6Xxk,3270
scipy/optimize/_highs/src/cython/HighsRuntimeOptions.pxd,sha256=lGf-04sraouYfLC-nMhelSxckt5KzVBIh6E2fC7IktU,270
scipy/optimize/_highs/src/cython/HighsStatus.pxd,sha256=zbFQHPW_dv9veyurF4ifqzQd4KMvaI-3c812neTMK_Q,351
scipy/optimize/_highs/src/cython/SimplexConst.pxd,sha256=8f0-itFFhq9qWHZCxeGFW1-wu61X6rTt1HhNqcFI_PM,5113
scipy/optimize/_highs/src/cython/highs_c_api.pxd,sha256=Acdk0ptEr5H66e3sFkfcRRRCGp2x-Wl98Z3tp9D7XFw,339
scipy/optimize/_isotonic.py,sha256=RxEmV_dGFcb7dKP7FBypBE3nbaXPak9RMl63l_1FM5E,6212
scipy/optimize/_lbfgsb.cp312-win_amd64.dll.a,sha256=fgBwB0Gj2CfxJNPX2mYD6kRMvIYCZBwmpUWzbwZ0Y7M,1544
scipy/optimize/_lbfgsb.cp312-win_amd64.pyd,sha256=zlnJrMwEBFh0k1ui5wPbUOaHe7EZCK02UqPG53mrz50,436224
scipy/optimize/_lbfgsb_py.py,sha256=g8KhFqjJ5DbNn9cvfr2iTZ_2MjrkqrBs-dZZn0lHmPw,19672
scipy/optimize/_linesearch.py,sha256=T5mu4eg-mOvSXjGQpM_IgNEtIWKBnrZHHlqOHVY6po4,28180
scipy/optimize/_linprog.py,sha256=PZWKfISr_yRzjcRagxqfQ5ahY8-14RKYsbATz5E2Zok,30428
scipy/optimize/_linprog_doc.py,sha256=no_OyJgCCqufunfiL-7B1booijS_1OTY8luxvJBQ3b0,63379
scipy/optimize/_linprog_highs.py,sha256=vzNHd5QFirk-fb3j7dBTLF6KC3fHL8fbX9lTcfybCCQ,18013
scipy/optimize/_linprog_ip.py,sha256=LvPFzxHXKOkcTD02x4M4I-eS5i1MVQ6iG3Xjbb5HZOc,46911
scipy/optimize/_linprog_rs.py,sha256=DsKjiN4S4U510ahDcSWyFPIhakRrAXEFBl6i_dl7IiY,23718
scipy/optimize/_linprog_simplex.py,sha256=bWEwU99qgy0fC1G6F6TJHSr765eiKcQXA1_bVgxMJlI,25377
scipy/optimize/_linprog_util.py,sha256=98yfDJILV7iLuBNRTdLmDgbzHBersO54SZfVGJB10Gw,64271
scipy/optimize/_lsap.cp312-win_amd64.dll.a,sha256=7oCxIUoxJN21ZvHUJFpHI-_RVSDdMJ6rC8Ak35bKj20,1520
scipy/optimize/_lsap.cp312-win_amd64.pyd,sha256=aNVUpEd0KKd7YuqgMc0pIlh2lVWUB8QCi4ICp7x18zg,177664
scipy/optimize/_lsq/__init__.py,sha256=c4V-tnMyi2dXNNXkjFQ8SdvGANtgbrqXEM-OpdBdZpQ,177
scipy/optimize/_lsq/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-312.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-312.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=h9bYh3astIRvMtMGghtEGpG5fDsh74SMRbmgF3HN8YM,5378
scipy/optimize/_lsq/common.py,sha256=zQN7x4qxtX5PCaY2gdYMv4Io0YM4Cit9Eqb_L_k8xrM,21256
scipy/optimize/_lsq/dogbox.py,sha256=KuyTxgJPRumBkrk8KlgZZVhm8tpdmsGNBtPAsj2U1Xs,12013
scipy/optimize/_lsq/givens_elimination.cp312-win_amd64.dll.a,sha256=qNArNmFZc6gl3EvZ8ca-9vKeQohfZOtd2SxrSbDpmTc,1676
scipy/optimize/_lsq/givens_elimination.cp312-win_amd64.pyd,sha256=0tUwNOcMkH9iSBO0XT0Fcri5ThydWaeBu74h9uuqjKw,209408
scipy/optimize/_lsq/least_squares.py,sha256=nHh4Fzgy28drPE3io6zAe_SfxVj6cOOgSW8RPA-YbU0,40639
scipy/optimize/_lsq/lsq_linear.py,sha256=9Xlb1mb02xqkOja-8Mb6cJDpW4ovNHvyoNRgZSt4QzM,15542
scipy/optimize/_lsq/trf.py,sha256=BBsxUjWZjzdVILU-Zr98t3WiySH_MUIp-unCQlRAo6U,20037
scipy/optimize/_lsq/trf_linear.py,sha256=GEMGyMqWwaVLUa7sCo9iwDmUkuT746oci1iK6KjnJQA,7891
scipy/optimize/_milp.py,sha256=vVoU-xFaKFD81wmmmp7TXrwy8_1p6GfqNXEqfu-aFBM,15579
scipy/optimize/_minimize.py,sha256=DBZTTzUJ5uMpvfhBQpwyUTIKr8wDACUfmu3m1ByJaqc,49315
scipy/optimize/_minpack.cp312-win_amd64.dll.a,sha256=cXPVngAOxy7XhkRRUv8eT_JESQqqUNTlcTtk312HEbA,1560
scipy/optimize/_minpack.cp312-win_amd64.pyd,sha256=t3XRo7MRlu7grkZhNKK5gzct2OLUYk2hRW9p0ewTpnU,108544
scipy/optimize/_minpack2.cp312-win_amd64.dll.a,sha256=U4ja2yqb-2V2zRwyLwE76z2QgHBTYWECIrYsCzp27T4,1568
scipy/optimize/_minpack2.cp312-win_amd64.pyd,sha256=WfuAC7K8N6ly6kkvww1ovUOKV-D4FH-8gNfXMwusN-Q,74240
scipy/optimize/_minpack_py.py,sha256=w8vl1S4T6XRbaR4uc0rR6av9CimbznJdJYUBJzzv9RE,44813
scipy/optimize/_moduleTNC.cp312-win_amd64.dll.a,sha256=80kZTzVxyEmONcumHbay3ImdC1_VC1PdhpZuVI3Gf1g,1580
scipy/optimize/_moduleTNC.cp312-win_amd64.pyd,sha256=yLsW1ezDbejp1zpnF6pWAs1ltVmMM5-v7vwb3gWNrXM,160256
scipy/optimize/_nnls.py,sha256=8ywKpNzwZ960ffjuY6iHXk7-17HdaFAfgu977ZShhKA,5349
scipy/optimize/_nonlin.py,sha256=bWLU3tJxFcrGAdKQkNWN-HH-ZQN1_vogiIx14yaC5s8,50624
scipy/optimize/_numdiff.py,sha256=sCRqhF7LdmRkxMBAcr0Wqz4oLq-fnBTCFPHK6z-OSM4,29495
scipy/optimize/_optimize.py,sha256=Efz4gjZcQolXqd47JuwhnawVLfjHo_yGo-rpyKu4Zjk,153995
scipy/optimize/_pava_pybind.cp312-win_amd64.dll.a,sha256=P8hbGxyLnL4j8Rcy44HR6Mht1kfocZGXrttuATkEvRA,1608
scipy/optimize/_pava_pybind.cp312-win_amd64.pyd,sha256=wVPvCaynFQwpuDWNyhn2Nm_hVBSWBoI-a-U825xJRDw,279552
scipy/optimize/_qap.py,sha256=lGe7ogeqUVEs-dUTAr2tumGUCjiNOJFcCQNch1k6OGA,28382
scipy/optimize/_remove_redundancy.py,sha256=wm3BTNUsnRTd-emKeiFTss68K09fIH4IYyFR7MD3cJg,19291
scipy/optimize/_root.py,sha256=GMgq_w1cwqimop2_FP4nHrpAdLPoXFTqUCe7gNSTMgc,29055
scipy/optimize/_root_scalar.py,sha256=zehSxyAbZI7bZNWLSzeR776P4c85v1La2R2yP7C3a5c,20120
scipy/optimize/_shgo.py,sha256=x39wcoLRMghqFJY3aGGtZFG35RCO79lpq-mNiifPOnk,63852
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-312.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-312.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=8r1bC4GHNpMYp2D5VT-Q98rBTK-l3x6r-PfXCG7LDs4,51484
scipy/optimize/_shgo_lib/_vertex.py,sha256=z9a-UXpMVD8NXa_y1ePp-Va3b7B1tcAQPmRcky1W2IA,14456
scipy/optimize/_slsqp.cp312-win_amd64.dll.a,sha256=jlljZWJHvICZ84j-gutVbedXeHpK-zrqsPWthzZVS9o,1532
scipy/optimize/_slsqp.cp312-win_amd64.pyd,sha256=rcCahpDRBx6Z_sx1BvwszByjE-1UvMtYCCg9-rQIFbI,107520
scipy/optimize/_slsqp_py.py,sha256=O0SxKS5SxEfn19ui6K87xpvg7Qpx4EAUgQBiKvKg2MY,19601
scipy/optimize/_spectral.py,sha256=j33TVc8_7styCVxKZP76_2C5CsdyFi5lD5TpeBbo54U,8392
scipy/optimize/_tnc.py,sha256=uL9ald4ZzsvDK4W-kPTiIx5Ra8Eqrc21GrvYOaqvGvI,17338
scipy/optimize/_trlib/__init__.py,sha256=5TbV8UJSSEwW_qONgzozoHDgvhhugzqykJzybDCrNj0,536
scipy/optimize/_trlib/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_trlib/_trlib.cp312-win_amd64.dll.a,sha256=Dwj1YvNo5ISfKoBaRDORlp_0NFiWDp1xniyA-qZl6yY,1532
scipy/optimize/_trlib/_trlib.cp312-win_amd64.pyd,sha256=f9LIWPVzGja6UukAsYrl1v_GJJK0Ev5oSzr__taA3ow,337408
scipy/optimize/_trustregion.py,sha256=EwpEXmSt_8p6fdUs5yRK2pzMH8HHfXqe12NXOE5WD1w,11101
scipy/optimize/_trustregion_constr/__init__.py,sha256=Y2OLn2HBQ5rxpAfI-UjWZc4j_sDqjOBGmgdS2zAC_24,186
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=pYMm2qLrCrvpD7pQZAZpz1H7Zfag3uiwezGBCDx6syk,12928
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=uuZ-Jtgl6G6sew7J2aeA4pERgAQ6tPtvvebGLBdNFUQ,8809
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=GkloTNfoZpPSmyOC7E91BEUU-ffCAaiI53UnwwrzWec,26308
scipy/optimize/_trustregion_constr/projections.py,sha256=-1w4LbwrFnQc8xMhoyiBazu-YNydDqvDcBUMNsu7yx8,13576
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=kzhhoadtUgeFmSoBKerKMnSgMSSpzHc_m43hkgqjTlg,23229
scipy/optimize/_trustregion_constr/report.py,sha256=Fvc4n77YuGiRQuGxiDIgkvkgScq5dqko07g7zJOwj-w,1869
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-312.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=z8MKN6NbaxsH7pj2rbCGdOQ4jXE-eM-SPu_IcNMtIaI,10165
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=YQ5iAUYXSTKZusnJxG8yD3Ctyb2qlCPm9IAk8y8pkAY,9048
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=YJmS5Zewk0mh8qsVCgWQKoDoIoMqOTE_32klYQXK8QY,28368
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=DWLQhuc4K6-UxV810cLlbTFQg7i7Ueja1XHZMWt4DsU,1102
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=FU2a3KT4uJ189s4eAxrBmwVh2GlIiSzMN9LFvZqkIBo,14144
scipy/optimize/_trustregion_dogleg.py,sha256=XTvNQtebZTPzl6xGtzHBLDcFD2qe5sSOFOTbmtDWswo,4511
scipy/optimize/_trustregion_exact.py,sha256=H9E0sxJmLMebKgfd5L64h-BxP-WFIdCf6CyvMRUUNjI,15993
scipy/optimize/_trustregion_krylov.py,sha256=OE6ABN1_oeRUUBXWGBA2QFlzxO4GJVBseoNOMB2b4Sw,3095
scipy/optimize/_trustregion_ncg.py,sha256=O2_8p92l_faUHLI26GHK2wV0RTdAR1scli0XhXTTsSo,4706
scipy/optimize/_tstutils.py,sha256=g6-cA3Yl9OQNmbrYK3ojJFIrRTS9c7EJHhF_hoU5f2Q,34878
scipy/optimize/_zeros.cp312-win_amd64.dll.a,sha256=hszYR9p7CCXr1sCERqd-N_Cpit8fJsoLXQMfvp8QrJg,1532
scipy/optimize/_zeros.cp312-win_amd64.pyd,sha256=3MEo8Mu1L5Qdt0pqZly1uL6Yq2IGC8h6tuRPmp_mZoI,23040
scipy/optimize/_zeros_py.py,sha256=Xfy7hPkGWKzh7bLNH6DAlCev8RRhg5_ets7xiwy0YFU,119852
scipy/optimize/cobyla.py,sha256=0zBa2apVL3bjSIZebIGcNc8mph8NxyxgpK9dhqbaxbc,642
scipy/optimize/cython_optimize.pxd,sha256=FhEeI0aq_Y2P00BpghHKOa7UjhW_uv4e7aEzXdS-QjU,453
scipy/optimize/cython_optimize/__init__.py,sha256=LEyUcZ_tUfRNMYgyFBYUsHfztui15TviJlLCsbZWrDw,5020
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/cython_optimize/_zeros.cp312-win_amd64.dll.a,sha256=ni44QF0uL2x-c7rcsQ_S1lrfMPSurWMLtZUieQo4N9o,1532
scipy/optimize/cython_optimize/_zeros.cp312-win_amd64.pyd,sha256=ksdfDOGkIy3p3cSKU-f5sfThwfMJq5Nm2FIw-XjnJCs,95744
scipy/optimize/cython_optimize/_zeros.pxd,sha256=5o3CUJecdwYcTh6bX9c4O8F04MtmvvW9KluxtFsYDh4,1272
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=kbSds0gdPDP4-oM6U2AaaFhsuEhCDXkzlfZ4BS82sHE,1144
scipy/optimize/lbfgsb.py,sha256=2VfUgbmMydioFy4UXnOZIuIhlb7vT9Vt-Y8G-OVsHw8,737
scipy/optimize/linesearch.py,sha256=BNAKRI9InccO4mPP7kZP9mPtl7xlEcxfI4d_Cskfq8Q,811
scipy/optimize/minpack.py,sha256=xxwF1UHbneXoJJ_F8tw25RA2zGIwRt0PJ_ctEhYcZDE,1106
scipy/optimize/minpack2.py,sha256=mIKiFAvS0cJ5as_BiqErPP_ey_htS3yGYtJfM3aFi0s,568
scipy/optimize/moduleTNC.py,sha256=0BZtj41NEa1nM2XYN9Df-kmG7W1QJG4PWcY40Zour-s,526
scipy/optimize/nonlin.py,sha256=PlyDnxrzMMX3fJrVFSYarqYO-Yv_jdwsQvGv0fRNltc,1257
scipy/optimize/optimize.py,sha256=b5JLY_axDmHbqDUjrYjm_krKlDv2azfOwEE5RQUarXk,1300
scipy/optimize/slsqp.py,sha256=F7lLTdRfavwHD53hcS39psok1Rly8yoYcgmgYisWsKQ,846
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_chandrupatla.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_isotonic_regression.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-312.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-312.pyc,,
scipy/optimize/tests/test__basinhopping.py,sha256=OnRO7GaHTr1QffrCZc0CbXnrX2zzQMUoMpzsnkbt5hU,19422
scipy/optimize/tests/test__differential_evolution.py,sha256=bc5vcvfFiHkrEat0Ew2lkw7VGOiWDDW_sc-0vMLLYb8,69377
scipy/optimize/tests/test__dual_annealing.py,sha256=IQq0e2n8dr3YidXZe4eMKazGLYP5C-FQgkoYmZo91hU,15552
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=8l7kHLqk5mqiRVkv8OLlMQpPXjNJOBWi-jkaWD5n2D0,11988
scipy/optimize/tests/test__numdiff.py,sha256=KBPAsPpz4DwxfbzfvVAgpYNqKWz9MGjlif--0k5RPYs,32166
scipy/optimize/tests/test__remove_redundancy.py,sha256=VC2Tz00TMHan0FJ8QXgKdvazARH58aWCLb97BQZyd08,7027
scipy/optimize/tests/test__root.py,sha256=qpL7_trYXSVfzBwsxIEfrSZNksLT_Ck-2QP1ouDYtzU,4334
scipy/optimize/tests/test__shgo.py,sha256=32ydOG-rpO_AzeUECCeeIGFbk0e5sqBiFpDtNwZNaZU,41442
scipy/optimize/tests/test__spectral.py,sha256=B3d_qUmY55_NiIrAljlKrk4kHDxP8iyb8PVAXFsx_U8,6890
scipy/optimize/tests/test_chandrupatla.py,sha256=l9-KsDptjVhB5KTmy-UdQNjhoNi7O8fiV9inL3GFy6E,16862
scipy/optimize/tests/test_cobyla.py,sha256=2Mp4jJ34SRSVcP83uG5l29i19xu4mDkTLxXskW7tcD0,5336
scipy/optimize/tests/test_constraint_conversion.py,sha256=ib0hzSBpHty30rMRHEug5ESN2PPXXHyujdwiCUDjnH4,12161
scipy/optimize/tests/test_constraints.py,sha256=cn-3BR5et1YJ4FSgvu8bVFsSmLeSqzoVDbcBlR6AvL8,9663
scipy/optimize/tests/test_cython_optimize.py,sha256=XmUrnJrxWKWfH5337FiCAfCV6djffjpQWVg1olG1vg0,2730
scipy/optimize/tests/test_differentiable_functions.py,sha256=h3F9dLx-18mV3iSOuGZPHHC0JV0nszMYlak6q7LLHmA,27777
scipy/optimize/tests/test_direct.py,sha256=_JopiGDv5bJeZuALeYyE5a9umkHw21Z-r7yfWbFtdtQ,13470
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=Wff9cHabaF98onJS3pTR-nOre9uIKWcsjwQZ0k9eCPY,10323
scipy/optimize/tests/test_isotonic_regression.py,sha256=h81VacabdXRg0CXrxmm2Q6zBI100sxC89bKdScM9ouM,7171
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=8_Bvj0kpB1mvK3fmh3v_vsYCrH8iFTnmaVjqe1EnSGA,1180
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=MBgku6a6vtpw8BWoGQNQygMhxYMhMV8vnbpJxXTL8RQ,3727
scipy/optimize/tests/test_least_squares.py,sha256=EsIjLhRNICefAvsODXlVI8x8xor0GT9APGfkDFJVmzY,34822
scipy/optimize/tests/test_linear_assignment.py,sha256=51pamAv4Kf0zrqIUkxk-8iFVF4-iZS6F8CxVH1h0J1A,4201
scipy/optimize/tests/test_linesearch.py,sha256=gpIOB_wcNZt-OAWunbrL34CxbuzkHzM_36C6U3Z9qV8,11210
scipy/optimize/tests/test_linprog.py,sha256=NhzaevVs5HQ93kiSwjedtvdwt3HvezndHBdbe_lwQ1Q,99183
scipy/optimize/tests/test_lsq_common.py,sha256=AXdfzIlCO5gQa3gy7Ou9mp1SAHD8-qMP1yCyfK1kaI4,9797
scipy/optimize/tests/test_lsq_linear.py,sha256=mxw9ot2qoE8uHzCuJZ4FZ_eKYjlur7sG8smqXx-952o,11145
scipy/optimize/tests/test_milp.py,sha256=0vwenAWdg--VjEJbZrG_agTQARI5v6wXGdEoZvhfaoM,14938
scipy/optimize/tests/test_minimize_constrained.py,sha256=eX3VCEGQUOM8gPzkdQqYtkRSKWTgYXjpwVLrpB1YnVs,27328
scipy/optimize/tests/test_minpack.py,sha256=-HKM7kSmYuC54cXgF5bvPz1yho9mKWZSA3AMVpy_BBg,42440
scipy/optimize/tests/test_nnls.py,sha256=8NlZ_NxQhVbiuUdb4L2O-_UzPM2PiVvVgihmVXKnFVU,1593
scipy/optimize/tests/test_nonlin.py,sha256=CwjjZsNYDsVOo2QsTIMlfdtodlEztaVSpIfM-4Vpco8,18778
scipy/optimize/tests/test_optimize.py,sha256=8qeK6xIt7Wo00ZP2Sl7IpqETxADpZ8tjp_vOSifWz84,126851
scipy/optimize/tests/test_quadratic_assignment.py,sha256=C_jLzYepRtwE5iQVbsXJvuzQE6vFzLwSOGWfofP1LSc,16738
scipy/optimize/tests/test_regression.py,sha256=jc-uV89QavAhf34j8J2riNxYY9gIIgOddrRMV5ltC0Y,1117
scipy/optimize/tests/test_slsqp.py,sha256=VwGhdCImOwE3Xq2aTxB5AT4f1tNN4U_Nc0J94q78cdQ,23866
scipy/optimize/tests/test_tnc.py,sha256=XWL1j_u_9ZrkUBOfyFVytIAdD_dw5rRo-0w5IHx_7PM,13045
scipy/optimize/tests/test_trustregion.py,sha256=S57y-AFuek-24XWgUozVMLaoTGySNufU84-nZ8bo-6o,4813
scipy/optimize/tests/test_trustregion_exact.py,sha256=0rw6xflyPotyI0iiuJIucXjWunzS7WJdKDbat7zjvFc,13303
scipy/optimize/tests/test_trustregion_krylov.py,sha256=S9GEHBHe_59OEEbR16q0ukOWOGpk6FFqUyBKZDGPgHc,6805
scipy/optimize/tests/test_zeros.py,sha256=dbz81CU7ZcwNm9SUJe-wWEz39h0up8JDq_v3zy0WVkA,77535
scipy/optimize/tnc.py,sha256=KF2pfcTx3eNBqx0xCABBjmomGmNdal9KrBKtmjaLgWY,964
scipy/optimize/zeros.py,sha256=ngRMAKNJ60Acb_zYjD4XPn9FKMizByjQeufD5wmlJRg,825
scipy/signal/__init__.py,sha256=bYkCnYtsGq6Mg5eH4pieRW9z9iu0qpXTFsv8OaLRfyo,16730
scipy/signal/__pycache__/__init__.cpython-312.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-312.pyc,,
scipy/signal/__pycache__/_bsplines.cpython-312.pyc,,
scipy/signal/__pycache__/_czt.cpython-312.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-312.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-312.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-312.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-312.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-312.pyc,,
scipy/signal/__pycache__/_short_time_fft.cpython-312.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-312.pyc,,
scipy/signal/__pycache__/_spectral.cpython-312.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-312.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-312.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-312.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-312.pyc,,
scipy/signal/__pycache__/bsplines.cpython-312.pyc,,
scipy/signal/__pycache__/filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-312.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-312.pyc,,
scipy/signal/__pycache__/ltisys.cpython-312.pyc,,
scipy/signal/__pycache__/signaltools.cpython-312.pyc,,
scipy/signal/__pycache__/spectral.cpython-312.pyc,,
scipy/signal/__pycache__/spline.cpython-312.pyc,,
scipy/signal/__pycache__/waveforms.cpython-312.pyc,,
scipy/signal/__pycache__/wavelets.cpython-312.pyc,,
scipy/signal/_arraytools.py,sha256=cFIGtwUylmJk3s8MTo6PrntsPyjKBTVwCqdUFfYdvRA,7948
scipy/signal/_bsplines.py,sha256=z7Cn1u9q_1e_BOR5NR-x7r34Hm-r9vk82VI9T1UKSYc,24831
scipy/signal/_czt.py,sha256=dhVmn1ScMOn8rzcvaou8n5s4ik8mlPH7BPsViaSA_so,20020
scipy/signal/_filter_design.py,sha256=iLoTyDziS8JBBlQC2Fqm9Z7FGpE4c_ud6L5qaleoMIw,191558
scipy/signal/_fir_filter_design.py,sha256=hyfUlGEQDmd_nG-1q3AFd18FHYkp9W0QIsmvWsZf5Qs,50455
scipy/signal/_lti_conversion.py,sha256=x_Ppo57VMk-Pvk3lKOuz2mGP934aH-H8Z8adZib8-lE,16675
scipy/signal/_ltisys.py,sha256=0R2r87EZ9mbrxRMf8zjPSvz3fEH9oSyTQxXigl9Haaw,134463
scipy/signal/_max_len_seq.py,sha256=oVUkL3qSfM1_FDWY_AcUI5fFi2vBQH0f1iXiDVOrIbE,5199
scipy/signal/_max_len_seq_inner.cp312-win_amd64.dll.a,sha256=1l8T8VK3kMe2IfGqvt5yTEMCQzj7hokhZ8yY5khJTv4,1676
scipy/signal/_max_len_seq_inner.cp312-win_amd64.pyd,sha256=bcmuRlH2MTzP0QfUJE7pKes5ByWgVWEZJsks3ITHQng,1005056
scipy/signal/_peak_finding.py,sha256=DL2gnnOuWLF0GLTnnO2Fi4wj2A_RNo_UFS5GJljfcaU,50208
scipy/signal/_peak_finding_utils.cp312-win_amd64.dll.a,sha256=OLJH7_mJROP8SNHQcMI26_Nw6pHBrU6O0drqoifzbt0,1688
scipy/signal/_peak_finding_utils.cp312-win_amd64.pyd,sha256=RhU55dLJ3g31kpseHibuKbDNXvPUb5y3KvXp2ovhnk8,268288
scipy/signal/_savitzky_golay.py,sha256=TA5zbmZeExTMJS2ORPe4BJ23PfS7NHKkoOTjRj552z8,13774
scipy/signal/_short_time_fft.py,sha256=F4ctIVtGWFi27qokLqnKu9bR7IRZFZ4hAEGt4HkEIb8,74741
scipy/signal/_signaltools.py,sha256=MABxPERGmUCadQb8wcrZWGShf90uIKWhWzHvRc5FOYI,162212
scipy/signal/_sigtools.cp312-win_amd64.dll.a,sha256=w2M7FCVyhcr0JaKC6oL1450V59TjMnE3lpkuXuyK8jk,1568
scipy/signal/_sigtools.cp312-win_amd64.pyd,sha256=8iflln5xKzXGyxe6IwvmwFIsPdUZZOy3UKMTDy1Df9Q,128000
scipy/signal/_sosfilt.cp312-win_amd64.dll.a,sha256=xOGHNU_0hpSAr9SE2DU04Iy-swfr-fcADzpnqrc8enQ,1560
scipy/signal/_sosfilt.cp312-win_amd64.pyd,sha256=8PzPiERTbzAcGWL9cqg0_io3Pznr5AJBv2fSEY0qsf0,279552
scipy/signal/_spectral.cp312-win_amd64.dll.a,sha256=FMN-ceSW3xkeuoN0HlcvUaPNgiTgdhl1-1KqQzQHI60,1568
scipy/signal/_spectral.cp312-win_amd64.pyd,sha256=Vaslqihf_g4PHDDYSInVDy5UKpryUGWnz_x_QTYsZSQ,1009152
scipy/signal/_spectral.py,sha256=b6WMIuLcBE4WuMBvQj8avTGmPX4wkW11mpoMmpD6tKE,2023
scipy/signal/_spectral_py.py,sha256=RWbbnRQiRowEY7PV-Fwv7eDxlx8FaADHbqcQrGq2u7M,80025
scipy/signal/_spline.cp312-win_amd64.dll.a,sha256=iIdeTqd6Aqa_vkeJz-91oAWgbEKCStkxlBpUKk_sj7o,1544
scipy/signal/_spline.cp312-win_amd64.pyd,sha256=49hPBifVOMn_ZeNN0HpfOAvCVUIMrENzMinjkwX3udA,83968
scipy/signal/_upfirdn.py,sha256=SZeLNVfPAOjmX6Bqjh0xvoUehjYcPFpJ3wwLwqf7RJg,8098
scipy/signal/_upfirdn_apply.cp312-win_amd64.dll.a,sha256=UVU2K_iM6s1ViaHH1AMVlBS0YaxcHjvbUVOnbDfzWI4,1628
scipy/signal/_upfirdn_apply.cp312-win_amd64.pyd,sha256=dmzz9yHJb2BQev0rpAyc_lRb-aHu2DBHZ2RyRoMhgXU,365056
scipy/signal/_waveforms.py,sha256=SjUjL0zu-cOy1V-xsWxzz3tkwT7ycG4LXlJdQbiuKiY,21195
scipy/signal/_wavelets.py,sha256=Ed42rmbMTF1gDrkvvZj5foxEV73q6XhiijCHJHZ75js,16651
scipy/signal/bsplines.py,sha256=7rHwi8Mc6Xr-CABQwIE9ekZ35d8YwkJS8qg0tMRZOgc,893
scipy/signal/filter_design.py,sha256=_QriamQ5xlcALbbu99bhiOOH8YT5wE6jyV5VD_A4q3M,1505
scipy/signal/fir_filter_design.py,sha256=uPZDlO9HNEvqzqYJVedyqD93-DfTcPOjsVRQX9zwzwc,788
scipy/signal/lti_conversion.py,sha256=4QYYriDXwapouFyJ1bldIguKbxMlTX1rA4aZC_jt4v8,728
scipy/signal/ltisys.py,sha256=4tDvKyukc7Dl9mRuKexOM1xSxzSJarilTfHy72zT6GI,1262
scipy/signal/signaltools.py,sha256=aZUkwLC_U5lDup43svcw7fr3xMgHfJk1fd5sZFLzr2c,1208
scipy/signal/spectral.py,sha256=RzHRkz_0kOZaS9gHuyLgtoY-lDN6-50M42Vz3bO1HEU,745
scipy/signal/spline.py,sha256=4xylmC4JyNUioC_v_jsn4fdi2aFMVOy23Id14X8Q9wM,836
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/signal/tests/__pycache__/_scipy_spectral_test_shim.cpython-312.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_short_time_fft.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-312.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-312.pyc,,
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=mDThCaIAywm55MH8pqan8xUBhNlpYNSwXZpwunLp6K4,20483
scipy/signal/tests/mpsig.py,sha256=3cd_WPgz9dkjalruhXxxAPYCV8Kzs0L8CH4h-0W8jX4,3430
scipy/signal/tests/test_array_tools.py,sha256=GHjEyc4Q-O3PN9C9R1CCQ2Izm-LKGZhg5mX5gZ_FxUk,3743
scipy/signal/tests/test_bsplines.py,sha256=JIKk4a_jRWTX7-awcvOZbLrOFmfdXzS76c-SUVzmqqg,10844
scipy/signal/tests/test_cont2discrete.py,sha256=1vzkouv0wINTChU3pmQksPPbN9WQ3sbL7l0hahGnOnY,15021
scipy/signal/tests/test_czt.py,sha256=gSrHRP4imVuSnNrYkNrxvb4sdYnAFp6rRUMFr-4C6Qs,7212
scipy/signal/tests/test_dltisys.py,sha256=2ETM-Wwlgc-p6-Vbpu7-Mz1Vp6UK51M8qR2yvb70gOg,22156
scipy/signal/tests/test_filter_design.py,sha256=BaSQeUC6_ZjLwCqc54AMBSG0w4-atO1i4N5OtdHaHcs,194508
scipy/signal/tests/test_fir_filter_design.py,sha256=kHXO6c4Zoj5wJxDk6tXQNjT2Gdt8Uk1r3D5VJ_tYEDs,29982
scipy/signal/tests/test_ltisys.py,sha256=qClN5NdpEA9BB1nE83cGv1KSbtFJV4U5wHWMxS0tNaU,49559
scipy/signal/tests/test_max_len_seq.py,sha256=8caIaIvvSgV9zsQ8t2MnNBeOSBLqJajPISerank05Qo,3171
scipy/signal/tests/test_peak_finding.py,sha256=wGIxBw9M10mWvrYNtUTGal7HVocplloRf5qRIOuOV-c,34754
scipy/signal/tests/test_result_type.py,sha256=zWsEnxBFDAKmi9IhebffJbvjY9R2aO0kFtUm7skwAm8,1679
scipy/signal/tests/test_savitzky_golay.py,sha256=e7NiioSNnsP2SXYW6xc_ygBPLske5eDIjVf6GjUCIQo,12782
scipy/signal/tests/test_short_time_fft.py,sha256=6GmBLeI9q-2ABSsKvheXkWV71khZ9D1rek-2lIuYzeY,33986
scipy/signal/tests/test_signaltools.py,sha256=RI_Y89xdw7LJ8yitM52iWnFEKD9mNmBe-pdkbHqxJTk,144783
scipy/signal/tests/test_spectral.py,sha256=PFut0iagiYkkqZSf9r9ZqD1sM9uQolK9iTmH6ymAQvs,61371
scipy/signal/tests/test_upfirdn.py,sha256=qpJJpoo_Hl0yk7pkuDIAQd6Zgs7CIX5_jE6qhHNICJk,11527
scipy/signal/tests/test_waveforms.py,sha256=j80USvnR7ddMZZeqQ5PeiHbJ5m4E7qHG7QbVD5TxKA4,12326
scipy/signal/tests/test_wavelets.py,sha256=_RGwdTrq5bOJhbBJNTlrcP0HGF5H3utiNv_zTfr4edg,6882
scipy/signal/tests/test_windows.py,sha256=Oar7H580vmlSTG6yYX37MJVoSGXhxXi2mmlMfKEP878,42621
scipy/signal/waveforms.py,sha256=YRq5urkoch1Ds5UapLlAqRA3u3TUdUj1oLd0knhRXe4,693
scipy/signal/wavelets.py,sha256=k-Ic_dp3OXXwcay9lq91nIn1s3xLCBK-rGdAlfijrfE,632
scipy/signal/windows/__init__.py,sha256=G97xwCUcxrUmZO1pb3K_VfzlyUrzcJYpGjMN9UWWC2k,2171
scipy/signal/windows/__pycache__/__init__.cpython-312.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-312.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-312.pyc,,
scipy/signal/windows/_windows.py,sha256=LGrWA14Bumyj7RZUDRIy_tL1Rqw3nOBk0k4g7oueB7g,85981
scipy/signal/windows/windows.py,sha256=a5ffA3zEmDdcNUDYiQ8Lcen36nEXCixMQn2U4FBQ5kA,903
scipy/sparse/__init__.py,sha256=HLeZGtUqc2AlDSnRAgU0Od9xZ7gl8DSz4E6Be3e6VPc,9596
scipy/sparse/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/__pycache__/_base.cpython-312.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-312.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-312.pyc,,
scipy/sparse/__pycache__/_construct.cpython-312.pyc,,
scipy/sparse/__pycache__/_coo.cpython-312.pyc,,
scipy/sparse/__pycache__/_csc.cpython-312.pyc,,
scipy/sparse/__pycache__/_csr.cpython-312.pyc,,
scipy/sparse/__pycache__/_data.cpython-312.pyc,,
scipy/sparse/__pycache__/_dia.cpython-312.pyc,,
scipy/sparse/__pycache__/_dok.cpython-312.pyc,,
scipy/sparse/__pycache__/_extract.cpython-312.pyc,,
scipy/sparse/__pycache__/_index.cpython-312.pyc,,
scipy/sparse/__pycache__/_lil.cpython-312.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-312.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-312.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-312.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-312.pyc,,
scipy/sparse/__pycache__/base.cpython-312.pyc,,
scipy/sparse/__pycache__/bsr.cpython-312.pyc,,
scipy/sparse/__pycache__/compressed.cpython-312.pyc,,
scipy/sparse/__pycache__/construct.cpython-312.pyc,,
scipy/sparse/__pycache__/coo.cpython-312.pyc,,
scipy/sparse/__pycache__/csc.cpython-312.pyc,,
scipy/sparse/__pycache__/csr.cpython-312.pyc,,
scipy/sparse/__pycache__/data.cpython-312.pyc,,
scipy/sparse/__pycache__/dia.cpython-312.pyc,,
scipy/sparse/__pycache__/dok.cpython-312.pyc,,
scipy/sparse/__pycache__/extract.cpython-312.pyc,,
scipy/sparse/__pycache__/lil.cpython-312.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-312.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-312.pyc,,
scipy/sparse/__pycache__/sputils.cpython-312.pyc,,
scipy/sparse/_base.py,sha256=Z2FAlO78_kM3xXgVjR8I10nofVmzqF-OGgUBonSPluo,52923
scipy/sparse/_bsr.py,sha256=RZuyyJJw8Lk0GuvodtFp2Zr46vVSRmSgDaLXHels2fU,30573
scipy/sparse/_compressed.py,sha256=qDL0p29YzzR9bpsNi4n5so6lC0Y8Pa0gJikf0m0YLsU,52613
scipy/sparse/_construct.py,sha256=46AYjR7PGzVNp3Krtuz8LE0vhEvknLp4NxaEqoCgWIQ,48507
scipy/sparse/_coo.py,sha256=wrOrk9hoIGItC8BvbZbeodjQxGzjVNouca9maoNAZmQ,27592
scipy/sparse/_csc.py,sha256=R6cBKqmpvgUg0b_QFLGazSCXq9QjEPdZHTFm1OQ19zI,11408
scipy/sparse/_csparsetools.cp312-win_amd64.dll.a,sha256=ovzfXwQKzGQ8bq4QWTXZaiq64OlU3-HiQgOTkUazqIQ,1616
scipy/sparse/_csparsetools.cp312-win_amd64.pyd,sha256=jnwLap0hOCyr5o4Vp75_skd5QLBTU4ybm6c3OrYhm6U,741376
scipy/sparse/_csr.py,sha256=54NK1vmBjqG4GgW5LY0O39cgIUI29Fgn0uYvUIuP0Ao,16153
scipy/sparse/_data.py,sha256=Ael2tUNSCjt98adVl8PRyJulenNc6IXtGRjKhS3b5Ng,17432
scipy/sparse/_dia.py,sha256=cAhR_tJi6nTShYAjveXx_gilzOuQNZ1YEzYECybRHNM,19319
scipy/sparse/_dok.py,sha256=N707AP9e4tEwgS5dnTz_R0iMOSga2KP1aXQps_ZaXNY,18083
scipy/sparse/_extract.py,sha256=2Dsbnq2DpthHCZgPqeckpVoAi2z4eULH8s-sz_9tYPQ,5165
scipy/sparse/_index.py,sha256=kOsx7U-HVEOmnloZxEjTUDndu8O-NCsaQLxeWY-y6ME,13376
scipy/sparse/_lil.py,sha256=PSGkaYUmcNm2-i1X8U-57tDC5_5tH56ICKDiMCo4Wfg,21139
scipy/sparse/_matrix.py,sha256=mYDr_hyZwvBKMWOQo2Qrvc7DJV9w159735TuSiv3UIQ,3188
scipy/sparse/_matrix_io.py,sha256=__pQRY6H9O6KHX7W0t76OK3Fih6kJ7vSYc5y57ZpnXk,6172
scipy/sparse/_sparsetools.cp312-win_amd64.dll.a,sha256=GUMbAVPL1Y701oWNQEZsoSxAbenh4mZYqj-FP8XwbJw,1608
scipy/sparse/_sparsetools.cp312-win_amd64.pyd,sha256=rZ04WII12H3DdRjukXAhkqJb7YXDxoQD64lG4SoGYIM,4172288
scipy/sparse/_spfuncs.py,sha256=vwBEj6xNGh1V17dONJX76wCAj9iFfmteGqh48DZ_vng,2063
scipy/sparse/_sputils.py,sha256=QhhUC0lBnfdRhZkvfpNg8Shnd22V6jbFE3wNh9O--ok,13561
scipy/sparse/base.py,sha256=JL80rDo0TwYloUa1n4tyLRxb_e6lp2ermCU4f0YkG4k,824
scipy/sparse/bsr.py,sha256=QSzbgv-z06WX8-uPsUnkow_tI2SKMjzbaIp_EWXLmvU,847
scipy/sparse/compressed.py,sha256=wx4UDp1GEYdVvdxZg7HdnAVhCYhfWb1Sg4zZICB9rbo,1052
scipy/sparse/construct.py,sha256=YbHiDtWgKth3gWo5UcK9cVQZdFbSYI03FvT5TCHLvy8,985
scipy/sparse/coo.py,sha256=irEGMbdj__vcKOQOcqARXS-exo17SFl7cplcmOOQ1qc,881
scipy/sparse/csc.py,sha256=2zsFtNCw5agBC2g8U-AUtB_PbQfVrPgPhzUSQ2bpA_A,634
scipy/sparse/csgraph/__init__.py,sha256=qDn3IX1VAWjclSfhoY0hKIZGJ_SNheENl-LUO2YIlqw,7961
scipy/sparse/csgraph/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-312.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-312.pyc,,
scipy/sparse/csgraph/_flow.cp312-win_amd64.dll.a,sha256=I2WQS7MbKv1C70ZgiLjI-qaD8_gQev355SCSbVw_kSM,1520
scipy/sparse/csgraph/_flow.cp312-win_amd64.pyd,sha256=dHvcm76eC0FyFGPlZUGV3ub-3HbKERJIxRglBI-rtpY,296448
scipy/sparse/csgraph/_laplacian.py,sha256=oj3ybRem722rsYybbLVrhLSnfJCY0OSUKgFJTVkI0L8,18419
scipy/sparse/csgraph/_matching.cp312-win_amd64.dll.a,sha256=3hiA9y0_L3rxQ8gSJU19HkPa8l1FtZixMuHtbEY7Z1s,1568
scipy/sparse/csgraph/_matching.cp312-win_amd64.pyd,sha256=NlLA6OjqSxdjiXLPXW2fyVmccAxZzC07WcsjMy8n87w,306688
scipy/sparse/csgraph/_min_spanning_tree.cp312-win_amd64.dll.a,sha256=aQRX54f1cUYGe_1uh8X4w68Xpy0OnMuVvbVlL3hwrjk,1676
scipy/sparse/csgraph/_min_spanning_tree.cp312-win_amd64.pyd,sha256=CZnZVSzIu18MsfGqeiEHLFcp4BRwQWyRAuldvOfQsCs,228864
scipy/sparse/csgraph/_reordering.cp312-win_amd64.dll.a,sha256=dc8jNJXpfXpPicjM_TrsfFWxQsI5rB4cjlctG14u82E,1592
scipy/sparse/csgraph/_reordering.cp312-win_amd64.pyd,sha256=N4767Tx7GLLL1rkObzkcEIbM87wQeQiMiXFTBIuxw5U,293376
scipy/sparse/csgraph/_shortest_path.cp312-win_amd64.dll.a,sha256=P7SFfOiFvBKngBHtvFyc2qg_100-u205i89xoMLkAOk,1628
scipy/sparse/csgraph/_shortest_path.cp312-win_amd64.pyd,sha256=tvuYztAYASySf3GnbQ1Zbf2Sk9794QXh2k-tNSnfnmc,431616
scipy/sparse/csgraph/_tools.cp312-win_amd64.dll.a,sha256=fXlueYFS2oq2rmyiz2aFxJozxkG-QkZKXJ1UBRk-bOg,1532
scipy/sparse/csgraph/_tools.cp312-win_amd64.pyd,sha256=KcdaEUpkRszZqVTwpuuzA5lq9b72PwDrizHXEAYm6g8,164352
scipy/sparse/csgraph/_traversal.cp312-win_amd64.dll.a,sha256=2ek6OOa_UP2UooQk4Hn2q2gAwmvSiDq__5WIGziLux4,1580
scipy/sparse/csgraph/_traversal.cp312-win_amd64.pyd,sha256=Een3b2aBjBclfE_IkwxJ6yT15F6eFPsNZ2JGRXiyep0,607744
scipy/sparse/csgraph/_validation.py,sha256=1hOpo7O_rkzhI_EjXM_AV0l5Tm2-LQ9jay5VGAY13iA,2385
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-312.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-312.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=HiW9gS_ttLPBl9XvdVCRZGRKfWdoiW4PsmVTqp1Q-kQ,4067
scipy/sparse/csgraph/tests/test_conversions.py,sha256=s0xD-47azJTCrYl3u1Gb7A6k2frjRnYYKXyomPAH2ug,1917
scipy/sparse/csgraph/tests/test_flow.py,sha256=FDRd335RogmRkkRtH_wUwylXXYr-3WUFwqvktzHLWlM,7621
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=voWlNpjqyE7ksNiPV4abXv-4ZCkn8bdCc2PRAwO52ew,11359
scipy/sparse/csgraph/tests/test_matching.py,sha256=GEoiMXISJlDsyVfWR0LS2fH1YkOpSJzyiyuZWcEbwMk,12238
scipy/sparse/csgraph/tests/test_reordering.py,sha256=4YgPFLLffko_c_k1QpqUd2NvXAwIsVbsECnIFNuVHmA,2683
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=0QopCD2rVQU1olqq_I1YNvBlfRC717LZta_pGOUg16M,14836
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=HfH-MvJm86KJoeIKAyuhgkzqU5VxvJEamusUAjPeiOg,2234
scipy/sparse/csgraph/tests/test_traversal.py,sha256=6kHqs9jJSzi5qrBGkP5A7LenAI646d-eg1ZoN5eobgU,2921
scipy/sparse/csr.py,sha256=pVwLe3hgplNxZCjDixenMe2_UhHwHnNm_uNTb8X1Ytw,685
scipy/sparse/data.py,sha256=jBxVtluwVoejuNTf-QYgAYw3DdQpJiXM4WfgMUd_SV0,596
scipy/sparse/dia.py,sha256=FI2MAFv_1H0zFa6rrjdE-UZC-TBilpKVdGAspwpL0yY,718
scipy/sparse/dok.py,sha256=GdVpJOh-A6vtT3hR-73ziDU1-V0PFdPVUAfqeTTvUTo,765
scipy/sparse/extract.py,sha256=tcwXkf3fvxKg4PotXMrvrjeMzCgqvUQBbDBzrKdLEJo,590
scipy/sparse/lil.py,sha256=-8OevezbOf1Car1z9mjlfaBOQactS4wWQtEmzIPDBtE,765
scipy/sparse/linalg/__init__.py,sha256=Tkrzqd0JaFrVJLC1CtQOmIQC37_ceXPTrTQDKCa4xOg,4145
scipy/sparse/linalg/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_special_sparse_arrays.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-312.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=kacCyi9C3cT-m5LKPk8RPUhbjPToaL1dZ9V-UEbJD0c,2062
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=4NQF7CmyoMBGK9Z_QwULJjgdwiugSyIJXP7Dv-U0XQ0,4065
scipy/sparse/linalg/_dsolve/_superlu.cp312-win_amd64.dll.a,sha256=cfLDDp6M8sWV61n3JL7wFYTVBnI0kj-z7gOlkyEhJe4,1560
scipy/sparse/linalg/_dsolve/_superlu.cp312-win_amd64.pyd,sha256=nVsc9RLSfTOGRtaDlyY1u0Xg2YsCo_waPrauFfoPdY8,412672
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=4MCFqWWnii3xZQsimwvup4PRzW60qgZfkLMGD9Z4q3g,27065
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-312.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=aRc3Hvcq0oKLY38n2KWd0kjpM1bdaZ_vHh29Al6TGaU,28589
scipy/sparse/linalg/_eigen/__init__.py,sha256=013F3u6pMe0J0wKjMFq_a1jSuO-pwS_h_XzmJI3xfMM,482
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=qo-GhUt0avuZmCFu3Q0NATlwvmEEH29Gp7EBI-i5aIo,21252
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=EoiLm60_nxfHhizy2cG6N93fs5V5g8GV_j-ivhf5Cdk,16005
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=_LPGx94UYM99CGPDxZluUY64AVouztNpEfPaF4RAs98,1937
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=EU0vXuTlZzMcXxCITUTVxUZ_N0EgcDF06Q2rMzy0Q3o,582
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cp312-win_amd64.dll.a,sha256=miEOaSvhuLKfyMcJWXyjLBr4-DbIpVNNmkWsNvWY7Ok,1544
scipy/sparse/linalg/_eigen/arpack/_arpack.cp312-win_amd64.pyd,sha256=OXbqMFvtrpLoFmWWljqQJPMm_gOvrnopLvrjpHEMu9U,797184
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=_8BUYPXO5lpi_H7XxDmO87SZYjfV4MQqiDV7DIhacWI,69103
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=Rlm-Fm25WDegCn422wcGCXwx909okcR2cQSVOJYBvHI,24468
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=8aw6542gPHNriFRBENTZ5rb1M3cqJKToG--paaqpaxM,436
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=TL11Ozp5YzNE_C6S01tbj7kn3XqwgqwS5pPI68v0sGo,43027
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=WABPdYlKlUhRHWVIo7Q2RGA-3wwD_9FUywwuyLdrZA8,24458
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-312.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=Bt893QtBNdQ7PnzCrGkU0yfsKZS76yNxQF5kjEwSX6M,38459
scipy/sparse/linalg/_expm_multiply.py,sha256=_XV3t6bbCFQZm_JfQ3KNUtC4e-QQ7K3T-mAdl4pUDHU,27106
scipy/sparse/linalg/_interface.py,sha256=ZL3f_IP4pUi7FGRhf6rbkNQeH1xLH3c1Iyc9oZ1ZEZQ,28875
scipy/sparse/linalg/_isolve/__init__.py,sha256=rFcoMYM70Tbtjlc3xhWhgjdNHUOwc4TSV1sOFZwL6Gw,499
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=UcnjruHTMrTeSYRF5h_RBj_1OoMtJkCBE_t4e2qxN6g,16726
scipy/sparse/linalg/_isolve/iterative.py,sha256=y0iXG_iO2wt6zYxfJX2RJFs3oFks4lg_Y6tyZH3OuYQ,36747
scipy/sparse/linalg/_isolve/lgmres.py,sha256=JoXIngYHL-zjxpRsGUn14zMEQUtnrKicV4WObazOOQk,9401
scipy/sparse/linalg/_isolve/lsmr.py,sha256=-TOkfv2dcjZYhk0C0zI7OK3mkV0ytSFil75ngO3t4aI,16139
scipy/sparse/linalg/_isolve/lsqr.py,sha256=6pld_K09HE1YPVYCSMEWfWynJoUR2cqnkHHB1-6hngQ,21801
scipy/sparse/linalg/_isolve/minres.py,sha256=f7iMkQ1kj95OC5CkO6DQO5w_Zk2G61rzrGbZsFJuufg,11998
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-312.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=utTSPmJ1OZPN_qEeCezs9FWSAYsh_P1ae53JYAn-3x4,5578
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=QdbNn2Y0RHVkn2yBMkl_RMhVYDGnrjTjB6DIO24XVcA,26157
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=JpvwkFXdDyTF5bmQamomfllVJVdEqgMW4RHRfyC4EHs,7275
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=H6BTOYSAtri-W0OzpJNGDYqIdliXB4egC1qrnDsGaT8,6550
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=KiB1ndwvfJ1oRE1NezjiQAifBVP8uZIvyhY109P2m0c,3874
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=QgErAFy1NTZDB_IjcKx0TZcpH7zmqTCRpt67h3DSg64,2532
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=W_ERpPV4ZfxThvgBHxuyhiTBmmfSbQKFFZaSK25mGBg,274
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=TZnfW_I-Kn6jO_uVjB4hcf399eSLwW5p0eTWv9JaMxI,6880
scipy/sparse/linalg/_isolve/utils.py,sha256=LjktcTFezfc_dtnhxbzOfGB-TZhsjTxx_CDVLDA4MZE,3725
scipy/sparse/linalg/_matfuncs.py,sha256=ocotsbEuxi-0o6ZagJq_7iC5UFJexqDVA-dS9mAYRRA,30325
scipy/sparse/linalg/_norm.py,sha256=ORHvQ7Pz-idLk-htCVSvAvlkdJG-ykKKXa-voelRr9A,6260
scipy/sparse/linalg/_onenormest.py,sha256=Ep8GIsh0rx7TlL2azhWfCm_vm6DK5zVtSljwMt2WISE,15953
scipy/sparse/linalg/_propack/_cpropack.cp312-win_amd64.dll.a,sha256=BO9Yq7MNxVxo5gbOko_fxnd6wj1CqSaLz0g-1wPd4Tg,1568
scipy/sparse/linalg/_propack/_cpropack.cp312-win_amd64.pyd,sha256=SCq7BrvcPZ7atyGnN5bijyCB5Zmk1pU2ZIR_AM2XeZQ,478720
scipy/sparse/linalg/_propack/_dpropack.cp312-win_amd64.dll.a,sha256=n5oOSgmGa6S58F2iuJXTdhaPx0gayenuYcW4M_TOFf8,1568
scipy/sparse/linalg/_propack/_dpropack.cp312-win_amd64.pyd,sha256=QiqwGFsrdue5fRgf2lOU_jLnLIcxXiehse6xRD623c0,448512
scipy/sparse/linalg/_propack/_spropack.cp312-win_amd64.dll.a,sha256=-bRu1qEY-vgVsaGgcgnNI6Q6QDZNWpv2xW7EBy_tALU,1568
scipy/sparse/linalg/_propack/_spropack.cp312-win_amd64.pyd,sha256=G9AhJSo8oHDhPo85lAEDIC73-hNd96ovh0-toZuZOWs,450048
scipy/sparse/linalg/_propack/_zpropack.cp312-win_amd64.dll.a,sha256=0aZOB_60sjJzbQwYlAou17qlEN9Pf5_Bw9uyDbv5G5U,1568
scipy/sparse/linalg/_propack/_zpropack.cp312-win_amd64.pyd,sha256=pmJaAH_pcJIH_Nb6B5sMgGGk3hOu4RDYP58d4jGB7vU,467456
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=Sg9dmMxm-2_U2C9pF-KeDQSiKjcLXleOn24UzfiqXXs,35246
scipy/sparse/linalg/_svdp.py,sha256=h5RJKSOBxbEWAiaKzyqfk616CBhussER-SJCICL51WQ,12007
scipy/sparse/linalg/dsolve.py,sha256=sfGJEXQ4lzZNi1XrpxrUnv9H2HyYbd_MC8TrCSqhyho,721
scipy/sparse/linalg/eigen.py,sha256=eVsVKh6PJv5yToPAZrBGkbXafLuVsZPTeqe60YAj0LQ,687
scipy/sparse/linalg/interface.py,sha256=19D-A1Fpnj_6RFMfIlzXOc9FFRVIZ0bja_QRuC9CLas,704
scipy/sparse/linalg/isolve.py,sha256=_UyMkdBJ0PI_bT1A9s_wUekhlFp8u1teot66-TpMQhc,693
scipy/sparse/linalg/matfuncs.py,sha256=lhuLmWkMNKBHA2xuNVanK43R2aMsLZJcos4BQhhr9Qg,719
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-312.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_special_sparse_arrays.cpython-312.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=looGI5_o6CTlX5WnohlnsW_qPIofdrmpVl75yVUqLvI,14301
scipy/sparse/linalg/tests/test_interface.py,sha256=AoiAmDNBot5HDtyTWN9Ymjx70MuJtUVZ0av4rvWglw8,18434
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=lPvG9v9QpSB9hQedbNpXYxIM_gIHs2IH3LYRwF1e0-8,22483
scipy/sparse/linalg/tests/test_norm.py,sha256=ZuZ0yjU4U5XmkGkWJLPFTdAwF0T-sNUfaHovTuuXcUI,6304
scipy/sparse/linalg/tests/test_onenormest.py,sha256=_1CbXJgWFL-72PYjtlheU27jd5xGX90_Vki834yZ6qk,9479
scipy/sparse/linalg/tests/test_propack.py,sha256=P4eSFGIdmnFUR7OaWDiOI2ej4lvcbwmfTAj81JwdsOA,6472
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=RAcfJVKMNzL3QZV1TG93kJLIintPxh9CIUDLMEDHw-M,6365
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=a3CKlj_vI_A45sZgx-36usRCswvQNeutrX0hRK9bkzk,13191
scipy/sparse/sparsetools.py,sha256=gPjiZqKCFLDgY68WW59SOMVYPjE1CCzARK3QSywCNwE,2266
scipy/sparse/spfuncs.py,sha256=wWCn33GmSfnR6aHu3L2eTFhbIcOXLaI59PU6gtQpMeM,604
scipy/sparse/sputils.py,sha256=hMvU6alEf5kux3-vEVVw4e8ssiEhhzU9QgvoGPvFROs,1017
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_deprecations.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-312.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-312.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_array_api.py,sha256=3Drk4W7T0EI4dUFTyNmnhinMLAEX_1j0yA5LTPbBw-Y,15026
scipy/sparse/tests/test_base.py,sha256=KpHJC6I8p5-ym96SWycqn6TO205YvZRTjMe0Yl7XT08,194539
scipy/sparse/tests/test_construct.py,sha256=gnGMhxBWPuyT9mFm8TexMXX8BZeF_cMfo50g-PH_cfo,33779
scipy/sparse/tests/test_csc.py,sha256=ak1_Ka7itovqPa9NltnRgnMx_yvjNNw6sFsPaISZZyU,3056
scipy/sparse/tests/test_csr.py,sha256=f4iz7R58wt1janCc4F2-J_-UqI11Pw9oomL_r68CHv4,5876
scipy/sparse/tests/test_deprecations.py,sha256=GZ3vtDCTfSbhasVppy-CM-8353AU5CngcwT4Vu3XnlM,743
scipy/sparse/tests/test_extract.py,sha256=NJIEcflkpb-k0llvH_6uJMAoaysWliWyi4bRjl0NhN8,1736
scipy/sparse/tests/test_matrix_io.py,sha256=7SOFHH8giia5Xdm5QmNuu1Sr5rLYtGF916iQYPqIsJU,3414
scipy/sparse/tests/test_sparsetools.py,sha256=5qjHLoIkDjWyiSTJQ0aXK9srBn9ysK2rJkGny5o1FZs,10882
scipy/sparse/tests/test_spfuncs.py,sha256=jUPhJjGat0YjcEeZWrD6Iz2G3el0ubAyts4ezvbrCM8,3355
scipy/sparse/tests/test_sputils.py,sha256=f1dj3tk2ttc5Zjg9j6Fh7Gt6SPzozPe4L46XKx4pnJQ,7493
scipy/spatial/__init__.py,sha256=R-KvSAgfE2qCcRt2wtRTFMlkdaoniXqXloDLaayMyLs,3826
scipy/spatial/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-312.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-312.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-312.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-312.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-312.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-312.pyc,,
scipy/spatial/__pycache__/distance.cpython-312.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-312.pyc,,
scipy/spatial/__pycache__/qhull.cpython-312.pyc,,
scipy/spatial/_ckdtree.cp312-win_amd64.dll.a,sha256=Ak91kObadxcIf8GNDNkDJN27GNqg8Zh7juZfkmckAUg,1560
scipy/spatial/_ckdtree.cp312-win_amd64.pyd,sha256=OvpC19GptX-c9HytXtZpHBdIdD9bzm5-7-DmlJmCg7E,1686016
scipy/spatial/_ckdtree.pyi,sha256=GGsYjAasPsHNWYhkkRfe08fP9mtp1e5kFO9ZM2SS-Ls,6106
scipy/spatial/_distance_pybind.cp312-win_amd64.dll.a,sha256=I7HxG3DKhGa4y_WqfeIZs6tf12Kt3Y22ftr0BZ4Sd6Y,1656
scipy/spatial/_distance_pybind.cp312-win_amd64.pyd,sha256=s35T0AnAlMgBnp0166T42YPHxkm1hDtzcp9TALxl5QM,1383936
scipy/spatial/_distance_wrap.cp312-win_amd64.dll.a,sha256=lBNkOnZyrKdcfCe5pH09rSzGk54Lee281gZ0wbEN9bY,1628
scipy/spatial/_distance_wrap.cp312-win_amd64.pyd,sha256=J2ZtgUJgKc-2n5XdgbXsZF70laIlEEmJNQbGW0C9Hns,110592
scipy/spatial/_geometric_slerp.py,sha256=BYxiz6U5lSov5Y238WxGbf51yEycmM3HjGgGvOhARVI,8221
scipy/spatial/_hausdorff.cp312-win_amd64.dll.a,sha256=Ii-TxdiC5wdhH1LkNq9X3WAhopgf5iO8_1Wp_ZmfEU0,1580
scipy/spatial/_hausdorff.cp312-win_amd64.pyd,sha256=cHJq9krfKFj5qXW7r12g6u3jdBn6VaazfJKVLGXvlrg,223232
scipy/spatial/_kdtree.py,sha256=zVE9nZP-rDaMFfSMiATExFwGHz7TMYmhuI_xQwX933w,34363
scipy/spatial/_plotutils.py,sha256=70I7ZDiCaRkzdaNfO5akRKnfPZcGhc_adnu6i-jCU_s,7445
scipy/spatial/_procrustes.py,sha256=5k0L3ausfrPyOjQFckcGa9_2BNxZp7E-6R7EY6jNCLE,4561
scipy/spatial/_qhull.cp312-win_amd64.dll.a,sha256=BiZaKsZCwawU4e_oa8GW-NUC3P00Zq6Q_Wqs1nNYbXQ,1532
scipy/spatial/_qhull.cp312-win_amd64.pyd,sha256=6XmCNcNvjv8HM22ByYTFIVV46hblbIONHsfmy2Gx2DY,1056256
scipy/spatial/_qhull.pyi,sha256=L06jd8pQcSB-DcRMJe8wq0XvlIEzuOrZQAffq4zD7xU,6182
scipy/spatial/_spherical_voronoi.py,sha256=4qbm4MAsoy-DcllT4erfjLgFLGVQL0X8iBKpZJKUaNE,13880
scipy/spatial/_voronoi.cp312-win_amd64.dll.a,sha256=ZDDntStkbkr1B7QN6xnIGj-V__0dWJ-58mUp0Din0bI,1560
scipy/spatial/_voronoi.cp312-win_amd64.pyd,sha256=biOlE-UIsDJujMK0hUOxhPqj1VCqknjSeDE38bfInl8,214528
scipy/spatial/_voronoi.pyi,sha256=gaEmdjWgHeIA9-D6tYXwimJCppJzgc6yc-sfLju9Vyc,130
scipy/spatial/ckdtree.py,sha256=6GA2m3VQ1dSnG-51Q6LuQLS7yw8JZhMpB4uKALy_jAU,672
scipy/spatial/distance.py,sha256=Q8HYEU8Q-R14Gc37llb-DZDxIKB8MpwCKmOJo3Wji_4,96155
scipy/spatial/distance.pyi,sha256=AvjPPnJxnoDRusxs6Mh-UgAdqLW3yJtdje7lZelWSk0,5484
scipy/spatial/kdtree.py,sha256=O9wDDCyL7bOMbhWZxzGPKZe33h7UEdStW779J089t5g,681
scipy/spatial/qhull.py,sha256=10n1eDcF9qrUmGpqrEb8sLjw_cXfSSgT_WU0-z4ucU4,647
scipy/spatial/qhull_src/COPYING.txt,sha256=liRS5zfffHQ6PcJ0QjIHECi4wEUOdnSlUFfDMOoZd-s,1673
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-312.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-312.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=LTxfJD5Hzxi0mmMzhzCgp0dUJflrg6O5duj8LVxa9lA,5760
scipy/spatial/tests/data/cdist-X2.txt,sha256=Tb2q8iKbPvUlxO_n1pBfYkWvu1z_LuBcwFXCIRZgBOg,11520
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=sWic54AuDE3IyLk6XOw0BcyCSzr5BkXrpDJc7B00OTQ,15150
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=D6kMC8OK5H2Z6G07l592VtEENnehwy8wuM1rjVq7wXU,50020
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Ysw196_xQ_ApwKiaB7OXZ-gVvhvhFH8wUZAw7EfacTs,178802
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=jtPmZwg5AfpIom-wMjty8_6tI69ePUnJ7eYdFyFq9G4,3042
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=Wf3dVmhan8EEYiKAsrgN5WNMmAmH9v9NjJpO4xRdmPE,178802
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=aMGSV28cjKIV2CRfdR2zZsisln6oxi1tYQbxZ9tuMMo,3042
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=PdVB_30O5CHL9ms8KRB1kFiswp9Sed8CV540WQvNaJc,178802
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=VDgXqx3vUn26r1EZFcSk-rq1-wef2WVFcoBWLG8zvro,3042
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=q5nrvDyyqdltrjBRyT5eFVQIW-y2Igb2JdC8tBAXl7E,178802
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=nLK5yDtcmU-svoYeLPuZZoolZpxmpOaeLswLLR5QoGM,3042
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=2UzMIvz_r7ZT2Twt-jH-pst0Avue4ueGZezk7T2_6q0,50020
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=VWMmzL2jP_vhDzTBhYtZH7-VBKJFpafRROrefDeokWI,178802
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=Kn8vHq17IbWtj1G9XzCFY1EuXFTPnD7gX9FwW2KSm44,3042
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=OqjHwrqGBwmwsfTeN2fI1EFiWSpOA6ZlZYwN121Ys1o,3042
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=nj_JYQ6bfvhlFDKu5OC_zBeOzPKgewkmpxScJEPAzhs,3042
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=wz1O-EHZT2cDwFtow4ZoLWQgS8WY6DPzb_Ik9SXA8Fs,172739
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=rps5AwUK4Up9qCR3wAE1wfwuKE5ftKZTHChPYH0hsfA,2819
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=rfWuQ4RgSfY0ko_jqlSTjQIJYGCHeEbEwjHYpAC9Ivo,178802
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=jKlA87ldGof8ndZLkV9l97-Tsh2uXTIlyZRsMoWr0gA,3042
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=bWYNT7XqpLTGkQBMfnCFJ7bnoxooZlzfI0_O54UOquI,178802
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=bGIB7ygJC8CGHAoVKtwOdPgAqDM5bzffpgxYMTCKgmQ,178802
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=g9-qvSA4qe4S0xPxnTa17zqa4Z-P5S0TV1gAsbr-RAs,3042
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=vpgBfMlrUUH-nYlnLqm_Ms-MIjrgOlNCW-TW8C4Yan0,3042
scipy/spatial/tests/data/random-bool-data.txt,sha256=sydRG9aL6CH9i7-Nt5X2mNyQDsu9OC1fbUD-MRdt5bc,6100
scipy/spatial/tests/data/random-double-data.txt,sha256=OcmsKJSbi_eY65ld50iAatodSzw2NxFfDJIDHx0lOpQ,75100
scipy/spatial/tests/data/random-int-data.txt,sha256=saHYkK0CMRzV78RV18tUGIF4uKQlOqkN4cEWqD4gnFw,10366
scipy/spatial/tests/data/random-uint-data.txt,sha256=aBbEt1tldXIvAvkDMbSr9WddkxXtMTa05Qq8j4s55CE,8811
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=x-c_sDM8alTwdaj7vYvGAQEsjrg6n5LycGrrPR4dlY0,507
scipy/spatial/tests/test__plotutils.py,sha256=LakEz_Dtuw8CExd_0__YE45wJ2Kdjcy1-1MzkZ-nob8,1997
scipy/spatial/tests/test__procrustes.py,sha256=iZypw3jevzOPiHv15UZHwc3sXE3pNlUlCeKTZfoL5vE,5090
scipy/spatial/tests/test_distance.py,sha256=YBT3b0aK8KKCyc8Zb_cRa0-epYvd_9rSvb_Vw1VOURQ,86198
scipy/spatial/tests/test_hausdorff.py,sha256=7ZZJjse3SoM8wYx_roN7d2nYIJwHAh_QbXD2ZqZxEjE,7286
scipy/spatial/tests/test_kdtree.py,sha256=U66JmJchwzH3vRrmEFnwVne9yNKpkAviAK6SCjConIA,50803
scipy/spatial/tests/test_qhull.py,sha256=7zXoQAFzWxuXEhd6quiSMaFuFq3xFBco3_LEsI0yP4g,45167
scipy/spatial/tests/test_slerp.py,sha256=DDZie6nQFvdtM8PAY26gnUluKQALFUPfudI-WQbP1cA,16812
scipy/spatial/tests/test_spherical_voronoi.py,sha256=nNxAPYBP0fzY5WAsGLBO3PHMIJ7UeIBqjY1_nAqrFdI,14850
scipy/spatial/transform/__init__.py,sha256=AR19SJ8oEv3Pt2eXGRiHkglp7wU_rGvRJ8JEIBqs4AI,729
scipy/spatial/transform/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-312.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-312.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-312.pyc,,
scipy/spatial/transform/_rotation.cp312-win_amd64.dll.a,sha256=HXTUlwP-DqIGwJfFIOSzwtkSnkpUNZr3ezWWauuAsQA,1568
scipy/spatial/transform/_rotation.cp312-win_amd64.pyd,sha256=djepwjMfHgTM5Dq1tIO5o4d0eOB8ToBMMIA0KSFl0CQ,885248
scipy/spatial/transform/_rotation.pyi,sha256=X5RTw3o4MIgco7GWagRt4_qg1HOKCjGIy__OJ3eN1aI,3145
scipy/spatial/transform/_rotation_groups.py,sha256=XBEazTsMg71VeDaexivk9VYmVDgHPkNji9FrlRypjyc,4562
scipy/spatial/transform/_rotation_spline.py,sha256=bQlcYfmYrF1-__9YFv_Utkr-5yLRwlsXJcS-y6eENDs,14543
scipy/spatial/transform/rotation.py,sha256=iLAmViCchCLGqwzAui2jx_YP2l4qBo4fhebbQJh1d5I,636
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-312.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-312.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-312.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=GitwhnP9779RlTe9AmZlhhbGbY-uT7HeOOf_OuTNKT0,62642
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=mATjBt62JCkoqpEMHU2zXB9HF-h7KFpWMBrSLyxnkTA,5729
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=yMBr91x2Tt2GqMvJVTOMcO2n3xgOwGC80MN7HNXQkdM,5267
scipy/special.pxd,sha256=vZV_tS467FzwvnjYCtD4T8r5NAuPmITjU8ccJioLZ3I,43
scipy/special/__init__.py,sha256=3Unqj-9K2tCJXIKkl5NjgjcBu2EO-mZUJDgYxxAXQbc,32838
scipy/special/__pycache__/__init__.cpython-312.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-312.pyc,,
scipy/special/__pycache__/_basic.cpython-312.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-312.pyc,,
scipy/special/__pycache__/_lambertw.cpython-312.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-312.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-312.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-312.pyc,,
scipy/special/__pycache__/_sf_error.cpython-312.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-312.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-312.pyc,,
scipy/special/__pycache__/_support_alternative_backends.cpython-312.pyc,,
scipy/special/__pycache__/_testutils.cpython-312.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-312.pyc,,
scipy/special/__pycache__/basic.cpython-312.pyc,,
scipy/special/__pycache__/orthogonal.cpython-312.pyc,,
scipy/special/__pycache__/sf_error.cpython-312.pyc,,
scipy/special/__pycache__/specfun.cpython-312.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-312.pyc,,
scipy/special/_add_newdocs.py,sha256=gefvZxwpjX7LvTng8cWFYoJeTZ9IgYdmX8z6DCri8kw,410117
scipy/special/_basic.py,sha256=GGWpUpzKamxr-ps_1KPnFujtIzAjVg1Gsd4FJIui-j8,105165
scipy/special/_comb.cp312-win_amd64.dll.a,sha256=lEOi2xbOefvZvfSOcUfpTRX2JgUs7-_ulSMkGer3y-w,1520
scipy/special/_comb.cp312-win_amd64.pyd,sha256=djWTGFzg2IE4V6VK5jLFITERptSPrv-WcKavSTFDLCg,50176
scipy/special/_ellip_harm.py,sha256=Km_A9XgXnYTleTtRuUzOYLxR8OEUtmiYJLYsRSJaSNI,5596
scipy/special/_ellip_harm_2.cp312-win_amd64.dll.a,sha256=8-536ZNg4WJBoIMU7UacANkM4JZI3JSgCgkleE1k1uc,1616
scipy/special/_ellip_harm_2.cp312-win_amd64.pyd,sha256=YrHM0xgcVeOe-_J0WU-5oK48VjNNeHxay_rqE6aPxGs,112640
scipy/special/_lambertw.py,sha256=JFJLw2sXbh4UUu2tAahw6oN8fHodq3D-yNRo6o8771A,3950
scipy/special/_logsumexp.py,sha256=atmzxn3ZufgjPG-insiMHtIZktn0SvdZjZIBmCwTzEI,8821
scipy/special/_mptestutils.py,sha256=L5x7loWaUvCVsexqUH130bhphXN9DH9OTFHcIWu2tMo,14894
scipy/special/_orthogonal.py,sha256=jlqsJK2xVS_RElIVwA22-K61JPBBx2zEarYq0GjyVic,77163
scipy/special/_orthogonal.pyi,sha256=coR_kB8pOaLo7ZLlFEBnpwqQTHR8WFAKTqVhRKFeXSQ,8635
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-312.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-312.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=7Fjlx721WrFcUMSUh6Vv40NMiR1QX71963X1tHOd55M,371
scipy/special/_precompute/expn_asy.py,sha256=trfFT7O7EweO7DfQHIHCzkwWNuqm7sTtCo-HjsUQthY,1319
scipy/special/_precompute/gammainc_asy.py,sha256=8XBo6RBUzdxUhH7GrVeVTzsMJgSnGb9NpknFAdHwcBY,2618
scipy/special/_precompute/gammainc_data.py,sha256=w3vsqOEvC6a7s58voRucZdWFXpO3jIfSGrGwVTNEalY,4201
scipy/special/_precompute/lambertw.py,sha256=UsSir6v9vfyYrlu5F3W-Qn3MMW5RzqqftfZAC-kTH8I,2029
scipy/special/_precompute/loggamma.py,sha256=J2jz62nBO-7JEOA7nG4JQQt5BuXSfUPRik2r3pwTr_s,1137
scipy/special/_precompute/struve_convergence.py,sha256=f21NuMoJE1Di4MF2kZfd6b_wiwqml-uQPylWdpncK_Q,3755
scipy/special/_precompute/utils.py,sha256=tJpjcNS6WaXzqP7cLac7yFyXfb_pfe3AKvDCa0mwi9A,925
scipy/special/_precompute/wright_bessel.py,sha256=4IHODoYYrAFKw8nlJWAPzrOduiHIMUILTJaqtA4jbq4,13210
scipy/special/_precompute/wright_bessel_data.py,sha256=TIwEyxKLTl2DqbLvppxdVWB1jJu-hL157M3JMjFK6eI,5799
scipy/special/_precompute/wrightomega.py,sha256=hAKXz3Kk9L-5JOBA86U6-yC_tsF8gOz0p8Y1vmWcAzE,996
scipy/special/_precompute/zetac.py,sha256=cZMVkubEXtildiIM9p5ldrV1sEoy240MckKPO3JhM6A,618
scipy/special/_sf_error.py,sha256=hDbymj1eSiPjl828PR-Kidkcd8RiVDpJEB-fGA9ltHM,390
scipy/special/_specfun.cp312-win_amd64.dll.a,sha256=9FuJghAhMhsx898xYUi101xBE2j5gpmIScGoVDp0hgs,1560
scipy/special/_specfun.cp312-win_amd64.pyd,sha256=f0jNR_6xi3jstPvGJUe4DPAZ_XLVQFEP2pwcv77VQwc,521216
scipy/special/_spfun_stats.py,sha256=Zb72pfmi5hzvkRXD2QXhVnJMmYyIjoqrg6tVxaCcoSM,3885
scipy/special/_spherical_bessel.py,sha256=o3RiJ46ravdMI1pF0HOLhahs8RLf-63WlUXNCi628jU,10774
scipy/special/_support_alternative_backends.py,sha256=xaIJFLSLXuOJChqayFnYCMQOQ8_G6qK9xIesf0It_CE,2386
scipy/special/_test_internal.cp312-win_amd64.dll.a,sha256=sMinv0PGbeicFXiJtk7MIaHLbDshrednat7uwmYUCcI,1628
scipy/special/_test_internal.cp312-win_amd64.pyd,sha256=0BjQ0Wcl_1xZfmFMz3AgoRUc-PckjYEFM5b4vBuAtxQ,266240
scipy/special/_test_internal.pyi,sha256=SLu2iMKH7AWdVCbKlF0SpGFEdu2i-opkrFoCTq6zMaY,347
scipy/special/_testutils.py,sha256=_yhL5TN4pQxabMRt9NLqoOh6hWI3GuX4MzkShaNA084,12348
scipy/special/_ufuncs.cp312-win_amd64.dll.a,sha256=g3ZoGAQE615GFtQXjTWw-a-6-l32vnU7iSsW-47nA18,1544
scipy/special/_ufuncs.cp312-win_amd64.pyd,sha256=JBdzNwIoGA2KUsN16S89sC_dBzcSiWABCk6C3sHdoQ0,1975296
scipy/special/_ufuncs.pyi,sha256=nVIdCRISPiuQ-bOKKC-HTv6vlcha0HpTJTV1G7qB5jA,9463
scipy/special/_ufuncs.pyx,sha256=YK_v9QDuOCZ_rj4njo3CfXBLi0OxJsfVskZ8QgjHlxs,908583
scipy/special/_ufuncs_cxx.cp312-win_amd64.dll.a,sha256=Dy_4KILPyhzY0HTqJGjlHO1ggKGaWUUU0weAP24CJHU,1592
scipy/special/_ufuncs_cxx.cp312-win_amd64.pyd,sha256=pw0xn4s99KZPTl7UY--ENw3GuAF-Gdw3pXxs0QXWSiI,1439744
scipy/special/_ufuncs_cxx.pxd,sha256=nkf8KspJKA8wkeZKUJIWEJsxRzZYH-ARvE0TFDv7hl0,1781
scipy/special/_ufuncs_cxx.pyx,sha256=M2SD_nEAZ7K8FvukSmEHAWVkyo_4zIm5mR2fvAZuIUo,9756
scipy/special/_ufuncs_cxx_defs.h,sha256=YXjluIM6lXlK7lS58d1vuUP1aUtqKs24Xrlg-gFW-ZM,2759
scipy/special/_ufuncs_defs.h,sha256=03FrLP7Q3ZctmCSEXKNMiaThBDpklAcamxIB4uVnxiw,11280
scipy/special/add_newdocs.py,sha256=zw7pZMfX7zr-kH6MdBFhrmXBqqjj3tlfGfuL1o7drmI,484
scipy/special/basic.py,sha256=UVJGOHZCijP6H5Kiexi5U2R-CVqfUSCo4Rhv6sxb4bU,1669
scipy/special/cython_special.cp312-win_amd64.dll.a,sha256=gd1g0yEU3AWM8sjUBYwn-9Pm-AqAIapoBGV2AgVG2dw,1628
scipy/special/cython_special.cp312-win_amd64.pyd,sha256=Um1nXvRUa2vZwVZYVhBYW5ySGh0zkf4SuRWXX34swl4,3386880
scipy/special/cython_special.pxd,sha256=kE7_hERcNcRglyTZ6ET1kEvYXKBlMrReFqPoL2c_F48,16609
scipy/special/cython_special.pyi,sha256=sC6nE-1ffPYfFKVvopszCin-ilDqBWkDW2O6s0and2M,61
scipy/special/cython_special.pyx,sha256=0ti9YBYYDJGSuDhUoTwCdZ6tiDDX_Ma8yFPlTfuaV-0,145377
scipy/special/orthogonal.py,sha256=coBKuqdBycBq4inCpauKEa40NAcd4hlH43kznUXgaJk,1877
scipy/special/sf_error.py,sha256=OhEWgiU5kk4QblZIhTAfAc0eU-mUKK_nTA3H-MHlJ68,593
scipy/special/specfun.py,sha256=UO9V0FqZmF1cpc3zUJhLFSm03-3Ri2hfuIgnDcuhuPs,869
scipy/special/special/config.h,sha256=HCnxQbOB793PrSoAH8Maz1UoP_plyP6wYYbps_2frPM,2456
scipy/special/special/error.h,sha256=_SaNUHWAu_UmmH24KRRbphf7lILxVtabVrmrCuMUCwQ,1233
scipy/special/special/evalpoly.h,sha256=DUNqXgUptl9KoGrQ3P8Ld8168HrX1O3JG1Ymv7FE3nw,1062
scipy/special/special/lambertw.h,sha256=asKFHl96wVEZkj3cVwu6blzcUMmUPDzPctPX6k80Qt4,5262
scipy/special/spfun_stats.py,sha256=V6hWZ3XlGmCqBydaXtgF2cEsmRBSEqvAn-TDJ2MWSPk,562
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_support_alternative_backends.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-312.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-312.pyc,,
scipy/special/tests/data/boost.npz,sha256=9W9uqJTP5Z7aJ23carRakXSwFWEt5x_6RDvsI9_rAw8,1270643
scipy/special/tests/data/gsl.npz,sha256=X7Vf4jA3wye4vNm0C6X9KNosK2_NSzPBxGwR1EYmvag,51433
scipy/special/tests/data/local.npz,sha256=ykTwo3nEwxCbDrGbvE7t-kjbmxQ-IJaoNyuXTdwOEFY,203438
scipy/special/tests/test_basic.py,sha256=vGjIkAe_iZGuCw_wHksCjRLLHlJSohLV9yM1X_zo6Yc,173825
scipy/special/tests/test_bdtr.py,sha256=ZPWchkB2hVjJ7MALAN9K4QWI8PeGSxcqduER3_myJRk,3343
scipy/special/tests/test_boxcox.py,sha256=K3BgEIIMADJLcnrzi9Nq7SWErOIiLQP36t6Bz6aoRMk,2778
scipy/special/tests/test_cdflib.py,sha256=pU9q5rxf3Xyr6v2U-FXYhYG9Nz676gamR1D4wTt2Tsk,13847
scipy/special/tests/test_cdft_asymptotic.py,sha256=uF6yhrAGSPdlP1tdLDoR2-IBlkjFUeEo2hBAr7DTxJY,1478
scipy/special/tests/test_cosine_distr.py,sha256=ENyfDI4OabouefeuzVJQO7UsBE3Eaorv39Z4GQoBc7E,2773
scipy/special/tests/test_cython_special.py,sha256=OYxUpC4Kxs6OcxFUpk8LNtTrw-tsjOsVcC514VGypYI,19201
scipy/special/tests/test_data.py,sha256=TejlZvAuGrzjHfKokakyshrgDgUEQHatVX0eR6pruUk,30734
scipy/special/tests/test_dd.py,sha256=3tsB8dnU22c5CEG5l-NJBxDmra5iQGhvUVKn7POdqvs,2020
scipy/special/tests/test_digamma.py,sha256=vBfs2G9WcIEP8VGpGaCsFlTD5g3XODAP7gsUEmAq6Ho,1427
scipy/special/tests/test_ellip_harm.py,sha256=qOVC4b0N6y7ubJNYa4K54W4R5oiHdWghdhy_rO5_gac,9918
scipy/special/tests/test_erfinv.py,sha256=JMvb2hWNQ63uPevk3kAL6KypAtOMJzg8n1G3Om9A15c,3148
scipy/special/tests/test_exponential_integrals.py,sha256=cPxuyehzPSgv_o3Q1Cfb2ON5NNxgBgyAi8dCDILIQe0,3805
scipy/special/tests/test_faddeeva.py,sha256=2BXe8JR2d_QBMvjX-bmHkP0TDzy4JgZhM6XvSlqO1HA,2653
scipy/special/tests/test_gamma.py,sha256=-u9wzNPSPsJTs_DJL6VZAXXIcIqJsxOeQy__MdRTGxY,270
scipy/special/tests/test_gammainc.py,sha256=Rrv_OnC7U6EtXSvrbGQpcPb7sccWVPIgIAzjs6DBIxM,3951
scipy/special/tests/test_hyp2f1.py,sha256=2MhaEiVjxEz-8AGRnT-AwHw7LjAUFHIpzPnDcftCjyk,80729
scipy/special/tests/test_hypergeometric.py,sha256=XDky5ALnj27_BLHO6VghO_iFcGsUVklAuR0lFb9tWQQ,5736
scipy/special/tests/test_kolmogorov.py,sha256=tumJSXhfuCXak-8qGtK9xD-qFK4Yt350fVN8ezZFqWI,19905
scipy/special/tests/test_lambertw.py,sha256=AoYbanXWmx5twRL-HBARQx4RPSfHMPboSEXl0GXwNfQ,4669
scipy/special/tests/test_log_softmax.py,sha256=0VhKfjYbv5-bV_d4tWyrG6OawlRjCUi_O8mRSgW0Mn8,3524
scipy/special/tests/test_loggamma.py,sha256=SdxYiItvixR4NabOKLyBHu-iEP7R52Rew8N8bZugH2s,2062
scipy/special/tests/test_logit.py,sha256=eJfxl4AYJF2VRFebHTw5NS7eF7Q_R82C42x3cou7uwE,5685
scipy/special/tests/test_logsumexp.py,sha256=89z07XS91GhkTVSQIIpRTVjdli9_8cCggHY0a5QenPg,6374
scipy/special/tests/test_mpmath.py,sha256=9gFGLgpPaUx1riEWjNZsBO5Uu-uNipQYWcoYZ-N0Jcg,74937
scipy/special/tests/test_nan_inputs.py,sha256=Zgaxcg14kZegSoO4t_vIIFs1c9ZYiTU3gNoHgGVYJgM,1895
scipy/special/tests/test_ndtr.py,sha256=n_Y8wG9MYVmZOXPgJoiYjPGlUULdiPoDQFsuR6HzPWg,2757
scipy/special/tests/test_ndtri_exp.py,sha256=b4vFFkhdzIDWNgmKOEy5UjRCYtHsdkoaasRi_L9lzIM,3802
scipy/special/tests/test_orthogonal.py,sha256=jGOpJz_awB9KOkMMvu3denxMmxILvY6Z57d-3R2VU5s,32342
scipy/special/tests/test_orthogonal_eval.py,sha256=cuTPvYgz-xIvKUHz34iPUZpB6r8b-U2ZkuYRWjySaYU,9624
scipy/special/tests/test_owens_t.py,sha256=sotiVxRrjoQ4lYjbbBx88nQKHEHhNowfJ7KPdIM-vNQ,1845
scipy/special/tests/test_pcf.py,sha256=VTEqviYLsm0DBnuu1Bpyn6rEocf9sZGSyY12ufwxaYo,688
scipy/special/tests/test_pdtr.py,sha256=YpZQ7ssQ5z0yL2lKss4lDooRlz_3n-aY2q4a8aVXXB8,1332
scipy/special/tests/test_powm1.py,sha256=AG5D4ex4jCS97xN7RlyumVc09OUTuczNxS2IYwt9OAg,2341
scipy/special/tests/test_precompute_expn_asy.py,sha256=DFW5CiFXjHNUkWgZrehKUxPNTd09Cllp1nZ0_FU172g,607
scipy/special/tests/test_precompute_gammainc.py,sha256=sY-xFU6MwT-bzjlXXYqu2_spch0XGJO0sZR6pQHMgPs,4567
scipy/special/tests/test_precompute_utils.py,sha256=EWXMuVz5WDUQISPzh87i02u1im9TcLbDBxyk-BO6iZY,1201
scipy/special/tests/test_round.py,sha256=aySiXp52xcim56HDkpumtSnFYgRTGzZFQ2USLNmcECI,437
scipy/special/tests/test_sf_error.py,sha256=cIpgVdMpEO9xOtEPT0cR_aPl8oQvllmOGkiYVUDsR3U,3878
scipy/special/tests/test_sici.py,sha256=REYntiqC0T4Pb-66ESBmjEmIoTJDbjYs786XNwUkzIw,1263
scipy/special/tests/test_spence.py,sha256=r4chXBi_nKK1uSfPse7owNhvcC93aBvcMBZf_8wTlF8,1131
scipy/special/tests/test_spfun_stats.py,sha256=ArofuQVYCm6RevEx0gP2AJ1_6ARrf4Qs5jL3j0LEPKU,2058
scipy/special/tests/test_sph_harm.py,sha256=jUhoguaBjEo2L6660qg41qaLxOxhbMZkMBsa0e5Afmo,1143
scipy/special/tests/test_spherical_bessel.py,sha256=gGZsHF5_rtQ-qpPmZEX-iyE3Th1XU5-HYWxEj5VdqSM,14695
scipy/special/tests/test_support_alternative_backends.py,sha256=Rx1F4WZlaZPLJ5a-RDsz0_o1O9l9VNyuusA_Je_OcCo,2053
scipy/special/tests/test_trig.py,sha256=vJL6u-XkIOfMOAAqAUs_xfbcJT4CG3HwF6iZ3fMmgjI,2404
scipy/special/tests/test_wright_bessel.py,sha256=t15XtWFFnN9YapbVZyyA88I32j8X14ADbUhZzbMRbLU,4270
scipy/special/tests/test_wrightomega.py,sha256=-2-tzEj7HA21xTo_WtCU5nc6rJDT9Tr0Z9IjA2f39mM,3677
scipy/special/tests/test_zeta.py,sha256=FbhrcRuDXJ_ZrVMRdUvICiaGMp7DM5CeWcOwYSKhXvk,1416
scipy/stats/__init__.py,sha256=_g0DYKySH3nDiMd9LY44KQiyMKZSBpk10mAjXDogH2k,18778
scipy/stats/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-312.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-312.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-312.pyc,,
scipy/stats/__pycache__/_bws_test.cpython-312.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-312.pyc,,
scipy/stats/__pycache__/_common.cpython-312.pyc,,
scipy/stats/__pycache__/_constants.cpython-312.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-312.pyc,,
scipy/stats/__pycache__/_covariance.cpython-312.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-312.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-312.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-312.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-312.pyc,,
scipy/stats/__pycache__/_entropy.cpython-312.pyc,,
scipy/stats/__pycache__/_fit.cpython-312.pyc,,
scipy/stats/__pycache__/_generate_pyx.cpython-312.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-312.pyc,,
scipy/stats/__pycache__/_kde.cpython-312.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-312.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-312.pyc,,
scipy/stats/__pycache__/_morestats.cpython-312.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-312.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-312.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-312.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-312.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-312.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-312.pyc,,
scipy/stats/__pycache__/_qmc.cpython-312.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-312.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-312.pyc,,
scipy/stats/__pycache__/_resampling.cpython-312.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-312.pyc,,
scipy/stats/__pycache__/_rvs_sampling.cpython-312.pyc,,
scipy/stats/__pycache__/_sampling.cpython-312.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-312.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-312.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-312.pyc,,
scipy/stats/__pycache__/_survival.cpython-312.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-312.pyc,,
scipy/stats/__pycache__/_variation.cpython-312.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-312.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-312.pyc,,
scipy/stats/__pycache__/contingency.cpython-312.pyc,,
scipy/stats/__pycache__/distributions.cpython-312.pyc,,
scipy/stats/__pycache__/kde.cpython-312.pyc,,
scipy/stats/__pycache__/morestats.cpython-312.pyc,,
scipy/stats/__pycache__/mstats.cpython-312.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-312.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-312.pyc,,
scipy/stats/__pycache__/mvn.cpython-312.pyc,,
scipy/stats/__pycache__/qmc.cpython-312.pyc,,
scipy/stats/__pycache__/sampling.cpython-312.pyc,,
scipy/stats/__pycache__/stats.cpython-312.pyc,,
scipy/stats/_ansari_swilk_statistics.cp312-win_amd64.dll.a,sha256=Bc7htnv512IXo3kRTn1dl88jehzvooeZdEKGoRASTLg,1752
scipy/stats/_ansari_swilk_statistics.cp312-win_amd64.pyd,sha256=BHARI44yHubi-UWf1qjhiMkTWrGhHzZMLXONesIw3ZA,252928
scipy/stats/_axis_nan_policy.py,sha256=r-fFgutwESmTEd_MjC6AlwJX5DqsHXTj7uvO6Qa8LS8,29749
scipy/stats/_biasedurn.cp312-win_amd64.dll.a,sha256=dNPRSHB4M4CCTAC48PkNjwqXH88Up89LUTEIpcrezcE,1580
scipy/stats/_biasedurn.cp312-win_amd64.pyd,sha256=sN5L6GiGNP8KwfqjgC7SSUg53UoYmzYRwvEhVXAPoF0,400896
scipy/stats/_biasedurn.pxd,sha256=OHa5weTjmvbIdkvmgDnD021p44bVaS5mNvDIx562SLI,1073
scipy/stats/_binned_statistic.py,sha256=IDN2tbE4N5CAl0DvjUmF0ChKSe0bsYdzCOCGfgtgIbs,33507
scipy/stats/_binomtest.py,sha256=gXacpbMuFCBgpwG_1__1InZhMgZsPfDZ3FFETkPybQQ,13493
scipy/stats/_boost/__init__.py,sha256=TOjKqfv_jPF6l8By1mdPbQFrV_H-ZMLRCUFZ7mNfL8A,1812
scipy/stats/_boost/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_boost/beta_ufunc.cp312-win_amd64.dll.a,sha256=8rne6wltDptnr2g7dB2cDoc24EW8St6Jywa41LaYac4,1580
scipy/stats/_boost/beta_ufunc.cp312-win_amd64.pyd,sha256=5bD6pnFTMFdG0iPbGV6OCwFW5B6zNei_oJyR8NrlvU4,1060352
scipy/stats/_boost/binom_ufunc.cp312-win_amd64.dll.a,sha256=KKCKvIiigibMeuHRZCwps7KgRrvo3jAzM9tIrB_Nbzs,1592
scipy/stats/_boost/binom_ufunc.cp312-win_amd64.pyd,sha256=YS1NQh2mAgZ6VlUkDuEAFe40tMGQrrMz-sPnQkCi3DA,1036288
scipy/stats/_boost/hypergeom_ufunc.cp312-win_amd64.dll.a,sha256=cTuTJxbNksQoHakcSHrK_F6E_6KMtzHyNsJWvtTe9h0,1640
scipy/stats/_boost/hypergeom_ufunc.cp312-win_amd64.pyd,sha256=ylcjbCOOWiKi3i37yguTUsdHpoOzAYrINbIX82HvRfQ,1001984
scipy/stats/_boost/invgauss_ufunc.cp312-win_amd64.dll.a,sha256=eUHA4surSY5uKx6mPkqWiHzMBSykhloJJ_kztrD1ldQ,1628
scipy/stats/_boost/invgauss_ufunc.cp312-win_amd64.pyd,sha256=8kUCrpM76xlIwTTdWfHSahyERb8zLfS-QNda0Y4a0Fo,1029120
scipy/stats/_boost/nbinom_ufunc.cp312-win_amd64.dll.a,sha256=v8iCdf-s4pANjVeDYMCdqdQ1IqiBjKvjQjlF__h7bZE,1608
scipy/stats/_boost/nbinom_ufunc.cp312-win_amd64.pyd,sha256=J7jpZhUa-hbf7HmCsBicfEbbRkg2bE_hBshHaDHrq-M,1037312
scipy/stats/_boost/ncf_ufunc.cp312-win_amd64.dll.a,sha256=4di-WDLcSk0u37Q-6SWDk6BRdPOiYe-hcQJHmguTVUE,1568
scipy/stats/_boost/ncf_ufunc.cp312-win_amd64.pyd,sha256=A3Y2H4EXU1eRGSldaTDzeywLspDtrFek7zBND5DgBwI,1035776
scipy/stats/_boost/nct_ufunc.cp312-win_amd64.dll.a,sha256=Kz9SEQy7ybGaihh0pFBYTnOiyHarb8yV8hDvKMN_Q1I,1568
scipy/stats/_boost/nct_ufunc.cp312-win_amd64.pyd,sha256=01jw16vM_O9N_JNyEA8Lbi1yrCHAoFA8Acw7X-d0CVY,1071104
scipy/stats/_boost/ncx2_ufunc.cp312-win_amd64.dll.a,sha256=3MH7jPaqLGZ05M2_dt675hFBY1HQiP8BBWZHpU0eAA4,1580
scipy/stats/_boost/ncx2_ufunc.cp312-win_amd64.pyd,sha256=C6Cj8ujUdsHYLSVnQi_ig9mbCalvCJOvUnu5Xju6A6w,1036288
scipy/stats/_boost/skewnorm_ufunc.cp312-win_amd64.dll.a,sha256=iEZjI2AjqDw-m3AKSmrMdvSbAGrn9mWI_dDg2QBbB8M,1628
scipy/stats/_boost/skewnorm_ufunc.cp312-win_amd64.pyd,sha256=WxQBJBnGaMCybBuyRCkA_aCzGrwdpr3B_q72Amd9qaU,239104
scipy/stats/_bws_test.py,sha256=h-6Ra_vazN0T6FA1kHZkG1AkEQ6JiRkFzJwVxD5ghS8,7239
scipy/stats/_censored_data.py,sha256=-mOWeG-hGYqCz8sjbELiBeBOE216wS8PsvmD99YNVxA,18765
scipy/stats/_common.py,sha256=PUBtrtKESxYoaVx3tlq8ZYImEYCIuYslFYikUS7uwdU,177
scipy/stats/_constants.py,sha256=AZugmM-2GSPX31Ppxo60H41VzwhlMFuanU8tpXAKFLM,1001
scipy/stats/_continuous_distns.py,sha256=R9Q9ehaeinqH-IHsQMjLWYy84vcSnSGkFm3YkUgYWMQ,393400
scipy/stats/_covariance.py,sha256=rkMxmsNHMtL7LZP0XmWgmev_rWKNiNrkik92ZWILC3s,23160
scipy/stats/_crosstab.py,sha256=dHftHq4CGFdfo87PDQFbNprSM7-qgfhtIUzUhTfWa8A,7559
scipy/stats/_discrete_distns.py,sha256=iKLkYAAkhzbp8X03BdC2GG-8hTk3_Fi7jzEIiMS7gLw,61298
scipy/stats/_distn_infrastructure.py,sha256=jGOg6H3q0VHfln4htdYEe-4s6Erk3sCVHTVy667Q5cs,150108
scipy/stats/_distr_params.py,sha256=N5IQKH5149uv4SwSZgke6zGwXSeH0e2UMC7qvMXAUxw,9020
scipy/stats/_entropy.py,sha256=ze_2es1LYIvwG3ALmiV4KOq_Smq46WS1cWnxrywqtdo,15655
scipy/stats/_fit.py,sha256=aDKXBphndTgCpi9-VMQcTWyyD521fxqxlGYtV43T5Y0,60580
scipy/stats/_generate_pyx.py,sha256=AMw8HjQxWB23m-MZYvyNEV6TPpytc1SfYwB41IHiEcY,856
scipy/stats/_hypotests.py,sha256=AtndVwkmRrDVqb-R9heKuPpiHwMBNPBibVsUolA06Kc,80861
scipy/stats/_kde.py,sha256=42pdDE_15sTMT6HTtZmNOcf_phHbopW9RjgcByHFk-I,25866
scipy/stats/_ksstats.py,sha256=RuzkXm3pY8ybimBFzPTBEhH2pcHk14useXulZJZ-Gn0,20700
scipy/stats/_levy_stable/__init__.py,sha256=XV3OS5J44tPRYHO6O7KPyx1P3BfEpKoN9tGVHXbgWeA,46765
scipy/stats/_levy_stable/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_levy_stable/levyst.cp312-win_amd64.dll.a,sha256=GROB98_eB5EIyWPh556fbd3AMaxoukoBFgtovkufTFM,1532
scipy/stats/_levy_stable/levyst.cp312-win_amd64.pyd,sha256=dBGm6NS_CiyEI7_TlrgA1VGkPDgzPjPX6reVM6MVgY8,56832
scipy/stats/_mannwhitneyu.py,sha256=fPa-hgANMzLDbxauo0s9KhVgOwXa4C5wXg63hXzOLKU,19992
scipy/stats/_morestats.py,sha256=cNF13C9lUX6ygqkDlHM6ZeYOgLZcH6ShkpOcZmjDuV8,194578
scipy/stats/_mstats_basic.py,sha256=5K4YFovUMwjgjhEFQIdXrgGoM69N5hZKNckGUxRjttY,121442
scipy/stats/_mstats_extras.py,sha256=IJgPfwpz9DNsW-nKcJRZTqebaiU89yuh7vQ29M2GR7s,16907
scipy/stats/_multicomp.py,sha256=a0QNETPUdVz-muYize7NnHIB50LpMGkXIfNiBbyBjhM,17741
scipy/stats/_multivariate.py,sha256=NmjF-ZKiK1BXUMG9skQCOJNVvbAXSgfFD7Ak0J5VqoY,244817
scipy/stats/_mvn.cp312-win_amd64.dll.a,sha256=m_lGbVqrDkgACHxhdYP_Zg2w9suESGxVEXiELim-348,1512
scipy/stats/_mvn.cp312-win_amd64.pyd,sha256=WTNUNpDjyCs2pgTTA270LQ8a4JcweLLRq2YyT9INj_w,105984
scipy/stats/_odds_ratio.py,sha256=Ovn36hoWLRYjV1AwnBiRj5ovUWsYingtY1BhClzPUSY,18340
scipy/stats/_page_trend_test.py,sha256=BGqQPLh3aOd5U5ntWcTbyY-t1M28xtAB5up4Z_K-R6Q,19486
scipy/stats/_qmc.py,sha256=90ho_IHxUwYj3owutykqIaLCKOA-YGHQGjciAxxJK7E,102163
scipy/stats/_qmc_cy.cp312-win_amd64.dll.a,sha256=qoJOkbJ_X3KND7GSXul3M9SBxzKpiY-EVb9ah5pUB7k,1544
scipy/stats/_qmc_cy.cp312-win_amd64.pyd,sha256=4DxL7XVZQzPfjtzTLKUCNcvM98NIKU0tVERDXf9DoK4,403968
scipy/stats/_qmc_cy.pyi,sha256=l2173UiDribq_G9S47QozpcKo8nO9D3rojwKLEd8m8k,1188
scipy/stats/_qmvnt.py,sha256=nlJZeatM_ZrfvGrZ5NKGPc4pP-g7TbdMj0aDdvVY6Z8,19300
scipy/stats/_rcont/__init__.py,sha256=xtM2CGKxJ2uUfF6Pfyoi_0em8i5pa6njGRaaGSrvFkA,88
scipy/stats/_rcont/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_rcont/rcont.cp312-win_amd64.dll.a,sha256=K0_BBFvzNmZLv4YO_sdrn1qK9eoq12FVkeSzvNNVk5w,1520
scipy/stats/_rcont/rcont.cp312-win_amd64.pyd,sha256=b2PQ6mad0lGLS4qI-mgXBvmKJV4KT65WwDnFxhmu52U,275968
scipy/stats/_relative_risk.py,sha256=txlXt8-p2wLydMIrKEOwdAt5giral_X51T2itd7VDDw,9834
scipy/stats/_resampling.py,sha256=dF5d_d2t_ft35AbScCHO1duGuHH8bM_xKfGxykZLc5o,81918
scipy/stats/_result_classes.py,sha256=904WSGrKwzuWHdd3MaS-HUwTJzLm8iF5aNzh0wZq-TM,1125
scipy/stats/_rvs_sampling.py,sha256=2wz3p8laZ60p2ow9H_G5hnj5ZFlulaMynnFE4laJYRc,2289
scipy/stats/_sampling.py,sha256=6pcrRPLvRFI-VRM4gRuw8Fub2l8Ot8WeyC-o_AEM3KA,47722
scipy/stats/_sensitivity_analysis.py,sha256=ZU5scZRNF5evHDM2ViyRKeRIeJskKXgpmObkEctohpw,25457
scipy/stats/_sobol.cp312-win_amd64.dll.a,sha256=Gjqoir-iZkKVepsioYLzs_4ETuv3BPwUBhq4SMKw5GU,1532
scipy/stats/_sobol.cp312-win_amd64.pyd,sha256=Ej9-rOuxwIOYz6W2ZK-MUVZu85_t6ht86aRGNY6gK9Q,358912
scipy/stats/_sobol.pyi,sha256=tskim056aVi6WIymUMEMdWHaLk8QMqWa2kCAbF2ZTPo,1025
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_stats.cp312-win_amd64.dll.a,sha256=wgWHTExLX6Uk-5PDuTSlyszKSN-UE1ZC-G6NyXiYQIo,1532
scipy/stats/_stats.cp312-win_amd64.pyd,sha256=7B74Ixy2DUfOrWj26RPTLnb7YEXodnHVuJqB16Oi5cQ,681472
scipy/stats/_stats.pxd,sha256=iR5uHEVtvhVTh99viNHee7PBYW-qF2mlo0zkhO8TGOw,717
scipy/stats/_stats_mstats_common.py,sha256=bvovoGu_4C7hk4UfJnOqKYKg2FzSn-zbQjLC2VQpG2E,19070
scipy/stats/_stats_py.py,sha256=Oj-OXZV-ZaBF5iFI9KSb7BMnh4GZgHVpU5RHf6xFQro,424403
scipy/stats/_stats_pythran.cp312-win_amd64.dll.a,sha256=sLPbsr81tauCGtTIKhrAXliNJF6qc5dusmdDit3Qtfo,1628
scipy/stats/_stats_pythran.cp312-win_amd64.pyd,sha256=FipKo-9J7ZVjK53W75aXSSXOMKf5j1kiFCKfUH-hGLU,1064448
scipy/stats/_survival.py,sha256=df8fIonPD_aC1Iq-mQokjbJew7EIR0ZMJqptFKGOD1E,26660
scipy/stats/_tukeylambda_stats.py,sha256=RtXsm72WCNJ8kQSYkL6x5XEsRopLVkY_wUSS_32NtmY,7070
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/_unuran/unuran_wrapper.cp312-win_amd64.dll.a,sha256=5B-aGNqysHvxCCs87YtScyHXPFK3u6fCh7ULbOmHZ68,1628
scipy/stats/_unuran/unuran_wrapper.cp312-win_amd64.pyd,sha256=eN5gPEHU8jIv5f-BLmvBJ6YLoS8IN0tsltb8SxzzKM4,1455104
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=aLU9IcfR08wTYyFdrL55ilePBemIa94wScMls0YkvHI,5730
scipy/stats/_variation.py,sha256=Vzve--tEjfZJnekfriYdBCdjlb6bJiHvFCFzH7639nc,4496
scipy/stats/_warnings_errors.py,sha256=f_Hxg3GzZJW2U1B608HFABSkV2w9Xjv1qtsND2AuGKA,1234
scipy/stats/biasedurn.py,sha256=31bvmg8wJmaIy2rs4CkSRnhG9jEbV9rn7fJ0RoRJafQ,549
scipy/stats/contingency.py,sha256=SxMGmqxWVIHJIRRJC2tI_aeU0mE-JnMx1PXoQLLtPQQ,16743
scipy/stats/distributions.py,sha256=_nRpDudL-PbnWvNm3fxRQ-00zEtCleSqhTNk1evZvq8,883
scipy/stats/kde.py,sha256=CceQ4q97WSElMimCGPjC5vEGx8QLuI8iQFH4KbltWN0,743
scipy/stats/morestats.py,sha256=FvoVSzbXNuhW4Pc4wAgL3sPAIwP0i8CgRqI2WxhtrTI,1425
scipy/stats/mstats.py,sha256=jzvELjOG5FulzAORL52iNL6VwQhA3N27RAr_-8fSu1Y,2606
scipy/stats/mstats_basic.py,sha256=RP6xgbhPD9MeyiMRgxHiGQSWJZXu3-Ay0Z9jc7l8uyQ,1938
scipy/stats/mstats_extras.py,sha256=6a3gBy_Tm578z4B3VXTn3siLMsRnLwYEL3hL8r5M-pU,811
scipy/stats/mvn.py,sha256=M0SeLZu6j1NULP0wMDLtWffsmGWgYS-pIsuQebBwPLg,588
scipy/stats/qmc.py,sha256=zzfKfqpVjNowjVqddBTmZdp1PvvmoVxnaQ0XAYJN6-w,11898
scipy/stats/sampling.py,sha256=IZYmnsnXtcwXxxHfGGNCE1nyIzRYIzWz-BuH1pSsRv0,1751
scipy/stats/stats.py,sha256=CBHVLbIYFiOYJMK8HfjZR9nIVOmqnwctIPuJfSEiIsc,2192
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-312.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_boost_ufuncs.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_fast_gen_inversion.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-312.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-312.pyc,,
scipy/stats/tests/common_tests.py,sha256=ahg4xq4lUSsJVlpPZOI9ub8AePgNbKtrGDqbijtwzRE,12639
scipy/stats/tests/data/__pycache__/_mvt.cpython-312.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-312.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=FhbZqSVqMei58-awIxMScB_gSDj_1Fy-RKRkJwuBBns,7076
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=AqKIctwYj-6FUc6XhwZIfrPCEw073oMKhSWrDhuz00I,27956
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=WhCDyQYtOWbyRL_6N2_JsDMkSr-GoNDdAWrznNcfuKc,3171
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=Wk8C1_JW4Yww3K0bgeAmC7gQtawRP2EH4G3uFo0HZj4,2032
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=rSjKwNT7ef_qJSK1U0kIO96hDxKm-MRs-6vMzbTWuJY,6304
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=cI4y6vNnaavnUz59zRttxnCRtg35ON03xPDmVmDvews,48430
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=4YQE0GKLeGcFgLIwo6O2zZl9lZ0A-Soog6TalCsZN3o,469635
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=EwjrFYHyjObJ3kypUg_Mcp-RbDTx-d7hD7bmWB5bTOc,7064
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=exMz5uVs7XQUUJbfd0z7H2MFITyyj5qCIsQYyoXQwpY,55668
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=DA8Jot8F8unbUySmqu7YVkdlHodOn1w4krjyn8SiAPc,541674
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=_ZXFEKqLPOf2fd0yhGzOrX2auuRBeQM2xvsPywrjVY8,7630
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=h9043__rZdOdvj6LpzvRZkT8OWwarQP8ApUiXzumrvk,61097
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=9GiMWg1WkPyG-ifgxaJl7vCyRkHnSvJ6py2WFQhN0T8,595702
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=WraQbGipxLLrFmHAzX0qCfWkaabU4FtraR8ZD197lE8,2688
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=ZIARw6MmSmQkPpmLKGurnF_WxcZqQxUJmgoVsooTJIU,30737
scipy/stats/tests/test_axis_nan_policy.py,sha256=88tWUvaxUIB4ZSte-ehI7K2zsPbKXivw_AEVBkp9mEY,51519
scipy/stats/tests/test_binned_statistic.py,sha256=Zx8gdkQGNDoF6gUXLbi3PJdXsN-4ZmVrB_4HnCcYYYY,19382
scipy/stats/tests/test_boost_ufuncs.py,sha256=APd62wRVk3ipERc5RlOW-z0Nyx4hn9vwI1QCQoAvYbg,1872
scipy/stats/tests/test_censored_data.py,sha256=LtTBWL6HY-0m7HzbZkYDLO1NH3Z0h_D7I2XZ27CwuWY,7087
scipy/stats/tests/test_contingency.py,sha256=IXGuId-yDBKkt3aYl-Qj16Ojl_hFKQ0NEkLjfFCzlZ8,7947
scipy/stats/tests/test_continuous_basic.py,sha256=IU8OIsdMbfYKW9z_7LbPl0zgoSkaNuExmyscb5dlK_s,42809
scipy/stats/tests/test_continuous_fit_censored.py,sha256=PtKzuvJPtFTF__pr209v-i34pcYOXSCB1vMfz8Uez3M,24871
scipy/stats/tests/test_crosstab.py,sha256=8NEB6tQ-kVkPYhGvW_nH0WQbWzTruHGWzlrZdTyXPcc,3997
scipy/stats/tests/test_discrete_basic.py,sha256=C1y26uGHIIhMX5a_PHjOp0U1Z0n6jLY5gSa7w1BqQPs,20470
scipy/stats/tests/test_discrete_distns.py,sha256=xONJZhMIyKpcxyvCSN_T1IG_uSu3nxeRUJ_zwbmWEug,23313
scipy/stats/tests/test_distributions.py,sha256=z76KYlaHxhca-8Tu8WmUWlo3nswX7A8zBulhbaTNYvI,388217
scipy/stats/tests/test_entropy.py,sha256=8WzBCUvPd0SHijC0pyNnK7b3RcjvSi93sJrnV2Wv4EA,11567
scipy/stats/tests/test_fast_gen_inversion.py,sha256=XX3949wQHNSieAp5sP26ANcFcRSVu2TWgVHzggNOU3E,16319
scipy/stats/tests/test_fit.py,sha256=ibd6lbb5lZWhpZ4q-4FUdWqhwAyE1wNK0gfRTbgV10c,44696
scipy/stats/tests/test_hypotests.py,sha256=9fKgi9BvxrAGOt2XSpcqTcFtnTtUIbEGOq0dYpuW3JI,80116
scipy/stats/tests/test_kdeoth.py,sha256=ctUd9YlnageW1tUrlZKbCfJVXVqeul_IiNokEnF9Jmg,21078
scipy/stats/tests/test_morestats.py,sha256=gNqNN1tUkcORJ8x9DyimM0Ji4gUM1BvvQwiDqV7cXaU,126571
scipy/stats/tests/test_mstats_basic.py,sha256=kU3OpNOwt21-aM89lysf1hbWsOY4CK6giX8ZlJwLCTk,87864
scipy/stats/tests/test_mstats_extras.py,sha256=ae8Qn_-iLAI70UaMDbl_r78eeX2xVShuNrRXa81jMxA,7469
scipy/stats/tests/test_multicomp.py,sha256=zabamhTMhUvYbxWS7vHQEPXuFJv6FAg5czBhVVYbchs,18230
scipy/stats/tests/test_multivariate.py,sha256=zne6CGX2dfqjXRbDbatfiDk3fYntsbOVg58x4RVHdk4,157172
scipy/stats/tests/test_odds_ratio.py,sha256=J5A_AiTlmnMM4ccKOl2cNrv9oBBRFyI5krfJB1F0GMo,6852
scipy/stats/tests/test_qmc.py,sha256=J4ce-tnYOEDchvb1zge0Xx2LtVkLZNgsqnfIewX8O-4,56055
scipy/stats/tests/test_rank.py,sha256=aDlzdvk1mVxWUUgsr27RvheZ7v7J9hRuGd6o9bo83AQ,11683
scipy/stats/tests/test_relative_risk.py,sha256=_JizDcNuNxqEQ5j9fNqbvhu0Xxa8YkDUlBivVTsykhY,3741
scipy/stats/tests/test_resampling.py,sha256=_FXasPb9WgMBZtr3jTcq2GLjDT71Gwc-qWDKbp-z3s0,72505
scipy/stats/tests/test_sampling.py,sha256=2tvh2caj9CAxAfetG_6Ut3lL00yfyqKTaQkPWqc74mI,55958
scipy/stats/tests/test_sensitivity_analysis.py,sha256=VnSlauhmxG2qbYY8h1b6SlOFD935mLcRVA3Es6xmPpU,10434
scipy/stats/tests/test_stats.py,sha256=b5sxL_G7uiCzNkmM8NGIQnJ4dU3EA90ROwNSG-HD-Tc,360023
scipy/stats/tests/test_survival.py,sha256=bO_oeXtEdpkMDEpralDhflpqVxiEOWwUnuqUcMQ1oTA,22424
scipy/stats/tests/test_tukeylambda_stats.py,sha256=AmqMknbKki17oYdfP5H_IaP6WyuvWRXNyUrWflzGLAE,3316
scipy/stats/tests/test_variation.py,sha256=pCy3hECRpUp_zYuV5oINSLQPmT9lQZcmrIsLr0o_1MU,6451
scipy/version.py,sha256=Im32ir_GCQktnDFAsqJatisG3CPCoqtNXeAmrMkSAKI,276
