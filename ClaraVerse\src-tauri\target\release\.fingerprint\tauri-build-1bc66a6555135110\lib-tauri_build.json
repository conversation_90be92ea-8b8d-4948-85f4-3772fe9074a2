{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3829077726856309774, "path": 15626780726307409701, "deps": [[4450062412064442726, "dirs_next", false, 8424232402311710600], [4899080583175475170, "semver", false, 13532318235394712103], [7468248713591957673, "cargo_toml", false, 6836683122386692569], [8292277814562636972, "tauri_utils", false, 6722458540902675132], [9689903380558560274, "serde", false, 11963765274265209295], [10301936376833819828, "json_patch", false, 12489595107858618062], [13077543566650298139, "heck", false, 14698631188825528352], [13625485746686963219, "anyhow", false, 6590538292527312679], [14189313126492979171, "tauri_winres", false, 1630095755937422513], [15367738274754116744, "serde_json", false, 9218750329480346621], [15622660310229662834, "walkdir", false, 15536933968146704417]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-1bc66a6555135110\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}