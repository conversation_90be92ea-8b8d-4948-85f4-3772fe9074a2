pywinpty-2.0.10.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pywinpty-2.0.10.dist-info/METADATA,sha256=uzdZmLLlXgqFJAzx1mPq2VbRj0OOy5ClabumKeWFEKE,5038
pywinpty-2.0.10.dist-info/RECORD,,
pywinpty-2.0.10.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pywinpty-2.0.10.dist-info/WHEEL,sha256=Ps2uXWS72XpjC9Xxsnp9OAo_EIzfUJTHR8Pq6WwGWk4,96
pywinpty-2.0.10.dist-info/direct_url.json,sha256=cmYdUqcQQcR2ELle-57-bp2-7u8wXblo6Ak969TaFxM,346
winpty/__init__.py,sha256=cSkFJBjTYWVg7OJZX32I5brecexEYr9OZIny2yJ-HGg,379
winpty/__pycache__/__init__.cpython-312.pyc,,
winpty/__pycache__/enums.cpython-312.pyc,,
winpty/__pycache__/ptyprocess.cpython-312.pyc,,
winpty/enums.py,sha256=L2TNXhGA2LobsCpTx1Tm9C7Cjg-9O5n9eGOC_rhjJRY,1805
winpty/ptyprocess.py,sha256=xnbp8nOLf1-S-fRFfkLlh8vU_wyu0jjRi7LHIwefCGo,11634
winpty/tests/__init__.py,sha256=gWIECeJo4H3OW_fCU9d_IH7KRjEBIW55o-j6-mz9cCw,51
winpty/tests/__pycache__/__init__.cpython-312.pyc,,
winpty/tests/__pycache__/test_pty.cpython-312.pyc,,
winpty/tests/__pycache__/test_ptyprocess.cpython-312.pyc,,
winpty/tests/test_pty.py,sha256=pv7VdeNQbhOW1JtsWNbxYsxvhsKzwjG-V2TQtgpLByQ,3566
winpty/tests/test_ptyprocess.py,sha256=t2vIIiyNTCGb0RXwqAmj0xjvjJdTdG3iRmP9MB46JCg,5655
winpty/winpty.cp312-win_amd64.pyd,sha256=BOjH5nq7dASgM4PB0fkfoOXw5B-RYPbyBO0C9lJuwto,518144
winpty/winpty.pyi,sha256=XRZjNuVWPxA6Bd0_DWr0AH2x02UC6NfyMtZSykMnl4A,1366
