{"version": 3, "sources": ["../../react-player/lib/Preview.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Preview_exports = {};\n__export(Preview_exports, {\n  default: () => Preview\n});\nmodule.exports = __toCommonJS(Preview_exports);\nvar import_react = __toESM(require(\"react\"));\nconst ICON_SIZE = \"64px\";\nconst cache = {};\nclass Preview extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"mounted\", false);\n    __publicField(this, \"state\", {\n      image: null\n    });\n    __publicField(this, \"handleKeyPress\", (e) => {\n      if (e.key === \"Enter\" || e.key === \" \") {\n        this.props.onClick();\n      }\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    this.fetchImage(this.props);\n  }\n  componentDidUpdate(prevProps) {\n    const { url, light } = this.props;\n    if (prevProps.url !== url || prevProps.light !== light) {\n      this.fetchImage(this.props);\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n  }\n  fetchImage({ url, light, oEmbedUrl }) {\n    if (import_react.default.isValidElement(light)) {\n      return;\n    }\n    if (typeof light === \"string\") {\n      this.setState({ image: light });\n      return;\n    }\n    if (cache[url]) {\n      this.setState({ image: cache[url] });\n      return;\n    }\n    this.setState({ image: null });\n    return window.fetch(oEmbedUrl.replace(\"{url}\", url)).then((response) => response.json()).then((data) => {\n      if (data.thumbnail_url && this.mounted) {\n        const image = data.thumbnail_url.replace(\"height=100\", \"height=480\").replace(\"-d_295x166\", \"-d_640\");\n        this.setState({ image });\n        cache[url] = image;\n      }\n    });\n  }\n  render() {\n    const { light, onClick, playIcon, previewTabIndex, previewAriaLabel } = this.props;\n    const { image } = this.state;\n    const isElement = import_react.default.isValidElement(light);\n    const flexCenter = {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\"\n    };\n    const styles = {\n      preview: {\n        width: \"100%\",\n        height: \"100%\",\n        backgroundImage: image && !isElement ? `url(${image})` : void 0,\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        cursor: \"pointer\",\n        ...flexCenter\n      },\n      shadow: {\n        background: \"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)\",\n        borderRadius: ICON_SIZE,\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        position: isElement ? \"absolute\" : void 0,\n        ...flexCenter\n      },\n      playIcon: {\n        borderStyle: \"solid\",\n        borderWidth: \"16px 0 16px 26px\",\n        borderColor: \"transparent transparent transparent white\",\n        marginLeft: \"7px\"\n      }\n    };\n    const defaultPlayIcon = /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.shadow, className: \"react-player__shadow\" }, /* @__PURE__ */ import_react.default.createElement(\"div\", { style: styles.playIcon, className: \"react-player__play-icon\" }));\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        style: styles.preview,\n        className: \"react-player__preview\",\n        onClick,\n        tabIndex: previewTabIndex,\n        onKeyPress: this.handleKeyPress,\n        ...previewAriaLabel ? { \"aria-label\": previewAriaLabel } : {}\n      },\n      isElement ? light : null,\n      playIcon || defaultPlayIcon\n    );\n  }\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,CAAC;AACvB,aAAS,iBAAiB;AAAA,MACxB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,eAAe;AAC7C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAM,YAAY;AAClB,QAAM,QAAQ,CAAC;AACf,QAAM,UAAN,cAAsB,aAAa,UAAU;AAAA,MAC3C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,WAAW,KAAK;AACpC,sBAAc,MAAM,SAAS;AAAA,UAC3B,OAAO;AAAA,QACT,CAAC;AACD,sBAAc,MAAM,kBAAkB,CAAC,MAAM;AAC3C,cAAI,EAAE,QAAQ,WAAW,EAAE,QAAQ,KAAK;AACtC,iBAAK,MAAM,QAAQ;AAAA,UACrB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,UAAU;AACf,aAAK,WAAW,KAAK,KAAK;AAAA,MAC5B;AAAA,MACA,mBAAmB,WAAW;AAC5B,cAAM,EAAE,KAAK,MAAM,IAAI,KAAK;AAC5B,YAAI,UAAU,QAAQ,OAAO,UAAU,UAAU,OAAO;AACtD,eAAK,WAAW,KAAK,KAAK;AAAA,QAC5B;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,WAAW,EAAE,KAAK,OAAO,UAAU,GAAG;AACpC,YAAI,aAAa,QAAQ,eAAe,KAAK,GAAG;AAC9C;AAAA,QACF;AACA,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,SAAS,EAAE,OAAO,MAAM,CAAC;AAC9B;AAAA,QACF;AACA,YAAI,MAAM,GAAG,GAAG;AACd,eAAK,SAAS,EAAE,OAAO,MAAM,GAAG,EAAE,CAAC;AACnC;AAAA,QACF;AACA,aAAK,SAAS,EAAE,OAAO,KAAK,CAAC;AAC7B,eAAO,OAAO,MAAM,UAAU,QAAQ,SAAS,GAAG,CAAC,EAAE,KAAK,CAAC,aAAa,SAAS,KAAK,CAAC,EAAE,KAAK,CAAC,SAAS;AACtG,cAAI,KAAK,iBAAiB,KAAK,SAAS;AACtC,kBAAM,QAAQ,KAAK,cAAc,QAAQ,cAAc,YAAY,EAAE,QAAQ,cAAc,QAAQ;AACnG,iBAAK,SAAS,EAAE,MAAM,CAAC;AACvB,kBAAM,GAAG,IAAI;AAAA,UACf;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS;AACP,cAAM,EAAE,OAAO,SAAS,UAAU,iBAAiB,iBAAiB,IAAI,KAAK;AAC7E,cAAM,EAAE,MAAM,IAAI,KAAK;AACvB,cAAM,YAAY,aAAa,QAAQ,eAAe,KAAK;AAC3D,cAAM,aAAa;AAAA,UACjB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,gBAAgB;AAAA,QAClB;AACA,cAAM,SAAS;AAAA,UACb,SAAS;AAAA,YACP,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,iBAAiB,SAAS,CAAC,YAAY,OAAO,KAAK,MAAM;AAAA,YACzD,gBAAgB;AAAA,YAChB,oBAAoB;AAAA,YACpB,QAAQ;AAAA,YACR,GAAG;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,YACN,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,UAAU,YAAY,aAAa;AAAA,YACnC,GAAG;AAAA,UACL;AAAA,UACA,UAAU;AAAA,YACR,aAAa;AAAA,YACb,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AACA,cAAM,kBAAkC,aAAa,QAAQ,cAAc,OAAO,EAAE,OAAO,OAAO,QAAQ,WAAW,uBAAuB,GAAmB,aAAa,QAAQ,cAAc,OAAO,EAAE,OAAO,OAAO,UAAU,WAAW,0BAA0B,CAAC,CAAC;AAC1Q,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,OAAO,OAAO;AAAA,YACd,WAAW;AAAA,YACX;AAAA,YACA,UAAU;AAAA,YACV,YAAY,KAAK;AAAA,YACjB,GAAG,mBAAmB,EAAE,cAAc,iBAAiB,IAAI,CAAC;AAAA,UAC9D;AAAA,UACA,YAAY,QAAQ;AAAA,UACpB,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}