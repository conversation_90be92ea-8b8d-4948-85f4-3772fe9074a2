warc3_wet_clueweb09-0.2.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
warc3_wet_clueweb09-0.2.5.dist-info/LICENSE,sha256=gXf5dRMhNSbfLPYYTY_5hsZ1r7UU1OaKQEAQUhuIBkM,18092
warc3_wet_clueweb09-0.2.5.dist-info/METADATA,sha256=MBbq9I8r-SFFg7k9IcG2KtY7k5dZrh7AlllJ8AgnSjs,2507
warc3_wet_clueweb09-0.2.5.dist-info/RECORD,,
warc3_wet_clueweb09-0.2.5.dist-info/WHEEL,sha256=GV9aMThwP_4oNCtvEC2ec3qUYutgWeAzklro_0m4WJQ,91
warc3_wet_clueweb09-0.2.5.dist-info/top_level.txt,sha256=XrxFtpk416qYU1pZFpodD39Pcxt8eDsraSYIPDJ4bos,20
warc3_wet_clueweb09/__init__.py,sha256=AO19mPZ0gWpNsMuuEsaGJDc0ETCvSb65_5E-qe59_1s,1010
warc3_wet_clueweb09/__pycache__/__init__.cpython-312.pyc,,
warc3_wet_clueweb09/__pycache__/arc.cpython-312.pyc,,
warc3_wet_clueweb09/__pycache__/utils.cpython-312.pyc,,
warc3_wet_clueweb09/__pycache__/warc.cpython-312.pyc,,
warc3_wet_clueweb09/arc.py,sha256=PjJTNTShhD61aODbGRDvi_s-Y-DXuS7qq3PQNvuEQCI,15679
warc3_wet_clueweb09/utils.py,sha256=CQyVfqY0BtH0BiFarU-UcWCvMOpF81mJ4vtExO6bu3o,2519
warc3_wet_clueweb09/warc.py,sha256=d7mSD0gbdhXO8qIAtKv62mPt1gJpJTveswJtJFojoDM,12547
