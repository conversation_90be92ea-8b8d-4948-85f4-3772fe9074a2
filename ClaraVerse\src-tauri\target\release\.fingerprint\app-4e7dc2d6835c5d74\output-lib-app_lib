{"$message_type":"diagnostic","message":"unused variable: `app`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":708,"byte_end":711,"line_start":26,"line_end":26,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"    .setup(|app| {","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":708,"byte_end":711,"line_start":26,"line_end":26,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"    .setup(|app| {","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":"_app","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:26:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    .setup(|app| {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused `Result` that must be used","code":{"code":"unused_must_use","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2350,"byte_end":2422,"line_start":59,"line_end":59,"column_start":11,"column_end":83,"is_primary":true,"text":[{"text":"          copy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));","highlight_start":11,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this `Result` may be an `Err` variant, which should be handled","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_must_use)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `let _ = ...` to ignore the resulting value","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2350,"byte_end":2350,"line_start":59,"line_end":59,"column_start":11,"column_end":11,"is_primary":true,"text":[{"text":"          copy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));","highlight_start":11,"highlight_end":11}],"label":null,"suggested_replacement":"let _ = ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused `Result` that must be used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:59:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m          copy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this `Result` may be an `Err` variant, which should be handled\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_must_use)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `let _ = ...` to ignore the resulting value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10mlet _ = \u001b[0m\u001b[0mcopy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[38;5;10m+++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused `Result` that must be used","code":{"code":"unused_must_use","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2645,"byte_end":2700,"line_start":65,"line_end":65,"column_start":11,"column_end":66,"is_primary":true,"text":[{"text":"          copy_dir_all(&backend_dir_source, &backend_dir_install);","highlight_start":11,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this `Result` may be an `Err` variant, which should be handled","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `let _ = ...` to ignore the resulting value","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2645,"byte_end":2645,"line_start":65,"line_end":65,"column_start":11,"column_end":11,"is_primary":true,"text":[{"text":"          copy_dir_all(&backend_dir_source, &backend_dir_install);","highlight_start":11,"highlight_end":11}],"label":null,"suggested_replacement":"let _ = ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused `Result` that must be used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:65:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m          copy_dir_all(&backend_dir_source, &backend_dir_install);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this `Result` may be an `Err` variant, which should be handled\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `let _ = ...` to ignore the resulting value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10mlet _ = \u001b[0m\u001b[0mcopy_dir_all(&backend_dir_source, &backend_dir_install);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[38;5;10m+++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"3 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 3 warnings emitted\u001b[0m\n\n"}
