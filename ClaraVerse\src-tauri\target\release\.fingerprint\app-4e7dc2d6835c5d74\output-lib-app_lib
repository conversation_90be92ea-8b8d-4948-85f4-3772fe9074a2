{"$message_type":"diagnostic","message":"unused `Result` that must be used","code":{"code":"unused_must_use","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2327,"byte_end":2399,"line_start":60,"line_end":60,"column_start":11,"column_end":83,"is_primary":true,"text":[{"text":"          copy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));","highlight_start":11,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this `Result` may be an `Err` variant, which should be handled","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_must_use)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `let _ = ...` to ignore the resulting value","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2327,"byte_end":2327,"line_start":60,"line_end":60,"column_start":11,"column_end":11,"is_primary":true,"text":[{"text":"          copy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));","highlight_start":11,"highlight_end":11}],"label":null,"suggested_replacement":"let _ = ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused `Result` that must be used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:60:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m          copy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this `Result` may be an `Err` variant, which should be handled\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_must_use)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `let _ = ...` to ignore the resulting value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10mlet _ = \u001b[0m\u001b[0mcopy_dir_all(&python_portable_source, &install_dir.join(\"python-embed\"));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[38;5;10m+++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused `Result` that must be used","code":{"code":"unused_must_use","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":2622,"byte_end":2677,"line_start":66,"line_end":66,"column_start":11,"column_end":66,"is_primary":true,"text":[{"text":"          copy_dir_all(&backend_dir_source, &backend_dir_install);","highlight_start":11,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this `Result` may be an `Err` variant, which should be handled","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use `let _ = ...` to ignore the resulting value","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":2622,"byte_end":2622,"line_start":66,"line_end":66,"column_start":11,"column_end":11,"is_primary":true,"text":[{"text":"          copy_dir_all(&backend_dir_source, &backend_dir_install);","highlight_start":11,"highlight_end":11}],"label":null,"suggested_replacement":"let _ = ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused `Result` that must be used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:66:11\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m          copy_dir_all(&backend_dir_source, &backend_dir_install);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this `Result` may be an `Err` variant, which should be handled\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use `let _ = ...` to ignore the resulting value\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[38;5;10mlet _ = \u001b[0m\u001b[0mcopy_dir_all(&backend_dir_source, &backend_dir_install);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[38;5;10m+++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"2 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 2 warnings emitted\u001b[0m\n\n"}
