Metadata-Version: 2.1
Name: pyviz_comms
Version: 3.0.2
Summary: A JupyterLab extension for rendering HoloViz content.
Project-URL: Homepage, https://github.com/holoviz/pyviz_comms
Project-URL: Bug Tracker, https://github.com/holoviz/pyviz_comms/issues
Project-URL: Repository, https://github.com/holoviz/pyviz_comms.git
Author: <PERSON>
Maintainer-email: Holo<PERSON><PERSON> <<EMAIL>>
License: BSD 3-Clause License
        
        Copyright (c) 2023, <PERSON>ger
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        1. Redistributions of source code must retain the above copyright notice, this
           list of conditions and the following disclaimer.
        
        2. Redistributions in binary form must reproduce the above copyright notice,
           this list of conditions and the following disclaimer in the documentation
           and/or other materials provided with the distribution.
        
        3. Neither the name of the copyright holder nor the names of its
           contributors may be used to endorse or promote products derived from
           this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: LICENSE
License-File: LICENSE.txt
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Jupyter
Classifier: Framework :: Jupyter :: JupyterLab
Classifier: Framework :: Jupyter :: JupyterLab :: 4
Classifier: Framework :: Jupyter :: JupyterLab :: Extensions
Classifier: Framework :: Jupyter :: JupyterLab :: Extensions :: Prebuilt
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.8
Requires-Dist: param
Provides-Extra: all
Requires-Dist: pyviz-comms[build]; extra == 'all'
Requires-Dist: pyviz-comms[tests]; extra == 'all'
Provides-Extra: build
Requires-Dist: jupyterlab~=4.0; extra == 'build'
Requires-Dist: keyring; extra == 'build'
Requires-Dist: rfc3986; extra == 'build'
Requires-Dist: setuptools>=40.8.0; extra == 'build'
Requires-Dist: twine; extra == 'build'
Provides-Extra: tests
Requires-Dist: flake8; extra == 'tests'
Requires-Dist: pytest; extra == 'tests'
Description-Content-Type: text/markdown

# pyviz_comms

![Github Actions Status](https://github.com/holoviz/pyviz_comms/workflows/tests/badge.svg)

Offers a simple bidirectional communication architecture between Python and JavaScript,
with support for Jupyter comms in both the classic notebook and Jupyterlab.
Available for use by any [PyViz](https://pyviz.org) tool, but currently primarily used by
[HoloViz](https://holoviz.org) tools.

There are two installable components in this repository: a Python
component used by various HoloViz tools and an extension to enable
Jupyterlab support. For JupyterLab 3.0 and above the extension is automatically
bundled with the `pyviz_comms` Python package.

## Installing the Jupyterlab extension

Jupyterlab users will need to install the Jupyterlab pyviz extension. Starting with JupyterLab 3.0 and above the extension will be automatically installed when installing `pyviz_comms` with `pip` using:

```bash
pip install pyviz_comms
```

or using `conda` with:

```bash
conda install -c pyviz pyviz_comms
```

For older versions of JupyterLab you must separately install:

```bash
jupyter labextension install @pyviz/jupyterlab_pyviz
```

## Compatibility

The [Holoviz](https://github.com/holoviz/holoviz) libraries are generally version independent of
[JupyterLab](https://github.com/jupyterlab/jupyterlab) and the `jupyterlab_pyviz` extension
has been supported since holoviews 1.10.0 and the first release of `pyviz_comms`.

Our goal is that `jupyterlab_pyviz` minor releases (using the [SemVer](https://semver.org/) pattern) are
made to follow JupyterLab minor release bumps and micro releases are for new `jupyterlab_pyviz` features
or bug fix releases. We've been previously inconsistent with having the extension release minor version bumps
track that of JupyterLab, so users seeking to find extension releases that are compatible with their JupyterLab
installation may refer to the below table.

###### Compatible JupyterLab and jupyterlab_pyviz versions

| JupyterLab | jupyterlab_pyviz |
| ---------- | ---------------- |
| 0.33.x     | 0.6.0            |
| 0.34.x     | 0.6.1-0.6.2      |
| 0.35.x     | 0.6.3-0.7.2      |
| 1.0.x      | 0.8.0            |
| 2.0.x      | 0.9.0-1.0.3      |
| 3.x        | 2.0              |
| 4.x        | 3.0              |

## Developing the Jupyterlab extension

Note: You will need NodeJS to build the extension package.

The `jlpm` command is JupyterLab's pinned version of
[yarn](https://yarnpkg.com/) that is installed with JupyterLab. You may use
`yarn` or `npm` in lieu of `jlpm` below.

```bash
# Clone the repo to your local environment
# Change directory to the holoviz_jlab directory
# Install package in development mode
pip install -e .
# Link your development version of the extension with JupyterLab
jupyter labextension develop . --overwrite
# Rebuild extension Typescript source after making changes
jlpm run build
```

You can watch the source directory and run JupyterLab at the same time in different terminals to watch for changes in the extension's source and automatically rebuild the extension.

```bash
# Watch the source directory in one terminal, automatically rebuilding when needed
jlpm run watch
# Run JupyterLab in another terminal
jupyter lab
```

With the watch command running, every saved change will immediately be built locally and available in your running JupyterLab. Refresh JupyterLab to load the change in your browser (you may need to wait several seconds for the extension to be rebuilt).

By default, the `jlpm run build` command generates the source maps for this extension to make it easier to debug using the browser dev tools. To also generate source maps for the JupyterLab core extensions, you can run the following command:

```bash
jupyter lab build --minimize=False
```
