{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 367816849085071872, "path": 12881424638952440647, "deps": [[40386456601120721, "percent_encoding", false, 7658543532785217382], [1200537532907108615, "url<PERSON><PERSON>n", false, 11194595537583971047], [2013030631243296465, "webview2_com", false, 16467297904636269633], [2671782512663819132, "tauri_utils", false, 1822794662215125040], [3150220818285335163, "url", false, 1318321174758569141], [3331586631144870129, "getrandom", false, 16309894506009513157], [4143744114649553716, "raw_window_handle", false, 16995855243923100015], [4494683389616423722, "muda", false, 15623521576002634448], [4919829919303820331, "serialize_to_javascript", false, 2683249948627000106], [5986029879202738730, "log", false, 5444832254770226198], [6089812615193535349, "tauri_runtime", false, 14947029587340180920], [7573826311589115053, "tauri_macros", false, 9775846451047591061], [9010263965687315507, "http", false, 3748586209917174119], [9689903380558560274, "serde", false, 415033232941934989], [10229185211513642314, "mime", false, 5010097230521052798], [10806645703491011684, "thiserror", false, 12697263202037361942], [11599800339996261026, "tauri_runtime_wry", false, 10818786399185144863], [11989259058781683633, "dunce", false, 17489086241525168324], [12393800526703971956, "tokio", false, 6173323844639048725], [12565293087094287914, "window_vibrancy", false, 9414140131252157018], [12986574360607194341, "serde_repr", false, 5258211303631381622], [13077543566650298139, "heck", false, 14587759250149389716], [13625485746686963219, "anyhow", false, 16250987596267273021], [14039947826026167952, "build_script_build", false, 11994938667547854538], [14585479307175734061, "windows", false, 11646464322459264762], [15367738274754116744, "serde_json", false, 16545619236785180510], [16928111194414003569, "dirs", false, 13055430869499980826], [17155886227862585100, "glob", false, 2701769016738312214]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-2e945f87fd4c63b7\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}