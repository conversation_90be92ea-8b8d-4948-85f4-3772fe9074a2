{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 367816849085071872, "path": 17672621077091763092, "deps": [[561782849581144631, "html5ever", false, 8349107734330156048], [3150220818285335163, "url", false, 14391494658513742578], [3334271191048661305, "windows_version", false, 11266095270351896108], [4071963112282141418, "serde_with", false, 3062292842772900353], [4899080583175475170, "semver", false, 9223161908353579748], [5986029879202738730, "log", false, 14925098686675124098], [6262254372177975231, "kuchiki", false, 7924878561596614354], [6606131838865521726, "ctor", false, 5310961280411030913], [6997837210367702832, "infer", false, 17414074194583914647], [8008191657135824715, "thiserror", false, 6747650114457504119], [9689903380558560274, "serde", false, 2694757531789484273], [10301936376833819828, "json_patch", false, 10135169521998350613], [11989259058781683633, "dunce", false, 17489086241525168324], [14132538657330703225, "brotli", false, 9943647706141296511], [15367738274754116744, "serde_json", false, 5354422923480748742], [15622660310229662834, "walkdir", false, 12074916792272278136], [15932120279885307830, "memchr", false, 13287116547952953302], [17155886227862585100, "glob", false, 2701769016738312214], [17186037756130803222, "phf", false, 7631885309361243117]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-93090c1b428e0f42\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}