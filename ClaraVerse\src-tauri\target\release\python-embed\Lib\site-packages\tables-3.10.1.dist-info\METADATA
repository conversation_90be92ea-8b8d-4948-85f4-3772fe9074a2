Metadata-Version: 2.1
Name: tables
Version: 3.10.1
Summary: Hierarchical datasets for Python
Author: <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al.
Author-email: <EMAIL>
Maintainer-email: PyTables maintainers <<EMAIL>>
License: BSD 3-Clause License
Project-URL: homepage, http://www.pytables.org
Project-URL: documentation, http://www.pytables.org
Project-URL: repository, https://github.com/PyTables/PyTables
Project-URL: changelog, http://www.pytables.org/release_notes.html
Project-URL: tracker, https://github.com/PyTables/PyTables/issues
Keywords: hdf5
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Database
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Requires-Dist: numpy >=1.20.0
Requires-Dist: numexpr >=2.6.2
Requires-Dist: packaging
Requires-Dist: py-cpuinfo
Requires-Dist: typing-extensions >=4.4.0
Provides-Extra: doc
Requires-Dist: sphinx <6,>=1.1 ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme ; extra == 'doc'
Requires-Dist: numpydoc ; extra == 'doc'
Requires-Dist: ipython ; extra == 'doc'

PyTables is a package for managing hierarchical datasets and
designed to efficiently cope with extremely large amounts of
data. PyTables is built on top of the HDF5 library and the
NumPy package and features an object-oriented interface
that, combined with C-code generated from Cython sources,
makes of it a fast, yet extremely easy to use tool for
interactively save and retrieve large amounts of data.
