<?if $(sys.B<PERSON><PERSON>AR<PERSON>)="x86"?>
    <?define Win64 = "no" ?>
    <?define PlatformProgramFilesFolder = "ProgramFilesFolder" ?>
<?elseif $(sys.BUILDARCH)="x64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?elseif $(sys.BUILDARCH)="arm64"?>
    <?define Win64 = "yes" ?>
    <?define PlatformProgramFilesFolder = "ProgramFiles64Folder" ?>
<?else?>
    <?error Unsupported value of sys.BUILDARCH=$(sys.BUILDARCH)?>
<?endif?>

<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Product
            Id="*"
            Name="WeMa IA"
            UpgradeCode="7adcaf1a-334a-54b2-baee-a32f3344ceea"
            Language="!(loc.TauriLanguage)"
            Manufacturer="wema-ia"
            Version="0.1.2">

        <Package Id="*"
                 Keywords="Installer"
                 InstallerVersion="450"
                 Languages="0"
                 Compressed="yes"
                 InstallScope="perMachine"
                 SummaryCodepage="!(loc.TauriCodepage)"/>

        <!-- https://docs.microsoft.com/en-us/windows/win32/msi/reinstallmode -->
        <!-- reinstall all files; rewrite all registry entries; reinstall all shortcuts -->
        <Property Id="REINSTALLMODE" Value="amus" />

        <!-- Auto launch app after installation, useful for passive mode which usually used in updates -->
        <Property Id="AUTOLAUNCHAPP" Secure="yes" />
        <!-- Property to forward cli args to the launched app to not lose those of the pre-update instance -->
        <Property Id="LAUNCHAPPARGS" Secure="yes" />

            <MajorUpgrade Schedule="afterInstallInitialize" AllowDowngrades="yes" />

        <InstallExecuteSequence>
            <RemoveShortcuts>Installed AND NOT UPGRADINGPRODUCTCODE</RemoveShortcuts>
        </InstallExecuteSequence>

        <Media Id="1" Cabinet="app.cab" EmbedCab="yes" />


        <Icon Id="ProductIcon" SourceFile="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\resources\icon.ico"/>
        <Property Id="ARPPRODUCTICON" Value="ProductIcon" />
        <Property Id="ARPNOREPAIR" Value="yes" Secure="yes" />      <!-- Remove repair -->
        <SetProperty Id="ARPNOMODIFY" Value="1" After="InstallValidate" Sequence="execute"/>


        <Property Id="INSTALLDIR">
          <!-- First attempt: Search for "InstallDir" -->
          <RegistrySearch Id="PrevInstallDirWithName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="InstallDir" Type="raw" />

          <!-- Second attempt: If the first fails, search for the default key value (this is how the nsis installer currently stores the path) -->
          <RegistrySearch Id="PrevInstallDirNoName" Root="HKCU" Key="Software\wema-ia\WeMa IA" Type="raw" />
        </Property>

        <!-- launch app checkbox -->
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT" Value="!(loc.LaunchApp)" />
        <Property Id="WIXUI_EXITDIALOGOPTIONALCHECKBOX" Value="1"/>
        <CustomAction Id="LaunchApplication" Impersonate="yes" FileKey="Path" ExeCommand="[LAUNCHAPPARGS]" Return="asyncNoWait" />

        <UI>
            <!-- launch app checkbox -->
            <Publish Dialog="ExitDialog" Control="Finish" Event="DoAction" Value="LaunchApplication">WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</Publish>

            <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />

            <!-- Skip license dialog -->
            <Publish Dialog="WelcomeDlg"
                     Control="Next"
                     Event="NewDialog"
                     Value="InstallDirDlg"
                     Order="2">1</Publish>
            <Publish Dialog="InstallDirDlg"
                     Control="Back"
                     Event="NewDialog"
                     Value="WelcomeDlg"
                     Order="2">1</Publish>
        </UI>

        <UIRef Id="WixUI_InstallDir" />

        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="DesktopFolder" Name="Desktop">
                <Component Id="ApplicationShortcutDesktop" Guid="*">
                    <Shortcut Id="ApplicationDesktopShortcut" Name="WeMa IA" Description="Runs WeMa IA" Target="[!Path]" WorkingDirectory="INSTALLDIR" />
                    <RemoveFolder Id="DesktopFolder" On="uninstall" />
                    <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Desktop Shortcut" Type="integer" Value="1" KeyPath="yes" />
                </Component>
            </Directory>
            <Directory Id="$(var.PlatformProgramFilesFolder)" Name="PFiles">
                <Directory Id="INSTALLDIR" Name="WeMa IA"/>
            </Directory>
            <Directory Id="ProgramMenuFolder">
                <Directory Id="ApplicationProgramsFolder" Name="WeMa IA"/>
            </Directory>
        </Directory>

        <DirectoryRef Id="INSTALLDIR">
            <Component Id="RegistryEntries" Guid="*">
                <RegistryKey Root="HKCU" Key="Software\wema-ia\WeMa IA">
                    <RegistryValue Name="InstallDir" Type="string" Value="[INSTALLDIR]" KeyPath="yes" />
                </RegistryKey>
                <!-- Change the Root to HKCU for perUser installations -->
</Component>
            <Component Id="Path" Guid="3b783432-6e5b-5f62-a8d8-17ab766c4f33" Win64="$(var.Win64)">
                <File Id="Path" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\target\release\app.exe" KeyPath="yes" Checksum="yes"/>
</Component>
            <Directory Id="I7bbddc47a2014d1384eca10da7a2d63e" Name="_up_"><Directory Id="If6408941b5d140428c168e6c442034ef" Name="py_backend"><Component Id="I7dd8283780dc4b71b2f1b1d8599f9e10" Guid="94284183-8d76-46d3-b3eb-28d1b20474ac" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I7dd8283780dc4b71b2f1b1d8599f9e10" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env" /></Component><Component Id="I8aa2a02571d74eccaceeac7351006c8e" Guid="9373dccf-0b13-4a55-9be7-5c6ba2fd0af3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I8aa2a02571d74eccaceeac7351006c8e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\.env.template" /></Component><Component Id="I47e8a5f254d04a6bab14527d6b3db4af" Guid="9cf7a5bf-0d2d-48bc-a9bd-d02ac6fd0107" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I47e8a5f254d04a6bab14527d6b3db4af" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Dockerfile" /></Component><Component Id="I0edcd68428cd403ab43022f53cf54029" Guid="55a1110e-8c2f-4061-b15a-f93833c38b9d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I0edcd68428cd403ab43022f53cf54029" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Speech2Text.py" /></Component><Component Id="I6da1481f754348e0b5cd62a0b6555558" Guid="*************-4d4b-a281-a19960aeb879" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6da1481f754348e0b5cd62a0b6555558" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\TTS_README.md" /></Component><Component Id="I6d2b1123d3f14f64a4a9a3ed57422e1c" Guid="4c2e880e-af39-431e-9e08-ca8f948ec9b9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6d2b1123d3f14f64a4a9a3ed57422e1c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\Text2Speech.py" /></Component><Component Id="I4a232e5abdaf4c54b95308251c12ab8c" Guid="d9b06aad-13ac-4d15-a742-285e902b5cc3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I4a232e5abdaf4c54b95308251c12ab8c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\auth_service.py" /></Component><Component Id="I50972df01a384e468e12445ae5e79705" Guid="a7448680-f9fd-47fa-b2af-11f1294934a8" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I50972df01a384e468e12445ae5e79705" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\clara_backend.db" /></Component><Component Id="I088e848664bd4976bc9baea0b9e13474" Guid="671ccb29-049c-47ab-8158-5c35e7b25831" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I088e848664bd4976bc9baea0b9e13474" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\compression_routes.py" /></Component><Component Id="Ib14f9fdf93c14bb1985ccea5f6d6e527" Guid="3c661a38-8d52-4cad-a9bb-2bdbedde4106" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib14f9fdf93c14bb1985ccea5f6d6e527" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\diffusers_api.py" /></Component><Component Id="I2b05018a8ca545db869f3c84794ac333" Guid="7ade73a8-3658-4fc9-9007-bcfd4b1e630e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2b05018a8ca545db869f3c84794ac333" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\documents.db" /></Component><Component Id="Id11f7af544fd4ae1a29a7d76cc56b1fc" Guid="eb8c0906-01e1-447d-b77d-82e59e038753" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id11f7af544fd4ae1a29a7d76cc56b1fc" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\install_kokoro.py" /></Component><Component Id="Ied5383a77f654a4890cad9dd5933c2ed" Guid="7442973d-3cde-4b2a-865d-3bf8285ebebf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ied5383a77f654a4890cad9dd5933c2ed" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\langfuse_service.py" /></Component><Component Id="Ie5cd13ceee034873826f18b6e111875f" Guid="037e0db1-f234-4c19-84bf-9d1cd0259ee2" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ie5cd13ceee034873826f18b6e111875f" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\main.py" /></Component><Component Id="Ia3e31bd2a841488c8766750b4e4afca7" Guid="88c59bba-20a1-47c7-a26e-e3c7ad4c5c95" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia3e31bd2a841488c8766750b4e4afca7" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\network_config.py" /></Component><Component Id="Ib165dbe5f3dd474f825884cb2e2f1e82" Guid="33fc0957-ee4f-4b00-b018-dc437d3edce9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib165dbe5f3dd474f825884cb2e2f1e82" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_processor_premium.py" /></Component><Component Id="Ib5d04809bdf74fbda4ecf3721cf07e0a" Guid="ddff1dfc-7b26-494f-923c-f9ab1973b349" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib5d04809bdf74fbda4ecf3721cf07e0a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\ocr_service.py" /></Component><Component Id="Iddfe73586da74534b7d12a7aaed4901a" Guid="a3f4478a-c37f-4ecf-b2c3-cf23c2b57e0d" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Iddfe73586da74534b7d12a7aaed4901a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\perfect_compressor.py" /></Component><Component Id="I003b21f06c5840b4bfad6fcf70f2399d" Guid="444cfca4-9199-43f3-bc0e-3944788e8567" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I003b21f06c5840b4bfad6fcf70f2399d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\requirements.txt" /></Component><Component Id="I75d4ccc994f24690ad93fa082e001f81" Guid="e18108d5-7d77-4534-b4fa-c87f189087b6" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I75d4ccc994f24690ad93fa082e001f81" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\start_server.py" /></Component><Component Id="I6d7f1ea91d884be681baf4f97c8e6743" Guid="fab9f665-31cd-4617-8686-41ba08861789" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6d7f1ea91d884be681baf4f97c8e6743" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_compression_tool.py" /></Component><Component Id="If7235c407aa34adfaa7ddd419b699f97" Guid="3a126ee1-f984-4973-ba9f-88a5a6a370f9" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_If7235c407aa34adfaa7ddd419b699f97" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_non_streaming.py" /></Component><Component Id="I1fb17992ed1f4a6caae3a8dc0fdb618a" Guid="98093651-6ee3-42c7-88d7-dbb0bb9a6339" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1fb17992ed1f4a6caae3a8dc0fdb618a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_optimizations.py" /></Component><Component Id="I23d33c1c858249238b3c26284b70b992" Guid="c1221cb4-326e-4de4-b0e9-53430ac4535e" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I23d33c1c858249238b3c26284b70b992" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_streaming_optimized.py" /></Component><Directory Id="I6bb0bdb88c5d4339bc3a442185baa64d" Name="__pycache__"><Component Id="I99e9ec556bc54b6ca4e0290c8b9f2241" Guid="3c28e299-de50-43b0-b5e8-a14e511f8531" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I99e9ec556bc54b6ca4e0290c8b9f2241" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\langfuse_service.cpython-312.pyc" /></Component><Component Id="I471702613a1e4a8bb7b12fbeda01d779" Guid="af7895fe-c83c-4632-b2e3-d53a92091f44" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I471702613a1e4a8bb7b12fbeda01d779" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\main.cpython-312.pyc" /></Component><Component Id="Ib50ad4138ce149cfaa99c34b5b1cfec0" Guid="c5495111-1d7a-42f9-998b-0ade3e297247" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ib50ad4138ce149cfaa99c34b5b1cfec0" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\network_config.cpython-312.pyc" /></Component><Component Id="I1005c4cf638d406188b0936e9ba78727" Guid="97707e4f-e8af-4c70-943b-6e4ad93d9325" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1005c4cf638d406188b0936e9ba78727" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_processor_premium.cpython-312.pyc" /></Component><Component Id="I2d584ecf233843b29c5732b8bbb219b1" Guid="c824cc71-94a1-4a83-b3a7-e873f19532bb" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I2d584ecf233843b29c5732b8bbb219b1" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\ocr_service.cpython-312.pyc" /></Component><Component Id="I10525f55391c45609803c36591fbdf7a" Guid="dbcfbd17-c182-4e08-b3ac-eae12839dbc3" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I10525f55391c45609803c36591fbdf7a" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\perfect_compressor.cpython-312.pyc" /></Component><Component Id="I818186aa655d4ba5a88d6f0ec51beb3c" Guid="8033abba-837e-420c-b6be-4af76fd0fecf" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I818186aa655d4ba5a88d6f0ec51beb3c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\__pycache__\rag_premium_service.cpython-312.pyc" /></Component></Directory><Directory Id="I4e4f67b1b3e546c5a8ad0fcb18936d0a" Name="qdrant_storage"><Component Id="Idb9796721f8449aba340fbb56716602d" Guid="8c9d327c-5771-42b2-978d-2fe72215faf4" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Idb9796721f8449aba340fbb56716602d" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\.lock" /></Component><Component Id="I1aee3f3a6f7644b7a3317513ef0fc37e" Guid="01b783c3-958e-4a7e-ad59-b265ef33550f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1aee3f3a6f7644b7a3317513ef0fc37e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\qdrant_storage\meta.json" /></Component></Directory><Directory Id="I6db430c70d73448e897f6d302e074858" Name="services"><Component Id="I97bc7ad829704796a92a08ebc65be8b3" Guid="58b722c5-fae4-4a8a-97af-aecb235eb186" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I97bc7ad829704796a92a08ebc65be8b3" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\internet_search_service.py" /></Component><Component Id="Ia209f62aa7fb4984af8cd4fb686fbf9e" Guid="a7d41283-3def-4438-b231-d43bc022665a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ia209f62aa7fb4984af8cd4fb686fbf9e" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\startup_service.py" /></Component><Component Id="I6e0de14487ce4721b5e0672ba62e0632" Guid="5321f295-0ba0-4ca3-9c9b-1d0fe955570a" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I6e0de14487ce4721b5e0672ba62e0632" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\web_scraper_service.py" /></Component><Directory Id="Ie16518754e48465cb8d631310a4e9168" Name="__pycache__"><Component Id="Ic08200135bb847f59a9d24fef5b0e63c" Guid="cd23a4a0-fbbf-46a6-9834-2e58893ac0e1" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ic08200135bb847f59a9d24fef5b0e63c" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\internet_search_service.cpython-312.pyc" /></Component><Component Id="I1fcea3afc0db4e81907f4823f3b36419" Guid="f27c5342-d10c-42dc-a3d7-a171cdb179fa" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I1fcea3afc0db4e81907f4823f3b36419" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\startup_service.cpython-312.pyc" /></Component><Component Id="Ibe76db5adf3247729dce4ad34f8866ac" Guid="f24bc05f-53c7-497f-9722-45a4439ac08f" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Ibe76db5adf3247729dce4ad34f8866ac" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\services\__pycache__\web_scraper_service.cpython-312.pyc" /></Component></Directory></Directory><Directory Id="I9b19c702270e4bbe9a0d41f7ac082935" Name="test_qdrant_temp"><Component Id="I37ba43d88db94af08a4f09c4406649ed" Guid="17eec2d3-f0ee-4b32-b471-35279b772180" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I37ba43d88db94af08a4f09c4406649ed" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\.lock" /></Component><Directory Id="I440bfa68b3a04ea4962011c8607e9704" Name="collection"><Directory Id="I3fcffca7056643eaaf387c8384ec3084" Name="test_collection"><Component Id="I5535b4398930449a9315bf71ff3ce12b" Guid="acf5dc01-450a-4fce-8d23-ef6dd4e127ed" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I5535b4398930449a9315bf71ff3ce12b" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\test_qdrant_temp\collection\test_collection\storage.sqlite" /></Component></Directory></Directory></Directory><Directory Id="I579cc01039ee449bafe58b33646a24ec" Name="tools"><Component Id="I230175d1c2114570856b73f761e97d93" Guid="ca477f4e-a424-4d6e-8567-890e373fda70" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_I230175d1c2114570856b73f761e97d93" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\internet_search_tools.py" /></Component><Directory Id="If22b2d9d54a54f1898c9bcf644ece3d2" Name="__pycache__"><Component Id="Id200805211a345cb852739b9fe92cd81" Guid="2d6991b6-5c7f-4f35-af5e-62428ded5219" Win64="$(var.Win64)" KeyPath="yes"><File Id="PathFile_Id200805211a345cb852739b9fe92cd81" Source="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\..\py_backend\tools\__pycache__\internet_search_tools.cpython-312.pyc" /></Component></Directory></Directory></Directory></Directory>
            <Component Id="CMP_UninstallShortcut" Guid="*">

                <Shortcut Id="UninstallShortcut"
						  Name="Uninstall WeMa IA"
						  Description="Uninstalls WeMa IA"
						  Target="[System64Folder]msiexec.exe"
						  Arguments="/x [ProductCode]" />

				<RemoveFolder Id="INSTALLDIR"
							  On="uninstall" />

				<RegistryValue Root="HKCU"
							   Key="Software\wema-ia\WeMa IA"
							   Name="Uninstaller Shortcut"
							   Type="integer"
							   Value="1"
							   KeyPath="yes" />
            </Component>
        </DirectoryRef>

        <DirectoryRef Id="ApplicationProgramsFolder">
            <Component Id="ApplicationShortcut" Guid="*">
                <Shortcut Id="ApplicationStartMenuShortcut"
                    Name="WeMa IA"
                    Description="Runs WeMa IA"
                    Target="[!Path]"
                    Icon="ProductIcon"
                    WorkingDirectory="INSTALLDIR">
                    <ShortcutProperty Key="System.AppUserModel.ID" Value="com.wema-ia.app"/>
                </Shortcut>
                <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall"/>
                <RegistryValue Root="HKCU" Key="Software\wema-ia\WeMa IA" Name="Start Menu Shortcut" Type="integer" Value="1" KeyPath="yes"/>
           </Component>
        </DirectoryRef>

<Feature
                Id="MainProgram"
                Title="Application"
                Description="!(loc.InstallAppFeature)"
                Level="1"
                ConfigurableDirectory="INSTALLDIR"
                AllowAdvertise="no"
                Display="expand"
                Absent="disallow">

            <ComponentRef Id="RegistryEntries"/>

<ComponentRef Id="I7dd8283780dc4b71b2f1b1d8599f9e10"/>
<ComponentRef Id="I8aa2a02571d74eccaceeac7351006c8e"/>
<ComponentRef Id="I47e8a5f254d04a6bab14527d6b3db4af"/>
<ComponentRef Id="I0edcd68428cd403ab43022f53cf54029"/>
<ComponentRef Id="I6da1481f754348e0b5cd62a0b6555558"/>
<ComponentRef Id="I6d2b1123d3f14f64a4a9a3ed57422e1c"/>
<ComponentRef Id="I4a232e5abdaf4c54b95308251c12ab8c"/>
<ComponentRef Id="I50972df01a384e468e12445ae5e79705"/>
<ComponentRef Id="I088e848664bd4976bc9baea0b9e13474"/>
<ComponentRef Id="Ib14f9fdf93c14bb1985ccea5f6d6e527"/>
<ComponentRef Id="I2b05018a8ca545db869f3c84794ac333"/>
<ComponentRef Id="Id11f7af544fd4ae1a29a7d76cc56b1fc"/>
<ComponentRef Id="Ied5383a77f654a4890cad9dd5933c2ed"/>
<ComponentRef Id="Ie5cd13ceee034873826f18b6e111875f"/>
<ComponentRef Id="Ia3e31bd2a841488c8766750b4e4afca7"/>
<ComponentRef Id="Ib165dbe5f3dd474f825884cb2e2f1e82"/>
<ComponentRef Id="Ib5d04809bdf74fbda4ecf3721cf07e0a"/>
<ComponentRef Id="Iddfe73586da74534b7d12a7aaed4901a"/>
<ComponentRef Id="I003b21f06c5840b4bfad6fcf70f2399d"/>
<ComponentRef Id="I75d4ccc994f24690ad93fa082e001f81"/>
<ComponentRef Id="I6d7f1ea91d884be681baf4f97c8e6743"/>
<ComponentRef Id="If7235c407aa34adfaa7ddd419b699f97"/>
<ComponentRef Id="I1fb17992ed1f4a6caae3a8dc0fdb618a"/>
<ComponentRef Id="I23d33c1c858249238b3c26284b70b992"/>
<ComponentRef Id="I99e9ec556bc54b6ca4e0290c8b9f2241"/>
<ComponentRef Id="I471702613a1e4a8bb7b12fbeda01d779"/>
<ComponentRef Id="Ib50ad4138ce149cfaa99c34b5b1cfec0"/>
<ComponentRef Id="I1005c4cf638d406188b0936e9ba78727"/>
<ComponentRef Id="I2d584ecf233843b29c5732b8bbb219b1"/>
<ComponentRef Id="I10525f55391c45609803c36591fbdf7a"/>
<ComponentRef Id="I818186aa655d4ba5a88d6f0ec51beb3c"/>
<ComponentRef Id="Idb9796721f8449aba340fbb56716602d"/>
<ComponentRef Id="I1aee3f3a6f7644b7a3317513ef0fc37e"/>
<ComponentRef Id="I97bc7ad829704796a92a08ebc65be8b3"/>
<ComponentRef Id="Ia209f62aa7fb4984af8cd4fb686fbf9e"/>
<ComponentRef Id="I6e0de14487ce4721b5e0672ba62e0632"/>
<ComponentRef Id="Ic08200135bb847f59a9d24fef5b0e63c"/>
<ComponentRef Id="I1fcea3afc0db4e81907f4823f3b36419"/>
<ComponentRef Id="Ibe76db5adf3247729dce4ad34f8866ac"/>
<ComponentRef Id="I37ba43d88db94af08a4f09c4406649ed"/>
<ComponentRef Id="I5535b4398930449a9315bf71ff3ce12b"/>
<ComponentRef Id="I230175d1c2114570856b73f761e97d93"/>
<ComponentRef Id="Id200805211a345cb852739b9fe92cd81"/>

            <Feature Id="ShortcutsFeature"
                Title="Shortcuts"
                Level="1">
                <ComponentRef Id="Path"/>
                <ComponentRef Id="CMP_UninstallShortcut" />
                <ComponentRef Id="ApplicationShortcut" />
                <ComponentRef Id="ApplicationShortcutDesktop" />
            </Feature>

            <Feature
                Id="Environment"
                Title="PATH Environment Variable"
                Description="!(loc.PathEnvVarFeature)"
                Level="1"
                Absent="allow">
            <ComponentRef Id="Path"/>
</Feature>
        </Feature>

        <Feature Id="External" AllowAdvertise="no" Absent="disallow">
</Feature>

        <!-- WebView2 -->
        <Property Id="WVRTINSTALLED">
            <RegistrySearch Id="WVRTInstalledSystem" Root="HKLM" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw" Win64="no" />
            <RegistrySearch Id="WVRTInstalledUser" Root="HKCU" Key="SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}" Name="pv" Type="raw"/>
        </Property>

        <CustomAction Id='DownloadAndInvokeBootstrapper' Directory="INSTALLDIR" Execute="deferred" ExeCommand='powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList (&apos;/silent&apos;, &apos;/install&apos;) -Wait' Return='check'/>
        <InstallExecuteSequence>
            <Custom Action='DownloadAndInvokeBootstrapper' Before='InstallFinalize'>
                <![CDATA[NOT(REMOVE OR WVRTINSTALLED)]]>
            </Custom>
        </InstallExecuteSequence>

        <!-- Embedded webview bootstrapper mode -->

        <!-- Embedded offline installer -->



        <InstallExecuteSequence>
          <Custom Action="LaunchApplication" After="InstallFinalize">AUTOLAUNCHAPP AND NOT Installed</Custom>
        </InstallExecuteSequence>

        <SetProperty Id="ARPINSTALLLOCATION" Value="[INSTALLDIR]" After="CostFinalize"/>
    </Product>
</Wix>
