Metadata-Version: 2.1
Name: uc-micro-py
Version: 1.0.1
Summary: Micro subset of unicode data files for linkify-it-py projects.
Home-page: https://github.com/tsutsu3/uc.micro-py
Author: tsutsu3
License: MIT
Keywords: unicode
Classifier: Development Status :: 5 - Production/Stable
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
Provides-Extra: test
Requires-Dist: coverage ; extra == 'test'
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'

# uc.micro-py

[![pypi](https://img.shields.io/pypi/v/uc-micro-py)](https://pypi.org/project/uc-micro-py/)
[![CI](https://github.com/tsutsu3/uc.micro-py/workflows/CI/badge.svg?branch=main)](https://github.com/tsutsu3/uc.micro-py/actions)
[![codecov](https://codecov.io/gh/tsutsu3/uc.micro-py/branch/main/graph/badge.svg?token=5Y7559D69U)](https://codecov.io/gh/tsutsu3/uc.micro-py)

This is a Python port of [uc.micro](https://github.com/markdown-it/uc.micro).

> Micro subset of unicode data files for [linkify-it-py](https://github.com/tsutsu3/linkify-it-py) projects.

**This package content is ONLY for [linkify-it-py](https://github.com/tsutsu3/linkify-it-py)projects needs.**

## install

```bash
pip install uc-micro-py
```
