#!/usr/bin/env python3
"""
Test de démarrage pour diagnostiquer les problèmes
"""

import sys
import os
import traceback

print("🚀 Test de démarrage WeMa IA Backend")
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current directory: {os.getcwd()}")

# Test des imports critiques
try:
    print("📦 Test import FastAPI...")
    from fastapi import FastAPI
    print("✅ FastAPI OK")
except Exception as e:
    print(f"❌ FastAPI ERREUR: {e}")
    traceback.print_exc()

try:
    print("📦 Test import uvicorn...")
    import uvicorn
    print("✅ uvicorn OK")
except Exception as e:
    print(f"❌ uvicorn ERREUR: {e}")
    traceback.print_exc()

try:
    print("📦 Test import sqlite3...")
    import sqlite3
    print("✅ sqlite3 OK")
except Exception as e:
    print(f"❌ sqlite3 ERREUR: {e}")
    traceback.print_exc()

try:
    print("📦 Test import OCR Premium...")
    from ocr_processor_premium import PremiumOcrProcessor
    print("✅ OCR Premium OK")
except Exception as e:
    print(f"❌ OCR Premium ERREUR: {e}")
    traceback.print_exc()

print("🎉 Test terminé - appuyez sur Entrée pour fermer")
input()
