{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 12900267964365444870, "path": 7005242700553962961, "deps": [[1615478164327904835, "pin_utils", false, 16689589079445700955], [1906322745568073236, "pin_project_lite", false, 13981041783533823058], [5451793922601807560, "slab", false, 4216433059520112891], [7620660491849607393, "futures_core", false, 12919646655898606666], [10565019901765856648, "futures_macro", false, 2365994716543717497], [16240732885093539806, "futures_task", false, 9952693014591665618]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-f55eca192980912c\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}