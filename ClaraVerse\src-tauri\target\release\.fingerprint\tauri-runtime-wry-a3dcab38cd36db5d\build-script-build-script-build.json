{"rustc": 16591470773350601817, "features": "[\"arboard\", \"clipboard\", \"global-shortcut\", \"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 5408242616063297496, "profile": 17984201634715228204, "path": 6281441542096271579, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-a3dcab38cd36db5d\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}