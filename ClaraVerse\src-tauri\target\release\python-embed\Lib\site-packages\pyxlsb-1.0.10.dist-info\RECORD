pyxlsb-1.0.10.dist-info/COPYING,sha256=jOtLnuWt7d5Hsx6XXB2QxzrSe2sWWh3NgMfFRetluQM,35147
pyxlsb-1.0.10.dist-info/COPYING.LESSER,sha256=2n6rt7r999OuXp8iOqW9we7ORaxWncIbOwN1ILRGR2g,7651
pyxlsb-1.0.10.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyxlsb-1.0.10.dist-info/METADATA,sha256=CdHFeRRHGX08NTquPhlpxcE4Nmw5gOVz2pndmu_FWf4,2460
pyxlsb-1.0.10.dist-info/RECORD,,
pyxlsb-1.0.10.dist-info/WHEEL,sha256=z9j0xAa_JmUKMpmz72K0ZGALSM_n-wQVmGbleXx2VHg,110
pyxlsb-1.0.10.dist-info/top_level.txt,sha256=190Zrl5NAJ7uMC9PxqKa8BN8BV_R74KQ8kQaALY1XNE,7
pyxlsb/__init__.py,sha256=Y55i2Wru5a8TeoF4_nLmfu-cfuj3oO_0E4M9ohWAc2M,1007
pyxlsb/__pycache__/__init__.cpython-312.pyc,,
pyxlsb/__pycache__/biff12.cpython-312.pyc,,
pyxlsb/__pycache__/handlers.cpython-312.pyc,,
pyxlsb/__pycache__/reader.cpython-312.pyc,,
pyxlsb/__pycache__/stringtable.cpython-312.pyc,,
pyxlsb/__pycache__/workbook.cpython-312.pyc,,
pyxlsb/__pycache__/worksheet.cpython-312.pyc,,
pyxlsb/biff12.py,sha256=iWgnioJHGocSh2ePeNF07P9B2jta26zjyhsM6Bee5IU,4251
pyxlsb/handlers.py,sha256=72WHQrDcmiSE2yfkL05C9AULnZC3MQmD93eT-0VIAb8,3711
pyxlsb/reader.py,sha256=4zDqX3yVcpCZl-1swxWiSUBCOV5A3tILDP6OwexQQCM,4793
pyxlsb/stringtable.py,sha256=IuCFr3xSjmq76ItUoiyPI-JOQCK5-tB1gQl1bOqFB8E,672
pyxlsb/workbook.py,sha256=7XpplIlmGz45rW9c2FqHVz4lkFx1dbNiqzDZ7WJomdc,2510
pyxlsb/worksheet.py,sha256=8VdP8xwPq1xzGyhK-i8alfkemaMmapOeqsuQvAWfQX0,2605
