{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/postiats/postiats.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/postiats/postiats.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  tokenPostfix: \".pats\",\n  // TODO: staload and dynload are followed by a special kind of string literals\n  // with {$IDENTIFER} variables, and it also may make sense to highlight\n  // the punctuation (. and / and \\) differently.\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  defaultToken: \"invalid\",\n  // keyword reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing_token.dats\n  keywords: [\n    //\n    \"abstype\",\n    // ABSTYPE\n    \"abst0ype\",\n    // ABST0YPE\n    \"absprop\",\n    // ABSPROP\n    \"absview\",\n    // ABSVIEW\n    \"absvtype\",\n    // ABSVIEWTYPE\n    \"absviewtype\",\n    // ABSVIEWTYPE\n    \"absvt0ype\",\n    // ABSVIEWT0YPE\n    \"absviewt0ype\",\n    // ABSVIEWT0YPE\n    //\n    \"as\",\n    // T_AS\n    //\n    \"and\",\n    // T_AND\n    //\n    \"assume\",\n    // T_ASSUME\n    //\n    \"begin\",\n    // T_BEGIN\n    //\n    /*\n    \t\t\"case\", // CASE\n    */\n    //\n    \"classdec\",\n    // T_CLASSDEC\n    //\n    \"datasort\",\n    // T_DATASORT\n    //\n    \"datatype\",\n    // DATATYPE\n    \"dataprop\",\n    // DATAPROP\n    \"dataview\",\n    // DATAVIEW\n    \"datavtype\",\n    // DATAVIEWTYPE\n    \"dataviewtype\",\n    // DATAVIEWTYPE\n    //\n    \"do\",\n    // T_DO\n    //\n    \"end\",\n    // T_END\n    //\n    \"extern\",\n    // T_EXTERN\n    \"extype\",\n    // T_EXTYPE\n    \"extvar\",\n    // T_EXTVAR\n    //\n    \"exception\",\n    // T_EXCEPTION\n    //\n    \"fn\",\n    // FN // non-recursive\n    \"fnx\",\n    // FNX // mutual tail-rec.\n    \"fun\",\n    // FUN // general-recursive\n    //\n    \"prfn\",\n    // PRFN\n    \"prfun\",\n    // PRFUN\n    //\n    \"praxi\",\n    // PRAXI\n    \"castfn\",\n    // CASTFN\n    //\n    \"if\",\n    // T_IF\n    \"then\",\n    // T_THEN\n    \"else\",\n    // T_ELSE\n    //\n    \"ifcase\",\n    // T_IFCASE\n    //\n    \"in\",\n    // T_IN\n    //\n    \"infix\",\n    // INFIX\n    \"infixl\",\n    // INFIXL\n    \"infixr\",\n    // INFIXR\n    \"prefix\",\n    // PREFIX\n    \"postfix\",\n    // POSTFIX\n    //\n    \"implmnt\",\n    // IMPLMNT // 0\n    \"implement\",\n    // IMPLEMENT // 1\n    //\n    \"primplmnt\",\n    // PRIMPLMNT // ~1\n    \"primplement\",\n    // PRIMPLMNT // ~1\n    //\n    \"import\",\n    // T_IMPORT // for importing packages\n    //\n    /*\n    \t\t\"lam\", // LAM\n    \t\t\"llam\", // LLAM\n    \t\t\"fix\", // FIX\n    */\n    //\n    \"let\",\n    // T_LET\n    //\n    \"local\",\n    // T_LOCAL\n    //\n    \"macdef\",\n    // MACDEF\n    \"macrodef\",\n    // MACRODEF\n    //\n    \"nonfix\",\n    // T_NONFIX\n    //\n    \"symelim\",\n    // T_SYMELIM\n    \"symintr\",\n    // T_SYMINTR\n    \"overload\",\n    // T_OVERLOAD\n    //\n    \"of\",\n    // T_OF\n    \"op\",\n    // T_OP\n    //\n    \"rec\",\n    // T_REC\n    //\n    \"sif\",\n    // T_SIF\n    \"scase\",\n    // T_SCASE\n    //\n    \"sortdef\",\n    // T_SORTDEF\n    /*\n    // HX: [sta] is now deprecated\n    */\n    \"sta\",\n    // T_STACST\n    \"stacst\",\n    // T_STACST\n    \"stadef\",\n    // T_STADEF\n    \"static\",\n    // T_STATIC\n    /*\n    \t\t\"stavar\", // T_STAVAR\n    */\n    //\n    \"staload\",\n    // T_STALOAD\n    \"dynload\",\n    // T_DYNLOAD\n    //\n    \"try\",\n    // T_TRY\n    //\n    \"tkindef\",\n    // T_TKINDEF // HX-2012-05-23\n    //\n    /*\n    \t\t\"type\", // TYPE\n    */\n    \"typedef\",\n    // TYPEDEF\n    \"propdef\",\n    // PROPDEF\n    \"viewdef\",\n    // VIEWDEF\n    \"vtypedef\",\n    // VIEWTYPEDEF\n    \"viewtypedef\",\n    // VIEWTYPEDEF\n    //\n    /*\n    \t\t\"val\", // VAL\n    */\n    \"prval\",\n    // PRVAL\n    //\n    \"var\",\n    // VAR\n    \"prvar\",\n    // PRVAR\n    //\n    \"when\",\n    // T_WHEN\n    \"where\",\n    // T_WHERE\n    //\n    /*\n    \t\t\"for\", // T_FOR\n    \t\t\"while\", // T_WHILE\n    */\n    //\n    \"with\",\n    // T_WITH\n    //\n    \"withtype\",\n    // WITHTYPE\n    \"withprop\",\n    // WITHPROP\n    \"withview\",\n    // WITHVIEW\n    \"withvtype\",\n    // WITHVIEWTYPE\n    \"withviewtype\"\n    // WITHVIEWTYPE\n    //\n  ],\n  keywords_dlr: [\n    \"$delay\",\n    // DLRDELAY\n    \"$ldelay\",\n    // DLRLDELAY\n    //\n    \"$arrpsz\",\n    // T_DLRARRPSZ\n    \"$arrptrsize\",\n    // T_DLRARRPSZ\n    //\n    \"$d2ctype\",\n    // T_DLRD2CTYPE\n    //\n    \"$effmask\",\n    // DLREFFMASK\n    \"$effmask_ntm\",\n    // DLREFFMASK_NTM\n    \"$effmask_exn\",\n    // DLREFFMASK_EXN\n    \"$effmask_ref\",\n    // DLREFFMASK_REF\n    \"$effmask_wrt\",\n    // DLREFFMASK_WRT\n    \"$effmask_all\",\n    // DLREFFMASK_ALL\n    //\n    \"$extern\",\n    // T_DLREXTERN\n    \"$extkind\",\n    // T_DLREXTKIND\n    \"$extype\",\n    // T_DLREXTYPE\n    \"$extype_struct\",\n    // T_DLREXTYPE_STRUCT\n    //\n    \"$extval\",\n    // T_DLREXTVAL\n    \"$extfcall\",\n    // T_DLREXTFCALL\n    \"$extmcall\",\n    // T_DLREXTMCALL\n    //\n    \"$literal\",\n    // T_DLRLITERAL\n    //\n    \"$myfilename\",\n    // T_DLRMYFILENAME\n    \"$mylocation\",\n    // T_DLRMYLOCATION\n    \"$myfunction\",\n    // T_DLRMYFUNCTION\n    //\n    \"$lst\",\n    // DLRLST\n    \"$lst_t\",\n    // DLRLST_T\n    \"$lst_vt\",\n    // DLRLST_VT\n    \"$list\",\n    // DLRLST\n    \"$list_t\",\n    // DLRLST_T\n    \"$list_vt\",\n    // DLRLST_VT\n    //\n    \"$rec\",\n    // DLRREC\n    \"$rec_t\",\n    // DLRREC_T\n    \"$rec_vt\",\n    // DLRREC_VT\n    \"$record\",\n    // DLRREC\n    \"$record_t\",\n    // DLRREC_T\n    \"$record_vt\",\n    // DLRREC_VT\n    //\n    \"$tup\",\n    // DLRTUP\n    \"$tup_t\",\n    // DLRTUP_T\n    \"$tup_vt\",\n    // DLRTUP_VT\n    \"$tuple\",\n    // DLRTUP\n    \"$tuple_t\",\n    // DLRTUP_T\n    \"$tuple_vt\",\n    // DLRTUP_VT\n    //\n    \"$break\",\n    // T_DLRBREAK\n    \"$continue\",\n    // T_DLRCONTINUE\n    //\n    \"$raise\",\n    // T_DLRRAISE\n    //\n    \"$showtype\",\n    // T_DLRSHOWTYPE\n    //\n    \"$vcopyenv_v\",\n    // DLRVCOPYENV_V\n    \"$vcopyenv_vt\",\n    // DLRVCOPYENV_VT\n    //\n    \"$tempenver\",\n    // T_DLRTEMPENVER\n    //\n    \"$solver_assert\",\n    // T_DLRSOLASSERT\n    \"$solver_verify\"\n    // T_DLRSOLVERIFY\n  ],\n  keywords_srp: [\n    //\n    \"#if\",\n    // T_SRPIF\n    \"#ifdef\",\n    // T_SRPIFDEF\n    \"#ifndef\",\n    // T_SRPIFNDEF\n    //\n    \"#then\",\n    // T_SRPTHEN\n    //\n    \"#elif\",\n    // T_SRPELIF\n    \"#elifdef\",\n    // T_SRPELIFDEF\n    \"#elifndef\",\n    // T_SRPELIFNDEF\n    //\n    \"#else\",\n    // T_SRPELSE\n    \"#endif\",\n    // T_SRPENDIF\n    //\n    \"#error\",\n    // T_SRPERROR\n    //\n    \"#prerr\",\n    // T_SRPPRERR // outpui to stderr\n    \"#print\",\n    // T_SRPPRINT // output to stdout\n    //\n    \"#assert\",\n    // T_SRPASSERT\n    //\n    \"#undef\",\n    // T_SRPUNDEF\n    \"#define\",\n    // T_SRPDEFINE\n    //\n    \"#include\",\n    // T_SRPINCLUDE\n    \"#require\",\n    // T_SRPREQUIRE\n    //\n    \"#pragma\",\n    // T_SRPPRAGMA // HX: general pragma\n    \"#codegen2\",\n    // T_SRPCODEGEN2 // for level-2 codegen\n    \"#codegen3\"\n    // T_SRPCODEGEN3 // for level-3 codegen\n    //\n    // HX: end of special tokens\n    //\n  ],\n  irregular_keyword_list: [\n    \"val+\",\n    \"val-\",\n    \"val\",\n    \"case+\",\n    \"case-\",\n    \"case\",\n    \"addr@\",\n    \"addr\",\n    \"fold@\",\n    \"free@\",\n    \"fix@\",\n    \"fix\",\n    \"lam@\",\n    \"lam\",\n    \"llam@\",\n    \"llam\",\n    \"viewt@ype+\",\n    \"viewt@ype-\",\n    \"viewt@ype\",\n    \"viewtype+\",\n    \"viewtype-\",\n    \"viewtype\",\n    \"view+\",\n    \"view-\",\n    \"view@\",\n    \"view\",\n    \"type+\",\n    \"type-\",\n    \"type\",\n    \"vtype+\",\n    \"vtype-\",\n    \"vtype\",\n    \"vt@ype+\",\n    \"vt@ype-\",\n    \"vt@ype\",\n    \"viewt@ype+\",\n    \"viewt@ype-\",\n    \"viewt@ype\",\n    \"viewtype+\",\n    \"viewtype-\",\n    \"viewtype\",\n    \"prop+\",\n    \"prop-\",\n    \"prop\",\n    \"type+\",\n    \"type-\",\n    \"type\",\n    \"t@ype\",\n    \"t@ype+\",\n    \"t@ype-\",\n    \"abst@ype\",\n    \"abstype\",\n    \"absviewt@ype\",\n    \"absvt@ype\",\n    \"for*\",\n    \"for\",\n    \"while*\",\n    \"while\"\n  ],\n  keywords_types: [\n    \"bool\",\n    \"double\",\n    \"byte\",\n    \"int\",\n    \"short\",\n    \"char\",\n    \"void\",\n    \"unit\",\n    \"long\",\n    \"float\",\n    \"string\",\n    \"strptr\"\n  ],\n  // TODO: reference for this?\n  keywords_effects: [\n    \"0\",\n    // no effects\n    \"fun\",\n    \"clo\",\n    \"prf\",\n    \"funclo\",\n    \"cloptr\",\n    \"cloref\",\n    \"ref\",\n    \"ntm\",\n    \"1\"\n    // all effects\n  ],\n  operators: [\n    \"@\",\n    // T_AT\n    \"!\",\n    // T_BANG\n    \"|\",\n    // T_BAR\n    \"`\",\n    // T_BQUOTE\n    \":\",\n    // T_COLON\n    \"$\",\n    // T_DOLLAR\n    \".\",\n    // T_DOT\n    \"=\",\n    // T_EQ\n    \"#\",\n    // T_HASH\n    \"~\",\n    // T_TILDE\n    //\n    \"..\",\n    // T_DOTDOT\n    \"...\",\n    // T_DOTDOTDOT\n    //\n    \"=>\",\n    // T_EQGT\n    // \"=<\", // T_EQLT\n    \"=<>\",\n    // T_EQLTGT\n    \"=/=>\",\n    // T_EQSLASHEQGT\n    \"=>>\",\n    // T_EQGTGT\n    \"=/=>>\",\n    // T_EQSLASHEQGTGT\n    //\n    \"<\",\n    // T_LT // opening a tmparg\n    \">\",\n    // T_GT // closing a tmparg\n    //\n    \"><\",\n    // T_GTLT\n    //\n    \".<\",\n    // T_DOTLT\n    \">.\",\n    // T_GTDOT\n    //\n    \".<>.\",\n    // T_DOTLTGTDOT\n    //\n    \"->\",\n    // T_MINUSGT\n    //\"-<\", // T_MINUSLT\n    \"-<>\"\n    // T_MINUSLTGT\n    //\n    /*\n    \t\t\":<\", // T_COLONLT\n    */\n  ],\n  brackets: [\n    { open: \",(\", close: \")\", token: \"delimiter.parenthesis\" },\n    // meta-programming syntax\n    { open: \"`(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"%(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"'(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"'{\", close: \"}\", token: \"delimiter.parenthesis\" },\n    { open: \"@(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"@{\", close: \"}\", token: \"delimiter.brace\" },\n    { open: \"@[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"#[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  IDENTFST: /[a-zA-Z_]/,\n  IDENTRST: /[a-zA-Z0-9_'$]/,\n  symbolic: /[%&+-./:=@~`^|*!$#?<>]/,\n  digit: /[0-9]/,\n  digitseq0: /@digit*/,\n  xdigit: /[0-9A-Za-z]/,\n  xdigitseq0: /@xdigit*/,\n  INTSP: /[lLuU]/,\n  FLOATSP: /[fFlL]/,\n  fexponent: /[eE][+-]?[0-9]+/,\n  fexponent_bin: /[pP][+-]?[0-9]+/,\n  deciexp: /\\.[0-9]*@fexponent?/,\n  hexiexp: /\\.[0-9a-zA-Z]*@fexponent_bin?/,\n  irregular_keywords: /val[+-]?|case[+-]?|addr\\@?|fold\\@|free\\@|fix\\@?|lam\\@?|llam\\@?|prop[+-]?|type[+-]?|view[+-@]?|viewt@?ype[+-]?|t@?ype[+-]?|v(iew)?t@?ype[+-]?|abst@?ype|absv(iew)?t@?ype|for\\*?|while\\*?/,\n  ESCHAR: /[ntvbrfa\\\\\\?'\"\\(\\[\\{]/,\n  start: \"root\",\n  // The main tokenizer for ATS/Postiats\n  // reference: https://github.com/githwxi/ATS-Postiats/blob/master/src/pats_lexing.dats\n  tokenizer: {\n    root: [\n      // lexing_blankseq0\n      { regex: /[ \\t\\r\\n]+/, action: { token: \"\" } },\n      // NOTE: (*) is an invalid ML-like comment!\n      { regex: /\\(\\*\\)/, action: { token: \"invalid\" } },\n      {\n        regex: /\\(\\*/,\n        action: { token: \"comment\", next: \"lexing_COMMENT_block_ml\" }\n      },\n      {\n        regex: /\\(/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /\\)/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /\\[/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /\\]/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /\\{/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      {\n        regex: /\\}/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      // lexing_COMMA\n      {\n        regex: /,\\(/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.parenthesis' }*/\n      },\n      // meta-programming syntax\n      { regex: /,/, action: { token: \"delimiter.comma\" } },\n      { regex: /;/, action: { token: \"delimiter.semicolon\" } },\n      // lexing_AT\n      {\n        regex: /@\\(/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.parenthesis' }*/\n      },\n      {\n        regex: /@\\[/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.bracket' }*/\n      },\n      {\n        regex: /@\\{/,\n        action: \"@brackets\"\n        /*{ token: 'delimiter.brace' }*/\n      },\n      // lexing_COLON\n      {\n        regex: /:</,\n        action: { token: \"keyword\", next: \"@lexing_EFFECT_commaseq0\" }\n      },\n      // T_COLONLT\n      /*\n      \t\t\tlexing_DOT:\n      \n      \t\t\t. // SYMBOLIC => lexing_IDENT_sym\n      \t\t\t. FLOATDOT => lexing_FLOAT_deciexp\n      \t\t\t. DIGIT => T_DOTINT\n      \t\t\t*/\n      { regex: /\\.@symbolic+/, action: { token: \"identifier.sym\" } },\n      // FLOATDOT case\n      {\n        regex: /\\.@digit*@fexponent@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      { regex: /\\.@digit+/, action: { token: \"number.float\" } },\n      // T_DOTINT\n      // lexing_DOLLAR:\n      // '$' IDENTFST IDENTRST* => lexing_IDENT_dlr, _ => lexing_IDENT_sym\n      {\n        regex: /\\$@IDENTFST@IDENTRST*/,\n        action: {\n          cases: {\n            \"@keywords_dlr\": { token: \"keyword.dlr\" },\n            \"@default\": { token: \"namespace\" }\n            // most likely a module qualifier\n          }\n        }\n      },\n      // lexing_SHARP:\n      // '#' IDENTFST IDENTRST* => lexing_ident_srp, _ => lexing_IDENT_sym\n      {\n        regex: /\\#@IDENTFST@IDENTRST*/,\n        action: {\n          cases: {\n            \"@keywords_srp\": { token: \"keyword.srp\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      // lexing_PERCENT:\n      { regex: /%\\(/, action: { token: \"delimiter.parenthesis\" } },\n      {\n        regex: /^%{(#|\\^|\\$)?/,\n        action: {\n          token: \"keyword\",\n          next: \"@lexing_EXTCODE\",\n          nextEmbedded: \"text/javascript\"\n        }\n      },\n      { regex: /^%}/, action: { token: \"keyword\" } },\n      // lexing_QUOTE\n      { regex: /'\\(/, action: { token: \"delimiter.parenthesis\" } },\n      { regex: /'\\[/, action: { token: \"delimiter.bracket\" } },\n      { regex: /'\\{/, action: { token: \"delimiter.brace\" } },\n      [/(')(\\\\@ESCHAR|\\\\[xX]@xdigit+|\\\\@digit+)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'[^\\\\']'/, \"string\"],\n      // lexing_DQUOTE\n      [/\"/, \"string.quote\", \"@lexing_DQUOTE\"],\n      // lexing_BQUOTE\n      {\n        regex: /`\\(/,\n        action: \"@brackets\"\n        /* { token: 'delimiter.parenthesis' }*/\n      },\n      // TODO: otherwise, try lexing_IDENT_sym\n      { regex: /\\\\/, action: { token: \"punctuation\" } },\n      // just T_BACKSLASH\n      // lexing_IDENT_alp:\n      // NOTE: (?!regex) is syntax for \"not-followed-by\" regex\n      // to resolve ambiguity such as foreach$fwork being incorrectly lexed as [for] [each$fwork]!\n      {\n        regex: /@irregular_keywords(?!@IDENTRST)/,\n        action: { token: \"keyword\" }\n      },\n      {\n        regex: /@IDENTFST@IDENTRST*[<!\\[]?/,\n        action: {\n          cases: {\n            // TODO: dynload and staload should be specially parsed\n            // dynload whitespace+ \"special_string\"\n            // this special string is really:\n            //  '/' '\\\\' '.' => punctuation\n            // ({\\$)([a-zA-Z_][a-zA-Z_0-9]*)(}) => punctuation,keyword,punctuation\n            // [^\"] => identifier/literal\n            \"@keywords\": { token: \"keyword\" },\n            \"@keywords_types\": { token: \"type\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      // lexing_IDENT_sym:\n      {\n        regex: /\\/\\/\\/\\//,\n        action: { token: \"comment\", next: \"@lexing_COMMENT_rest\" }\n      },\n      { regex: /\\/\\/.*$/, action: { token: \"comment\" } },\n      {\n        regex: /\\/\\*/,\n        action: { token: \"comment\", next: \"@lexing_COMMENT_block_c\" }\n      },\n      // AS-20160627: specifically for effect annotations\n      {\n        regex: /-<|=</,\n        action: { token: \"keyword\", next: \"@lexing_EFFECT_commaseq0\" }\n      },\n      {\n        regex: /@symbolic+/,\n        action: {\n          cases: {\n            \"@operators\": \"keyword\",\n            \"@default\": \"operator\"\n          }\n        }\n      },\n      // lexing_ZERO:\n      // FIXME: this one is quite messy/unfinished yet\n      // TODO: lexing_INT_hex\n      // - testing_hexiexp => lexing_FLOAT_hexiexp\n      // - testing_fexponent_bin => lexing_FLOAT_hexiexp\n      // - testing_intspseq0 => T_INT_hex\n      // lexing_INT_hex:\n      {\n        regex: /0[xX]@xdigit+(@hexiexp|@fexponent_bin)@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      { regex: /0[xX]@xdigit+@INTSP*/, action: { token: \"number.hex\" } },\n      {\n        regex: /0[0-7]+(?![0-9])@INTSP*/,\n        action: { token: \"number.octal\" }\n      },\n      // lexing_INT_oct\n      //{regex: /0/, action: { token: 'number' } }, // INTZERO\n      // lexing_INT_dec:\n      // - testing_deciexp => lexing_FLOAT_deciexp\n      // - testing_fexponent => lexing_FLOAT_deciexp\n      // - otherwise => intspseq0 ([0-9]*[lLuU]?)\n      {\n        regex: /@digit+(@fexponent|@deciexp)@FLOATSP*/,\n        action: { token: \"number.float\" }\n      },\n      {\n        regex: /@digit@digitseq0@INTSP*/,\n        action: { token: \"number.decimal\" }\n      },\n      // DIGIT, if followed by digitseq0, is lexing_INT_dec\n      { regex: /@digit+@INTSP*/, action: { token: \"number\" } }\n    ],\n    lexing_COMMENT_block_ml: [\n      [/[^\\(\\*]+/, \"comment\"],\n      [/\\(\\*/, \"comment\", \"@push\"],\n      [/\\(\\*/, \"comment.invalid\"],\n      [/\\*\\)/, \"comment\", \"@pop\"],\n      [/\\*/, \"comment\"]\n    ],\n    lexing_COMMENT_block_c: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/, 'comment', '@push' ],    // nested C-style block comments not allowed\n      // [/\\/\\*/,    'comment.invalid' ],\t// NOTE: this breaks block comments in the shape of /* //*/\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    lexing_COMMENT_rest: [\n      [/$/, \"comment\", \"@pop\"],\n      // FIXME: does it match? docs say 'no'\n      [/.*/, \"comment\"]\n    ],\n    // NOTE: added by AS, specifically for highlighting\n    lexing_EFFECT_commaseq0: [\n      {\n        regex: /@IDENTFST@IDENTRST+|@digit+/,\n        action: {\n          cases: {\n            \"@keywords_effects\": { token: \"type.effect\" },\n            \"@default\": { token: \"identifier\" }\n          }\n        }\n      },\n      { regex: /,/, action: { token: \"punctuation\" } },\n      { regex: />/, action: { token: \"@rematch\", next: \"@pop\" } }\n    ],\n    lexing_EXTCODE: [\n      {\n        regex: /^%}/,\n        action: {\n          token: \"@rematch\",\n          next: \"@pop\",\n          nextEmbedded: \"@pop\"\n        }\n      },\n      { regex: /[^%]+/, action: \"\" }\n    ],\n    lexing_DQUOTE: [\n      { regex: /\"/, action: { token: \"string.quote\", next: \"@pop\" } },\n      // AS-20160628: additional hi-lighting for variables in staload/dynload strings\n      {\n        regex: /(\\{\\$)(@IDENTFST@IDENTRST*)(\\})/,\n        action: [{ token: \"string.escape\" }, { token: \"identifier\" }, { token: \"string.escape\" }]\n      },\n      { regex: /\\\\$/, action: { token: \"string.escape\" } },\n      {\n        regex: /\\\\(@ESCHAR|[xX]@xdigit+|@digit+)/,\n        action: { token: \"string.escape\" }\n      },\n      { regex: /[^\\\\\"]+/, action: { token: \"string\" } }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA;AAAA,IAER;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,EAGF;AAAA,EACA,cAAc;AAAA,IACZ;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,EAEF;AAAA,EACA,cAAc;AAAA;AAAA,IAEZ;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF;AAAA,EACA,wBAAwB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAAA,IAChB;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EAEF;AAAA,EACA,WAAW;AAAA,IACT;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA,IAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMF;AAAA,EACA,UAAU;AAAA,IACR,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA;AAAA,IAEzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACzD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,kBAAkB;AAAA,IACnD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACpD,EAAE,MAAM,MAAM,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,WAAW;AAAA,EACX,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,oBAAoB;AAAA,EACpB,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA;AAAA,EAGP,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,EAAE,OAAO,cAAc,QAAQ,EAAE,OAAO,GAAG,EAAE;AAAA;AAAA,MAE7C,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,MAChD;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,0BAA0B;AAAA,MAC9D;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA;AAAA,MAEA,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,kBAAkB,EAAE;AAAA,MACnD,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,sBAAsB,EAAE;AAAA;AAAA,MAEvD;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,2BAA2B;AAAA,MAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,EAAE,OAAO,gBAAgB,QAAQ,EAAE,OAAO,iBAAiB,EAAE;AAAA;AAAA,MAE7D;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MAClC;AAAA,MACA,EAAE,OAAO,aAAa,QAAQ,EAAE,OAAO,eAAe,EAAE;AAAA;AAAA;AAAA;AAAA,MAIxD;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,iBAAiB,EAAE,OAAO,cAAc;AAAA,YACxC,YAAY,EAAE,OAAO,YAAY;AAAA;AAAA,UAEnC;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA,MAGA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,iBAAiB,EAAE,OAAO,cAAc;AAAA,YACxC,YAAY,EAAE,OAAO,aAAa;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,wBAAwB,EAAE;AAAA,MAC3D;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA;AAAA,MAE7C,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,wBAAwB,EAAE;AAAA,MAC3D,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,oBAAoB,EAAE;AAAA,MACvD,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,kBAAkB,EAAE;AAAA,MACrD,CAAC,8CAA8C,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MACpF,CAAC,YAAY,QAAQ;AAAA;AAAA,MAErB,CAAC,KAAK,gBAAgB,gBAAgB;AAAA;AAAA,MAEtC;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA;AAAA,MAEV;AAAA;AAAA,MAEA,EAAE,OAAO,MAAM,QAAQ,EAAE,OAAO,cAAc,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,MAKhD;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,UAAU;AAAA,MAC7B;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOL,aAAa,EAAE,OAAO,UAAU;AAAA,YAChC,mBAAmB,EAAE,OAAO,OAAO;AAAA,YACnC,YAAY,EAAE,OAAO,aAAa;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,uBAAuB;AAAA,MAC3D;AAAA,MACA,EAAE,OAAO,WAAW,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,MACjD;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,0BAA0B;AAAA,MAC9D;AAAA;AAAA,MAEA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,WAAW,MAAM,2BAA2B;AAAA,MAC/D;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MAClC;AAAA,MACA,EAAE,OAAO,wBAAwB,QAAQ,EAAE,OAAO,aAAa,EAAE;AAAA,MACjE;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,eAAe;AAAA,MAClC;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,iBAAiB;AAAA,MACpC;AAAA;AAAA,MAEA,EAAE,OAAO,kBAAkB,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,IACzD;AAAA,IACA,yBAAyB;AAAA,MACvB,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,QAAQ,WAAW,OAAO;AAAA,MAC3B,CAAC,QAAQ,iBAAiB;AAAA,MAC1B,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,MAAM,SAAS;AAAA,IAClB;AAAA,IACA,wBAAwB;AAAA,MACtB,CAAC,WAAW,SAAS;AAAA;AAAA;AAAA,MAGrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,KAAK,WAAW,MAAM;AAAA;AAAA,MAEvB,CAAC,MAAM,SAAS;AAAA,IAClB;AAAA;AAAA,IAEA,yBAAyB;AAAA,MACvB;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA,YACL,qBAAqB,EAAE,OAAO,cAAc;AAAA,YAC5C,YAAY,EAAE,OAAO,aAAa;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,cAAc,EAAE;AAAA,MAC/C,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,YAAY,MAAM,OAAO,EAAE;AAAA,IAC5D;AAAA,IACA,gBAAgB;AAAA,MACd;AAAA,QACE,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,OAAO;AAAA,UACP,MAAM;AAAA,UACN,cAAc;AAAA,QAChB;AAAA,MACF;AAAA,MACA,EAAE,OAAO,SAAS,QAAQ,GAAG;AAAA,IAC/B;AAAA,IACA,eAAe;AAAA,MACb,EAAE,OAAO,KAAK,QAAQ,EAAE,OAAO,gBAAgB,MAAM,OAAO,EAAE;AAAA;AAAA,MAE9D;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,CAAC,EAAE,OAAO,gBAAgB,GAAG,EAAE,OAAO,aAAa,GAAG,EAAE,OAAO,gBAAgB,CAAC;AAAA,MAC1F;AAAA,MACA,EAAE,OAAO,OAAO,QAAQ,EAAE,OAAO,gBAAgB,EAAE;AAAA,MACnD;AAAA,QACE,OAAO;AAAA,QACP,QAAQ,EAAE,OAAO,gBAAgB;AAAA,MACnC;AAAA,MACA,EAAE,OAAO,WAAW,QAAQ,EAAE,OAAO,SAAS,EAAE;AAAA,IAClD;AAAA,EACF;AACF;", "names": []}