Metadata-Version: 2.2
Name: tensorboard
Version: 2.19.0
Summary: TensorBoard lets you watch Tensors Flow
Home-page: https://github.com/tensorflow/tensorboard
Author: Google Inc.
Author-email: <EMAIL>
License: Apache 2.0
Keywords: tensorflow tensorboard tensor machine learning visualizer
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.9
License-File: LICENSE
Requires-Dist: absl-py>=0.4
Requires-Dist: grpcio>=1.48.2
Requires-Dist: markdown>=2.6.8
Requires-Dist: numpy>=1.12.0
Requires-Dist: packaging
Requires-Dist: protobuf!=4.24.0,>=3.19.6
Requires-Dist: setuptools>=41.0.0
Requires-Dist: six>1.9
Requires-Dist: tensorboard-data-server<0.8.0,>=0.7.0
Requires-Dist: werkzeug>=1.0.1
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

TensorBoard is a suite of web applications for inspecting and understanding
your TensorFlow runs and graphs.

Releases prior to 1.6.0 were published under the ``tensorflow-tensorboard`` name
and may be found at https://pypi.python.org/pypi/tensorflow-tensorboard.
