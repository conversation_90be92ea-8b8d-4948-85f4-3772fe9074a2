{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 17984201634715228204, "path": 7048512659236156197, "deps": [[3060637413840920116, "proc_macro2", false, 1268470941054599307], [4899080583175475170, "semver", false, 13532318235394712103], [7392050791754369441, "ico", false, 7205394907753893387], [8008191657135824715, "thiserror", false, 14690304464686658702], [8292277814562636972, "tauri_utils", false, 6722458540902675132], [8319709847752024821, "uuid", false, 1291299774086602387], [9451456094439810778, "regex", false, 7755353282736826793], [9689903380558560274, "serde", false, 11963765274265209295], [9857275760291862238, "sha2", false, 15284423489976726817], [10301936376833819828, "json_patch", false, 12489595107858618062], [12687914511023397207, "png", false, 8441074092918439930], [14132538657330703225, "brotli", false, 4495218237123493846], [15367738274754116744, "serde_json", false, 9218750329480346621], [15622660310229662834, "walkdir", false, 15536933968146704417], [17990358020177143287, "quote", false, 10852422733387940731], [18066890886671768183, "base64", false, 17135951520889961857]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-ce2826526a21e80b\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}