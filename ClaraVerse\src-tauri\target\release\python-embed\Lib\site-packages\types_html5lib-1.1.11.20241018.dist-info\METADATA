Metadata-Version: 2.1
Name: types-html5lib
Version: 1.1.11.20241018
Summary: Typing stubs for html5lib
Home-page: https://github.com/python/typeshed
License: Apache-2.0
Project-URL: GitHub, https://github.com/python/typeshed
Project-URL: Changes, https://github.com/typeshed-internal/stub_uploader/blob/main/data/changelogs/html5lib.md
Project-URL: Issue tracker, https://github.com/python/typeshed/issues
Project-URL: Chat, https://gitter.im/python/typing
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Typing :: Stubs Only
Requires-Python: >=3.8
Description-Content-Type: text/markdown

## Typing stubs for html5lib

This is a [PEP 561](https://peps.python.org/pep-0561/)
type stub package for the [`html5lib`](https://github.com/html5lib/html5lib-python) package.
It can be used by type-checking tools like
[mypy](https://github.com/python/mypy/),
[pyright](https://github.com/microsoft/pyright),
[pytype](https://github.com/google/pytype/),
PyCharm, etc. to check code that uses
`html5lib`.

This version of `types-html5lib` aims to provide accurate annotations
for `html5lib==1.1.*`.
The source for this package can be found at
https://github.com/python/typeshed/tree/main/stubs/html5lib. All fixes for
types and metadata should be contributed there.

This stub package is marked as [partial](https://peps.python.org/pep-0561/#partial-stub-packages).
If you find that annotations are missing, feel free to contribute and help complete them.


See https://github.com/python/typeshed/blob/main/README.md for more details.
This package was generated from typeshed commit
[`559ae9730ba3dab1305cdbaf2c29786ff38d740d`](https://github.com/python/typeshed/commit/559ae9730ba3dab1305cdbaf2c29786ff38d740d) and was tested
with mypy 1.11.2, pyright 1.1.385, and
pytype 2024.10.11.
