{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 367816849085071872, "path": 4926901037613790429, "deps": [[1573238666360410412, "rand_chacha", false, 11024883171755745597], [18130209639506977569, "rand_core", false, 5797755251508702302]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rand-53ba4b5ec4ec49c0\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}