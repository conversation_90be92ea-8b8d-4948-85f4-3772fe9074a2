Metadata-Version: 2.1
Name: tencentcloud-sdk-python
Version: 3.0.1336
Summary: Tencent Cloud SDK for Python
Home-page: https://github.com/TencentCloud/tencentcloud-sdk-python
Author: Tencent Cloud
Maintainer-email: tencentclou<PERSON><PERSON>@tencent.com
License: Apache License 2.0
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
License-File: LICENSE
Requires-Dist: requests (>=2.16.0)

============================
Tencent Cloud SDK for Python
============================

Tencent Cloud Python SDK is the official software development kit, which allows Python developers to write software that makes use of Tencent Cloud services like CVM and CBS.

The SDK works on Python versions:

   * 2.7 and greater, including 3.x

Quick Start
-----------
First, install the library:

.. code-block:: sh

    $ pip install tencentcloud-sdk-python

or download source code from github and install:

.. code-block:: sh

    $ git clone https://github.com/tencentcloud/tencentcloud-sdk-python.git
    $ cd tencentcloud-sdk-python
    $ python setup.py install


