
[certs]

[check]
pytest-checkdocs>=2.4

[check:sys_platform != "cygwin"]
pytest-ruff>=0.2.1
ruff>=0.5.2

[core]
packaging>=24
more_itertools>=8.8
jaraco.text>=3.7
wheel>=0.43.0
platformdirs>=2.6.2
jaraco.collections
jaraco.functools
packaging
more_itertools

[core:python_version < "3.10"]
importlib_metadata>=6

[core:python_version < "3.11"]
tomli>=2.0.1

[core:python_version < "3.9"]
importlib_resources>=5.10.2

[cover]
pytest-cov

[doc]
sphinx>=3.5
jaraco.packaging>=9.3
rst.linker>=1.9
furo
sphinx-lint
jaraco.tidelift>=1.4
pygments-github-lexers==0.0.5
sphinx-favicon
sphinx-inline-tabs
sphinx-reredirects
sphinxcontrib-towncrier
sphinx-notfound-page<2,>=1
pyproject-hooks!=1.1
towncrier<24.7

[enabler]
pytest-enabler>=2.2

[ssl]

[test]
pytest!=8.1.*,>=6
virtualenv>=13.0.0
wheel>=0.44.0
pip>=19.1
packaging>=23.2
jaraco.envs>=2.2
pytest-xdist>=3
jaraco.path>=3.2.0
build[virtualenv]>=1.0.3
filelock>=3.4.0
ini2toml[lite]>=0.14
tomli-w>=1.0.0
pytest-timeout
pytest-home>=0.5
pytest-subprocess
pyproject-hooks!=1.1
jaraco.test

[test:python_version >= "3.9" and sys_platform != "cygwin"]
jaraco.develop>=7.21

[test:sys_platform != "cygwin"]
pytest-perf

[type]
pytest-mypy
mypy==1.11.*

[type:python_version < "3.10"]
importlib_metadata>=7.0.2

[type:sys_platform != "cygwin"]
jaraco.develop>=7.21
