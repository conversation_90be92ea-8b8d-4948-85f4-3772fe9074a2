spyder_kernels-2.5.0.dist-info/AUTHORS.txt,sha256=7SPRdBdXvugoBIOHjhXxLxQry_bGxzPwm5Z_oOHvHDQ,891
spyder_kernels-2.5.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spyder_kernels-2.5.0.dist-info/LICENSE.txt,sha256=qZxlkBBsVG64P9DGPt1mQZBcxMp_-Awn6CAO54rOgMs,1103
spyder_kernels-2.5.0.dist-info/METADATA,sha256=Zznt4OuJnsyrLxpV6W2WMiIOcM188jPigoewiIiUw_Y,5600
spyder_kernels-2.5.0.dist-info/RECORD,,
spyder_kernels-2.5.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder_kernels-2.5.0.dist-info/WHEEL,sha256=iYlv5fX357PQyRT2o6tw1bN-YcKFFHKqB_LwHO5wP-g,110
spyder_kernels-2.5.0.dist-info/direct_url.json,sha256=F8aYUzLrKQaSSBAQaiJpc26kmWdCTszYaIPXsO63Us0,94
spyder_kernels-2.5.0.dist-info/top_level.txt,sha256=igyz4IUyhL3rWuD9Cj5Bk4oxglRc-51dxYx4f5hGBY0,15
spyder_kernels/__init__.py,sha256=6El0ia3x-4khdC7XQsL6GyxfRgM2lLxPISh0l1n7ma0,1476
spyder_kernels/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/__pycache__/_version.cpython-312.pyc,,
spyder_kernels/__pycache__/py3compat.cpython-312.pyc,,
spyder_kernels/_version.py,sha256=gHqNrIe_KF_62P2d32ZvTSI-FbDZQwihfUkk6UNNi0U,401
spyder_kernels/comms/__init__.py,sha256=DPIC-ojChQGQ_0QrAY-HvJJHiQAF3ixQYhfDBv-5RWw,1540
spyder_kernels/comms/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/comms/__pycache__/commbase.cpython-312.pyc,,
spyder_kernels/comms/__pycache__/frontendcomm.cpython-312.pyc,,
spyder_kernels/comms/commbase.py,sha256=7LoWoP6UbPjWkT9T_Ym_oKdvCRp9y7RFacRRNQNKvz0,18501
spyder_kernels/comms/frontendcomm.py,sha256=w9wfoYSRrCdDG-LS385cGDhzP03DvlRX_TbSlX1ptjw,11218
spyder_kernels/console/__init__.py,sha256=CBjqUZOWWSE-Bra0xikYnAN6yXfqQg-wEbTE71YM1b4,353
spyder_kernels/console/__main__.py,sha256=k_p8S9J6qyuTcngFdbmT3q3m0-MT5XV1B1AOCgS1z9Q,1023
spyder_kernels/console/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/console/__pycache__/__main__.cpython-312.pyc,,
spyder_kernels/console/__pycache__/kernel.cpython-312.pyc,,
spyder_kernels/console/__pycache__/outstream.cpython-312.pyc,,
spyder_kernels/console/__pycache__/shell.cpython-312.pyc,,
spyder_kernels/console/__pycache__/start.cpython-312.pyc,,
spyder_kernels/console/kernel.py,sha256=EZqmhsqxhoKZ_IN3lqcyK7XdLVa5xIsCQhDOc0VZDoY,30967
spyder_kernels/console/outstream.py,sha256=Q8CaF6KyG1bKRDOgFnj80lpT5L2R7cIFfxJL0tZFjeo,731
spyder_kernels/console/shell.py,sha256=Kl5IDy49rw9JLmdIKy1vLvDxnAAxQT9ZxlHE1U4ZPB8,3389
spyder_kernels/console/start.py,sha256=7j_GtGkyPQtqwOJ1YiAkTl1DVQ0jmtY3BmNIDtXAfDU,10987
spyder_kernels/console/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spyder_kernels/console/tests/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/console/tests/__pycache__/test_console_kernel.cpython-312.pyc,,
spyder_kernels/console/tests/load_data.npz,sha256=Aw1W4s5rR8IRfLbjbSJmGuGrGrpPa0YclBdYif2P1l0,270
spyder_kernels/console/tests/load_data.spydata,sha256=0SoXlA9eMdh0fKBWUWSf_sAVXGFS4cEg1nUBgBS7VJE,10240
spyder_kernels/console/tests/test_console_kernel.py,sha256=pV12jynPORGiGkFDB5OUPO1y7fGyfgn-BoH0vx83UXA,40110
spyder_kernels/customize/__init__.py,sha256=cq_NnziQmmMulDdVmW6ner5F-63SYLA0YI1ddNApliQ,511
spyder_kernels/customize/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/customize/__pycache__/namespace_manager.cpython-312.pyc,,
spyder_kernels/customize/__pycache__/spydercustomize.cpython-312.pyc,,
spyder_kernels/customize/__pycache__/spyderpdb.cpython-312.pyc,,
spyder_kernels/customize/__pycache__/umr.cpython-312.pyc,,
spyder_kernels/customize/__pycache__/utils.cpython-312.pyc,,
spyder_kernels/customize/namespace_manager.py,sha256=jK29qkyll4a8vtR6SKgpcJKrTl3jCRCcZxynapFx5o8,4657
spyder_kernels/customize/spydercustomize.py,sha256=LqVHB1iwQVpXB5kbZhgXV8L9miKmN6JjLFbmH0bvMGY,28522
spyder_kernels/customize/spyderpdb.py,sha256=RVO6d1Wdn57K01Oq9y5m69vTnGlZHvSKcCF-dZMTyhA,35987
spyder_kernels/customize/tests/__init__.py,sha256=YTSO-0bbPEcBVUaZYJkR_HBcfdaCY9Z-_6GUcFVzocA,363
spyder_kernels/customize/tests/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/customize/tests/__pycache__/test_umr.cpython-312.pyc,,
spyder_kernels/customize/tests/__pycache__/test_utils.cpython-312.pyc,,
spyder_kernels/customize/tests/test_umr.py,sha256=VAKTTUe04UVYa2p2-iuh4yd9ikASVII7XUL1RV26LIU,3074
spyder_kernels/customize/tests/test_utils.py,sha256=odMRkHqcDxFQ4Wkj8DcpAv-dRKL_Tb8A5DPYtL-3CHs,820
spyder_kernels/customize/umr.py,sha256=ncZpUzGgqhcv9gbaP7F_1DCZQVIX4IwrBowODFO5nUg,4901
spyder_kernels/customize/utils.py,sha256=T8RxHWowr2CLb9-6YQIz9DKzXwSiZ8zj81pd3CzxCes,4514
spyder_kernels/py3compat.py,sha256=B_KUloJpbEY23OaVcn_RAAqF4JMTBvgRxvM7Tkp52CU,9786
spyder_kernels/utils/__init__.py,sha256=l38K3s6n5dtZstvEWrIEKJhciyGQc6uL03JvSKhIZ9g,348
spyder_kernels/utils/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/utils/__pycache__/dochelpers.cpython-312.pyc,,
spyder_kernels/utils/__pycache__/iofuncs.cpython-312.pyc,,
spyder_kernels/utils/__pycache__/lazymodules.cpython-312.pyc,,
spyder_kernels/utils/__pycache__/misc.cpython-312.pyc,,
spyder_kernels/utils/__pycache__/mpl.cpython-312.pyc,,
spyder_kernels/utils/__pycache__/nsview.cpython-312.pyc,,
spyder_kernels/utils/__pycache__/test_utils.cpython-312.pyc,,
spyder_kernels/utils/dochelpers.py,sha256=0YtbS8hhETAQf8YagQlKyooXAsFlBitixqebTIXUTKU,12666
spyder_kernels/utils/iofuncs.py,sha256=T5tcW6Ky5rPj0fl2aQx3J0sC5SsUhbBUT-UAgtXMcWY,19217
spyder_kernels/utils/lazymodules.py,sha256=j6IXIsMl1cDeHiXoCDdf4qqowAgzmPcEZKXD38Lvlaw,2110
spyder_kernels/utils/misc.py,sha256=52LClnNR7E4RsJrbpEKBMlTSS0J2imKmPvPDx_p-YRI,1579
spyder_kernels/utils/mpl.py,sha256=lMgBhfr3Guh-qJU3kqNvGhf-IgWMl8jHau09s46Xc4A,1351
spyder_kernels/utils/nsview.py,sha256=9F3mbb6lkoxmhJdMVGndQDtfDQUEK5VPNjHB_rrYAd0,25417
spyder_kernels/utils/test_utils.py,sha256=Xh0s1AQn4LgBR_ecjXq-8gZKQ2jEQuNDoHNrcxSMHI0,1425
spyder_kernels/utils/tests/__init__.py,sha256=CWRK_HCO-SBhdl_lHObK2i3Y4ziWC6OVgplH0X3uPEQ,343
spyder_kernels/utils/tests/__pycache__/__init__.cpython-312.pyc,,
spyder_kernels/utils/tests/__pycache__/test_dochelpers.cpython-312.pyc,,
spyder_kernels/utils/tests/__pycache__/test_iofuncs.cpython-312.pyc,,
spyder_kernels/utils/tests/__pycache__/test_lazymodules.cpython-312.pyc,,
spyder_kernels/utils/tests/__pycache__/test_nsview.cpython-312.pyc,,
spyder_kernels/utils/tests/data.mat,sha256=ubfj8shtEm44FOKkrSAdl4BxGPcaHvBxMpiebBjiUyA,482
spyder_kernels/utils/tests/export_data.spydata,sha256=Gcf6-tsjHyeUZR9HLKbIbox4wLX-WG33HnlocN7T1GQ,20480
spyder_kernels/utils/tests/export_data_renamed.spydata,sha256=Gcf6-tsjHyeUZR9HLKbIbox4wLX-WG33HnlocN7T1GQ,20480
spyder_kernels/utils/tests/export_data_withfunction.spydata,sha256=AzhgDVwLKV4SocjX2Ccgpi3rTjXLGS7Fw5Ln_V7hrXI,10240
spyder_kernels/utils/tests/import_data.npz,sha256=Aw1W4s5rR8IRfLbjbSJmGuGrGrpPa0YclBdYif2P1l0,270
spyder_kernels/utils/tests/numpy_data.npz,sha256=S2XtqRBdxCVzmK9LKu-E6KlZnkvgpGiUuyMQmUBO8Ko,1301
spyder_kernels/utils/tests/test_dochelpers.py,sha256=CQOHccw8cYLBAs3BdkaVMwHUnq-KFW39h5Th3Vso7e8,4638
spyder_kernels/utils/tests/test_iofuncs.py,sha256=4jvhIYPNWMuNwYfA5AeIMONloKsJxe1JLsSD_jCQNVc,11525
spyder_kernels/utils/tests/test_lazymodules.py,sha256=yyMW5U88BlOt8xIMByEvBaD1fM8mPuEwmos8lMfrCaw,1257
spyder_kernels/utils/tests/test_nsview.py,sha256=LwBFLHcT2H5bn99Ky94GRGrlguMF1EeJfonpp6Nw7fw,14141
