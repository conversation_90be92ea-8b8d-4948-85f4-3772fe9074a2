webencodings-0.5.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
webencodings-0.5.1.dist-info/LICENSE,sha256=8juuatp2CVYQp3E3-5KuxzQnI5ACEcWCbVS0xXkHylY,1490
webencodings-0.5.1.dist-info/METADATA,sha256=szalLltRBLTDZK37da9Ytrs_z3jHh95gJdmeohDlRD4,2180
webencodings-0.5.1.dist-info/RECORD,,
webencodings-0.5.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
webencodings-0.5.1.dist-info/WHEEL,sha256=iYlv5fX357PQyRT2o6tw1bN-YcKFFHKqB_LwHO5wP-g,110
webencodings-0.5.1.dist-info/direct_url.json,sha256=mAeQdwoR7KNfqEw8dfL827Y-76KHQpnMPtvJ8waFd8s,120
webencodings-0.5.1.dist-info/top_level.txt,sha256=bZs_aZHSf_PNlfIHD4-BETJmRi99BJdKLrOW7rQngeo,13
webencodings/__init__.py,sha256=qOBJIuPy_4ByYH6W_bNgJF-qYQ2DoU-dKsDu5yRWCXg,10579
webencodings/__pycache__/__init__.cpython-312.pyc,,
webencodings/__pycache__/labels.cpython-312.pyc,,
webencodings/__pycache__/mklabels.cpython-312.pyc,,
webencodings/__pycache__/tests.cpython-312.pyc,,
webencodings/__pycache__/x_user_defined.cpython-312.pyc,,
webencodings/labels.py,sha256=4AO_KxTddqGtrL9ns7kAPjb0CcN6xsCIxbK37HY9r3E,8979
webencodings/mklabels.py,sha256=GYIeywnpaLnP0GSic8LFWgd0UVvO_l1Nc6YoF-87R_4,1305
webencodings/tests.py,sha256=OtGLyjhNY1fvkW1GvLJ_FV9ZoqC9Anyjr7q3kxTbzNs,6563
webencodings/x_user_defined.py,sha256=yOqWSdmpytGfUgh_Z6JYgDNhoc-BAHyyeeT15Fr42tM,4307
