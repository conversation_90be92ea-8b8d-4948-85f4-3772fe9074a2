{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 367816849085071872, "path": 7532663446879285561, "deps": [[9620753569207166497, "zerovec_derive", false, 2409762344332658546], [10706449961930108323, "yoke", false, 86396055711956957], [17046516144589451410, "zerofrom", false, 16325726506127661421]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zerovec-c364104988fbbaa0\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}