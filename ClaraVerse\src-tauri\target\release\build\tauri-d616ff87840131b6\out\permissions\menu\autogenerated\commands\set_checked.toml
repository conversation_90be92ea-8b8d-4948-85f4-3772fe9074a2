# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-checked"
description = "Enables the set_checked command without any pre-configured scope."
commands.allow = ["set_checked"]

[[permission]]
identifier = "deny-set-checked"
description = "Denies the set_checked command without any pre-configured scope."
commands.deny = ["set_checked"]
