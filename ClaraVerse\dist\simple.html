<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WeMa IA - Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 90%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: #4a9eff;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #4a9eff, #00d4ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        
        .btn {
            background: linear-gradient(45deg, #4a9eff, #00d4ff);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 158, 255, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 158, 255, 0.4);
        }
        
        .success { color: #00ff88; }
        .error { color: #ff4757; }
        .warning { color: #ffa502; }
        
        .logs {
            background: rgba(0, 0, 0, 0.5);
            color: #00ff88;
            padding: 15px;
            border-radius: 10px;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1>WeMa IA</h1>
        <p class="subtitle">Interface de Test Tauri</p>
        
        <div class="status">
            <div class="status-item">
                <span>🖥️ Interface Tauri</span>
                <span class="success">✅ Fonctionnelle</span>
            </div>
            <div class="status-item">
                <span>🔧 Backend Python</span>
                <span id="backend-status" class="warning">🔄 Test...</span>
            </div>
            <div class="status-item">
                <span>🌐 API REST</span>
                <span id="api-status" class="warning">🔄 Test...</span>
            </div>
        </div>
        
        <button class="btn" onclick="testBackend()">🔍 Tester Backend</button>
        <button class="btn" onclick="openMainApp()">🚀 App Principale</button>
        
        <div class="logs" id="logs">
            <div>🎯 WeMa IA - Interface Tauri chargée avec succès !</div>
            <div>📡 Test de connexion au backend...</div>
        </div>
    </div>

    <script>
        let logs = [];
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            if (logs.length > 10) logs.shift();
            updateLogs();
        }
        
        function updateLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        async function testBackend() {
            addLog('🔍 Test du backend Python...');
            try {
                const response = await fetch('http://localhost:5001/health', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addLog(`✅ Backend OK - ${data.status}`);
                    document.getElementById('backend-status').innerHTML = '<span class="success">✅ Connecté</span>';
                    document.getElementById('api-status').innerHTML = '<span class="success">✅ Fonctionnelle</span>';
                } else {
                    addLog(`❌ Backend erreur: ${response.status}`);
                    document.getElementById('backend-status').innerHTML = '<span class="error">❌ Erreur</span>';
                }
            } catch (error) {
                addLog(`❌ Connexion impossible: ${error.message}`);
                document.getElementById('backend-status').innerHTML = '<span class="error">❌ Déconnecté</span>';
                document.getElementById('api-status').innerHTML = '<span class="error">❌ Inaccessible</span>';
            }
        }
        
        function openMainApp() {
            addLog('🚀 Redirection vers interface principale...');
            // Ici on pourrait rediriger vers index.html ou ouvrir une nouvelle fenêtre
            window.location.href = 'index.html';
        }
        
        // Test automatique au chargement
        window.addEventListener('load', () => {
            addLog('🎯 Interface Tauri initialisée');
            setTimeout(testBackend, 2000);
        });
        
        // Test périodique toutes les 30 secondes
        setInterval(testBackend, 30000);
    </script>
</body>
</html>
