#!/usr/bin/env powershell
# Script pour corriger les dépendances Python du build Tauri WeMa IA

Write-Host "🔧 CORRECTION DES DÉPENDANCES PYTHON TAURI" -ForegroundColor Green
Write-Host "=" * 60

$tauriPython = "C:\Program Files\WeMa IA\python-embed\python.exe"
$tauriSitePackages = "C:\Program Files\WeMa IA\python-embed\Lib\site-packages"
$systemSitePackages = "C:\Users\<USER>\anaconda3\Lib\site-packages"

# Vérifier que les chemins existent
if (-not (Test-Path $tauriPython)) {
    Write-Host "❌ Python Tauri non trouvé: $tauriPython" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $systemSitePackages)) {
    Write-Host "❌ Site-packages système non trouvé: $systemSitePackages" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Chemins validés" -ForegroundColor Green

# Liste des dépendances critiques à copier
$dependencies = @(
    "pydantic_core",
    "annotated_types",
    "typing_inspection", 
    "typing_extensions",
    "starlette",
    "anyio",
    "sniffio",
    "idna",
    "h11",
    "click",
    "colorama"
)

Write-Host "📦 Copie des dépendances critiques..." -ForegroundColor Yellow

foreach ($dep in $dependencies) {
    $sourcePath = Join-Path $systemSitePackages "$dep*"
    $sourceItems = Get-ChildItem $sourcePath -ErrorAction SilentlyContinue
    
    if ($sourceItems) {
        foreach ($item in $sourceItems) {
            $destPath = Join-Path $tauriSitePackages $item.Name
            Write-Host "📁 Copie: $($item.Name)" -ForegroundColor Cyan
            
            try {
                if ($item.PSIsContainer) {
                    Copy-Item $item.FullName $tauriSitePackages -Recurse -Force
                } else {
                    Copy-Item $item.FullName $destPath -Force
                }
                Write-Host "  ✅ OK" -ForegroundColor Green
            } catch {
                Write-Host "  ❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "⚠️  $dep non trouvé dans le système" -ForegroundColor Yellow
    }
}

Write-Host "`n🧪 Test du backend..." -ForegroundColor Yellow

# Tester l'import de FastAPI
$testResult = & $tauriPython -c "
try:
    from fastapi import FastAPI
    print('✅ FastAPI OK')
except Exception as e:
    print(f'❌ FastAPI Error: {e}')
    exit(1)
"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Test réussi - FastAPI fonctionne !" -ForegroundColor Green
} else {
    Write-Host "❌ Test échoué" -ForegroundColor Red
    Write-Host $testResult
}

Write-Host "`n🚀 Lancement du backend de test..." -ForegroundColor Yellow
Write-Host "Appuyez sur Ctrl+C pour arrêter" -ForegroundColor Gray

# Lancer le backend en arrière-plan pour test
$backendPath = "C:\Program Files\WeMa IA\python-embed\backend\main.py"
if (Test-Path $backendPath) {
    & $tauriPython $backendPath
} else {
    Write-Host "❌ Backend non trouvé: $backendPath" -ForegroundColor Red
}

Write-Host "`n✅ Script terminé" -ForegroundColor Green
