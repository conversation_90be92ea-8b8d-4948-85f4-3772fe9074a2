{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 17984201634715228204, "path": 14595086563058826864, "deps": [[2671782512663819132, "tauri_utils", false, 8944806976073788331], [3060637413840920116, "proc_macro2", false, 1268470941054599307], [4974441333307933176, "syn", false, 4882867613712703763], [13077543566650298139, "heck", false, 14698631188825528352], [14455244907590647360, "tauri_codegen", false, 1153521035256017009], [17990358020177143287, "quote", false, 10852422733387940731]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-14d1476b08ae1653\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}