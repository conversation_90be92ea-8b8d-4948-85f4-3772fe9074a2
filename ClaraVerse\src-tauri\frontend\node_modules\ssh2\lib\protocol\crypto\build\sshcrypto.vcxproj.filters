<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:">
      <UniqueIdentifier>{7B735499-E5DD-1C2B-6C26-70023832A1CF}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users">
      <UniqueIdentifier>{E9F714C1-DA89-54E2-60CF-39FEB20BF756}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Users\tdemo\Documents">
      <UniqueIdentifier>{87566BA0-CA33-1144-65F5-087C5F9D6C20}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli">
      <UniqueIdentifier>{259727B8-8E24-0ACA-09BB-2D42457EAFEE}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli\ClaraVerse">
      <UniqueIdentifier>{6F38DC19-9F4E-5CCB-0581-AE1AEE4E6FF9}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri">
      <UniqueIdentifier>{25C923DD-589F-8401-F4E2-BF763D7E71F2}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\frontend">
      <UniqueIdentifier>{422E7AE6-2626-A88C-79BF-ADB0E3D18C82}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\frontend\node_modules">
      <UniqueIdentifier>{56DF7A98-063D-FB9D-485C-089023B4C16A}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\frontend\node_modules\@electron">
      <UniqueIdentifier>{6FD122B1-87CE-5BE7-127F-692B6E499EA3}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\frontend\node_modules\@electron\node-gyp">
      <UniqueIdentifier>{77348C0E-2034-7791-74D5-63C077DF5A3B}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\frontend\node_modules\@electron\node-gyp\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\binding.cc">
      <Filter>..\src</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\frontend\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc">
      <Filter>C:\Users\<USER>\Documents\appli\ClaraVerse\src-tauri\frontend\node_modules\@electron\node-gyp\src</Filter>
    </ClCompile>
    <None Include="..\binding.gyp">
      <Filter>..</Filter>
    </None>
  </ItemGroup>
</Project>
