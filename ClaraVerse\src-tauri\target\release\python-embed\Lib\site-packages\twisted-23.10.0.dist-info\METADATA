Metadata-Version: 2.1
Name: Twisted
Version: 23.10.0
Summary: An asynchronous networking framework written in Python
Project-URL: Changelog, https://github.com/twisted/twisted/blob/HEAD/NEWS.rst
Project-URL: Documentation, https://docs.twistedmatrix.com/
Project-URL: Homepage, https://twistedmatrix.com/
Project-URL: Issues, https://twistedmatrix.com/trac/report
Project-URL: Source, https://github.com/twisted/twisted
Project-URL: Twitter, https://twitter.com/twistedmatrix
Author-email: Twisted Matrix Laboratories <<EMAIL>>
License: MIT License
License-File: LICENSE
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.8.0
Requires-Dist: attrs>=21.3.0
Requires-Dist: automat>=0.8.0
Requires-Dist: constantly>=15.1
Requires-Dist: hyperlink>=17.1.1
Requires-Dist: incremental>=22.10.0
Requires-Dist: twisted-iocpsupport<2,>=1.0.2; platform_system == 'Windows'
Requires-Dist: typing-extensions>=4.2.0
Requires-Dist: zope-interface>=5
Provides-Extra: all-non-platform
Requires-Dist: twisted[conch,http2,serial,test,tls]; extra == 'all-non-platform'
Provides-Extra: all_non_platform
Requires-Dist: twisted[conch,http2,serial,test,tls]; extra == 'all_non_platform'
Provides-Extra: conch
Requires-Dist: appdirs>=1.4.0; extra == 'conch'
Requires-Dist: bcrypt>=3.1.3; extra == 'conch'
Requires-Dist: cryptography>=3.3; extra == 'conch'
Provides-Extra: dev
Requires-Dist: coverage<7,>=6b1; extra == 'dev'
Requires-Dist: pyflakes~=2.2; extra == 'dev'
Requires-Dist: python-subunit~=1.4; extra == 'dev'
Requires-Dist: twisted[dev-release]; extra == 'dev'
Requires-Dist: twistedchecker~=0.7; extra == 'dev'
Provides-Extra: dev-release
Requires-Dist: pydoctor~=23.9.0; extra == 'dev-release'
Requires-Dist: sphinx-rtd-theme~=1.3; extra == 'dev-release'
Requires-Dist: sphinx<7,>=6; extra == 'dev-release'
Requires-Dist: towncrier~=23.6; extra == 'dev-release'
Provides-Extra: dev_release
Requires-Dist: pydoctor~=23.9.0; extra == 'dev_release'
Requires-Dist: sphinx-rtd-theme~=1.3; extra == 'dev_release'
Requires-Dist: sphinx<7,>=6; extra == 'dev_release'
Requires-Dist: towncrier~=23.6; extra == 'dev_release'
Provides-Extra: gtk-platform
Requires-Dist: pygobject; extra == 'gtk-platform'
Requires-Dist: twisted[all-non-platform]; extra == 'gtk-platform'
Provides-Extra: gtk_platform
Requires-Dist: pygobject; extra == 'gtk_platform'
Requires-Dist: twisted[all-non-platform]; extra == 'gtk_platform'
Provides-Extra: http2
Requires-Dist: h2<5.0,>=3.0; extra == 'http2'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'http2'
Provides-Extra: macos-platform
Requires-Dist: pyobjc-core; extra == 'macos-platform'
Requires-Dist: pyobjc-framework-cfnetwork; extra == 'macos-platform'
Requires-Dist: pyobjc-framework-cocoa; extra == 'macos-platform'
Requires-Dist: twisted[all-non-platform]; extra == 'macos-platform'
Provides-Extra: macos_platform
Requires-Dist: pyobjc-core; extra == 'macos_platform'
Requires-Dist: pyobjc-framework-cfnetwork; extra == 'macos_platform'
Requires-Dist: pyobjc-framework-cocoa; extra == 'macos_platform'
Requires-Dist: twisted[all-non-platform]; extra == 'macos_platform'
Provides-Extra: mypy
Requires-Dist: mypy-zope~=1.0.1; extra == 'mypy'
Requires-Dist: mypy~=1.5.1; extra == 'mypy'
Requires-Dist: twisted[all-non-platform,dev]; extra == 'mypy'
Requires-Dist: types-pyopenssl; extra == 'mypy'
Requires-Dist: types-setuptools; extra == 'mypy'
Provides-Extra: osx-platform
Requires-Dist: twisted[macos-platform]; extra == 'osx-platform'
Provides-Extra: osx_platform
Requires-Dist: twisted[macos-platform]; extra == 'osx_platform'
Provides-Extra: serial
Requires-Dist: pyserial>=3.0; extra == 'serial'
Requires-Dist: pywin32!=226; platform_system == 'Windows' and extra == 'serial'
Provides-Extra: test
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'test'
Requires-Dist: hypothesis>=6.56; extra == 'test'
Requires-Dist: pyhamcrest>=2; extra == 'test'
Provides-Extra: tls
Requires-Dist: idna>=2.4; extra == 'tls'
Requires-Dist: pyopenssl>=21.0.0; extra == 'tls'
Requires-Dist: service-identity>=18.1.0; extra == 'tls'
Provides-Extra: windows-platform
Requires-Dist: pywin32!=226; extra == 'windows-platform'
Requires-Dist: twisted[all-non-platform]; extra == 'windows-platform'
Provides-Extra: windows_platform
Requires-Dist: pywin32!=226; extra == 'windows_platform'
Requires-Dist: twisted[all-non-platform]; extra == 'windows_platform'
