{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 367816849085071872, "path": 11183322622621637024, "deps": [[376837177317575824, "build_script_build", false, 8513037059706413919], [4143744114649553716, "raw_window_handle", false, 16995855243923100015], [5986029879202738730, "log", false, 5444832254770226198], [10281541584571964250, "windows_sys", false, 1220341334078358156]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-b621fcb5396cadd0\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}