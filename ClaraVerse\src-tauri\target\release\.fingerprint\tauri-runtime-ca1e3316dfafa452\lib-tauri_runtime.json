{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 367816849085071872, "path": 4231087634136942338, "deps": [[2671782512663819132, "tauri_utils", false, 1822794662215125040], [3150220818285335163, "url", false, 1318321174758569141], [4143744114649553716, "raw_window_handle", false, 16995855243923100015], [6089812615193535349, "build_script_build", false, 7885336170051698956], [7606335748176206944, "dpi", false, 13628436113119927691], [9010263965687315507, "http", false, 3748586209917174119], [9689903380558560274, "serde", false, 415033232941934989], [10806645703491011684, "thiserror", false, 12697263202037361942], [14585479307175734061, "windows", false, 11646464322459264762], [15367738274754116744, "serde_json", false, 16545619236785180510], [16727543399706004146, "cookie", false, 10638122473424882545]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-ca1e3316dfafa452\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}