cargo:rustc-check-cfg=cfg(custom_protocol)
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:rustc-check-cfg=cfg(updater)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(api_all)
cargo:rustc-cfg=api_all
cargo:rustc-check-cfg=cfg(fs_all)
cargo:rustc-cfg=fs_all
cargo:rustc-check-cfg=cfg(fs_read_file)
cargo:rustc-cfg=fs_read_file
cargo:rustc-check-cfg=cfg(fs_write_file)
cargo:rustc-cfg=fs_write_file
cargo:rustc-check-cfg=cfg(fs_read_dir)
cargo:rustc-cfg=fs_read_dir
cargo:rustc-check-cfg=cfg(fs_copy_file)
cargo:rustc-cfg=fs_copy_file
cargo:rustc-check-cfg=cfg(fs_create_dir)
cargo:rustc-cfg=fs_create_dir
cargo:rustc-check-cfg=cfg(fs_remove_dir)
cargo:rustc-cfg=fs_remove_dir
cargo:rustc-check-cfg=cfg(fs_remove_file)
cargo:rustc-cfg=fs_remove_file
cargo:rustc-check-cfg=cfg(fs_rename_file)
cargo:rustc-cfg=fs_rename_file
cargo:rustc-check-cfg=cfg(fs_exists)
cargo:rustc-cfg=fs_exists
cargo:rustc-check-cfg=cfg(fs_any)
cargo:rustc-cfg=fs_any
cargo:rustc-check-cfg=cfg(window_all)
cargo:rustc-cfg=window_all
cargo:rustc-check-cfg=cfg(window_create)
cargo:rustc-cfg=window_create
cargo:rustc-check-cfg=cfg(window_center)
cargo:rustc-cfg=window_center
cargo:rustc-check-cfg=cfg(window_request_user_attention)
cargo:rustc-cfg=window_request_user_attention
cargo:rustc-check-cfg=cfg(window_set_resizable)
cargo:rustc-cfg=window_set_resizable
cargo:rustc-check-cfg=cfg(window_set_maximizable)
cargo:rustc-cfg=window_set_maximizable
cargo:rustc-check-cfg=cfg(window_set_minimizable)
cargo:rustc-cfg=window_set_minimizable
cargo:rustc-check-cfg=cfg(window_set_closable)
cargo:rustc-cfg=window_set_closable
cargo:rustc-check-cfg=cfg(window_set_title)
cargo:rustc-cfg=window_set_title
cargo:rustc-check-cfg=cfg(window_maximize)
cargo:rustc-cfg=window_maximize
cargo:rustc-check-cfg=cfg(window_unmaximize)
cargo:rustc-cfg=window_unmaximize
cargo:rustc-check-cfg=cfg(window_minimize)
cargo:rustc-cfg=window_minimize
cargo:rustc-check-cfg=cfg(window_unminimize)
cargo:rustc-cfg=window_unminimize
cargo:rustc-check-cfg=cfg(window_show)
cargo:rustc-cfg=window_show
cargo:rustc-check-cfg=cfg(window_hide)
cargo:rustc-cfg=window_hide
cargo:rustc-check-cfg=cfg(window_close)
cargo:rustc-cfg=window_close
cargo:rustc-check-cfg=cfg(window_set_decorations)
cargo:rustc-cfg=window_set_decorations
cargo:rustc-check-cfg=cfg(window_set_always_on_top)
cargo:rustc-cfg=window_set_always_on_top
cargo:rustc-check-cfg=cfg(window_set_content_protected)
cargo:rustc-cfg=window_set_content_protected
cargo:rustc-check-cfg=cfg(window_set_size)
cargo:rustc-cfg=window_set_size
cargo:rustc-check-cfg=cfg(window_set_min_size)
cargo:rustc-cfg=window_set_min_size
cargo:rustc-check-cfg=cfg(window_set_max_size)
cargo:rustc-cfg=window_set_max_size
cargo:rustc-check-cfg=cfg(window_set_position)
cargo:rustc-cfg=window_set_position
cargo:rustc-check-cfg=cfg(window_set_fullscreen)
cargo:rustc-cfg=window_set_fullscreen
cargo:rustc-check-cfg=cfg(window_set_focus)
cargo:rustc-cfg=window_set_focus
cargo:rustc-check-cfg=cfg(window_set_icon)
cargo:rustc-cfg=window_set_icon
cargo:rustc-check-cfg=cfg(window_set_skip_taskbar)
cargo:rustc-cfg=window_set_skip_taskbar
cargo:rustc-check-cfg=cfg(window_set_cursor_grab)
cargo:rustc-cfg=window_set_cursor_grab
cargo:rustc-check-cfg=cfg(window_set_cursor_visible)
cargo:rustc-cfg=window_set_cursor_visible
cargo:rustc-check-cfg=cfg(window_set_cursor_icon)
cargo:rustc-cfg=window_set_cursor_icon
cargo:rustc-check-cfg=cfg(window_set_cursor_position)
cargo:rustc-cfg=window_set_cursor_position
cargo:rustc-check-cfg=cfg(window_set_ignore_cursor_events)
cargo:rustc-cfg=window_set_ignore_cursor_events
cargo:rustc-check-cfg=cfg(window_start_dragging)
cargo:rustc-cfg=window_start_dragging
cargo:rustc-check-cfg=cfg(window_print)
cargo:rustc-cfg=window_print
cargo:rustc-check-cfg=cfg(window_any)
cargo:rustc-cfg=window_any
cargo:rustc-check-cfg=cfg(shell_all)
cargo:rustc-cfg=shell_all
cargo:rustc-check-cfg=cfg(shell_execute)
cargo:rustc-cfg=shell_execute
cargo:rustc-check-cfg=cfg(shell_sidecar)
cargo:rustc-cfg=shell_sidecar
cargo:rustc-check-cfg=cfg(shell_open)
cargo:rustc-cfg=shell_open
cargo:rustc-check-cfg=cfg(shell_any)
cargo:rustc-cfg=shell_any
cargo:rustc-check-cfg=cfg(shell_script)
cargo:rustc-cfg=shell_script
cargo:rustc-check-cfg=cfg(shell_scope)
cargo:rustc-cfg=shell_scope
cargo:rustc-check-cfg=cfg(dialog_all)
cargo:rustc-cfg=dialog_all
cargo:rustc-check-cfg=cfg(dialog_open)
cargo:rustc-cfg=dialog_open
cargo:rustc-check-cfg=cfg(dialog_save)
cargo:rustc-cfg=dialog_save
cargo:rustc-check-cfg=cfg(dialog_message)
cargo:rustc-cfg=dialog_message
cargo:rustc-check-cfg=cfg(dialog_ask)
cargo:rustc-cfg=dialog_ask
cargo:rustc-check-cfg=cfg(dialog_confirm)
cargo:rustc-cfg=dialog_confirm
cargo:rustc-check-cfg=cfg(dialog_any)
cargo:rustc-cfg=dialog_any
cargo:rustc-check-cfg=cfg(http_all)
cargo:rustc-cfg=http_all
cargo:rustc-check-cfg=cfg(http_request)
cargo:rustc-cfg=http_request
cargo:rustc-check-cfg=cfg(http_any)
cargo:rustc-cfg=http_any
cargo:rustc-check-cfg=cfg(cli)
cargo:rustc-check-cfg=cfg(notification_all)
cargo:rustc-cfg=notification_all
cargo:rustc-check-cfg=cfg(notification_any)
cargo:rustc-cfg=notification_any
cargo:rustc-check-cfg=cfg(global_shortcut_all)
cargo:rustc-cfg=global_shortcut_all
cargo:rustc-check-cfg=cfg(global_shortcut_any)
cargo:rustc-cfg=global_shortcut_any
cargo:rustc-check-cfg=cfg(os_all)
cargo:rustc-cfg=os_all
cargo:rustc-check-cfg=cfg(os_any)
cargo:rustc-cfg=os_any
cargo:rustc-check-cfg=cfg(path_all)
cargo:rustc-cfg=path_all
cargo:rustc-check-cfg=cfg(path_any)
cargo:rustc-cfg=path_any
cargo:rustc-check-cfg=cfg(protocol_all)
cargo:rustc-cfg=protocol_all
cargo:rustc-check-cfg=cfg(protocol_asset)
cargo:rustc-cfg=protocol_asset
cargo:rustc-check-cfg=cfg(protocol_any)
cargo:rustc-cfg=protocol_any
cargo:rustc-check-cfg=cfg(process_all)
cargo:rustc-cfg=process_all
cargo:rustc-check-cfg=cfg(process_relaunch)
cargo:rustc-cfg=process_relaunch
cargo:rustc-check-cfg=cfg(process_exit)
cargo:rustc-cfg=process_exit
cargo:rustc-check-cfg=cfg(process_any)
cargo:rustc-cfg=process_any
cargo:rustc-check-cfg=cfg(clipboard_all)
cargo:rustc-cfg=clipboard_all
cargo:rustc-check-cfg=cfg(clipboard_write_text)
cargo:rustc-cfg=clipboard_write_text
cargo:rustc-check-cfg=cfg(clipboard_read_text)
cargo:rustc-cfg=clipboard_read_text
cargo:rustc-check-cfg=cfg(clipboard_any)
cargo:rustc-cfg=clipboard_any
cargo:rustc-check-cfg=cfg(app_all)
cargo:rustc-cfg=app_all
cargo:rustc-check-cfg=cfg(app_show)
cargo:rustc-cfg=app_show
cargo:rustc-check-cfg=cfg(app_hide)
cargo:rustc-cfg=app_hide
cargo:rustc-check-cfg=cfg(app_any)
cargo:rustc-cfg=app_any
