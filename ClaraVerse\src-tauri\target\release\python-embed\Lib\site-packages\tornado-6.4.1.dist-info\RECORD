tornado-6.4.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
tornado-6.4.1.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
tornado-6.4.1.dist-info/METADATA,sha256=CxBiBf6u1p4Tq0BJEG-b0ED-DfHAszfBIAdsM2WaU7g,2556
tornado-6.4.1.dist-info/RECORD,,
tornado-6.4.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tornado-6.4.1.dist-info/WHEEL,sha256=Aavq5XXl58tL6bL-VsXq6M2KHwSF4I7YrvRYfeAzm5A,100
tornado-6.4.1.dist-info/direct_url.json,sha256=r0_SIemGNQkrvRTBqrA3Uf1_gDE9fx9i9tkhHkriQm8,87
tornado-6.4.1.dist-info/top_level.txt,sha256=5QAK1MeNpWgYdqWoU8iYlDuGB8j6NDPgx-uSUHTe0A4,8
tornado/__init__.py,sha256=PurB6LglNt-a5TKsSx9cjFENqa4o-u70ZTwcbffl1eE,1761
tornado/__pycache__/__init__.cpython-312.pyc,,
tornado/__pycache__/_locale_data.cpython-312.pyc,,
tornado/__pycache__/auth.cpython-312.pyc,,
tornado/__pycache__/autoreload.cpython-312.pyc,,
tornado/__pycache__/concurrent.cpython-312.pyc,,
tornado/__pycache__/curl_httpclient.cpython-312.pyc,,
tornado/__pycache__/escape.cpython-312.pyc,,
tornado/__pycache__/gen.cpython-312.pyc,,
tornado/__pycache__/http1connection.cpython-312.pyc,,
tornado/__pycache__/httpclient.cpython-312.pyc,,
tornado/__pycache__/httpserver.cpython-312.pyc,,
tornado/__pycache__/httputil.cpython-312.pyc,,
tornado/__pycache__/ioloop.cpython-312.pyc,,
tornado/__pycache__/iostream.cpython-312.pyc,,
tornado/__pycache__/locale.cpython-312.pyc,,
tornado/__pycache__/locks.cpython-312.pyc,,
tornado/__pycache__/log.cpython-312.pyc,,
tornado/__pycache__/netutil.cpython-312.pyc,,
tornado/__pycache__/options.cpython-312.pyc,,
tornado/__pycache__/process.cpython-312.pyc,,
tornado/__pycache__/queues.cpython-312.pyc,,
tornado/__pycache__/routing.cpython-312.pyc,,
tornado/__pycache__/simple_httpclient.cpython-312.pyc,,
tornado/__pycache__/tcpclient.cpython-312.pyc,,
tornado/__pycache__/tcpserver.cpython-312.pyc,,
tornado/__pycache__/template.cpython-312.pyc,,
tornado/__pycache__/testing.cpython-312.pyc,,
tornado/__pycache__/util.cpython-312.pyc,,
tornado/__pycache__/web.cpython-312.pyc,,
tornado/__pycache__/websocket.cpython-312.pyc,,
tornado/__pycache__/wsgi.cpython-312.pyc,,
tornado/_locale_data.py,sha256=AO8ZtZ75VeZsiRpirbH-FNvz3OFDQxGQcXmqT29vaWo,4503
tornado/auth.py,sha256=fWdE13Hx_FBMBSq324HKJXTXb971FdwAMkFJ6WkFViI,49347
tornado/autoreload.py,sha256=UY29Qf0gBl_jwrS3QmGhzLYvn-mD9KOJ4a7XxM9PK30,13136
tornado/concurrent.py,sha256=MmOE82Aw8c4DB-mPKc_uOQZ0uaruut5c2026iWyr7zc,8307
tornado/curl_httpclient.py,sha256=gGq_aThR_VYWUMS0dZrFEdoxwO0PxgaXmoYmd7eGK5A,24862
tornado/escape.py,sha256=UxbVr5tr7zCngdR8zyOfmSMwcgMDwyHZCMpxpLaF7rE,14257
tornado/gen.py,sha256=HPLuIu-o--UPH1Fk-lbuAQ4K0sk5rrB2etYhE8EpAic,31678
tornado/http1connection.py,sha256=mDqF_yQXpWmavtpvtCXjdVOBFUsFOPm3UcPwish6nCI,37688
tornado/httpclient.py,sha256=fDKLM0VLSOz4weyBOhG8w-WPfu_yjbT5mw0Q0YxR5Rs,31919
tornado/httpserver.py,sha256=RclFpP4QB97-KLhrvHq6i-p-3PBz2tt5jevl3hv6DIw,16137
tornado/httputil.py,sha256=D1Vigmzuhxdkq1BeB1GurleAnn3SNhM07plwLSsMw9g,36229
tornado/ioloop.py,sha256=q2lh5ueSFD6yW7k4WIvG9QDKqqYep3lo6PlKu7PQTB0,36818
tornado/iostream.py,sha256=qMCy9gTdFQcG0h0dezQYsa7orHMjXidcODDM-KIZzeM,64412
tornado/locale.py,sha256=JVi6kuA-Ia09EegXwftFitvqKMJQB0XL-4biiSrj13c,21154
tornado/locks.py,sha256=h3F_P3HpDql5XhaNTdSr8_biX0s0CeYqRm27U1JXXAE,17356
tornado/log.py,sha256=rehSx-obuV-yY_vc3XiSiUCYSEy-U40zgOxMprlIG4c,12549
tornado/netutil.py,sha256=RtgJNwYy-qVPUyMDyk1jAdozqkazJdSdjMZkyxgcWhg,25045
tornado/options.py,sha256=5knewY_eZulDEisBU55Xyd9YJdSfXwvkBnKEdMcDPEU,26254
tornado/platform/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tornado/platform/__pycache__/__init__.cpython-312.pyc,,
tornado/platform/__pycache__/asyncio.cpython-312.pyc,,
tornado/platform/__pycache__/caresresolver.cpython-312.pyc,,
tornado/platform/__pycache__/twisted.cpython-312.pyc,,
tornado/platform/asyncio.py,sha256=B8KYaIDjHq1jjhZnkDmSQzsodn23UNDzb6dNxoDH4EQ,26733
tornado/platform/caresresolver.py,sha256=aC5g1uku5n_bDV2HcAkEIuMqeDjJo90XiBx1WOXTR9o,3500
tornado/platform/twisted.py,sha256=WOv5hBl6b4vUYguwKMgiLJgcI6wG5lm6NV6649aj2hE,5624
tornado/process.py,sha256=HFG4hwXV_LJktKNcQUo7DS1HxilFOKPqqA6zBcmlSDA,12704
tornado/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tornado/queues.py,sha256=mXeI_-MpVpibaT46ozmZG9L7COMqeqDhbMZ0_DE1V-8,12530
tornado/routing.py,sha256=fEUpBPHKRd4PCp4LH1XKHspkqEG2jkIE_9hvJr3MEiA,25082
tornado/simple_httpclient.py,sha256=29-kfqaNkk75ZYecuVXwAT9wp5LD4vJ_d-R65-Po7EA,27798
tornado/speedups.pyd,sha256=SCDVOsp5Pxc_H-xBwnAXfEFxbwiZqQNjI6vyES9aeXc,10752
tornado/tcpclient.py,sha256=1jmNEfu8S_RV38iKmTktACkoC4EZfiRAjoBVzJ6Cb88,12152
tornado/tcpserver.py,sha256=KCnhxn_Y2S9Ad-IRejF5E9E_tvBcTD8Nguw7Ohgbhk0,15033
tornado/template.py,sha256=owU2JLnxZzOcv3rS7QeH5TEpLLh0PIQ3P7feeCal_gY,37793
tornado/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tornado/test/__main__.py,sha256=f5xh9jgW6upCWRiV2J2A7wSg0NJYI1wn74YEDr_8ZNw,336
tornado/test/__pycache__/__init__.cpython-312.pyc,,
tornado/test/__pycache__/__main__.cpython-312.pyc,,
tornado/test/__pycache__/asyncio_test.cpython-312.pyc,,
tornado/test/__pycache__/auth_test.cpython-312.pyc,,
tornado/test/__pycache__/autoreload_test.cpython-312.pyc,,
tornado/test/__pycache__/circlerefs_test.cpython-312.pyc,,
tornado/test/__pycache__/concurrent_test.cpython-312.pyc,,
tornado/test/__pycache__/curl_httpclient_test.cpython-312.pyc,,
tornado/test/__pycache__/escape_test.cpython-312.pyc,,
tornado/test/__pycache__/gen_test.cpython-312.pyc,,
tornado/test/__pycache__/http1connection_test.cpython-312.pyc,,
tornado/test/__pycache__/httpclient_test.cpython-312.pyc,,
tornado/test/__pycache__/httpserver_test.cpython-312.pyc,,
tornado/test/__pycache__/httputil_test.cpython-312.pyc,,
tornado/test/__pycache__/import_test.cpython-312.pyc,,
tornado/test/__pycache__/ioloop_test.cpython-312.pyc,,
tornado/test/__pycache__/iostream_test.cpython-312.pyc,,
tornado/test/__pycache__/locale_test.cpython-312.pyc,,
tornado/test/__pycache__/locks_test.cpython-312.pyc,,
tornado/test/__pycache__/log_test.cpython-312.pyc,,
tornado/test/__pycache__/netutil_test.cpython-312.pyc,,
tornado/test/__pycache__/options_test.cpython-312.pyc,,
tornado/test/__pycache__/process_test.cpython-312.pyc,,
tornado/test/__pycache__/queues_test.cpython-312.pyc,,
tornado/test/__pycache__/resolve_test_helper.cpython-312.pyc,,
tornado/test/__pycache__/routing_test.cpython-312.pyc,,
tornado/test/__pycache__/runtests.cpython-312.pyc,,
tornado/test/__pycache__/simple_httpclient_test.cpython-312.pyc,,
tornado/test/__pycache__/tcpclient_test.cpython-312.pyc,,
tornado/test/__pycache__/tcpserver_test.cpython-312.pyc,,
tornado/test/__pycache__/template_test.cpython-312.pyc,,
tornado/test/__pycache__/testing_test.cpython-312.pyc,,
tornado/test/__pycache__/twisted_test.cpython-312.pyc,,
tornado/test/__pycache__/util.cpython-312.pyc,,
tornado/test/__pycache__/util_test.cpython-312.pyc,,
tornado/test/__pycache__/web_test.cpython-312.pyc,,
tornado/test/__pycache__/websocket_test.cpython-312.pyc,,
tornado/test/__pycache__/wsgi_test.cpython-312.pyc,,
tornado/test/asyncio_test.py,sha256=0KlT8LxuDsCqONyyHaea__1w6ySM-IIJVvLaccYohbc,10481
tornado/test/auth_test.py,sha256=jxZYra8rz9XyWSJYze_oFY-0OfbcPETqt9Ew2Qjrlqs,23347
tornado/test/autoreload_test.py,sha256=Hc7qZJKXrBjhXuVEuDGul4efvbTd29HIleBmctiH0i8,8971
tornado/test/circlerefs_test.py,sha256=3Cb-plf9aBT4kGaQTAFE647dII8XkxGbjttCq_AR_kM,7337
tornado/test/concurrent_test.py,sha256=MDdxERAV6-xkn6K1MG9Ngbcay20J2NYS7cS-Ql3iw40,6049
tornado/test/csv_translations/fr_FR.csv,sha256=0UsMzfh1cw3yQdhS7pCmRfQoAkbqWpgzzodpZqp7ttM,18
tornado/test/curl_httpclient_test.py,sha256=hVXuIq32IdBz8_UOOuurUQGbfMgLpJb11ghF_wk7l4Q,4303
tornado/test/escape_test.py,sha256=WTYdoDJbMbEDUZn7wxoj_sQZ-GAMH4TQiugwCgE7wA0,12336
tornado/test/gen_test.py,sha256=oWAOPHVu5hcCop9fzwI_mlAGogXy3Sy8t2funZNXoaY,34093
tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.mo,sha256=fl0ZVZIlNwwU9lPx29pgZ4X-HfyEVYphJu7UWtll7jo,665
tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.po,sha256=Clw6HyQUcopGV25qw3pvw3gn1ZqZRYrovsi8PQTQAnM,1049
tornado/test/http1connection_test.py,sha256=0Lr47wyU4sGsG4SNWaTIWjgkAIKeK-8OE5SYWXWoEqc,1964
tornado/test/httpclient_test.py,sha256=IG0tcSuZpSTUHhUaqc7NdBB4Y3s0jyA3HtH8HixBDtg,35986
tornado/test/httpserver_test.py,sha256=zfhmrm_srG1ilo5KgLF4SvQDPlZyUE9KiID5b-RF4vE,52223
tornado/test/httputil_test.py,sha256=qgxLo9VmwAT6Arrmk-9yoM5BLLshEKTuD_D1bvxdMHY,20658
tornado/test/import_test.py,sha256=y7_9ZxhgoF_5AJNkFnjKNagWlZao1qhldcPQ6QDfXUQ,2231
tornado/test/ioloop_test.py,sha256=6l2iuybNvyYs_0OUbpYdkVfQ5Jh07jtRxNw7rtVRVMI,27484
tornado/test/iostream_test.py,sha256=wl8XOFVPG54Ac2E2bdl8jVsiJDSXlPpUhzWoyVZLOQs,50551
tornado/test/locale_test.py,sha256=bW1ooegzBpqv9Iy4m7B5sJWqkMa81wODJ9Azs_JbJS8,6359
tornado/test/locks_test.py,sha256=nGyZUEW7r2FMnfLqihSzv6F_WlxZoSWyKDKa-cnqF-g,17010
tornado/test/log_test.py,sha256=YDmwWmsiDHM-4ptXsTHojuV4Y3z8rhM2Am_W60m9hk8,8918
tornado/test/netutil_test.py,sha256=mc8LCgvHr4R8srrtIi4RfH0I7musOGrTFzrIgjHz_NY,8334
tornado/test/options_test.cfg,sha256=SpUupk-MfXBMhLENqXmD-K5a2Mp888REqfICQupcgqc,69
tornado/test/options_test.py,sha256=XWyrYHOiZOoGy6ET0lBgAqYV9KfmWliSBCNLf1Z6hyU,11932
tornado/test/options_test_types.cfg,sha256=17sAubRDinYX_DRhdCaVJ0UpX1AefZ9o_wQ2ypMVwL0,296
tornado/test/options_test_types_str.cfg,sha256=ypIodrtpymSX2HsevGJJ4A07oLifb5ffkBISog364qo,172
tornado/test/process_test.py,sha256=WFNVVMGB4xmiNbBsmmzN1hsC7i3O2MM0UkFe-vaPO-g,11308
tornado/test/queues_test.py,sha256=EGTV1_BMK3gTjlrFEyqQE1FE2WM9CPA6D7Nj20J2i7c,13981
tornado/test/resolve_test_helper.py,sha256=rTiOMC4KVo_21XvsLhtiZCjaquk_zt6DVM_hCyCM7yE,410
tornado/test/routing_test.py,sha256=YWZGtYp2UVj2Vf-e1KYdhXz35MgD0zwNXLy7ISYO0Q0,8827
tornado/test/runtests.py,sha256=8I-0Vs44ghJB_56CmohCtnrpW-9KVqGtQs8TBYF_Lk8,6990
tornado/test/simple_httpclient_test.py,sha256=7zddsp8jXFPm-y2AlwwX-kD9eu-LJdIIKkBF5H9a-SI,31108
tornado/test/static/dir/index.html,sha256=tBwBanUSjISUy0BVan_QNKkYdLau8qs__P0G9oAiP78,18
tornado/test/static/robots.txt,sha256=Mx6pCQ2wyfb1l72YQP1bFxgw9uCzuhyyTfqR8Mla7cE,26
tornado/test/static/sample.xml,sha256=7LeTf16BWDucipsUaZZK7oDxtKEMDH9sFtsNR1G6pME,666
tornado/test/static/sample.xml.bz2,sha256=2Ql5ccWnaSpDdTyioino7Bw_dcGFkG_RQO5Lm5cfT6A,285
tornado/test/static/sample.xml.gz,sha256=_App0wKpn31lZVA9P_rslytPm4ei5GvNPVKh55r7l28,264
tornado/test/static_foo.txt,sha256=DdAKABzHb8kunGrAvUoTLnHlgdTN-6_PjH1irVmokm8,95
tornado/test/tcpclient_test.py,sha256=QzWDNl82-orQPVYSsa2PVGWKuDdKUl8lv7i6Mc7-4zo,16847
tornado/test/tcpserver_test.py,sha256=EBaNIQ49r7WHtbIugUm1FH5mhLyYoRGn7llRTz70rLY,7720
tornado/test/template_test.py,sha256=RZi2gM3InqFfNZJsGbrMlPKpA-709-2jyPtFqlK1NUM,18657
tornado/test/templates/utf8.html,sha256=9d1eiaw5KCjUTCbRRIl_RLSy0LCJXaO-bzVF2L_32fM,7
tornado/test/test.crt,sha256=ZwLT9G7BdN5oRcCD48m-iAE9JT8SCSokPVzJYMw9uvo,1042
tornado/test/test.key,sha256=LJXshpCeMpmvgHeGdBQBWaRMK4yEIvMagugdQFcvDHQ,1708
tornado/test/testing_test.py,sha256=0Kz_IpffRki0E3GbP5QPe7XUkj4nlp77jjM5KQ0pwm4,10509
tornado/test/twisted_test.py,sha256=qmwduR2Qj3RMdCNX1fy8heZWP9LqIfNT69G61u9jSFs,1732
tornado/test/util.py,sha256=LoAdDDLZai4L45le4bMSkBq2drQegm3CKdLYOkOPwYQ,3654
tornado/test/util_test.py,sha256=T7LwwdDRnxuxstTuokzgbWUUnFCmNUwZphiROTHMMCU,9771
tornado/test/web_test.py,sha256=pto5qRjnp7RuOmyUaH8RXqnRTlgDs9tZYYoIXkyuj2w,121646
tornado/test/websocket_test.py,sha256=icOy-reMAdT21iy57sv9AC6B56M4aCw-6svficjtyn4,29435
tornado/test/wsgi_test.py,sha256=tQJa7kCQ6CHCqrYzkBcbDjjwl3qipsjailNrcVJgi2o,3918
tornado/testing.py,sha256=pVopE2gAXDDp0C7Q7iqu8mClPI1BsHAJTEIrHmxXZ50,32856
tornado/util.py,sha256=_TFMLQl15tVeKsdNXL1Ub85ITCSA_GGzydIMrQX_xuY,16249
tornado/web.py,sha256=f0aWofjNrNIgvzn6bnCvSz-NZjz1Tu7ioJ1t0-666N8,143248
tornado/websocket.py,sha256=zHs5l_5wpXMMGAay_PbcdZz5YVLP_mNoQI_aDdUkcMQ,61715
tornado/wsgi.py,sha256=k6WDDKP2wmyMTjQdv0mY2F5SadFcuTI6gOmWjVgybuQ,10817
