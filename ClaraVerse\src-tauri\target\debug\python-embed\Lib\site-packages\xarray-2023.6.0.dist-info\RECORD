xarray-2023.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
xarray-2023.6.0.dist-info/LICENSE,sha256=c7p036pSC0mkAbXSFFmoUjoUbzt1GKgz7qXvqFEwv2g,10273
xarray-2023.6.0.dist-info/METADATA,sha256=t3SrZ9ukdHgDeLqcN_euaBku8ET_VRBVI0j4TpYC8yk,6084
xarray-2023.6.0.dist-info/RECORD,,
xarray-2023.6.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray-2023.6.0.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
xarray-2023.6.0.dist-info/direct_url.json,sha256=3roY3uAS9QtUtcFeFqqPRTeZbEbyJPHE6RBwkJOrrVE,114
xarray-2023.6.0.dist-info/entry_points.txt,sha256=oL9vS8WD_v2ribOoyTtKL3BZ2w-wZMC1bf8GF8X53U0,66
xarray-2023.6.0.dist-info/top_level.txt,sha256=OGV8AqTgYtuaw6YV6tevWXEdDI5vHJiARQCJgRyT7co,7
xarray/__init__.py,sha256=MDyu3HuPEcTwwGXOafqP8liESl-QjINKeRkotC6zJrE,2843
xarray/__pycache__/__init__.cpython-312.pyc,,
xarray/__pycache__/conventions.cpython-312.pyc,,
xarray/__pycache__/convert.cpython-312.pyc,,
xarray/__pycache__/testing.cpython-312.pyc,,
xarray/__pycache__/tutorial.cpython-312.pyc,,
xarray/backends/__init__.py,sha256=STbpNTPTRh1xuNHFwYK55o0nHOyd-zEeI4V-Ai4Gd_M,1708
xarray/backends/__pycache__/__init__.cpython-312.pyc,,
xarray/backends/__pycache__/api.cpython-312.pyc,,
xarray/backends/__pycache__/common.cpython-312.pyc,,
xarray/backends/__pycache__/file_manager.cpython-312.pyc,,
xarray/backends/__pycache__/h5netcdf_.cpython-312.pyc,,
xarray/backends/__pycache__/locks.cpython-312.pyc,,
xarray/backends/__pycache__/lru_cache.cpython-312.pyc,,
xarray/backends/__pycache__/memory.cpython-312.pyc,,
xarray/backends/__pycache__/netCDF4_.cpython-312.pyc,,
xarray/backends/__pycache__/netcdf3.cpython-312.pyc,,
xarray/backends/__pycache__/plugins.cpython-312.pyc,,
xarray/backends/__pycache__/pseudonetcdf_.cpython-312.pyc,,
xarray/backends/__pycache__/pydap_.cpython-312.pyc,,
xarray/backends/__pycache__/pynio_.cpython-312.pyc,,
xarray/backends/__pycache__/scipy_.cpython-312.pyc,,
xarray/backends/__pycache__/store.cpython-312.pyc,,
xarray/backends/__pycache__/zarr.cpython-312.pyc,,
xarray/backends/api.py,sha256=pjjsV6diFjc9oejybgWKrraj6Yx7Xs0F-20XO5noYgw,66968
xarray/backends/common.py,sha256=1Erw-H7BUf7rguKCwxZTg3XK2hynr38qiMQbSCJq8nQ,13833
xarray/backends/file_manager.py,sha256=YnGk-QTd5JCagU-KJTVb2_E7M3pp6VTaZ_Da22Z_iac,12498
xarray/backends/h5netcdf_.py,sha256=FGvh0J8BaONaQlb3soWmGTdigtPri4crwrOI5VOuD20,13947
xarray/backends/locks.py,sha256=3hzG_S_ZimcQZwQbW-9VxyF91EUgwmvOx2a4Te_Ka4E,5595
xarray/backends/lru_cache.py,sha256=bdqbS9MY4jurqrtjx1vUbhlTfvGKGBuvjf2wpS0UDYA,3661
xarray/backends/memory.py,sha256=_QhBCbOeXQr2y6fQALFejINQuLhvWbkffyBtoR5GsTM,1392
xarray/backends/netCDF4_.py,sha256=h0vc07DoZLu96j3W7TQFvjqMxzrT5YPqonqFKExYMkE,20445
xarray/backends/netcdf3.py,sha256=_NdddBggyHZz260ybJO6ZOsIB7O-RsSOt-cqs6Q3_ok,4163
xarray/backends/plugins.py,sha256=zlr4bqlZjhSqKgcCzr9If0xL3a2GyjlqwasS6AZCw1U,8172
xarray/backends/pseudonetcdf_.py,sha256=JuaJqU3_TbQ_Y7IW47X3MtSidkij-TYT3s0xhxDgz6w,5696
xarray/backends/pydap_.py,sha256=nCrh94_hT8bQSUeXYGvmlnbxzwVp5rIg09VTyvU8mUo,6472
xarray/backends/pynio_.py,sha256=drxa7KS7iVyldQACLFpioKA96kO5wTKg53u8hPqjbGw,4893
xarray/backends/scipy_.py,sha256=DpYeNN1JZGkEdH6F4Q5BpKNmd9YrjPdEsyyrtLWSxVU,10902
xarray/backends/store.py,sha256=Woa4TcV1kVXPjOG_lYm2n4pQUTj0kylmL-V1VaqpWHA,2062
xarray/backends/zarr.py,sha256=8lAwODMA4EB7LangHjif-Y9ubwyZ6Zq7Y9qNrc_CDx4,35626
xarray/coding/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/coding/__pycache__/__init__.cpython-312.pyc,,
xarray/coding/__pycache__/calendar_ops.cpython-312.pyc,,
xarray/coding/__pycache__/cftime_offsets.cpython-312.pyc,,
xarray/coding/__pycache__/cftimeindex.cpython-312.pyc,,
xarray/coding/__pycache__/frequencies.cpython-312.pyc,,
xarray/coding/__pycache__/strings.cpython-312.pyc,,
xarray/coding/__pycache__/times.cpython-312.pyc,,
xarray/coding/__pycache__/variables.cpython-312.pyc,,
xarray/coding/calendar_ops.py,sha256=ULaVQfvst9ercmtzTbMj5H-MT13ynwVqPzxL3ETZueY,13824
xarray/coding/cftime_offsets.py,sha256=pWgSpdigx3P9thD3Btw4lN_1S0YkgXsFVv1FPSKl2jw,47274
xarray/coding/cftimeindex.py,sha256=30tQmQ0-hWwIZPEdVL763iinUux9RT-M9tDy6JLWo8k,29864
xarray/coding/frequencies.py,sha256=rOFg7xjCYjkz_jCCDp9mz4q4M1eAIw-uk7a0GzDpNJo,9256
xarray/coding/strings.py,sha256=rqFlTJEyDc6mchp5FYU3do8Xgvz725ghLgI7X4FgK8o,8130
xarray/coding/times.py,sha256=ViD6uOw_vPuLGohcXM3i3P1h5tKnYHnzLtiTuyiYbWo,27896
xarray/coding/variables.py,sha256=i4YkBgpCPM16HR87fCIJjInSkpCLc7i_CDLZH363uWQ,19168
xarray/conventions.py,sha256=rBYD5NHTg6oagfZsQkOnd8uEw7RPb-zE9Sjt0YJgsW4,29123
xarray/convert.py,sha256=COigpJ7eEXOe_cyqbexMZM2o1jAPfQmLmflOgOh278o,9712
xarray/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/core/__pycache__/__init__.cpython-312.pyc,,
xarray/core/__pycache__/_aggregations.cpython-312.pyc,,
xarray/core/__pycache__/_typed_ops.cpython-312.pyc,,
xarray/core/__pycache__/accessor_dt.cpython-312.pyc,,
xarray/core/__pycache__/accessor_str.cpython-312.pyc,,
xarray/core/__pycache__/alignment.cpython-312.pyc,,
xarray/core/__pycache__/arithmetic.cpython-312.pyc,,
xarray/core/__pycache__/combine.cpython-312.pyc,,
xarray/core/__pycache__/common.cpython-312.pyc,,
xarray/core/__pycache__/computation.cpython-312.pyc,,
xarray/core/__pycache__/concat.cpython-312.pyc,,
xarray/core/__pycache__/coordinates.cpython-312.pyc,,
xarray/core/__pycache__/dask_array_ops.cpython-312.pyc,,
xarray/core/__pycache__/daskmanager.cpython-312.pyc,,
xarray/core/__pycache__/dataarray.cpython-312.pyc,,
xarray/core/__pycache__/dataset.cpython-312.pyc,,
xarray/core/__pycache__/dtypes.cpython-312.pyc,,
xarray/core/__pycache__/duck_array_ops.cpython-312.pyc,,
xarray/core/__pycache__/extensions.cpython-312.pyc,,
xarray/core/__pycache__/formatting.cpython-312.pyc,,
xarray/core/__pycache__/formatting_html.cpython-312.pyc,,
xarray/core/__pycache__/groupby.cpython-312.pyc,,
xarray/core/__pycache__/indexes.cpython-312.pyc,,
xarray/core/__pycache__/indexing.cpython-312.pyc,,
xarray/core/__pycache__/merge.cpython-312.pyc,,
xarray/core/__pycache__/missing.cpython-312.pyc,,
xarray/core/__pycache__/nanops.cpython-312.pyc,,
xarray/core/__pycache__/npcompat.cpython-312.pyc,,
xarray/core/__pycache__/nputils.cpython-312.pyc,,
xarray/core/__pycache__/ops.cpython-312.pyc,,
xarray/core/__pycache__/options.cpython-312.pyc,,
xarray/core/__pycache__/parallel.cpython-312.pyc,,
xarray/core/__pycache__/parallelcompat.cpython-312.pyc,,
xarray/core/__pycache__/pdcompat.cpython-312.pyc,,
xarray/core/__pycache__/pycompat.cpython-312.pyc,,
xarray/core/__pycache__/resample.cpython-312.pyc,,
xarray/core/__pycache__/resample_cftime.cpython-312.pyc,,
xarray/core/__pycache__/rolling.cpython-312.pyc,,
xarray/core/__pycache__/rolling_exp.cpython-312.pyc,,
xarray/core/__pycache__/types.cpython-312.pyc,,
xarray/core/__pycache__/utils.cpython-312.pyc,,
xarray/core/__pycache__/variable.cpython-312.pyc,,
xarray/core/__pycache__/weighted.cpython-312.pyc,,
xarray/core/_aggregations.py,sha256=4VsfohNeYL_YdLM6FCaa5VMeLWN7dDiCHRBng5uVx4k,293555
xarray/core/_typed_ops.py,sha256=fGWJcY3hg1QNrI4_RBiRfgyOn4_F_A92oclj0HDU1I4,28533
xarray/core/_typed_ops.pyi,sha256=nMDz0eZ4XqpVH9SCUzGZydH62gdL_pG4DTYGg3A2r0k,32142
xarray/core/accessor_dt.py,sha256=LQMZ_hdda5UB-JG6LeV1ctTJHWZlnS_RqxhCtmBB_Oo,20311
xarray/core/accessor_str.py,sha256=Kp6Vd5X9t7wnYNZJm71zDcOb8nuBszOG8U0nXCJXzg4,99007
xarray/core/alignment.py,sha256=wx44_oaGho2_5kBAEEeNmLqAK5C2roPy8OKFGSEZgcU,37538
xarray/core/arithmetic.py,sha256=iOlY4l9Fo3KmZT4rJz5z0uHvvTdp8tAovU4ey3hxUi8,4406
xarray/core/combine.py,sha256=xZGs6b7glu-LWISizXAg89Kq4WjHrl0tOJhgX4KWnIw,37464
xarray/core/common.py,sha256=QKa6JEi5RCPBqITrPPigrMFrjD_E1uJhaaxrTZqQqcA,68915
xarray/core/computation.py,sha256=BSXaesDCzbVCwTTo-ruEbN0F5w5GVEa9g8Sc2mApSPg,75278
xarray/core/concat.py,sha256=DMC7tl4fn0ECfH3aBRl8akMxRusnWMWz2eSxQmQRPNA,28496
xarray/core/coordinates.py,sha256=UgRD1WH3N8PyOd8Z_58jJ0h5pphMdZCjTEIZfN9TY1I,16433
xarray/core/dask_array_ops.py,sha256=n7B7Z-fV8XiB-3QBenlS6XdHDxZ7v0H1k_mAbNsnqb8,3078
xarray/core/daskmanager.py,sha256=AfNSidLi_gbMoU1Jb06qM27pWYhlZb1g6ZfcXusg-U0,6190
xarray/core/dataarray.py,sha256=YOSvRsOcwqyI2xiuLrpZDbn1JFVvyftwy_8nCoV4LeM,263588
xarray/core/dataset.py,sha256=WV0fiB0quCsy2cJ6m82_SMqyTDpbLXuIOTEmoqbqxNU,360536
xarray/core/dtypes.py,sha256=C9DX_78cLwNI4IW-wzAMjEAW7FqZYtcrUSFVTKZb4kI,5045
xarray/core/duck_array_ops.py,sha256=u7Ns2s0UzPtLT4yxH8V-4H2d9HBynaNBq1jjC9yFQw0,23633
xarray/core/extensions.py,sha256=U710R2o9XEdAFxiijre6VTH47580H5DfNlf0boVK2E0,3516
xarray/core/formatting.py,sha256=LCwB0dLsHnY_1K00CMxWseDPrBoLWETpSKnARv-ftOs,27908
xarray/core/formatting_html.py,sha256=_uwryEuo8XQEIbxybLdydqoVhzGe_GqjWEUJrCCx5K0,10214
xarray/core/groupby.py,sha256=zOT2zKDguQeZ1t1PsOxhPwGC_0PuspofHic5ixrQebk,56933
xarray/core/indexes.py,sha256=zIuvqS1OAuONU_5o4XQLBJaJkOorpeX7z5RkvmCG-Gc,54152
xarray/core/indexing.py,sha256=IvL2ebrXGA3jJlFs5IrReivpGHkW1JYdzKHOYYL76iI,57900
xarray/core/merge.py,sha256=Ijz_MbvGuRKjemcXvpitdhOgctR7ndKpOSxF9oeY3zQ,39754
xarray/core/missing.py,sha256=Drzz9la2PCwyPUD76zXIgdyJir8Sb8YmxgcgQ6QyQMM,26507
xarray/core/nanops.py,sha256=ENOR9qc8oTPo001p_ePHNmL95wvay6L02LujjqvyBU8,5644
xarray/core/npcompat.py,sha256=ti172we0ypdAR3K4bYoUpZ7_wj-e5nF6WY3mDZ8UnuY,1593
xarray/core/nputils.py,sha256=L696hrdVr3JAAa-XyZ1FMeAgMXPvOXCz2kkIRktz41I,8209
xarray/core/ops.py,sha256=wRU7WR5B8ppgsviLdFODIGkXdzv3mRE3sRnQiwwEcHs,9973
xarray/core/options.py,sha256=ecKmH9ixO3etOp65-04Nre8oWbjN9-GCOP8fvTHdkHA,11050
xarray/core/parallel.py,sha256=xeWmGMktmC1t3IeedRJFfpuHcWLvVdlZDqnfr9Gd6IU,22317
xarray/core/parallelcompat.py,sha256=ze--GabaHjpwtNCmsHZsStVst30YhwXEnbWRaywwPIo,9105
xarray/core/pdcompat.py,sha256=yt4kohPH9bUMg00gk_RWz60WlvVmqpKn2DOose3HR9c,3903
xarray/core/pycompat.py,sha256=48AwhW4CT0Cj13lp6S2xDBoLC89ExT-SyeASAMEFYjY,3159
xarray/core/resample.py,sha256=gkzyrkWxd2SPp48Mg6MXFih93DsGMAfyKrE6ZLi4ZVs,14121
xarray/core/resample_cftime.py,sha256=pm9jHdtudP7v32nxdYv0Nacj5Ser48p5qK8hAy2fKgE,19469
xarray/core/rolling.py,sha256=3v5jSWwJd-rsdLpO3QNbklUnQv0Cr8mNjH2nvAsR_3A,40165
xarray/core/rolling_exp.py,sha256=LFEzmyXpk7IxUljnf9MnxiBLFFFNl3_8bGQkHd08S0M,5745
xarray/core/types.py,sha256=_Zgh-5qvw1mnGCU1MNvNIDuz-bIoRS9fhz0f5pmzvvY,6750
xarray/core/utils.py,sha256=TYswtfSKaGpk58IaOMRw8s0_dU7AF6kBRr4uHYTMDYI,37261
xarray/core/variable.py,sha256=v6y9bpQnUcMKpczKf-Ex7fjQ8klXo3c7Nx-LWRzVxOI,122275
xarray/core/weighted.py,sha256=l8l_3zWIbYPALaHO9yNiWa5gJ6Nn6h5w27W5pT6Jblw,19161
xarray/indexes/__init__.py,sha256=LyEnC1a_R93SiSHoqc2blarnjl90LMHmasrFoxkNK4o,226
xarray/indexes/__pycache__/__init__.cpython-312.pyc,,
xarray/plot/__init__.py,sha256=aBfyHSoR6BELAj9fOgokx1L8u7_PklpsvGIilfD0rYs,573
xarray/plot/__pycache__/__init__.cpython-312.pyc,,
xarray/plot/__pycache__/accessor.cpython-312.pyc,,
xarray/plot/__pycache__/dataarray_plot.cpython-312.pyc,,
xarray/plot/__pycache__/dataset_plot.cpython-312.pyc,,
xarray/plot/__pycache__/facetgrid.cpython-312.pyc,,
xarray/plot/__pycache__/utils.cpython-312.pyc,,
xarray/plot/accessor.py,sha256=VVu9I6DxLM_ci5NurtwkvsIkM3mR2bS2bYYG5lyS7xI,41974
xarray/plot/dataarray_plot.py,sha256=qqISjMocdBGhBPhFGbPBqi1bm3cqTohbNQ_-mGb4FKE,86031
xarray/plot/dataset_plot.py,sha256=y0j7BEVODwepEVzwNdpOXSUKsCTiRI-GUrb-N4OPU34,30818
xarray/plot/facetgrid.py,sha256=kqWIXcb-uMBkUnVhF7NWp8WMTao_5o4qKYqPmQIztSc,37227
xarray/plot/utils.py,sha256=OewJOVnx8uzD3C_WVrJ0ZW0NLtOmIf32LQAGt2J7eKU,59520
xarray/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/__pycache__/__init__.cpython-312.pyc,,
xarray/static/css/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/css/__pycache__/__init__.cpython-312.pyc,,
xarray/static/css/style.css,sha256=1wPCQ-JguavclDWCLMGcenTw9RaR9pDgnq6tAYol4hQ,6075
xarray/static/html/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/static/html/__pycache__/__init__.cpython-312.pyc,,
xarray/static/html/icons-svg-inline.html,sha256=t-ChbtS1Gv8uZxc31DCJS8SuXDsLGUHoKgwv8zu6j2M,1343
xarray/testing.py,sha256=Mh5HQbLzmRFHn6FAPaRndadukM2usTgF5tfMkbHFjGo,14507
xarray/tests/__init__.py,sha256=_5RDs3dOO537fNZwDnXQNpeCwJo8moFbXrEDAVIHz7Q,8789
xarray/tests/__pycache__/__init__.cpython-312.pyc,,
xarray/tests/__pycache__/conftest.cpython-312.pyc,,
xarray/tests/__pycache__/test_accessor_dt.cpython-312.pyc,,
xarray/tests/__pycache__/test_accessor_str.cpython-312.pyc,,
xarray/tests/__pycache__/test_array_api.cpython-312.pyc,,
xarray/tests/__pycache__/test_backends.cpython-312.pyc,,
xarray/tests/__pycache__/test_backends_api.cpython-312.pyc,,
xarray/tests/__pycache__/test_backends_common.cpython-312.pyc,,
xarray/tests/__pycache__/test_backends_file_manager.cpython-312.pyc,,
xarray/tests/__pycache__/test_backends_locks.cpython-312.pyc,,
xarray/tests/__pycache__/test_backends_lru_cache.cpython-312.pyc,,
xarray/tests/__pycache__/test_calendar_ops.cpython-312.pyc,,
xarray/tests/__pycache__/test_cftime_offsets.cpython-312.pyc,,
xarray/tests/__pycache__/test_cftimeindex.cpython-312.pyc,,
xarray/tests/__pycache__/test_cftimeindex_resample.cpython-312.pyc,,
xarray/tests/__pycache__/test_coarsen.cpython-312.pyc,,
xarray/tests/__pycache__/test_coding.cpython-312.pyc,,
xarray/tests/__pycache__/test_coding_strings.cpython-312.pyc,,
xarray/tests/__pycache__/test_coding_times.cpython-312.pyc,,
xarray/tests/__pycache__/test_combine.cpython-312.pyc,,
xarray/tests/__pycache__/test_computation.cpython-312.pyc,,
xarray/tests/__pycache__/test_concat.cpython-312.pyc,,
xarray/tests/__pycache__/test_conventions.cpython-312.pyc,,
xarray/tests/__pycache__/test_cupy.cpython-312.pyc,,
xarray/tests/__pycache__/test_dask.cpython-312.pyc,,
xarray/tests/__pycache__/test_dataarray.cpython-312.pyc,,
xarray/tests/__pycache__/test_dataset.cpython-312.pyc,,
xarray/tests/__pycache__/test_deprecation_helpers.cpython-312.pyc,,
xarray/tests/__pycache__/test_distributed.cpython-312.pyc,,
xarray/tests/__pycache__/test_dtypes.cpython-312.pyc,,
xarray/tests/__pycache__/test_duck_array_ops.cpython-312.pyc,,
xarray/tests/__pycache__/test_extensions.cpython-312.pyc,,
xarray/tests/__pycache__/test_formatting.cpython-312.pyc,,
xarray/tests/__pycache__/test_formatting_html.cpython-312.pyc,,
xarray/tests/__pycache__/test_groupby.cpython-312.pyc,,
xarray/tests/__pycache__/test_indexes.cpython-312.pyc,,
xarray/tests/__pycache__/test_indexing.cpython-312.pyc,,
xarray/tests/__pycache__/test_interp.cpython-312.pyc,,
xarray/tests/__pycache__/test_merge.cpython-312.pyc,,
xarray/tests/__pycache__/test_missing.cpython-312.pyc,,
xarray/tests/__pycache__/test_nputils.cpython-312.pyc,,
xarray/tests/__pycache__/test_options.cpython-312.pyc,,
xarray/tests/__pycache__/test_parallelcompat.cpython-312.pyc,,
xarray/tests/__pycache__/test_plot.cpython-312.pyc,,
xarray/tests/__pycache__/test_plugins.cpython-312.pyc,,
xarray/tests/__pycache__/test_print_versions.cpython-312.pyc,,
xarray/tests/__pycache__/test_rolling.cpython-312.pyc,,
xarray/tests/__pycache__/test_sparse.cpython-312.pyc,,
xarray/tests/__pycache__/test_testing.cpython-312.pyc,,
xarray/tests/__pycache__/test_tutorial.cpython-312.pyc,,
xarray/tests/__pycache__/test_ufuncs.cpython-312.pyc,,
xarray/tests/__pycache__/test_units.cpython-312.pyc,,
xarray/tests/__pycache__/test_utils.cpython-312.pyc,,
xarray/tests/__pycache__/test_variable.cpython-312.pyc,,
xarray/tests/__pycache__/test_weighted.cpython-312.pyc,,
xarray/tests/conftest.py,sha256=yC3SnQny6_Fe06xWVSPvgzJSCHSzbeNrTxbb1Z5jkw4,2145
xarray/tests/data/bears.nc,sha256=912tQ5fHIS-VDTBe3UplQi2rdRcreMSQ0tIdCOg9FRI,1184
xarray/tests/data/example.grib,sha256=kCvzClBl8_r_G5Nhzpd4Zn-haB_rdNND1PF6rkmKsm0,5232
xarray/tests/data/example.ict,sha256=h53gvFYHQyb-9NOAulIsVYUOwKy5xZZxhNHXAg7L8mE,783
xarray/tests/data/example.uamiv,sha256=a3-OlPxWaY6iaOkLPzTVrYwE3mZDF7zvh-6CK8d5q3o,608
xarray/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
xarray/tests/data/example_1.nc.gz,sha256=2I3vFd3bUqecY6G0EpIrMMkRLrow03SUQXYRC6UjJh4,470
xarray/tests/test_accessor_dt.py,sha256=VPHdcH9gV2yoO9aMEKJtOglWFFGyYRLcR2sx7IrtooU,20566
xarray/tests/test_accessor_str.py,sha256=LtjU_9wqUh7LPg8zO0R75LtU6UVwxYxnfg6YFMMp_IY,121928
xarray/tests/test_array_api.py,sha256=_eYOpUKftTFUXhHkBFV4GyVeUvaVBVdHRXYUirgGWB0,3705
xarray/tests/test_backends.py,sha256=20ZgaXFPj6ClgzHdfUx8SccoDfbA6K4Tfs3CNbngv8c,206336
xarray/tests/test_backends_api.py,sha256=DHRNxPx8wyW7FT67EH2keSlrVnqGJgvF_hOtnqeMOhk,7152
xarray/tests/test_backends_common.py,sha256=L2-Cx4fk5UUOMOFrL8FoWvu0huW4AOl-zKWeFWB7Ul4,774
xarray/tests/test_backends_file_manager.py,sha256=RDP-r1u40FGXutz0nvIl-pdxo0x6TVEndpIr6qp01yY,7176
xarray/tests/test_backends_locks.py,sha256=RYLmZIBFvQgmxIB8jFrOdw4GYzwFDd_gqvz5lUiZ_sI,366
xarray/tests/test_backends_lru_cache.py,sha256=gPDx1ognk_SzTJVWrgdPY8wTi7PAdeDPFRpQZCAXjek,2300
xarray/tests/test_calendar_ops.py,sha256=jM2Pdmpo_vEHzvxdXigoFDIRfv4U2ZzcikxQyHK8G9c,7536
xarray/tests/test_cftime_offsets.py,sha256=2yZ1fhLxMXfPZOvkk4hEWM_Z0nF3f7H1TEF6zT6v0pg,44969
xarray/tests/test_cftimeindex.py,sha256=NC_P3Gkpwk5JdtF3twBw-NAUgNjFEb2WpRb4k31owiM,40106
xarray/tests/test_cftimeindex_resample.py,sha256=WA6od-08irjWqKZSKbLcG9CkXKnAerA_Lu97ftU1o4g,9248
xarray/tests/test_coarsen.py,sha256=BS-_CWBiDaaOFZBFBSOLu80MhgqYwLIHPlnkYG9I6t4,11478
xarray/tests/test_coding.py,sha256=NvZZ6Hs3BUeCbnvNTLjoSVexkmTwOwFDVd4FLiicU6M,5162
xarray/tests/test_coding_strings.py,sha256=DQC4hVqpQngOvGOhVbo4Z6ZUAE1_Is5RIQoYSrLIcxs,8237
xarray/tests/test_coding_times.py,sha256=OYF2seNwx4oadYeVGQOgillc3RmNa8vdhjqBzgq-yJU,43161
xarray/tests/test_combine.py,sha256=mDVe0kNsO4ZA01QxJIny_lQ3VIIQ2Tzc9iTIodChPMw,44271
xarray/tests/test_computation.py,sha256=l2aKv7UwJxDKdShIiWqvkvBqSXya7C20qxROblnEPME,77115
xarray/tests/test_concat.py,sha256=vW9Ty-MqlIhNtnaUTYMnyIV1cHDBg79zXbKIDlhlMVU,46051
xarray/tests/test_conventions.py,sha256=60khgoZwQuKD5VaTZV35BL5OvaAk38qrRZO6exRFChw,19481
xarray/tests/test_cupy.py,sha256=DVQOX2JEO8VLeKRRU78UPnUqoC-8damScHevr6V1sWM,1681
xarray/tests/test_dask.py,sha256=VNfL6OaOYn66L3oM_Sk9aJWm59YY5nS45k2AECL9-Nk,60630
xarray/tests/test_dataarray.py,sha256=9QgneknALUmvXeYTGUS0y96U1l6RUtmYWFdNtTMEF8Y,255487
xarray/tests/test_dataset.py,sha256=pqfJUuJSpOnD1KnXK4C5sBU5CoTOm8Q22M2J26DTu3M,261289
xarray/tests/test_deprecation_helpers.py,sha256=4QcdA-lWSMe0EM9Gppq-nNBJuwk_6Ebd1bStJ7hXyAI,4300
xarray/tests/test_distributed.py,sha256=6U2pZwVpOeH28-fT8KkrK7cEr9EeCC3yGWMIBC0gqaI,7699
xarray/tests/test_dtypes.py,sha256=EHE7JflXa3vjSz6uYp0ddBb9VgCm_AnfrTGYBOSWSao,3520
xarray/tests/test_duck_array_ops.py,sha256=WjDjU8stQO05DiOjYWv6k5scxwg5xkIfDYG-1LMhGmY,30666
xarray/tests/test_extensions.py,sha256=1o2DlGRgrIm4opTdLMBp2eoXy1FDRJ7thChBcX0YVQg,2737
xarray/tests/test_formatting.py,sha256=q9iyBKnRcrvdiOICv1wweSY1kAlp80qri-wPyqVP-AQ,24804
xarray/tests/test_formatting_html.py,sha256=-aFAA0qO7E061dq8Pr2NbAZ_pNhjn4O4POOo7kyFI1c,6549
xarray/tests/test_groupby.py,sha256=EQoExIlHvvA2D-gDMv10mPgnwUrM5DBSw60HL0PMJMs,85622
xarray/tests/test_indexes.py,sha256=UrmImGvf60L8eyrH55ZKZPMV9XrbaPXBbrvb1gKZ4Wg,25785
xarray/tests/test_indexing.py,sha256=Th0pTxptdslC0tVqSVnrrDuZuJkDzLTlhdC8p-Oy6Ss,31184
xarray/tests/test_interp.py,sha256=g8daLytmCjU_xhePwN0E2_JKKMtCqiCaMeTMhRF1QLQ,29970
xarray/tests/test_merge.py,sha256=6Nsp76gYucaneXJ9v3bHNmnldUJNacmtb9YOgJR8uwI,18119
xarray/tests/test_missing.py,sha256=HpgBj-oFbSfW0y81U-hSQlA9FsnkHMt79li9px14q44,23834
xarray/tests/test_nputils.py,sha256=KAes7xQZSusV-ReKD0eJISkel1AWTeKf5q3lkQwtUtA,987
xarray/tests/test_options.py,sha256=_tzhfwHAyondxAcIJ4dSOH4pfHOVII1mpLY-F_glFFE,7670
xarray/tests/test_parallelcompat.py,sha256=D26m3Q_QrazNCDq220MEmABSTrnca6QMDN2sEzTs9IY,6984
xarray/tests/test_plot.py,sha256=CHNLjPGzDN4D7GCUP3TpdDt5hvHu9x0vY76aiDdGtSM,119180
xarray/tests/test_plugins.py,sha256=zFqG6Yo-yXt42JUZ-8wHiGYRRGcVNatrNwU72F7duww,10361
xarray/tests/test_print_versions.py,sha256=TV6fQHajYW71VLvQRpemQQv8trMlFYrXRXwLTFRi7YM,200
xarray/tests/test_rolling.py,sha256=2j_KWVfl1nZFSoT20FdHUtZ6s3_bkyHaPgeIV0Kn-_c,31207
xarray/tests/test_sparse.py,sha256=bP_mcRVMfG1errRPZ6iXnWwp0FkVSHa7cnPdgdfFUlA,28760
xarray/tests/test_testing.py,sha256=sv36yJZuPjFz_bOr27Pg3KmGg74fs3Tqo6GyHuAKG10,4723
xarray/tests/test_tutorial.py,sha256=7hg-CrO0WYSjq4-WwwHKHRaQc28cZOgb51mp45AQ2jM,982
xarray/tests/test_ufuncs.py,sha256=ajzoPF3MwvhGxtxgO4eD0BYtpfvsw-Kkt1NubiG_9g4,4731
xarray/tests/test_units.py,sha256=OMy9Ap2ndbv1xH-e7UV3xo6nPgsEaCdJuv0IXbgqgjA,189039
xarray/tests/test_utils.py,sha256=wIFxQLp5M_5mLkBVipkahAQSvxdnR_7BGFSUyfTEj0k,11253
xarray/tests/test_variable.py,sha256=dc5CCaXkO-IdfkcJ6Ivk6mufWXMgdJUO1R_No5KGt44,111128
xarray/tests/test_weighted.py,sha256=TVwmwkRQbV060q6h_oEIs_nzB2AAWtum64bx4nYPx08,24189
xarray/tutorial.py,sha256=Jp3_s_pWegwgNc5EWI7C1NqKI6IQoaLf_QPyBtMBLUU,7254
xarray/util/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xarray/util/__pycache__/__init__.cpython-312.pyc,,
xarray/util/__pycache__/deprecation_helpers.cpython-312.pyc,,
xarray/util/__pycache__/generate_aggregations.cpython-312.pyc,,
xarray/util/__pycache__/generate_ops.cpython-312.pyc,,
xarray/util/__pycache__/print_versions.cpython-312.pyc,,
xarray/util/deprecation_helpers.py,sha256=fy1XPhYY-aSCAjd4R6AVl4OupkQffJQ9gP4a4Dpzang,4272
xarray/util/generate_aggregations.py,sha256=c9AK-1EAY0zEptmmqU5vdyaANf1tvIWJdYPT3lAMy80,17583
xarray/util/generate_ops.py,sha256=2Ri-g_cO6js4uYNmS2OLG0sB9LihTSVXU_82TR060lU,9354
xarray/util/print_versions.py,sha256=mXN0AfFFw1Rp3CTKpOrV5B8MTy-Xt1Sk9ffqVdhcmeE,5219
