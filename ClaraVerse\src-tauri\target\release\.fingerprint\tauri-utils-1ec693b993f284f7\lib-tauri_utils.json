{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 367816849085071872, "path": 12427970797809321433, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 11194595537583971047], [3150220818285335163, "url", false, 1318321174758569141], [3191507132440681679, "serde_untagged", false, 12874421104460377238], [4071963112282141418, "serde_with", false, 10688897353556629355], [4899080583175475170, "semver", false, 13178651444652797232], [5986029879202738730, "log", false, 5444832254770226198], [6606131838865521726, "ctor", false, 5310961280411030913], [7170110829644101142, "json_patch", false, 5202980725447740791], [8319709847752024821, "uuid", false, 1872584820895421030], [9010263965687315507, "http", false, 3748586209917174119], [9451456094439810778, "regex", false, 2655598239937456584], [9556762810601084293, "brotli", false, 14154120633352452173], [9689903380558560274, "serde", false, 415033232941934989], [10806645703491011684, "thiserror", false, 12697263202037361942], [11989259058781683633, "dunce", false, 17489086241525168324], [13625485746686963219, "anyhow", false, 16250987596267273021], [15367738274754116744, "serde_json", false, 16545619236785180510], [15609422047640926750, "toml", false, 14030983771938319729], [15622660310229662834, "walkdir", false, 11530537379731878862], [15932120279885307830, "memchr", false, 13287116547952953302], [17146114186171651583, "infer", false, 11941494544237643805], [17155886227862585100, "glob", false, 2701769016738312214], [17186037756130803222, "phf", false, 16246140824276824635]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-1ec693b993f284f7\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}