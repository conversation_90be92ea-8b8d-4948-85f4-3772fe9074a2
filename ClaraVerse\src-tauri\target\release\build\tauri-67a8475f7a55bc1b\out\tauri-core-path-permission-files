["\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\C:\\Users\\<USER>\\Documents\\appli\\ClaraVerse\\src-tauri\\target\\release\\build\\tauri-67a8475f7a55bc1b\\out\\permissions\\path\\autogenerated\\default.toml"]