{"rustc": 16591470773350601817, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 367816849085071872, "path": 1574582203216649921, "deps": [[2779309023524819297, "aho_corasick", false, 16029392829927983407], [9408802513701742484, "regex_syntax", false, 9270184084865196698], [15932120279885307830, "memchr", false, 13287116547952953302]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-automata-b2893fe46cae077c\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}