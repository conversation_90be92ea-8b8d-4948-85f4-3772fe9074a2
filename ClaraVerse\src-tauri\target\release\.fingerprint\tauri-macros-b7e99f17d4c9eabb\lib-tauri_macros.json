{"rustc": 16591470773350601817, "features": "[\"compression\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 17984201634715228204, "path": 18391740974206782255, "deps": [[2713742371683562785, "syn", false, 6839976677991131985], [3060637413840920116, "proc_macro2", false, 1268470941054599307], [8292277814562636972, "tauri_utils", false, 6722458540902675132], [13077543566650298139, "heck", false, 14698631188825528352], [17492769205600034078, "tauri_codegen", false, 2307843630706747250], [17990358020177143287, "quote", false, 10852422733387940731]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-b7e99f17d4c9eabb\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}