../../Scripts/rapidocr_onnxruntime.exe,sha256=mQ7OigYyVOG-pA3_0ps8cFtxBqkpi4rL8ZFNXo3jULM,108395
rapidocr_onnxruntime-1.4.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
rapidocr_onnxruntime-1.4.4.dist-info/METADATA,sha256=GsFb7Xx_t30_rFjCq6Ck0-LaSN5cZbrdNQOQCs688d8,1264
rapidocr_onnxruntime-1.4.4.dist-info/RECORD,,
rapidocr_onnxruntime-1.4.4.dist-info/WHEEL,sha256=tZoeGjtWxWRfdplE7E3d45VPlLNQnvbKiYnx7gwAy8A,92
rapidocr_onnxruntime-1.4.4.dist-info/entry_points.txt,sha256=tn2kAawJPRQkcMthw_n7xITGNcAipn_zYppu2LJKsXs,72
rapidocr_onnxruntime-1.4.4.dist-info/top_level.txt,sha256=wuq8r1w2Vih-jQZuS9M_u-ZqJKfzOQEllC68Vml8Kog,21
rapidocr_onnxruntime/__init__.py,sha256=JeeFkTXTBq9etiYIbiIRqbTaAc_xMnvrD7IpjcujOlE,143
rapidocr_onnxruntime/__pycache__/__init__.cpython-312.pyc,,
rapidocr_onnxruntime/__pycache__/main.cpython-312.pyc,,
rapidocr_onnxruntime/cal_rec_boxes/__init__.py,sha256=SF1bygzYTIDqRXb_TtXpBoKIUbKHxYHHRqmicqfOlFw,104
rapidocr_onnxruntime/cal_rec_boxes/__pycache__/__init__.cpython-312.pyc,,
rapidocr_onnxruntime/cal_rec_boxes/__pycache__/main.cpython-312.pyc,,
rapidocr_onnxruntime/cal_rec_boxes/main.py,sha256=18-csB8Ymoc875iXFQ43kIVgj5kIpyiIV-olmf9-7nY,10493
rapidocr_onnxruntime/ch_ppocr_cls/__init__.py,sha256=rq8ZtJ-2taNhVHVE58uYly3QtnVumtMg2f5wCIka6bI,111
rapidocr_onnxruntime/ch_ppocr_cls/__pycache__/__init__.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_cls/__pycache__/text_cls.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_cls/__pycache__/utils.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_cls/text_cls.py,sha256=6tVV3et6ig2gZ_TS-eJ2DpPbnkbZBoiOBSInfdpmIMg,4229
rapidocr_onnxruntime/ch_ppocr_cls/utils.py,sha256=ptPeuq8hsJJzGyCiSbgWUSBwuOky23sPFdVTbc2iMoo,1025
rapidocr_onnxruntime/ch_ppocr_det/__init__.py,sha256=jzPxgedoCcYJ6tj11Er-0eg_dOdSI5wPMMqWH98VLmM,112
rapidocr_onnxruntime/ch_ppocr_det/__pycache__/__init__.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_det/__pycache__/text_detect.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_det/__pycache__/utils.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_det/text_detect.py,sha256=_J1jukeUDT5b_v5nOPMQtYPNTeRwj-_hFPzhEjbywZk,4556
rapidocr_onnxruntime/ch_ppocr_det/utils.py,sha256=CzQFOQw3x9bwyccJnu7DawULjg3udln72CpzvL0mXNA,7930
rapidocr_onnxruntime/ch_ppocr_rec/__init__.py,sha256=mJ9unodQj5HN_ElsfE73WAu7bMGg-HqQ4TkCnRCXDeA,117
rapidocr_onnxruntime/ch_ppocr_rec/__pycache__/__init__.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_rec/__pycache__/text_recognize.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_rec/__pycache__/utils.cpython-312.pyc,,
rapidocr_onnxruntime/ch_ppocr_rec/text_recognize.py,sha256=SNhlbRFAPQmy7TK2WmhpBrlQLKGk4Wo5NNkbNRKdAyo,4700
rapidocr_onnxruntime/ch_ppocr_rec/utils.py,sha256=Sh3C6GAtEjdLEhJLvpmhtCCA1yozd5l2C-rw1RoyypA,6907
rapidocr_onnxruntime/config.yaml,sha256=v5Sh2ky6go5nsdYeJ87hTZ59onyfJy4EBIoX5BrpczI,1221
rapidocr_onnxruntime/main.py,sha256=FWxJ5oP93SbwQoDzkfiCuFenTS8CBJeHD33Y2FDtWiw,12799
rapidocr_onnxruntime/models/ch_PP-OCRv4_det_infer.onnx,sha256=0qdyDUWlQlcgix4T42qEeYlMt0FVpe_ilGJRLUL0nak,4745517
rapidocr_onnxruntime/models/ch_PP-OCRv4_rec_infer.onnx,sha256=SPxA8k9tKiB6KxCR00N-s8w-trZ23D75w3OEAFSDaDs,10857958
rapidocr_onnxruntime/models/ch_ppocr_mobile_v2.0_cls_infer.onnx,sha256=5HrO32YyMPiGP_GrDmTdLYK4OPzrWVcUbasYWonWIVw,585532
rapidocr_onnxruntime/utils/__init__.py,sha256=JiOAj49UBFLMApUYWMtl712s1hW1QJ7GyXC8N1sflG0,619
rapidocr_onnxruntime/utils/__pycache__/__init__.cpython-312.pyc,,
rapidocr_onnxruntime/utils/__pycache__/infer_engine.cpython-312.pyc,,
rapidocr_onnxruntime/utils/__pycache__/load_image.cpython-312.pyc,,
rapidocr_onnxruntime/utils/__pycache__/logger.cpython-312.pyc,,
rapidocr_onnxruntime/utils/__pycache__/parse_parameters.cpython-312.pyc,,
rapidocr_onnxruntime/utils/__pycache__/process_img.cpython-312.pyc,,
rapidocr_onnxruntime/utils/__pycache__/vis_res.cpython-312.pyc,,
rapidocr_onnxruntime/utils/infer_engine.py,sha256=PkAslQJqt5NTOw3ilrC36JetUGGNCJsrIhS9kIM8Pyk,8152
rapidocr_onnxruntime/utils/load_image.py,sha256=IzUXG_BXS1b8TW1yYb2b8NyVXbbOw7Wq8GDTRSVuZaQ,3755
rapidocr_onnxruntime/utils/logger.py,sha256=Lvytnw96-3fGc02ewvzOYPMEWz5s5-R1L2ntnh5kfxc,510
rapidocr_onnxruntime/utils/parse_parameters.py,sha256=pZB4-ERknpsIY4NFoyzTmBon_XMfT4RKEVX4UMt6bJ4,7476
rapidocr_onnxruntime/utils/process_img.py,sha256=xUv9fLWS5Ax76QQvzkpA5fpTWejBGibDSSU0Bl9C8jY,2145
rapidocr_onnxruntime/utils/vis_res.py,sha256=UiXP2UX9mZaM_iiDlglJ4EPXYKjQ2rkuyRLpiwHkIgU,5077
