warc/__init__.py,sha256=AO19mPZ0gWpNsMuuEsaGJDc0ETCvSb65_5E-qe59_1s,1010
warc/__pycache__/__init__.cpython-312.pyc,,
warc/__pycache__/arc.cpython-312.pyc,,
warc/__pycache__/utils.cpython-312.pyc,,
warc/__pycache__/warc.cpython-312.pyc,,
warc/arc.py,sha256=H97j3FDsIF7MDhc9Bz5kEMJkEUbPaIyxaPeUvsB7b74,15651
warc/utils.py,sha256=CQyVfqY0BtH0BiFarU-UcWCvMOpF81mJ4vtExO6bu3o,2519
warc/warc.py,sha256=vNO3M0LE8YiDwRCOT9Vj-4MnMP9Nj8fiXEI5xzrc1CY,13196
warc3_wet-0.2.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
warc3_wet-0.2.5.dist-info/LICENSE,sha256=gXf5dRMhNSbfLPYYTY_5hsZ1r7UU1OaKQEAQUhuIBkM,18092
warc3_wet-0.2.5.dist-info/METADATA,sha256=JbQcoXRDDPj2y-ANUfnRPbTunnczJI1K0IHeHaQ6Klg,2189
warc3_wet-0.2.5.dist-info/RECORD,,
warc3_wet-0.2.5.dist-info/WHEEL,sha256=Z4pYXqR_rTB7OWNDYFOm1qRk0RX6GFP2o8LgvP453Hk,91
warc3_wet-0.2.5.dist-info/top_level.txt,sha256=BxIsumNkTK4ToJymQPoiWNPnWHyeFeTeDMS6Lr-3h0I,5
