{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 367816849085071872, "path": 929282023312626405, "deps": [[1462335029370885857, "quick_xml", false, 14909701344024397928], [3334271191048661305, "windows_version", false, 11266095270351896108], [10806645703491011684, "thiserror", false, 12697263202037361942], [14585479307175734061, "windows", false, 14759611550065284233]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winrt-notification-8d3e69f18600ea7b\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}