const fs = require('fs');
const path = require('path');

console.log('📁 COPIE AUTOMATIQUE VERS TÉLÉCHARGEMENTS');
console.log('=========================================');

// Chemins des fichiers générés
const bundlePath = 'src-tauri/target/release/bundle';
const nsisFile = path.join(bundlePath, 'nsis', 'WeMa IA_0.1.2_x64-setup.exe');
const msiFile = path.join(bundlePath, 'msi', 'WeMa IA_0.1.2_x64_en-US.msi');

// Dossier de destination
const downloadsPath = path.join(process.env.USERPROFILE, 'Downloads');

// Générer un timestamp pour les noms de fichiers
const now = new Date();
const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);

try {
    // Copier le fichier NSIS (EXE)
    if (fs.existsSync(nsisFile)) {
        const destExe = path.join(downloadsPath, `WeMa_IA_v0.1.2_${timestamp}.exe`);
        fs.copyFileSync(nsisFile, destExe);
        
        const stats = fs.statSync(destExe);
        const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
        
        console.log(`✅ EXE copié: ${path.basename(destExe)}`);
        console.log(`📏 Taille: ${sizeMB} MB`);
        console.log(`📁 Emplacement: ${destExe}`);
    } else {
        console.log('❌ Fichier EXE non trouvé:', nsisFile);
    }
    
    // Copier le fichier MSI
    if (fs.existsSync(msiFile)) {
        const destMsi = path.join(downloadsPath, `WeMa_IA_v0.1.2_${timestamp}.msi`);
        fs.copyFileSync(msiFile, destMsi);
        
        const stats = fs.statSync(destMsi);
        const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
        
        console.log(`✅ MSI copié: ${path.basename(destMsi)}`);
        console.log(`📏 Taille: ${sizeMB} MB`);
        console.log(`📁 Emplacement: ${destMsi}`);
    } else {
        console.log('❌ Fichier MSI non trouvé:', msiFile);
    }
    
    console.log('\n🎉 COPIE TERMINÉE !');
    console.log('Les fichiers sont maintenant disponibles dans Téléchargements');
    
} catch (error) {
    console.error('❌ Erreur lors de la copie:', error.message);
    process.exit(1);
}
