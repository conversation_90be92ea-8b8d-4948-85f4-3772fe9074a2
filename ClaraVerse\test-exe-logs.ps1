#!/usr/bin/env powershell
# Script de test pour voir les logs de l'EXE WeMa IA

Write-Host "🚀 TEST EXE WEMA IA AVEC LOGS" -ForegroundColor Green
Write-Host "=" * 50

# Chemin vers l'EXE
$exePath = "$env:USERPROFILE\Downloads\WeMa-IA-AutoInstall-v0.1.2.exe"

if (-not (Test-Path $exePath)) {
    Write-Host "❌ EXE non trouvé: $exePath" -ForegroundColor Red
    Write-Host "📁 Fichiers disponibles:" -ForegroundColor Yellow
    Get-ChildItem "$env:USERPROFILE\Downloads\WeMa-IA*.exe" | Select-Object Name, Length, LastWriteTime
    Read-Host "Appuyez sur Entrée pour fermer"
    exit
}

Write-Host "✅ EXE trouvé: $exePath" -ForegroundColor Green
Write-Host "📊 Taille: $((Get-Item $exePath).Length / 1MB) MB" -ForegroundColor Cyan

Write-Host "`n🔧 Lancement avec capture des logs..." -ForegroundColor Yellow
Write-Host "⚠️  Laissez cette fenêtre ouverte pour voir les logs" -ForegroundColor Red
Write-Host "🔍 Logs du processus:" -ForegroundColor Cyan
Write-Host "-" * 50

try {
    # Lancer l'EXE et capturer les logs
    $process = Start-Process -FilePath $exePath -PassThru -WindowStyle Normal
    
    Write-Host "✅ Processus lancé (PID: $($process.Id))" -ForegroundColor Green
    Write-Host "⏳ Surveillance du processus..." -ForegroundColor Yellow
    
    # Surveiller le processus
    while (-not $process.HasExited) {
        Start-Sleep -Seconds 1
        Write-Host "." -NoNewline -ForegroundColor Gray
    }
    
    Write-Host "`n📊 Processus terminé" -ForegroundColor Yellow
    Write-Host "🔍 Code de sortie: $($process.ExitCode)" -ForegroundColor Cyan
    
    if ($process.ExitCode -ne 0) {
        Write-Host "❌ Le processus s'est terminé avec une erreur" -ForegroundColor Red
    } else {
        Write-Host "✅ Le processus s'est terminé normalement" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ Erreur lors du lancement: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🔍 Vérification des logs dans AppData..." -ForegroundColor Yellow
$logPath = "$env:LOCALAPPDATA\com.wema-ia.app"
if (Test-Path $logPath) {
    Write-Host "📁 Dossier trouvé: $logPath" -ForegroundColor Green
    Get-ChildItem $logPath -Recurse | Select-Object Name, Length, LastWriteTime | Format-Table
} else {
    Write-Host "❌ Dossier non trouvé: $logPath" -ForegroundColor Red
}

Write-Host "`n✅ Test terminé" -ForegroundColor Green
Read-Host "Appuyez sur Entrée pour fermer"
