statsmodels-0.14.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
statsmodels-0.14.2.dist-info/LICENSE.txt,sha256=HKeOHeydzrxV87lqhiMX8j52QixfyUNWjZVarR9rX60,1636
statsmodels-0.14.2.dist-info/METADATA,sha256=23N9ESPaPSzRIeKX8b88egzjwHam_o-cfFsIokUKO9o,9474
statsmodels-0.14.2.dist-info/RECORD,,
statsmodels-0.14.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels-0.14.2.dist-info/WHEEL,sha256=fZWyj_84lK0cA-ZNCsdwhbJl0OTrpWkxInEn424qrSs,102
statsmodels-0.14.2.dist-info/direct_url.json,sha256=zNVb0u7RhQ62fBo5pqXlhiCKHtu_QjZBP_UeOUYMrtk,91
statsmodels-0.14.2.dist-info/top_level.txt,sha256=cucLjFr_CwyszbZXR9yr9y0AB3j85RPFlmC11dsyblU,12
statsmodels/LICENSE.txt,sha256=HKeOHeydzrxV87lqhiMX8j52QixfyUNWjZVarR9rX60,1636
statsmodels/__init__.py,sha256=tCJIGT6Bs9BlFtQJ7A5wtUIKR5Ju1hBoe2SceZ_Ytps,1112
statsmodels/__pycache__/__init__.cpython-312.pyc,,
statsmodels/__pycache__/_version.cpython-312.pyc,,
statsmodels/__pycache__/api.cpython-312.pyc,,
statsmodels/__pycache__/conftest.cpython-312.pyc,,
statsmodels/_version.py,sha256=IlAr5T7Yed-wpRPBQOdTSkG3tJiPqgLJanRACdE0WoY,429
statsmodels/api.py,sha256=VP1hBXDOCDFZSBltBd4rPxwcoEYA1hiLOjhDAMyldcU,3409
statsmodels/base/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/base/__pycache__/__init__.cpython-312.pyc,,
statsmodels/base/__pycache__/_constraints.cpython-312.pyc,,
statsmodels/base/__pycache__/_parameter_inference.cpython-312.pyc,,
statsmodels/base/__pycache__/_penalized.cpython-312.pyc,,
statsmodels/base/__pycache__/_penalties.cpython-312.pyc,,
statsmodels/base/__pycache__/_prediction_inference.cpython-312.pyc,,
statsmodels/base/__pycache__/_screening.cpython-312.pyc,,
statsmodels/base/__pycache__/covtype.cpython-312.pyc,,
statsmodels/base/__pycache__/data.cpython-312.pyc,,
statsmodels/base/__pycache__/distributed_estimation.cpython-312.pyc,,
statsmodels/base/__pycache__/elastic_net.cpython-312.pyc,,
statsmodels/base/__pycache__/l1_cvxopt.cpython-312.pyc,,
statsmodels/base/__pycache__/l1_slsqp.cpython-312.pyc,,
statsmodels/base/__pycache__/l1_solvers_common.cpython-312.pyc,,
statsmodels/base/__pycache__/model.cpython-312.pyc,,
statsmodels/base/__pycache__/optimizer.cpython-312.pyc,,
statsmodels/base/__pycache__/transform.cpython-312.pyc,,
statsmodels/base/__pycache__/wrapper.cpython-312.pyc,,
statsmodels/base/_constraints.py,sha256=hNXbFPBPlk7aqJw4ozSpuQvjyDzKSXKeGPVDS7Vwj48,13166
statsmodels/base/_parameter_inference.py,sha256=yH5dz4t2lgNvtEMP1zXCn5FLgB-3Fi_u84WFIIjA_Rs,15378
statsmodels/base/_penalized.py,sha256=0dxuF6iUTfg-7XoglIVxzxpBdxai89hKyoa9gw3d8_o,7588
statsmodels/base/_penalties.py,sha256=dpNTVeyH8qMhILqNksUGCRxB7GyFrBZrPMDHEzpEyzs,17064
statsmodels/base/_prediction_inference.py,sha256=1FopBXmvICbYgYHpNAi37CvZaJkuh9oL4YyaS6P7s28,29946
statsmodels/base/_screening.py,sha256=6Br1o0E22XOFHV2M40a1kwWJRipJd3naQjGOeEfZGSA,18124
statsmodels/base/covtype.py,sha256=KcRsSdjRfzpUEmimi6MwTXV3-nVmiVbXZUm0ZUm5oMM,15460
statsmodels/base/data.py,sha256=Coz8VvLB-8GSqVNqZMg24v4fdIbs6Uu5RXY4u6VLVoQ,24349
statsmodels/base/distributed_estimation.py,sha256=kbRYhgsozg6-aMYDeHxmwb-R1f_-J1XBQz3Tl9UFL8M,22154
statsmodels/base/elastic_net.py,sha256=nGDfF4-8e4qqqp0RNohskF-PZxoBFwkUKAx15hyw6WY,12801
statsmodels/base/l1_cvxopt.py,sha256=9ipH4gh0JdkmbWOcHp6d7d7kUyP32MPKRdzMOq-csFU,6691
statsmodels/base/l1_slsqp.py,sha256=p0rejE3C0_88AFTUM-oAjAAO2WVJtzSgMdLegvSLttE,5604
statsmodels/base/l1_solvers_common.py,sha256=TAGd60jMSO0DuB26Dyj7MpsRPFvqjOAsk2Fv_k8c7vI,5582
statsmodels/base/model.py,sha256=ujxpxqNa3BefwmObykJVRof0ttFrOzsLHRwDURBAkL0,112365
statsmodels/base/optimizer.py,sha256=SasqgfVZ41knoi5UaAQucGth2Yoj5k6XqO4x10Ip0CU,43473
statsmodels/base/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/base/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_data.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_distributed_estimation.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_generic_methods.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_optimize.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_penalized.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_penalties.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_predict.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_screening.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_shrink_pickle.cpython-312.pyc,,
statsmodels/base/tests/__pycache__/test_transform.cpython-312.pyc,,
statsmodels/base/tests/test_data.py,sha256=O1ObaAhePFdyAD7d9lKCamLpRcXOEfRs9DjpM2gjcHU,34049
statsmodels/base/tests/test_distributed_estimation.py,sha256=RYfTvDXYI4zh7Vx4OntCAc9mEhBV7ug7pU85Iv2iioY,16034
statsmodels/base/tests/test_generic_methods.py,sha256=55HaRmoWh-pHJ-IfTPVrC3yR8MWYm0060heE083fXKA,28868
statsmodels/base/tests/test_optimize.py,sha256=S-X8TCnYbZh3I4bpPrtMD7fAdBr6ZgW_AJtYPwc5Brs,4459
statsmodels/base/tests/test_penalized.py,sha256=YGCi61tTJBB6Ro_5cWwV1ZYKJs95XmrgoYsF4kgW8ro,25600
statsmodels/base/tests/test_penalties.py,sha256=Q4Xkya4HNPt1jt6NvOb8xTCHv1UXRxZxLECVGgZORpc,4431
statsmodels/base/tests/test_predict.py,sha256=4_dQGQ9PcuEAMH1vpKsjP9sGtML9qHvlaV7y7OKDdqU,5353
statsmodels/base/tests/test_screening.py,sha256=iHz9RMLzEUo-uuiSmmwdzjxiPbYw7jnIT81UOu_q9nU,11684
statsmodels/base/tests/test_shrink_pickle.py,sha256=9KaZxyR2tg8f3i7QgX0K5zZqI3L66gcvDP0z30Kpw40,10761
statsmodels/base/tests/test_transform.py,sha256=PZM80Lj7bzGDKrX2NiI-6X0htNF9NY2WNuKtCO95V20,4469
statsmodels/base/transform.py,sha256=DxMuXbeqCxebpoc2Pna9_rAcakOWs8nq9PnBH7vizXs,8209
statsmodels/base/wrapper.py,sha256=-fZdOn6fajrzaYp-DoyEPSQClb0UkBMglL7-BT3wUTU,3590
statsmodels/compat/__init__.py,sha256=xxqIFQ5XqZnmBgQAdTJy6vxr-ZdHNo4nJFZKXB9ifsw,309
statsmodels/compat/__pycache__/__init__.cpython-312.pyc,,
statsmodels/compat/__pycache__/_scipy_multivariate_t.cpython-312.pyc,,
statsmodels/compat/__pycache__/numpy.cpython-312.pyc,,
statsmodels/compat/__pycache__/pandas.cpython-312.pyc,,
statsmodels/compat/__pycache__/patsy.cpython-312.pyc,,
statsmodels/compat/__pycache__/platform.cpython-312.pyc,,
statsmodels/compat/__pycache__/pytest.cpython-312.pyc,,
statsmodels/compat/__pycache__/python.cpython-312.pyc,,
statsmodels/compat/__pycache__/scipy.cpython-312.pyc,,
statsmodels/compat/_scipy_multivariate_t.py,sha256=COzSq4qyl7iITqqRsCHbgrI0oBfAZ3faNx3sIFzphog,39438
statsmodels/compat/numpy.py,sha256=D2WR9e5QeG_o4JrNpuzNv9QI6rzN1f0GHnDBnULrBsA,2370
statsmodels/compat/pandas.py,sha256=P5PZ36PJWm49NvOgIAklAPYxqHV6nfv3upaEUUqi2k0,4213
statsmodels/compat/patsy.py,sha256=1lnumPtdTWvaa7dZrA9A6FtY3hbiuRT8_hKFiM2lGaQ,378
statsmodels/compat/platform.py,sha256=o5tPaHLowiAUoue3U3YmrQ7dp5d8pXy3PDPt6CEqQbQ,461
statsmodels/compat/pytest.py,sha256=VV97AXWZWF879y3ahZB2t4zOQuO_O29zW8Z_nEyZJDs,1165
statsmodels/compat/python.py,sha256=P961c1pX4JKjxhMG4gPXflZd-AhHM8lHK7B2bnrGJ24,1220
statsmodels/compat/scipy.py,sha256=ClKg5a9A4rBpSoW0cxXgGB1th6w5QE0xLcW6O3G0UmM,2053
statsmodels/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/compat/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/compat/tests/__pycache__/test_itercompat.cpython-312.pyc,,
statsmodels/compat/tests/__pycache__/test_pandas.cpython-312.pyc,,
statsmodels/compat/tests/__pycache__/test_scipy_compat.cpython-312.pyc,,
statsmodels/compat/tests/test_itercompat.py,sha256=dQwlXhVGClCqF24etmM1MkFh-NuTLWsgcyhRD1qek2o,1185
statsmodels/compat/tests/test_pandas.py,sha256=l5q7ecuzD1HeYah7Wy1VJWlPm4CJRCReJZlmJl2GU28,1238
statsmodels/compat/tests/test_scipy_compat.py,sha256=YznusHpC0ySh_7mfeu8j_eaV0QL-fkhRsgLkL6Q98kM,2823
statsmodels/conftest.py,sha256=wKManUVAQ3rr01fPiWmks0L_tSpwV57DcqO97ZjkX5A,3845
statsmodels/datasets/__init__.py,sha256=0HuCDB-tMAHux-_uXpVa9r0KGJU-ZJW2RVGMbKW1zuc,1176
statsmodels/datasets/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/__pycache__/template_data.cpython-312.pyc,,
statsmodels/datasets/__pycache__/utils.cpython-312.pyc,,
statsmodels/datasets/anes96/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/anes96/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/anes96/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/anes96/anes96.csv,sha256=wSTYVW1vjEMpsf6mHj3GiRxeZj8Vt_5XkSNZY0ILqJY,21590
statsmodels/datasets/anes96/data.py,sha256=YS13FrkPqSfozB1YKFXgpyB5YY-z7-gKLfDPfbaah14,3687
statsmodels/datasets/cancer/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/cancer/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/cancer/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/cancer/cancer.csv,sha256=RtEAGQpXRPOrq1OqsV2AsOmI_qj8ixvzabH8ZGIgk28,2482
statsmodels/datasets/cancer/data.py,sha256=-tOWJVUPJs_kkPVoqu4H4rGO1j-l8U4_DUtIfS30ErU,1177
statsmodels/datasets/ccard/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/ccard/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/ccard/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/ccard/ccard.csv,sha256=Whwy5sL5JWFjKR6uN5EFGl-5O3h7HbqKB_qvVDG28Pk,1571
statsmodels/datasets/ccard/data.py,sha256=FezVp3Pfl1WHee8g0BOebFMKzwcn3YYrO7ksUuE-AWw,1357
statsmodels/datasets/china_smoking/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/china_smoking/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/china_smoking/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/china_smoking/china_smoking.csv,sha256=80invoSZZs6w7ZFX74QlfChj5M4OlSiURy49dz-oek4,280
statsmodels/datasets/china_smoking/data.py,sha256=rgXlzQUZxl_IssruhKk5adHTIDmS1SFiBluUni2q4GI,1518
statsmodels/datasets/co2/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/co2/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/co2/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/co2/co2.csv,sha256=FmlfonhuU0FOWmtUdno_313pnPvGhhf2nRNi2Sd2qS8,33974
statsmodels/datasets/co2/data.py,sha256=8MzA5IUFDarSmuvFWi3AlAzvPimdndV3UHJMcb4ciwY,2356
statsmodels/datasets/committee/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/committee/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/committee/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/committee/committee.csv,sha256=_kkTqo7BMiUVwu6kzIYeMTGZBlZ-RtP0FM8-E0JnGZE,712
statsmodels/datasets/committee/data.py,sha256=qF6Jf1lKCWh_xPUSeAorut38gKCYLgnCKg_YXbjzL8Y,2317
statsmodels/datasets/copper/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/copper/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/copper/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/copper/copper.csv,sha256=WwvHC3TwYEpcuyExHZFoz2TzsVlcgv2bE5XIDau-KL0,982
statsmodels/datasets/copper/data.py,sha256=BF1h19lDaBfDEOIol9_zb4oaBmQfukY7Tfm0YCb4H0g,2029
statsmodels/datasets/cpunish/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/cpunish/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/cpunish/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/cpunish/cpunish.csv,sha256=8gC290ELkk1m6THO5D2DDbeifTmIYpX7rKd455cp1lM,746
statsmodels/datasets/cpunish/data.py,sha256=euMlWpcU-ZA8okh0oNmHWNmzGY2Fm3-fQHw3hfxyazs,2320
statsmodels/datasets/danish_data/__init__.py,sha256=28FW06KV5cRneIXeyx2s9jtLSr4Yo2glhSVFNBbMfs4,264
statsmodels/datasets/danish_data/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/danish_data/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/danish_data/data.csv,sha256=XuV77qlsB5W_MMwNcK6ZrkNhBfZzzQ-0d9sXpdXDpbs,3293
statsmodels/datasets/danish_data/data.py,sha256=mf5FR-wrohM5_F3NrE2yw7Pdk-jeCpT01np7d_UiRbQ,1742
statsmodels/datasets/elec_equip/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/elec_equip/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/elec_equip/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/elec_equip/data.py,sha256=HOD8KJ2t5fl1lha5QzuoXvFhFIeYIob3I78esa0CeUg,1519
statsmodels/datasets/elec_equip/elec_equip.csv,sha256=-w6Gd0SE0skXk3Q3cEFgD_0l6gI2rq99KTvy31roY2k,4505
statsmodels/datasets/elnino/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/elnino/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/elnino/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/elnino/data.py,sha256=EiwEaAegX-vc3MiyFB4GTqikA2o_IB-8hoMWABcQceE,1464
statsmodels/datasets/elnino/elnino.csv,sha256=tke-AOD9Jkvpdk4xfmuWPzUDABTsyishsgRSFxbkY60,5508
statsmodels/datasets/engel/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/engel/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/engel/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/engel/data.py,sha256=ygCuaQMhTsCNPMqAu4bmdFyxOwoCQ3InahPW51wKAck,1371
statsmodels/datasets/engel/engel.csv,sha256=eWw9oEBikd0yTFGQG1E4a-ErX1LjMK-vaVhPV8Bq1Fw,7941
statsmodels/datasets/fair/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/fair/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/fair/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/fair/data.py,sha256=v_Yfmy7oA1WVYaruB-uU-uhrgFtylhHcrZsudFcNzYo,2491
statsmodels/datasets/fair/fair.csv,sha256=_V8_CUo0_DXKNGoUw1ngRu0nhDA41pIe_NUKerIfavA,151816
statsmodels/datasets/fair/fair_pt.csv,sha256=RKhA7B-Ca5ETXNDpKdrrXRGhGUt7VOX300wF0gqy1CY,22393
statsmodels/datasets/fertility/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/fertility/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/fertility/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/fertility/data.py,sha256=mW2HHyXOLD5zdkj5jRo3GpXggtS87Cmxn9N8_A4H2Zk,2034
statsmodels/datasets/fertility/fertility.csv,sha256=IhmhsDmy2kbLi3_Q4Wkt5RhR4Cv-5KmW1kx1xGYijig,94455
statsmodels/datasets/grunfeld/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/grunfeld/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/grunfeld/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/grunfeld/data.py,sha256=BArYVBMku0MtjvL-5vl29kAIRkAQZC-j9L8oPESzVxo,2309
statsmodels/datasets/grunfeld/grunfeld.csv,sha256=b2yhOOZF7u5v8-VP5bm0mPfdtcSEI30qhInFJLPJQJg,7629
statsmodels/datasets/heart/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/heart/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/heart/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/heart/data.py,sha256=9graJnJy6i2jqyACvIW2oFlNdFtZGatzQVPkxoYaCG0,1312
statsmodels/datasets/heart/heart.csv,sha256=CUGy61GMLIPcwSUaCsYq-wFC_c_wgWEac4xBuzpYDeo,2064
statsmodels/datasets/interest_inflation/E6.csv,sha256=RY7ewJqfa5H8yWobMfC4ifXxUgicE5PRRrguDsC0Hz8,2534
statsmodels/datasets/interest_inflation/E6_jmulti.csv,sha256=jaqC8YjyGT4YzCxDPPWj8_5nRD1Huwc8O5D2GnxEGZs,1759
statsmodels/datasets/interest_inflation/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/interest_inflation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/interest_inflation/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/interest_inflation/data.py,sha256=nJ-51t7ZwUE40vO0U8GELWtMd0UJH5icMFV6c1b0-XY,1402
statsmodels/datasets/longley/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/longley/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/longley/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/longley/data.py,sha256=pUSy9w6LpiwKUDgVwK_9dV-D7YChuAMRMO-Eagen2_0,1680
statsmodels/datasets/longley/longley.csv,sha256=CSfsfMNO21Zwkgyy_xVC5G3ieiAQdG4WYvQnbPNWmiQ,742
statsmodels/datasets/macrodata/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/macrodata/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/macrodata/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/macrodata/data.py,sha256=274MRxtAIqTjwERouluDiybvbVMwKj1GE78oVSmIULs,2939
statsmodels/datasets/macrodata/macrodata.csv,sha256=2TwNOnp374PDrxTkYDK7HQKuOlErIquUFZqMoib89wg,17829
statsmodels/datasets/macrodata/macrodata.dta,sha256=zVSRVduL-pxTDNFoNmxS_wY-NXwNUg7eS3d1Yte27t8,13255
statsmodels/datasets/modechoice/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/modechoice/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/modechoice/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/modechoice/data.py,sha256=BBlUTjHDiLQqNdQbddWJxVmxciRlYN6JBTEKLOe1_8s,2633
statsmodels/datasets/modechoice/modechoice.csv,sha256=0tcsHbRA-P_OAfWO05_BFFVp7BcDlw2sFjbBVPwB_Y4,21566
statsmodels/datasets/nile/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/nile/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/nile/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/nile/data.py,sha256=kvEzfnhdOIAxo_8Aa4QDsKRkBLYeyUlzwef5zpqefEs,1398
statsmodels/datasets/nile/nile.csv,sha256=iOl76nJJ5YMqheQa7GzkuPexsUqukwyDY9p_GTKGtZg,942
statsmodels/datasets/randhie/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/randhie/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/randhie/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/randhie/data.py,sha256=baRoes6VIlQtmoctM2rGCvV-bb004cI-g74tba3nh58,2303
statsmodels/datasets/randhie/randhie.csv,sha256=n2yH0FrvCHqCzERlMQyM0_ODJ75ur6Q72B-5jE89CIw,748181
statsmodels/datasets/scotland/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/scotland/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/scotland/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/scotland/data.py,sha256=wj3MxdSaMb4SD_2brqT7pOFGowILe7ttgAjKFpjatc8,2726
statsmodels/datasets/scotland/scotvote.csv,sha256=Ks6Lnklw-KfQQkpwB21j5uHD1lJZ5uQk-IZC_daiRyc,1890
statsmodels/datasets/spector/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/spector/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/spector/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/spector/data.py,sha256=EpYRHSeP5EBL2cq34RTZbTXe2WybLzG2PkQHzaAX9OY,1745
statsmodels/datasets/spector/spector.csv,sha256=sQ0Qi6P6lrJ-i7dxrKnOFIqh_em6DJ2AujhLhRtuYZI,497
statsmodels/datasets/stackloss/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/stackloss/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/stackloss/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/stackloss/data.py,sha256=uYf_pcbZgyCoiKBa_AJMtG5J2HcRkpb9J4Bz9AEPlbE,1608
statsmodels/datasets/stackloss/stackloss.csv,sha256=c5WVPWLux6ureDrpYD_4LwkdBKRol4DkVcI58PVQn2Q,292
statsmodels/datasets/star98/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/star98/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/star98/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/star98/data.py,sha256=ZeRIgd1gmnmXEgMYpOqdftdkA9iIIno-TaZx9EFazSY,3497
statsmodels/datasets/star98/star98.csv,sha256=_R-7KbU1bDjEIr4QC5TBOsP5n2gzDwILSTNCqG-bTEk,65277
statsmodels/datasets/statecrime/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/statecrime/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/statecrime/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/statecrime/data.py,sha256=Vdex3vF3QzkGkLNASgFCIPUlOZMto7V9ZrVm-Z25NIg,2499
statsmodels/datasets/statecrime/statecrime.csv,sha256=c8iqoSJyy9M6CdD_zaAag18vCRahaq7VRzLvoxJDBog,2369
statsmodels/datasets/strikes/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/strikes/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/strikes/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/strikes/data.py,sha256=canobuwHejLIcg718EM7NrVY73hWiDWTj7uSo52DfqQ,1631
statsmodels/datasets/strikes/strikes.csv,sha256=khHLU4xu5n_3wS1vymTqhqVxfknUZwtx08RWyWVnjpE,718
statsmodels/datasets/sunspots/__init__.py,sha256=eZnkT9E7g88N_tjwn4ylSYHaZZLM2xliqTFvIHQEgd8,214
statsmodels/datasets/sunspots/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/sunspots/__pycache__/data.cpython-312.pyc,,
statsmodels/datasets/sunspots/data.py,sha256=cH1y51PERYH_ZiITK1CUZlkULVu5h2fGFPrCvR5hJtk,1535
statsmodels/datasets/sunspots/sunspots.csv,sha256=9niJsdkALNUifw4O9U41tBnN2FoxJ5re9vc_tB5cCps,2944
statsmodels/datasets/template_data.py,sha256=A5BE2REzPstvXYgDH14KOkxI04vfX8mBChNk2pIDPZI,1223
statsmodels/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/datasets/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/datasets/tests/__pycache__/test_data.cpython-312.pyc,,
statsmodels/datasets/tests/__pycache__/test_utils.cpython-312.pyc,,
statsmodels/datasets/tests/test_data.py,sha256=pjDkW7hf7jEkMqSTddwGl_exzSplJH9_WmbKiA8nE5A,1199
statsmodels/datasets/tests/test_utils.py,sha256=uHFkfS148eOrtYwv5ucjv2Y1O6aHmFphh5I7jOC6A3k,3309
statsmodels/datasets/utils.py,sha256=jslRtbTVvIVqiyY4enHliWPLdM2CIu09lcrouH29zaE,10361
statsmodels/discrete/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/discrete/__pycache__/__init__.cpython-312.pyc,,
statsmodels/discrete/__pycache__/_diagnostics_count.cpython-312.pyc,,
statsmodels/discrete/__pycache__/conditional_models.cpython-312.pyc,,
statsmodels/discrete/__pycache__/count_model.cpython-312.pyc,,
statsmodels/discrete/__pycache__/diagnostic.cpython-312.pyc,,
statsmodels/discrete/__pycache__/discrete_margins.cpython-312.pyc,,
statsmodels/discrete/__pycache__/discrete_model.cpython-312.pyc,,
statsmodels/discrete/__pycache__/truncated_model.cpython-312.pyc,,
statsmodels/discrete/_diagnostics_count.py,sha256=lCwmo4UBSN44nV8eycW-_SLiZ-jS-bgLPqg2bknqwKU,20131
statsmodels/discrete/conditional_models.py,sha256=RiyIKGkMPkWzsEiZ11lfvH54tQQEuoCM_ReEVhTYgas,18716
statsmodels/discrete/count_model.py,sha256=U8RIPKAwsEvcjxJAnBzMO1tkpAMRbrJUSD1_wCZGStg,43649
statsmodels/discrete/diagnostic.py,sha256=TDf8yP78iAMGTS-b3rNsQv7uIPsuJt56ttJVo8calHY,9069
statsmodels/discrete/discrete_margins.py,sha256=US8sm0pbNCw0J6cihlswzNIuSDASbUGUjvWoZT6ftHM,26628
statsmodels/discrete/discrete_model.py,sha256=XSgPpY-zG6asMpJbdVIABDvF17-Ib2POFpgoU8Alarc,200117
statsmodels/discrete/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/discrete/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_conditional.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_constrained.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_count_model.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_diagnostic.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_discrete.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_margins.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_predict.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_sandwich_cov.cpython-312.pyc,,
statsmodels/discrete/tests/__pycache__/test_truncated_model.cpython-312.pyc,,
statsmodels/discrete/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/discrete/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_count_margins.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_count_robust_cluster.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_discrete.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_glm_logit_constrained.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_poisson_constrained.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_predict.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_truncated.cpython-312.pyc,,
statsmodels/discrete/tests/results/__pycache__/results_truncated_st.cpython-312.pyc,,
statsmodels/discrete/tests/results/mn_logit_summary.txt,sha256=k1QhXAdcvGgNox4Eq8FOfv1LyYf1aypmM-yDlxc0QbU,3871
statsmodels/discrete/tests/results/mnlogit_resid.csv,sha256=876cPQQ02vzDDBbKR_9HcK8Qg33YbTHnJRc0dQwD10w,4720
statsmodels/discrete/tests/results/nbinom_resids.csv,sha256=H2ZsNtlrXAoopCtiALTM5adL3nKjJ9VH42ey2q4qarw,941
statsmodels/discrete/tests/results/phat_mnlogit.csv,sha256=xMMwIP_NvJWXWq9HYzy4dVYOeCeLp9L7vrhoeWhVSJc,165200
statsmodels/discrete/tests/results/poisson_resid.csv,sha256=OI6OjC0G-PW90E9aYJ3bdaNKqPhScjhCjB8BrRkdIxI,135256
statsmodels/discrete/tests/results/predict_prob_poisson.csv,sha256=7SHiuh960h9uU8R8A2C5dYUl83xVTme3r0B5fTr09I4,195000
statsmodels/discrete/tests/results/results_count_margins.py,sha256=UTzt39_FEa_CCi5AnNE-Kos9yEAZazzhqW3fG1zimVw,17123
statsmodels/discrete/tests/results/results_count_robust_cluster.py,sha256=X0g5_ujeNj2AepZghMHwfyg-uixR0pyXELlzq0pe3VI,23423
statsmodels/discrete/tests/results/results_discrete.py,sha256=3_ewT2205JD3-iwFsdLxjoPmfFBclDbeG771W7SJTVU,52382
statsmodels/discrete/tests/results/results_glm_logit_constrained.py,sha256=KJhZHX3MFFG-tX-d1p-6G6mUTM07euAYSg282aWIXN8,24547
statsmodels/discrete/tests/results/results_poisson_constrained.py,sha256=RWBtnL6uMq4cp_iM0x2CJV_J0178XV0pbRXXz6qnpnQ,27922
statsmodels/discrete/tests/results/results_predict.py,sha256=Ne1Rjrv2hM1tuZL3ZEXS-ekso66U0onQbiAUarmt5Hc,11609
statsmodels/discrete/tests/results/results_truncated.py,sha256=wkH4PqXyZKpm_MYq4_9U5ZO4F44gblKXkLqEi_WlWdo,2284
statsmodels/discrete/tests/results/results_truncated_st.py,sha256=SjfpT4tmv9QazKqCZY3P1VlCg-d9-h7eVngqJdpWf4o,26506
statsmodels/discrete/tests/results/ships.csv,sha256=o4ltLlAjJ4VhvgdeeMGScwL2ecLjrhPuEfmQXeX7q2U,879
statsmodels/discrete/tests/results/sm3533.csv,sha256=CjxPxDGIZpYxBJTYLjJ3tbrCzaOm06s8bEm7tHLraXw,6537
statsmodels/discrete/tests/results/yhat_mnlogit.csv,sha256=zmImx7kcyhK3oVmyRXYsIUtQLhNS74mVDrk6AfgR3wo,168500
statsmodels/discrete/tests/results/yhat_poisson.csv,sha256=gt5ivePpSemQxwc5r8qt1EaLsyEh5QuN1j7vak5EYAg,358789
statsmodels/discrete/tests/test_conditional.py,sha256=U6Ae3Z03eJAOXPi-SYzugW3uF2YEjV3_FRpxWI5fHJE,9650
statsmodels/discrete/tests/test_constrained.py,sha256=LSruQuKoVL_7_Ga4OX1RHER6rMtm4jKK13jsXoFywVs,22857
statsmodels/discrete/tests/test_count_model.py,sha256=kZJC3VG1HmmfwWI5-bfjclbogcMKRPCuo4qomP-GfhY,28701
statsmodels/discrete/tests/test_diagnostic.py,sha256=BI1ePW51FZIitjD5e2-fOJ09w8Y7H9skv1baz8kIeQc,4678
statsmodels/discrete/tests/test_discrete.py,sha256=_-uMUQtjhWeyRuB5kQ4U4wHWC4lqw09AluzaURF2bTA,99190
statsmodels/discrete/tests/test_margins.py,sha256=kgyppTqlCQGk4nbDbQBT7T965GpOyYBcf4rx4DWqQTw,4632
statsmodels/discrete/tests/test_predict.py,sha256=c-BU27mF8TK3RM0pmrAR0_RCDqfQgl2pfdF9Fc6FIuU,15495
statsmodels/discrete/tests/test_sandwich_cov.py,sha256=yHh7pVkyDy963plUsGY5i-WoCEzLl3oNps70OQZrRfM,26983
statsmodels/discrete/tests/test_truncated_model.py,sha256=bi6lOAb7wZG_yeTkiIJGd_7MOCXBaIG47tvAnLfdGCc,21137
statsmodels/discrete/truncated_model.py,sha256=EipC0Xg82Q0Q1FW11T3l5A0Bib50GfjRKJ0l7PI_REw,50852
statsmodels/distributions/__init__.py,sha256=5aoQnV_Kapr_cyhkiv3h3KGX-_1z7VhQE2-D9K14vD4,505
statsmodels/distributions/__pycache__/__init__.cpython-312.pyc,,
statsmodels/distributions/__pycache__/bernstein.cpython-312.pyc,,
statsmodels/distributions/__pycache__/discrete.cpython-312.pyc,,
statsmodels/distributions/__pycache__/edgeworth.cpython-312.pyc,,
statsmodels/distributions/__pycache__/empirical_distribution.cpython-312.pyc,,
statsmodels/distributions/__pycache__/mixture_rvs.cpython-312.pyc,,
statsmodels/distributions/__pycache__/tools.cpython-312.pyc,,
statsmodels/distributions/bernstein.py,sha256=-rJ0PYKOh6DAOKKLOqvmqHW4BG_jFuBB3mb-ERjJCSk,7370
statsmodels/distributions/copula/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/distributions/copula/__pycache__/__init__.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/_special.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/api.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/archimedean.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/copulas.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/depfunc_ev.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/elliptical.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/extreme_value.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/other_copulas.cpython-312.pyc,,
statsmodels/distributions/copula/__pycache__/transforms.cpython-312.pyc,,
statsmodels/distributions/copula/_special.py,sha256=j2Vx6G06Yp19kN0bKdFadIiGAktAVGMVTsxxSuPR20c,2418
statsmodels/distributions/copula/api.py,sha256=7-ELOMLR5p88aLKnPGxUnZs44V7FK1i4djo0tuySohE,889
statsmodels/distributions/copula/archimedean.py,sha256=1K7gdMW44BZff357AfZR1CCiS343OwADYpjpHtUOEds,14469
statsmodels/distributions/copula/copulas.py,sha256=SgEEKnu9VUBivfIR8kFAHMtnSR8iEcPa8A9Af-cny7k,17721
statsmodels/distributions/copula/depfunc_ev.py,sha256=zcI7ujM7yTecyDfPk3wTTSXLOXH7_jBFFp8GI_cIR3A,9220
statsmodels/distributions/copula/elliptical.py,sha256=mSsYuOg-0WjB1NvB33ajNqwFgFh9xVWWYqreEj6SdLs,10232
statsmodels/distributions/copula/extreme_value.py,sha256=yNFyH0rfdq9M8nFc3Zq_KP283gPPGMn7XHRQViNv54I,4952
statsmodels/distributions/copula/other_copulas.py,sha256=arXbkJ7elvpN2sIjPQHWTd6jmMP-e_lsh2cj1LlIFJg,3485
statsmodels/distributions/copula/transforms.py,sha256=RLIOPQv1RKTYn-aWt4IinvxcpjkyUWa8X1AM36CtaiY,6413
statsmodels/distributions/discrete.py,sha256=Q0CfReFx7F4-ylLbqGTzrj0vkjDoT6WAWMan22syZU8,14992
statsmodels/distributions/edgeworth.py,sha256=vdzYnB_BqNLPvzPI2zleZCdeK0GZFSbSPL_SURdJJ50,6657
statsmodels/distributions/empirical_distribution.py,sha256=-Codhdqdij2My4uUCNQxuE7IJUHqpS2HrgBKwA-dpVA,7022
statsmodels/distributions/mixture_rvs.py,sha256=DgEFKUMEqGT7TqHz_N7ckruWLXm6NCt7fBUnRfUzpaA,10150
statsmodels/distributions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/distributions/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_bernstein.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_discrete.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_ecdf.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_edgeworth.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_mixture.cpython-312.pyc,,
statsmodels/distributions/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/distributions/tests/test_bernstein.py,sha256=rATaRD6SmU4fz-s6P1uY6MGDU1foD1C6cWDWOZaw5HU,5542
statsmodels/distributions/tests/test_discrete.py,sha256=Ak4j5xU0Ra_rhsnInf7aD5nybho2wLEMHriBl69V9Cs,19196
statsmodels/distributions/tests/test_ecdf.py,sha256=miNOjB9NAsFrPlE55XZvsApU52wAqq_VjmOfByis368,2075
statsmodels/distributions/tests/test_edgeworth.py,sha256=DXTZtvmyw-4t13CdoDX2YocR-cvYtTPPgufaQ3kI9bg,6415
statsmodels/distributions/tests/test_mixture.py,sha256=2Qi6XyqtD64_OW4qBB5fci0A3FrtQj6aGOUERs6DNLM,5063
statsmodels/distributions/tests/test_tools.py,sha256=GTqE70fqbwwSzl7yhohzxr_kyd7qEtK-7NRywtCcqhQ,5568
statsmodels/distributions/tools.py,sha256=boLsoL7Az8pMX779B4PpGK92aqD8fnddAp75RPkQq5g,15074
statsmodels/duration/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/duration/__pycache__/__init__.cpython-312.pyc,,
statsmodels/duration/__pycache__/_kernel_estimates.cpython-312.pyc,,
statsmodels/duration/__pycache__/api.cpython-312.pyc,,
statsmodels/duration/__pycache__/hazard_regression.cpython-312.pyc,,
statsmodels/duration/__pycache__/survfunc.cpython-312.pyc,,
statsmodels/duration/_kernel_estimates.py,sha256=IafUMOPszX4PzDCMx4zY21AbxTymL5yZbuIg3UmUNwA,6224
statsmodels/duration/api.py,sha256=Fh3mjsIa12voJaF7WEj-k2AhzxhYanEYwX0rTjaZ50k,197
statsmodels/duration/hazard_regression.py,sha256=tvu7iD0lfoWIdI6pEuTfRhsDt3Hq423er7SERECiuQc,58485
statsmodels/duration/survfunc.py,sha256=t3ACp-QPk6Zh_TqnujfjObhSHOWG1zbSOHZbRB9RT64,27227
statsmodels/duration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/duration/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/duration/tests/__pycache__/test_phreg.cpython-312.pyc,,
statsmodels/duration/tests/__pycache__/test_survfunc.cpython-312.pyc,,
statsmodels/duration/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/duration/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/duration/tests/results/__pycache__/phreg_gentests.cpython-312.pyc,,
statsmodels/duration/tests/results/__pycache__/survival_enet_r_results.cpython-312.pyc,,
statsmodels/duration/tests/results/__pycache__/survival_r_results.cpython-312.pyc,,
statsmodels/duration/tests/results/bmt.csv,sha256=simYtbHNE4dI5fKzM5T-xFr-QrlD1uVofyfQrr1RIks,2344
statsmodels/duration/tests/results/bmt_results.csv,sha256=gs0NZGa3FV_Kr0g1clMVxDF43Kirr6gN6B79MX8xdWg,1617
statsmodels/duration/tests/results/phreg_gentests.py,sha256=8CHpqbwfHItNoVZm3CNhfO7H0eLTa3GTTJrALQ4U6jQ,1937
statsmodels/duration/tests/results/survival_data_1000_10.csv,sha256=bQy0KZ-c4ZiOhhNCSw6iE4xoguDP7WucGCeii4foHIg,109015
statsmodels/duration/tests/results/survival_data_100_5.csv,sha256=ftSXO57g9nKRlTudaKfFmpmkUsZIwwjJaXSkePeqAKg,6639
statsmodels/duration/tests/results/survival_data_20_1.csv,sha256=FQQ_FeSan-dpJ5DnNYrBykhrFV7Q2id6ZVuQqnnDGG0,648
statsmodels/duration/tests/results/survival_data_50_1.csv,sha256=zgtkLX5LxFPTXuUwKdqnL4qJZNJ1GEMe5PIzUt6JL80,1620
statsmodels/duration/tests/results/survival_data_50_2.csv,sha256=7imn-Qg_muEfqDCchhEcIsVC6W-0rIFgB3OE8UcGER0,2058
statsmodels/duration/tests/results/survival_enet_r_results.py,sha256=-ejQJzRYWfeGLaUixBeYOQmMarkac4uOhEnO99kY12s,273
statsmodels/duration/tests/results/survival_r_results.py,sha256=2sunnYE9q0upqfR0enbUiJE4HNbEM7wv3OGFFVjFRhM,13236
statsmodels/duration/tests/test_phreg.py,sha256=iV8O1npaog0SJ-9TOAPG2VMM4gg7sBhGaOt0w4eeqDU,14995
statsmodels/duration/tests/test_survfunc.py,sha256=BlH-z4XXp1E5RYrj1-P_CqSYBjHBVqshEURZmSq8uVw,19285
statsmodels/emplike/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/emplike/__pycache__/__init__.cpython-312.pyc,,
statsmodels/emplike/__pycache__/aft_el.cpython-312.pyc,,
statsmodels/emplike/__pycache__/api.cpython-312.pyc,,
statsmodels/emplike/__pycache__/descriptive.cpython-312.pyc,,
statsmodels/emplike/__pycache__/elanova.cpython-312.pyc,,
statsmodels/emplike/__pycache__/elregress.cpython-312.pyc,,
statsmodels/emplike/__pycache__/originregress.cpython-312.pyc,,
statsmodels/emplike/aft_el.py,sha256=8o0LZqCJ9Yj70UpuuMA02A_G1a95YsTxVvJGVsulaA4,18145
statsmodels/emplike/api.py,sha256=JFVsZxK9XQoU44kpnmaNKH739iv9QHm2bO6okG8Lnqw,301
statsmodels/emplike/descriptive.py,sha256=1TjV6sGwcrPuqZHYfyAFeRv4QboCQMwXIh8N5zV4Eyg,38939
statsmodels/emplike/elanova.py,sha256=OhYRL8fEf1lB8f7vM8LrQZ_5VdfhteMABZJBNm-ev5Q,3674
statsmodels/emplike/elregress.py,sha256=xxZv4oHvLoYrmSsHqzsnHBqxO5jJRIpuCHt9IBrT-c8,3093
statsmodels/emplike/originregress.py,sha256=lpNEhezf4mc9wHOlA1r-g22hikPQlnPXsnWahp8QQKY,8951
statsmodels/emplike/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/emplike/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_aft.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_anova.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_descriptive.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_origin.cpython-312.pyc,,
statsmodels/emplike/tests/__pycache__/test_regression.cpython-312.pyc,,
statsmodels/emplike/tests/results/__init__.py,sha256=wmpiL9QRqCkWLdHQVRIhoL1BqjlBBEHNuHsalSyum9U,19
statsmodels/emplike/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/emplike/tests/results/__pycache__/el_results.cpython-312.pyc,,
statsmodels/emplike/tests/results/el_results.py,sha256=GZQRYXVImJN7wAsBTyvu_GdfaFV1ZAzPGsGcoiV9l1I,18427
statsmodels/emplike/tests/test_aft.py,sha256=WLngMChPOJiPqnCxBYSpmoLKd09BjmRtG7NGbCbaAyI,1597
statsmodels/emplike/tests/test_anova.py,sha256=sKCShB2fAgKFP3flmVFIcEHyv1Y9sXuTYnZMVEUwHDs,862
statsmodels/emplike/tests/test_descriptive.py,sha256=qKDoR8zOBPWd9mG3rb-DHD_MprJRMbqOR9nO4EostTo,4314
statsmodels/emplike/tests/test_origin.py,sha256=OSX6cCCKBoa11CmqE-IzMrmC0trHCExKTuZSG1tLIUI,1442
statsmodels/emplike/tests/test_regression.py,sha256=3vYyJrNM2GrdiUrtQyWuiwkT2OQpzr-oNCYDUERTsbM,5764
statsmodels/formula/__init__.py,sha256=4HepspReFRgizNMVNRUrcN1MAEOdupJs98bCPXCK1P4,164
statsmodels/formula/__pycache__/__init__.cpython-312.pyc,,
statsmodels/formula/__pycache__/api.cpython-312.pyc,,
statsmodels/formula/__pycache__/formulatools.cpython-312.pyc,,
statsmodels/formula/api.py,sha256=-Y77e7AsAqPADEVFMT43QrovQ_XvUbPz2USsZla4RKg,1757
statsmodels/formula/formulatools.py,sha256=rgch5wN_8Tu5PYc_1E_nsM3TKwnFL7-rY3Ecz6WDSDA,3803
statsmodels/formula/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/formula/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/formula/tests/__pycache__/test_formula.cpython-312.pyc,,
statsmodels/formula/tests/test_formula.py,sha256=6WgXQ6QXbVz-tbV8rUgrXx0yOoPs6QYqC0zhTkyp-vI,8730
statsmodels/gam/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/gam/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/__pycache__/api.cpython-312.pyc,,
statsmodels/gam/__pycache__/gam_penalties.cpython-312.pyc,,
statsmodels/gam/__pycache__/generalized_additive_model.cpython-312.pyc,,
statsmodels/gam/__pycache__/smooth_basis.cpython-312.pyc,,
statsmodels/gam/api.py,sha256=9EQf90M3nVIFq_ZVy3XDm6foPsm1sIu2J1tHoAbuPuo,260
statsmodels/gam/gam_cross_validation/__init__.py,sha256=9rQu0Mm61vg7DmdV-GOCv1XkwBhpDopyzQfi8qnuvGE,50
statsmodels/gam/gam_cross_validation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/gam_cross_validation/__pycache__/cross_validators.cpython-312.pyc,,
statsmodels/gam/gam_cross_validation/__pycache__/gam_cross_validation.cpython-312.pyc,,
statsmodels/gam/gam_cross_validation/cross_validators.py,sha256=o_JQ_P0zBkAx7AsBpFKGDo4Cq3wMjO_boIBnTyiuL1E,1636
statsmodels/gam/gam_cross_validation/gam_cross_validation.py,sha256=3_ylz2TcGtf7cKn9EHnQqZchYXMXAMAEIeIC0cwQNv4,7564
statsmodels/gam/gam_penalties.py,sha256=MgAPpIFJVl13weCZz6UWHZtt_yl66iJo_7jPHoHdfzI,9745
statsmodels/gam/generalized_additive_model.py,sha256=PxXZZrGN8bczmP8X66FxKE1a_DXY4-RM85BeIPuzTIE,38226
statsmodels/gam/smooth_basis.py,sha256=HdJe3nNQkkX_PNF5_vKj4_pTFTug38UB-kuD9k-nZWA,39194
statsmodels/gam/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/gam/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/tests/__pycache__/test_gam.cpython-312.pyc,,
statsmodels/gam/tests/__pycache__/test_penalized.cpython-312.pyc,,
statsmodels/gam/tests/__pycache__/test_smooth_basis.cpython-312.pyc,,
statsmodels/gam/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/gam/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/gam/tests/results/__pycache__/results_mpg_bs.cpython-312.pyc,,
statsmodels/gam/tests/results/__pycache__/results_mpg_bs_poisson.cpython-312.pyc,,
statsmodels/gam/tests/results/__pycache__/results_pls.cpython-312.pyc,,
statsmodels/gam/tests/results/autos.csv,sha256=ohq6Ar8PkUT9YWEi2jOgPEocFXb-6pRL13uYs8qQLWQ,28844
statsmodels/gam/tests/results/autos_exog.csv,sha256=5iVQNPMqGe4Pdc2qC1AnQa8QY2I22RCgUrmSt1Lt7UI,35924
statsmodels/gam/tests/results/autos_predict.csv,sha256=bWWJAJ5o_9i-ykRvlvsoJzXrpbId1NIJML_hBixh7bs,36186
statsmodels/gam/tests/results/cubic_cyclic_splines_from_mgcv.csv,sha256=nXXv9D7evzMwBS_Cgvo63dxm5weaveOWgkzkECtFGhg,65519
statsmodels/gam/tests/results/gam_PIRLS_results.csv,sha256=XSYKjckAhVWY7k3bp8_sl8og4pfshcASa1ie04aaOhs,16540
statsmodels/gam/tests/results/logit_gam_mgcv.csv,sha256=fCRIHsP4XeeqvZzQGFF3DFb5yakB9XCtSrcYJ6_t5js,8454
statsmodels/gam/tests/results/motorcycle.csv,sha256=EwNxBBGodPf-kOWIpn4_wvCYuQOw6pbBq5xxna_Y0Gg,1324
statsmodels/gam/tests/results/prediction_from_mgcv.csv,sha256=4DR6NzBJkNbcDJPxjWhfaSkEcf5QUqVFnM-8y9YbzcA,118661
statsmodels/gam/tests/results/results_mpg_bs.py,sha256=gjsfw84PIlFa5r-X4vyCANImBazxX9_Fr0JaV8Q6hx4,76491
statsmodels/gam/tests/results/results_mpg_bs_poisson.py,sha256=rKvfO2Z0ZU0F206I_ML9teBSKNUlzOd5PFJfYMda8Lk,104520
statsmodels/gam/tests/results/results_pls.py,sha256=IQmeGwo-nDRIPjEmBd9Y385yES-GFl3jCP6vZtyGx44,21723
statsmodels/gam/tests/test_gam.py,sha256=Wv3GRg50kSEjw2hj5i8kId4YSZbRZfKELGx81k1KPMk,26173
statsmodels/gam/tests/test_penalized.py,sha256=R_fOpmsFQMZ6UNQBEyVQ7ujGybomcBOWOFBZORftaZQ,24671
statsmodels/gam/tests/test_smooth_basis.py,sha256=nDRfSvksKUyHG-DHgRmJg6GkdMLCCsMJamumcB-06Hs,1243
statsmodels/genmod/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/genmod/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/__pycache__/_tweedie_compound_poisson.cpython-312.pyc,,
statsmodels/genmod/__pycache__/api.cpython-312.pyc,,
statsmodels/genmod/__pycache__/bayes_mixed_glm.cpython-312.pyc,,
statsmodels/genmod/__pycache__/cov_struct.cpython-312.pyc,,
statsmodels/genmod/__pycache__/generalized_estimating_equations.cpython-312.pyc,,
statsmodels/genmod/__pycache__/generalized_linear_model.cpython-312.pyc,,
statsmodels/genmod/__pycache__/qif.cpython-312.pyc,,
statsmodels/genmod/_tweedie_compound_poisson.py,sha256=RncG-Okr1KiACzTsaxR7udhtTnyUJa2_25TrtYmCDPA,2839
statsmodels/genmod/api.py,sha256=BNhuba1WObYvtZXielmhCo9j5-2NJ2st5go56YjHu18,379
statsmodels/genmod/bayes_mixed_glm.py,sha256=MxuCNVdVFvePHrsajtCKjgm7F45dJR0zuBE91BVpSps,38848
statsmodels/genmod/cov_struct.py,sha256=3ScMMxJyLHrRXXI_Nhfl0nO0ZAAIm2_vvlUdumaeZDY,51188
statsmodels/genmod/families/__init__.py,sha256=xIczKnQufncOf6Lu7X1OE4dqzHyKPngiQLtADU-mBDs,664
statsmodels/genmod/families/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/families/__pycache__/family.cpython-312.pyc,,
statsmodels/genmod/families/__pycache__/links.cpython-312.pyc,,
statsmodels/genmod/families/__pycache__/varfuncs.cpython-312.pyc,,
statsmodels/genmod/families/family.py,sha256=Iw5840iLmcb0uUih7U69xZTh3M8oPI2zwa0hnaEnZdQ,58129
statsmodels/genmod/families/links.py,sha256=reTW6oKuqpTrvMPK9s5wa-dzlF6GMBFZMle6sh2I7Aw,32267
statsmodels/genmod/families/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/genmod/families/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/families/tests/__pycache__/test_family.cpython-312.pyc,,
statsmodels/genmod/families/tests/__pycache__/test_link.cpython-312.pyc,,
statsmodels/genmod/families/tests/test_family.py,sha256=b0-Xa1cc93jQguFg0xlsnA4V2Z5uPYjXbcb0Kp2_kTc,3680
statsmodels/genmod/families/tests/test_link.py,sha256=utYmH1Pct3_iwnyvwe5YMZoGZ9NH6MH-GTpPDvQcjSY,6336
statsmodels/genmod/families/varfuncs.py,sha256=laYv5eY8nQr5yoBQ1enk56JHZE27Ch28eRhCzfh6kuo,5377
statsmodels/genmod/generalized_estimating_equations.py,sha256=N-T9OVGLPthRgSwJAQ6DLdbPtbqWOgrpTf5ob7OGRRc,115923
statsmodels/genmod/generalized_linear_model.py,sha256=sVdc4KnfYVcp7njvGmZ_w3HA8avIs30iKChui68tHKQ,104187
statsmodels/genmod/qif.py,sha256=aURbhVMnccHmFj7bvpMQqcuiGtTSdWTAKBBXfUvhMaE,16439
statsmodels/genmod/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/genmod/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_categorical_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_gaussian_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_poisson_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/gee_simulation_check.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_bayes_mixed_glm.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_constrained.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_gee.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_gee_glm.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_glm.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_glm_weights.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_qif.cpython-312.pyc,,
statsmodels/genmod/tests/__pycache__/test_score_test.cpython-312.pyc,,
statsmodels/genmod/tests/gee_categorical_simulation_check.py,sha256=DEPiukiLaW4uvXgeULzZsczoVWv2rwUKW6AxXFKx-LI,8581
statsmodels/genmod/tests/gee_gaussian_simulation_check.py,sha256=_y4pbArJnY7P_zB09e03Z-mcLN-jRvFqGoSQmlm348U,9912
statsmodels/genmod/tests/gee_poisson_simulation_check.py,sha256=OP2Q3EdBlm_yI9ttp5qVJF-QO8tDX784M8wW27ZL_hY,9115
statsmodels/genmod/tests/gee_simulation_check.py,sha256=skwG1y5mF3eX2YpJuWCbUqdOtJr3_KpNjg6eU_FhXI4,9451
statsmodels/genmod/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/genmod/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/elastic_net_generate_tests.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/gee_generate_tests.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/glm_test_resids.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/glmnet_r_results.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/res_R_var_weight.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/results_glm.cpython-312.pyc,,
statsmodels/genmod/tests/results/__pycache__/results_glm_poisson_weights.cpython-312.pyc,,
statsmodels/genmod/tests/results/elastic_net_generate_tests.py,sha256=MjVg3X2DBaEXqz7nhzlb9aJgdt5OxOdbGCEUplrVYq0,638
statsmodels/genmod/tests/results/enet_binomial.csv,sha256=MjzR8-TqfV8B-AtVCWgd4Wjb9-NODNaJkestxxDazV0,6529
statsmodels/genmod/tests/results/enet_poisson.csv,sha256=JAH4iRmIoivbWTfqg_Tq6F2Ad1ZvBmPMcq8mQohGZtU,6473
statsmodels/genmod/tests/results/epil.csv,sha256=fNtibUBJvFCUIGxyXnzEvye_CJ7rlAW3xv__yeA9m9Y,16205
statsmodels/genmod/tests/results/gee_generate_tests.py,sha256=gZgFpOewrijINF_Nrp2NcqzWCF3aifBCNi88Y0LzoR4,5371
statsmodels/genmod/tests/results/gee_linear_1.csv,sha256=sV_TnapGUtvFg-cOnAVJI7XGRxoaKNIvA7opW_2Dy4M,10127
statsmodels/genmod/tests/results/gee_logistic_1.csv,sha256=nnJJhAh7r21iJotZSMJt63E0SPQMD05ED8jC1466XBE,9917
statsmodels/genmod/tests/results/gee_nested_linear_1.csv,sha256=w-nykam7hR0agGp71YBX1XnUHhtGeKpqp8ZsZJgULRI,58996
statsmodels/genmod/tests/results/gee_nominal_1.csv,sha256=Ig9cgB80PkrDdh9bxd_7W-lOQvllphfGlhF4gyiPROI,14284
statsmodels/genmod/tests/results/gee_ordinal_1.csv,sha256=9O2EhwVwLvocpQWuFDwZ-_x153hux_m0lajld2TG6w0,29823
statsmodels/genmod/tests/results/gee_poisson_1.csv,sha256=4p0E-PVZWEgzlb-Wwi2Ev4LzJY2DUA1ww2uH2wq-3w0,14969
statsmodels/genmod/tests/results/glm_test_resids.py,sha256=_lC5v9IGrFi-oEK01Zhitak4GJrNsmVMhSF8rHWalzA,321699
statsmodels/genmod/tests/results/glmnet_r_results.py,sha256=lhAx6HOcIpYWQXR5UIJMSP5jiopTeF1yOdc2hQPjP_4,1926
statsmodels/genmod/tests/results/igaussident_resids.csv,sha256=HffakQSVrcBAaNsQExjOYdDb7kQDxy9S1Vo9vRRvNIE,173962
statsmodels/genmod/tests/results/inv_gaussian.csv,sha256=SxJtCoPk1675fg3w3DIFhH0fPiEk3I0wnsckwOEUo_U,741638
statsmodels/genmod/tests/results/iris.csv,sha256=YCfa_b5NIHDR4-0oethYhizJW4nMyd5Gd9JnvlYCeF0,2734
statsmodels/genmod/tests/results/medparlogresids.csv,sha256=_6bHnl8sgDVjTpl9RjK9ciZUT2qPnJLZMp9DYieBl9Q,471390
statsmodels/genmod/tests/results/res_R_var_weight.py,sha256=1zWOXhpF3Anoq-LslsBM_WDzAI22uaNN9TzvQB4HoPE,8572
statsmodels/genmod/tests/results/results_glm.py,sha256=1yUqyFvKpQfLVluGGvcyFMg0wHaZkC_T9EGi0v14ZIY,273706
statsmodels/genmod/tests/results/results_glm_poisson_weights.py,sha256=43tTDMZgXsxBosGtS3F8T6wNvVjwy92rxsDTzcQXYZM,95254
statsmodels/genmod/tests/results/results_tweedie_aweights_nonrobust.csv,sha256=eenV1DiBcUviJY4SiqCrTO18c9o_SirNGb7xxE9HawE,395919
statsmodels/genmod/tests/results/stata_cancer_glm.csv,sha256=ovRRC0UwggG_O-PNggmBU8ltDvoujZvXVGjsQ24HYiE,386
statsmodels/genmod/tests/results/stata_lbw_glm.csv,sha256=KAsUOYVscK7mzhaf8OUGWnXndrqnW93S1Jwpg9RhJzs,6755
statsmodels/genmod/tests/results/stata_medpar1_glm.csv,sha256=DL_d_a0KfQWkjh5iWmsodI6zburVGBXG18GQjsZwg5M,49548
statsmodels/genmod/tests/test_bayes_mixed_glm.py,sha256=0MBC6J82snHzdLqzYe5_-HThJ5g2a8bNqYJElMkGKBw,18166
statsmodels/genmod/tests/test_constrained.py,sha256=YPO9nLKvANM347RHiUiFRvmRo-GrwmkTpp_SK3KZKBI,9189
statsmodels/genmod/tests/test_gee.py,sha256=tEYVZpSuGg5qf6jT78ubtZjFc6UEmDha9rZyKQMktEs,79652
statsmodels/genmod/tests/test_gee_glm.py,sha256=11Z50FtbPwiT6VgIViUAS-GCyNsU0ehP3grJwBQ3OUQ,4282
statsmodels/genmod/tests/test_glm.py,sha256=uTHYCUuJ6kO1DEWP7xzFVA-EgJDfPW6Bacl7CY9QpgI,102706
statsmodels/genmod/tests/test_glm_weights.py,sha256=zqXwT714ylirnon_QeQKNOTHZ8tRErN24nlKct3zJvo,39593
statsmodels/genmod/tests/test_qif.py,sha256=kb-H-BVzQh0-yPN6DXlyWKCZJDIRW4-1Jp5uR_ENymc,3912
statsmodels/genmod/tests/test_score_test.py,sha256=QWIOJ6CJTbsqHtDg8L0uqX2oBK9TRqkZL_UdzPdISZQ,9557
statsmodels/graphics/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/graphics/__pycache__/__init__.cpython-312.pyc,,
statsmodels/graphics/__pycache__/_regressionplots_doc.cpython-312.pyc,,
statsmodels/graphics/__pycache__/agreement.cpython-312.pyc,,
statsmodels/graphics/__pycache__/api.cpython-312.pyc,,
statsmodels/graphics/__pycache__/boxplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/correlation.cpython-312.pyc,,
statsmodels/graphics/__pycache__/dotplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/factorplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/functional.cpython-312.pyc,,
statsmodels/graphics/__pycache__/gofplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/mosaicplot.cpython-312.pyc,,
statsmodels/graphics/__pycache__/plot_grids.cpython-312.pyc,,
statsmodels/graphics/__pycache__/plottools.cpython-312.pyc,,
statsmodels/graphics/__pycache__/regressionplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/tsaplots.cpython-312.pyc,,
statsmodels/graphics/__pycache__/tukeyplot.cpython-312.pyc,,
statsmodels/graphics/__pycache__/utils.cpython-312.pyc,,
statsmodels/graphics/_regressionplots_doc.py,sha256=I-Ij6EcxnMM1SxdbW2YwWv-qfHyrjSea-7Q3JiKwv5Y,7728
statsmodels/graphics/agreement.py,sha256=nknBktzXW4taKkNlMLu2Y7NM3eXE3chijkKZmbul7DM,5292
statsmodels/graphics/api.py,sha256=kvXROlafcJDcNfJqKg7b5Fg3jLC_qZvLCzHy_DLEq1I,968
statsmodels/graphics/boxplots.py,sha256=ehKA71hX1W5zYJG6QlAMCeJ9RxFOIEwXUtmsN_avRSg,16685
statsmodels/graphics/correlation.py,sha256=TEJpnVmv4KHizlB47tPUMLbc0DNBISTzJhqCSXYambg,7755
statsmodels/graphics/dotplots.py,sha256=M1wKFturZk4WvYObcXJkPlqSlfnGtYAQKK2r__bO8oE,17903
statsmodels/graphics/factorplots.py,sha256=ljE-pGtBNBDmcrnYIgZwQb4yEzFcb3VAFEfEFdn4FaQ,7428
statsmodels/graphics/functional.py,sha256=QY_dwB0aWK5ppj0xnmpIGLtY6vBHFGDKtBqQcMhAGiY,31523
statsmodels/graphics/gofplots.py,sha256=GqfmMilQ0BS6cbJxzeRhwHvDHZn9pgNMoGpnEsxrzVw,35575
statsmodels/graphics/mosaicplot.py,sha256=nlJooAktNqKlggqM48EaZICvuqteZuJ3tIMj2ii42lo,26939
statsmodels/graphics/plot_grids.py,sha256=-uOLPujJW2943-7v84i5g8sY-BWV3ZKr9jqYQdyvwks,6139
statsmodels/graphics/plottools.py,sha256=vMQhmikidsNXXJaLVdbeeUFBAmU33bqTJ2JwLsMZROw,635
statsmodels/graphics/regressionplots.py,sha256=P0vxfXXTdoFReUhxgcnWq0C_mYoBu-XvtgdE6ykKVi4,44489
statsmodels/graphics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/graphics/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_agreement.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_boxplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_correlation.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_dotplot.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_factorplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_functional.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_gofplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_mosaicplot.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_regressionplots.cpython-312.pyc,,
statsmodels/graphics/tests/__pycache__/test_tsaplots.cpython-312.pyc,,
statsmodels/graphics/tests/test_agreement.py,sha256=ifSiw9shVF1s6Kkup9QQmCF13uzqMA5kQPdwalRjNmo,1195
statsmodels/graphics/tests/test_boxplots.py,sha256=Us2m-exJOslIP_dRoUCQTqeX-S7Cyhqv076sgsXQ9Ck,3185
statsmodels/graphics/tests/test_correlation.py,sha256=2FpapC4Uv2s9FAukPnfCnXYCZrD97IJ86B8KwKwTruY,847
statsmodels/graphics/tests/test_dotplot.py,sha256=XoNCn56QjSvhmZjnK_Sigrpces5aaaRJTHXS-80CJ9Y,15104
statsmodels/graphics/tests/test_factorplots.py,sha256=m7fXxMwWzIiiNON9ct28cIhKyNm9M9xtYTgC6ZwysPM,3026
statsmodels/graphics/tests/test_functional.py,sha256=3bqBnm_tWqYjl0fkspad4XQlKYFC9O7k9fEPZ6SiDQ4,8969
statsmodels/graphics/tests/test_gofplots.py,sha256=SbaPZjhrO8G-xZYELvrk5bE9XiiLJ5pY4TQMWjbWDvs,21656
statsmodels/graphics/tests/test_mosaicplot.py,sha256=XfF_3xPmToLAWlLn6QffZ7N8vsJ7MrvOEEptYlkAPT8,18912
statsmodels/graphics/tests/test_regressionplots.py,sha256=POmR9hEclne2JOyxrAXpv1JoRL_pzL7MQlhd6qc-yaY,12711
statsmodels/graphics/tests/test_tsaplots.py,sha256=49h-V0sfRrN88V88L1I5Xk66rInMfpMje2mkAUlM1VU,11191
statsmodels/graphics/tsaplots.py,sha256=Lx1PMhl7JiPVwFem9Ln-VPE5m2O-mW4JeqUIAAz8bPg,28299
statsmodels/graphics/tukeyplot.py,sha256=JXhj0FklrhFvbnp04FY1j3e_kRfL5XrG7XnoryVbaBo,2392
statsmodels/graphics/utils.py,sha256=sneUyqvLSoNiUPRhU8FZ2QsncaXHackFiOqruYeZhb0,4032
statsmodels/imputation/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/imputation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/imputation/__pycache__/bayes_mi.cpython-312.pyc,,
statsmodels/imputation/__pycache__/mice.cpython-312.pyc,,
statsmodels/imputation/__pycache__/ros.cpython-312.pyc,,
statsmodels/imputation/bayes_mi.py,sha256=lXryQ5rT7L1vEtWdiuq5PYeD8KwV59P8giMCHSWrPu4,13490
statsmodels/imputation/mice.py,sha256=hzL4q4tBEzJhT16tYgvYGPiUHeMRU6pPN0Lc_6A62oU,45324
statsmodels/imputation/ros.py,sha256=Gfju52riAJUcp2LREGvbUHXWK-arHEJ1rn7O0_U1LO0,18908
statsmodels/imputation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/imputation/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/imputation/tests/__pycache__/test_bayes_mi.cpython-312.pyc,,
statsmodels/imputation/tests/__pycache__/test_mice.cpython-312.pyc,,
statsmodels/imputation/tests/__pycache__/test_ros.cpython-312.pyc,,
statsmodels/imputation/tests/test_bayes_mi.py,sha256=G84L9jsOIJh0EC8AE9wK-J5KkkiRzbWLIkhqZvE6EZ4,5076
statsmodels/imputation/tests/test_mice.py,sha256=vUdsHiWvatt386p7hqhoxqokTzZdz_RAJ3zaMRI5iRg,13183
statsmodels/imputation/tests/test_ros.py,sha256=A_IyzkyCtiZSTG9_r46AqmMqRDhc4dDJm0ny7Rm2qnM,27900
statsmodels/interface/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/interface/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/__init__.py,sha256=kBub1BJGDwLftl8Y5wQPIboth4jhS24Ze16cFP4eSCk,288
statsmodels/iolib/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/__pycache__/api.cpython-312.pyc,,
statsmodels/iolib/__pycache__/foreign.cpython-312.pyc,,
statsmodels/iolib/__pycache__/openfile.cpython-312.pyc,,
statsmodels/iolib/__pycache__/smpickle.cpython-312.pyc,,
statsmodels/iolib/__pycache__/stata_summary_examples.cpython-312.pyc,,
statsmodels/iolib/__pycache__/summary.cpython-312.pyc,,
statsmodels/iolib/__pycache__/summary2.cpython-312.pyc,,
statsmodels/iolib/__pycache__/table.cpython-312.pyc,,
statsmodels/iolib/__pycache__/tableformatting.cpython-312.pyc,,
statsmodels/iolib/api.py,sha256=DX0JJ5jakS8sHeRnG-6z63bBNfsCAlfQrOUKlD0lmOg,202
statsmodels/iolib/foreign.py,sha256=a2BOsCgARfryCsgy8cPho7Cua2UAJG6STnjzwWrqeKo,4345
statsmodels/iolib/openfile.py,sha256=big0YVlyAvDClr_XTDebk5g38LQqOL_zsE304IS0tSs,2129
statsmodels/iolib/smpickle.py,sha256=mgUZ-SNtevKodHdclsYs9a3QSr7kqjtLmh9Ysl2FQl8,922
statsmodels/iolib/stata_summary_examples.py,sha256=cCe49dnbTeteD4Xk6mliB7PsiPLobHZgoOP2rXLnRFo,4614
statsmodels/iolib/summary.py,sha256=1iQ5hNn-W2Mpxqe74Bo-MsF0E4msC4RObBbMHSxM_qs,31093
statsmodels/iolib/summary2.py,sha256=mfUC75c3m1ukY5GOAbsKQ3EC18UL0sUpsHDipFXh_y8,22507
statsmodels/iolib/table.py,sha256=PbnX32wveOnLm2GhEUBii0pP0OD_yLzwfIP0u7_KSs4,33315
statsmodels/iolib/tableformatting.py,sha256=8OxLJ17XeEx948U2uNmINkFx4HSoiPMp8EqpBfymvt8,3462
statsmodels/iolib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/iolib/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_pickle.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_summary.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_summary2.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_summary_old.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_table.cpython-312.pyc,,
statsmodels/iolib/tests/__pycache__/test_table_econpy.cpython-312.pyc,,
statsmodels/iolib/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/iolib/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/iolib/tests/results/__pycache__/macrodata.cpython-312.pyc,,
statsmodels/iolib/tests/results/data_missing.dta,sha256=BqDWGqR2JOVEqy8xrmyFj071kc-PujilaVJ27bE9Pss,1130
statsmodels/iolib/tests/results/macrodata.py,sha256=s5NVdwOyzepeQK2DHgQoIFRJCtWj-3kXx6dnGe9ZVP8,23569
statsmodels/iolib/tests/results/time_series_examples.dta,sha256=gu5nIm245mw9hWRhfAK2pHybxai0Ca7kwfjVScxZE9g,1760
statsmodels/iolib/tests/test_pickle.py,sha256=FxN1Ol_tVlUDpnA2MmnPEbWW3cbcBPm9WQRn_7SiL-c,1720
statsmodels/iolib/tests/test_summary.py,sha256=Ax5d9fjJdVSlldZhk-0Q5-cu8Kc8OWTC7wtTpkOY7LQ,3254
statsmodels/iolib/tests/test_summary2.py,sha256=f6jmjjycGlwRHH8cQvpNE30FOCSsOsN6y1Swx6AJyPI,6529
statsmodels/iolib/tests/test_summary_old.py,sha256=3ZFAPWYyKdWgLnLOzwLOhvbA3VY1km6psYtTUan9EjM,3507
statsmodels/iolib/tests/test_table.py,sha256=nZnFuZjfIp9Nnnw6h88RR07PXDoSI_X4BgbQNq6IVFA,9454
statsmodels/iolib/tests/test_table_econpy.py,sha256=DuJ-ZSkHST_3iBnpWSM0wBcet2_iucmCevZ7pA5YdL8,4196
statsmodels/miscmodels/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/miscmodels/__pycache__/__init__.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/api.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/count.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/nonlinls.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/ordinal_model.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/tmodel.cpython-312.pyc,,
statsmodels/miscmodels/__pycache__/try_mlecov.cpython-312.pyc,,
statsmodels/miscmodels/api.py,sha256=XwAdn6C16w1QxXLkG38jftTka63ZtUGacMCrjERpTu4,241
statsmodels/miscmodels/count.py,sha256=0bgwZ8FphF6rC3GNSiXSmUSfNURe-RgJs8HHjqRi_KA,7021
statsmodels/miscmodels/nonlinls.py,sha256=SsfpkOTGN1CSiwZqFicURSsWRSi1o5PPNDtX5OD4IJM,9324
statsmodels/miscmodels/ordinal_model.py,sha256=PTvNGJyIHcpEKrifOHZJ4pksV0uLnmVBhYKfIMW-OUc,24477
statsmodels/miscmodels/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/miscmodels/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/results_tmodel.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_generic_mle.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_ordinal_model.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_poisson.cpython-312.pyc,,
statsmodels/miscmodels/tests/__pycache__/test_tmodel.cpython-312.pyc,,
statsmodels/miscmodels/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/miscmodels/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/miscmodels/tests/results/__pycache__/results_ordinal_model.cpython-312.pyc,,
statsmodels/miscmodels/tests/results/ologit_ucla.csv,sha256=HUhvszCYUvis5Nxh2IRAvECi7cGlBcv-VPFz6gYSDiA,8278
statsmodels/miscmodels/tests/results/results_ordinal_model.py,sha256=iZXQJUCpF-COwvjQR_PoewDpoUJgHNRgt6e54QgRXTE,5453
statsmodels/miscmodels/tests/results_tmodel.py,sha256=kcGMsywnAyMjEjQtrPF2bPadgVa9BUlPecf7yM1l1D0,11985
statsmodels/miscmodels/tests/test_generic_mle.py,sha256=TrD0oEtVvVL6k2kv1ibpY2dc0vxYR-0dbCL1TfhZAU8,7842
statsmodels/miscmodels/tests/test_ordinal_model.py,sha256=es-OruHfMlSpjknCxDIaY_sg8I3aoP_1gBFNJOcYmlg,19605
statsmodels/miscmodels/tests/test_poisson.py,sha256=nvy7DnHacafyjfLE5eJtizbvCkX2Kir_Okn9YEuvqCQ,7878
statsmodels/miscmodels/tests/test_tmodel.py,sha256=fezu_c0dNxaP2PXQlZsL0OrIcjn-WXEf_xzeZO1UDY0,7607
statsmodels/miscmodels/tmodel.py,sha256=6xXSRJCCqKBAPRA7f5u-UVvzuiLqctesxToAJ4HmbOg,7266
statsmodels/miscmodels/try_mlecov.py,sha256=ePS7PzsOiZS-uQ2Tdd20XsRzaKUXnG2ZTzjZgmS6Uak,6971
statsmodels/multivariate/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/multivariate/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/api.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/cancorr.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/factor.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/manova.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/multivariate_ols.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/pca.cpython-312.pyc,,
statsmodels/multivariate/__pycache__/plots.cpython-312.pyc,,
statsmodels/multivariate/api.py,sha256=syQRuH7FNU2ey6tqAH2jRMxX9PHzspaxr1bw2yTAhxU,245
statsmodels/multivariate/cancorr.py,sha256=0YLWx0LmNsbuHS1vewcPo-TyeruTcwJV6FfIE8dbjXQ,5887
statsmodels/multivariate/factor.py,sha256=n1Egw-Dz07Ju1iaj8xmqxEd1zrG0RjjUoRPHntMRwAE,35669
statsmodels/multivariate/factor_rotation/__init__.py,sha256=9Cu_afKbuM36KSuuceNCrJQ2-tv4XDKjodWa288juZ8,1232
statsmodels/multivariate/factor_rotation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/__pycache__/_analytic_rotation.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/__pycache__/_gpa_rotation.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/__pycache__/_wrappers.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/_analytic_rotation.py,sha256=96YJ-wOZVD_M88TuIufCfCfBFSw1MhQBh5nW8gA40xQ,3984
statsmodels/multivariate/factor_rotation/_gpa_rotation.py,sha256=K_Zc47J3MOmUxRAKJE64ikxe-VrMLgbAREjlsw2M0Xs,18625
statsmodels/multivariate/factor_rotation/_wrappers.py,sha256=HsE2_jPA9JoYtuGKnb9aGGRtkR9Huyoz2hqge8jdBTU,13611
statsmodels/multivariate/factor_rotation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/multivariate/factor_rotation/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/tests/__pycache__/test_rotation.cpython-312.pyc,,
statsmodels/multivariate/factor_rotation/tests/test_rotation.py,sha256=bDy-9xjAHWj25kycBgn9XBxs2GE2tCFJzmpJLn-gvHQ,21805
statsmodels/multivariate/manova.py,sha256=nr4R-EcqxSJaELZfzvxlY347FThqcMtYYkP2LPsCEdo,4758
statsmodels/multivariate/multivariate_ols.py,sha256=NkC4IptXV_YiHBpRawiHGW0r5xzaYkeiX1a5a_WhKf4,20931
statsmodels/multivariate/pca.py,sha256=vLYb4Lk9MuYBdpBmelk1Nsl6yGD-U_MuGXJ9Lqk6heI,33067
statsmodels/multivariate/plots.py,sha256=M3GMT9lg3NeU0khnBlXqi7scqQifMM5PtadgWoHj85g,4378
statsmodels/multivariate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/multivariate/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_cancorr.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_factor.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_manova.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_ml_factor.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_multivariate_ols.cpython-312.pyc,,
statsmodels/multivariate/tests/__pycache__/test_pca.cpython-312.pyc,,
statsmodels/multivariate/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/multivariate/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/multivariate/tests/results/__pycache__/datamlw.cpython-312.pyc,,
statsmodels/multivariate/tests/results/datamlw.py,sha256=5FrYd6WqIk92Rtzc6wpuyFZQGkMNDA588Ynzw6ha3Ro,15571
statsmodels/multivariate/tests/results/factor_data.csv,sha256=vc1p_saZaZGnkxuGvNrZTzjf_RnJjNrzs-Vk66kdn_c,3719
statsmodels/multivariate/tests/results/factors_stata.csv,sha256=pdb2XP73jg_bN3VJAtJWv-KCXNzRDsOc3LaGmFpMB8E,8351
statsmodels/multivariate/tests/test_cancorr.py,sha256=PydnVGsdwNyLndZrC2WewNsyyPtE5x2LzddG8PWT0g0,5308
statsmodels/multivariate/tests/test_factor.py,sha256=eMJN_MM809_aoIyOc6hs6pY1tbNglsEDoh_VE680p0k,11497
statsmodels/multivariate/tests/test_manova.py,sha256=CXRVMaU7Bk8IHApiK3TjXqrePQT4OS9QNTcT1MEMpSg,9667
statsmodels/multivariate/tests/test_ml_factor.py,sha256=0ihJbydZ64Yl5BaUOajrmvH2qVYaK2fV0PkGRGe1cx8,6124
statsmodels/multivariate/tests/test_multivariate_ols.py,sha256=SvvBnQSHi-Q52y4Lak7qZLZQKnGp7LtzYvzEl5zr4Kg,8982
statsmodels/multivariate/tests/test_pca.py,sha256=4_ZYhVhZGH7sUhMx5U97vsIqi2z49bi6FIEaAd7HDEQ,16544
statsmodels/nonparametric/__init__.py,sha256=YfBb3ny6Zl0t_ifCYzXS97Bj0xY-JvEF3qJ6xPkvaVs,229
statsmodels/nonparametric/__pycache__/__init__.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/_kernel_base.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/api.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/bandwidths.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kde.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kdetools.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernel_density.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernel_regression.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernels.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/kernels_asymmetric.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/smoothers_lowess.cpython-312.pyc,,
statsmodels/nonparametric/__pycache__/smoothers_lowess_old.cpython-312.pyc,,
statsmodels/nonparametric/_kernel_base.py,sha256=y4CZXg-GPchaYT_SE5ayEWOzwqcZP_J4Uo_d-rsBQuM,18297
statsmodels/nonparametric/_smoothers_lowess.cp312-win_amd64.pyd,sha256=1Bi_D65T3AmOILpxI09TlmhLfhzB6lN2lhWt3J64bdo,202752
statsmodels/nonparametric/api.py,sha256=2nmyA1UJo618g3h2EgZHFgkX8HObba_M1AWeBbnt228,532
statsmodels/nonparametric/bandwidths.py,sha256=0g2Pz6CFEI2aZwX6btfzZV5-l2ARE0Sr6Y8oSgVd2ls,4578
statsmodels/nonparametric/kde.py,sha256=rbesG6QQpzilyjXT2IFEGYaxPucUGc_civ873hDkLBs,19159
statsmodels/nonparametric/kdetools.py,sha256=c1hx_IlgghJ-YRVJhUPjzeN0Y4xErlL025nI-ahrVk8,1350
statsmodels/nonparametric/kernel_density.py,sha256=_e9kuH47sNEIu7yWGKDkRspZitrm7u0KawQRRMWFlLk,25886
statsmodels/nonparametric/kernel_regression.py,sha256=C5gxmh9j-mK3a_KQFMOdGH3ixBaLTEpAooePQivaIHE,34852
statsmodels/nonparametric/kernels.py,sha256=J6gcSLshkzcofFyaOvRykONzbfwfhAQCZbFX0pz3k0o,6997
statsmodels/nonparametric/kernels_asymmetric.py,sha256=vIj8Bz_fC2d3X80vPhiTcGpjUALVfLLdfmfgpLevHPk,26514
statsmodels/nonparametric/linbin.cp312-win_amd64.pyd,sha256=7eucAGO5mnEI3NoD-bg22qLCpTTrzVp7GNglfW8MiFY,58368
statsmodels/nonparametric/smoothers_lowess.py,sha256=ZHgGxssFUA2eRVazwI42tJoc13IkgOWsPfp47uvWa9g,10100
statsmodels/nonparametric/smoothers_lowess_old.py,sha256=7VdhVla9bBFVvO8pdM8Vyj3gDPd-Wvx2s8SeYdXUfJU,10386
statsmodels/nonparametric/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/nonparametric/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_asymmetric.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_bandwidths.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kde.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kernel_density.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kernel_regression.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_kernels.cpython-312.pyc,,
statsmodels/nonparametric/tests/__pycache__/test_lowess.cpython-312.pyc,,
statsmodels/nonparametric/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/nonparametric/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/nonparametric/tests/results/results_kcde.csv,sha256=RCPvPvPgvyZFhpxv62zaClHungfsImVORyjxRtfMiaY,12087
statsmodels/nonparametric/tests/results/results_kde.csv,sha256=m7khB2te6EAP42aLq35n-7bSmljr8rOflBbAK5fGHG4,9578
statsmodels/nonparametric/tests/results/results_kde_fft.csv,sha256=wNFj84mct3y0_25Yi_QafrfM8frwqDR6v4dounGwKWE,12800
statsmodels/nonparametric/tests/results/results_kde_univ_weights.csv,sha256=Lp0EiXJX6Dtv7H_hwKBpE3CaTfNxuckOv6PHXJ90ntI,6043
statsmodels/nonparametric/tests/results/results_kde_weights.csv,sha256=rEa85agHdegwxZk-hmubUuTYGtZFFQGDzmWQkSVO8MY,862
statsmodels/nonparametric/tests/results/results_kernel_regression.csv,sha256=xGqPpBBs4F74ZJxWeJCh8JWdJk_doQgXwpYA_K96taY,6800
statsmodels/nonparametric/tests/results/test_lowess_delta.csv,sha256=3VkTo8iOR3uWy5tEp-C_-4uJJxt1mmQxCzWx1rnM3rY,8371
statsmodels/nonparametric/tests/results/test_lowess_frac.csv,sha256=rmf8w-92QY6EnO39I4ESOIUOkdWhvGiynjYHXgFI4bQ,2180
statsmodels/nonparametric/tests/results/test_lowess_iter.csv,sha256=qNWS0ulvpx7BxKAsF0Qlhu8RFuLeO5oDzQGDiO5x6oE,978
statsmodels/nonparametric/tests/results/test_lowess_simple.csv,sha256=oOrjWI_7kcuQhpE-dW0XzlzlR6pxhuIrOlsgW2yzcJM,631
statsmodels/nonparametric/tests/test_asymmetric.py,sha256=Tu-RuQ-B9F7NUaP22yDUVBvPp-ece8ic_Zo1dPe6_OE,4529
statsmodels/nonparametric/tests/test_bandwidths.py,sha256=t5igVb6A7ZKEXH5yj9p9Ko1UJppm502L9ECntfeYy14,2447
statsmodels/nonparametric/tests/test_kde.py,sha256=-3lfZ8Mn7CDslGrTN3Nnw-YuhF6vpPFeTNh9Vz2bsZk,13062
statsmodels/nonparametric/tests/test_kernel_density.py,sha256=z6UviO1ZTTT8C8psypZIPpB2Z5GE4ZaUYWppYRnNLXU,18850
statsmodels/nonparametric/tests/test_kernel_regression.py,sha256=xxhuMGrK_g1ptmhwqgDyAqPy4erp3WdgrupE1kYTtJg,15937
statsmodels/nonparametric/tests/test_kernels.py,sha256=zCRGUMkMU0lyFhe7tFCn-culFqXc4XNnyG3P2JR5VDA,4945
statsmodels/nonparametric/tests/test_lowess.py,sha256=fmJmOg2trF69crwSmIaz-WW-g-5_bJQ-bAFGHsVzeLI,10866
statsmodels/othermod/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/othermod/__pycache__/__init__.cpython-312.pyc,,
statsmodels/othermod/__pycache__/api.cpython-312.pyc,,
statsmodels/othermod/__pycache__/betareg.cpython-312.pyc,,
statsmodels/othermod/api.py,sha256=Z225wxMNA3YST3dQR7dPaQ4g3t2oE4kgSzSZ9fvKKcM,56
statsmodels/othermod/betareg.py,sha256=RWM9GYm4MFvma8mki8PHp2vDPKsMomTPVVlzyhyi7hw,31441
statsmodels/othermod/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/othermod/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/othermod/tests/__pycache__/test_beta.cpython-312.pyc,,
statsmodels/othermod/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/othermod/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/othermod/tests/results/__pycache__/results_betareg.cpython-312.pyc,,
statsmodels/othermod/tests/results/foodexpenditure.csv,sha256=3A0bac1ABgoFgqpOKlgSoc5yd9klQgVmWMF3JqDHbRQ,615
statsmodels/othermod/tests/results/methylation-test.csv,sha256=IF5DUHxjWoyKWqM4WFM6bTeYaHhP0VwcIvItWaY-Dog,2920
statsmodels/othermod/tests/results/resid_methylation.csv,sha256=rAaiC4IH5iKzLfyMCkWEUzNNMybBhJVJnpeAm8s-hss,4764
statsmodels/othermod/tests/results/results_betareg.py,sha256=pGF_VVI2_fzNAV9NUYS7_-SCIGwPNYMNqTdHapS733U,3398
statsmodels/othermod/tests/test_beta.py,sha256=4Amj0ZLsRpZ0Wbhu9azi3Wto0TUAtw_9F8L65nVHb08,16073
statsmodels/regression/__init__.py,sha256=Kbc5oI18PfKtgo0IZw3lsUfDu2Xv-khWun9DAej1Y00,149
statsmodels/regression/__pycache__/__init__.cpython-312.pyc,,
statsmodels/regression/__pycache__/_prediction.cpython-312.pyc,,
statsmodels/regression/__pycache__/_tools.cpython-312.pyc,,
statsmodels/regression/__pycache__/dimred.cpython-312.pyc,,
statsmodels/regression/__pycache__/feasible_gls.cpython-312.pyc,,
statsmodels/regression/__pycache__/linear_model.cpython-312.pyc,,
statsmodels/regression/__pycache__/mixed_linear_model.cpython-312.pyc,,
statsmodels/regression/__pycache__/process_regression.cpython-312.pyc,,
statsmodels/regression/__pycache__/quantile_regression.cpython-312.pyc,,
statsmodels/regression/__pycache__/recursive_ls.cpython-312.pyc,,
statsmodels/regression/__pycache__/rolling.cpython-312.pyc,,
statsmodels/regression/_prediction.py,sha256=QW7E_q1RA2zGMZ92TdCqLXu_TzAM2JKEUmj_UeeY4RQ,7031
statsmodels/regression/_tools.py,sha256=3CVExSXHIUGxIVs3wHMQegLCgA5fAMbf0weLssGzYBg,4334
statsmodels/regression/dimred.py,sha256=B6TyFg5NVRxPNQoiF6nCYQehZUkQPxeLizAM430kIhs,20264
statsmodels/regression/feasible_gls.py,sha256=aiC3JIAX_cCmRiYzfMgPguy4X98pAmwJ-J_6PtbjiH4,7013
statsmodels/regression/linear_model.py,sha256=Lhvi06apbaKdaQj_j9EV1lQ5K9778Y2IB97s9hUBu2k,118364
statsmodels/regression/mixed_linear_model.py,sha256=N8VwGkr8qhESbyNx4aDbPlxT3Fk7GhFR-Zu87AcAdb8,104344
statsmodels/regression/process_regression.py,sha256=zFKH-QvHUPMR5yMb-KrVeK8B3-GUhNFOLhJJtY6_Bto,28599
statsmodels/regression/quantile_regression.py,sha256=Bvsj9h4rdUzjk_0OpFLHh5Dm32ZqVYoj1ia_uhkWQCQ,14088
statsmodels/regression/recursive_ls.py,sha256=f88kmJmsZuf02q_-vfuJkHBugkW3rSzDXjF0dyTkFBs,32424
statsmodels/regression/rolling.py,sha256=3iV0etDE3yh9vhuYN5MeabacivYJjqaKXVNd3DbrJiQ,28524
statsmodels/regression/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/regression/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_cov.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_dimred.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_glsar_gretl.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_glsar_stata.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_lme.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_predict.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_processreg.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_quantile_regression.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_recursive_ls.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_regression.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_robustcov.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_rolling.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_theil.cpython-312.pyc,,
statsmodels/regression/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/regression/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/regression/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/generate_lasso.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/generate_lme.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/glmnet_r_results.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/lme_r_results.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/macro_gr_corc_stata.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_grunfeld_ols_robust_cluster.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_macro_ols_robust.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_quantile_regression.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_regression.cpython-312.pyc,,
statsmodels/regression/tests/results/__pycache__/results_theil_textile.cpython-312.pyc,,
statsmodels/regression/tests/results/dietox.csv,sha256=IQTe1l_KuQT2UIvvBjOLlRvoKV91MDmVFjjxEVY5D1s,29856
statsmodels/regression/tests/results/generate_lasso.py,sha256=0QzlnCDdwkrcCZt1Yiu3UmbMcJnHwmX7jrWi6XnoKNA,632
statsmodels/regression/tests/results/generate_lme.py,sha256=UBaoiY5QbXdnf8Uk9H4uteUcDX7pSwlN8x60zjUaB5s,2030
statsmodels/regression/tests/results/glmnet_r_results.py,sha256=FB7bPpQoJqP9LDaHh6bjne6luDUk5M1r7D-1JnMqZQE,6855
statsmodels/regression/tests/results/lasso_data.csv,sha256=yCyI8XyL2H8oJPiMWS_B8wQAY_UVEsML0Xz9L-nqp9g,6834
statsmodels/regression/tests/results/leverage_influence_ols_nostars.txt,sha256=l7rQNSWq3pqKvWkJ4JiWXHbJz8UCPCcGCwXnBayPESo,14543
statsmodels/regression/tests/results/lme00.csv,sha256=thgKWeMr-CelfUXl2T9pMkg4amTh9jOuFyXLGo0evBc,6260
statsmodels/regression/tests/results/lme01.csv,sha256=LCQVBVFspana2kiqdnsWpIy5tE4mLfTZNLGbqPd9uuQ,6610
statsmodels/regression/tests/results/lme02.csv,sha256=kXJfU3GbZ7-Zk68TdHI777jL6CUJEwzs3NOAev__91o,7654
statsmodels/regression/tests/results/lme03.csv,sha256=M5Qm5uRVLkGUY-If-ONIcJ8vCzAh2h6JWgdprHBJhXE,7461
statsmodels/regression/tests/results/lme04.csv,sha256=VbY6FudLe2IDWCFoRoWo16hDLqvkVOsRN5rmjkTGPdo,9869
statsmodels/regression/tests/results/lme05.csv,sha256=D0M9QJIgHK6owmuS-SIsm3gwEPBP3dBKzHVF3_ctoCQ,10474
statsmodels/regression/tests/results/lme06.csv,sha256=Crri_f0kjs_T6ZlXOgcfEXDhOc1qakg8SAbYY24GDQE,8468
statsmodels/regression/tests/results/lme07.csv,sha256=vyJu7pUcZAgHl6_7o6VJoUSmGCyrg_Oe3gex6ZoZhfQ,8502
statsmodels/regression/tests/results/lme08.csv,sha256=wKHPP7-B82KnDDKWJXApsQdUsfFZsB1v9M029vviKBc,9753
statsmodels/regression/tests/results/lme09.csv,sha256=BfivHwJ6_G218j1ubqHMFadrH1bMR0HK72a3LvlZvzk,9990
statsmodels/regression/tests/results/lme10.csv,sha256=GhRq_VD7DHa6lvZgGkzpxq2mRtCcR6YWz2Bo5K0dweQ,11983
statsmodels/regression/tests/results/lme11.csv,sha256=RQoZKq_2j3VM8c7OtkzrY2B9KKDz_rXhW5Dx0zYcPv4,11906
statsmodels/regression/tests/results/lme_r_results.py,sha256=osDvQZYOYZSErbaX9d6CdlzRZsmXUJJg1dnjAKX5mc0,15667
statsmodels/regression/tests/results/macro_gr_corc_stata.py,sha256=Jx4zarx87aPzb8EuW91e8QjjCuHzOSMDt9IB8-bp09M,11100
statsmodels/regression/tests/results/pastes.csv,sha256=SyuWd1zbQ7UKZ5WVdxjwUZq6zopjfn89Cy1FM5XdV6w,1457
statsmodels/regression/tests/results/results_grunfeld_ols_robust_cluster.py,sha256=bFSHTvxIgpixc5SFMbmMFuV3Nc7TCMKPtg9Gls8vsQM,23552
statsmodels/regression/tests/results/results_macro_ols_robust.py,sha256=H1xAtYlhRB9dTpmRYjJjZxejslDA9opMLa56O6kNHH0,12960
statsmodels/regression/tests/results/results_quantile_regression.py,sha256=Eu96rw30EXzfz7x5G7heirI3LXrV7KQhJL294TQZzx4,27646
statsmodels/regression/tests/results/results_regression.py,sha256=yzSzdDig7DnGx45-8bfBAvjXGTtHVyFnkrFe7afS2ig,9784
statsmodels/regression/tests/results/results_rls_R.csv,sha256=LCj6yKr-iBAT2YS20vpV_8CLeqW7nIDvDjZO4VOsFVI,14286
statsmodels/regression/tests/results/results_rls_stata.csv,sha256=mEkq8_lC-giOvz5TJzIgWBNPj3WcuPN01lCo5lqFu4U,13108
statsmodels/regression/tests/results/results_theil_textile.py,sha256=GxJ7SQjhRFqRP09hUFsW_tTuTwMj-yLU-V-st3BTpHs,2201
statsmodels/regression/tests/results/theil_textile_predict.csv,sha256=xp87iNDrkqZce8iWm5BE1hIi-T8H_2huDi4_feJ6Cug,365
statsmodels/regression/tests/test_cov.py,sha256=tasiuupgmy26JHqa0NQqYcLzWuypJP6rlMQs_2G2PDA,1185
statsmodels/regression/tests/test_dimred.py,sha256=BoIzvD7W0cRbgJgbBseprIgyimFoAuAu-zEnXfIa_5g,6682
statsmodels/regression/tests/test_glsar_gretl.py,sha256=Bm_5Wrb_BJoO5x9mSM6Sg9LzMyp0Qgtix42bkgJY7ls,25924
statsmodels/regression/tests/test_glsar_stata.py,sha256=SKvE9lfFdSNIxS39VIuQ2PUO5d5fYYTzu2hPqSCTv-o,3420
statsmodels/regression/tests/test_lme.py,sha256=f8zxhxl_C_KafpQu9Wc-NDq5wLseer1pRbFZSNpKNUg,45876
statsmodels/regression/tests/test_predict.py,sha256=UaOF3-DaKbqHnqLYUV5DveOD6dfbOnCiWKXRAHA0xLg,10144
statsmodels/regression/tests/test_processreg.py,sha256=U8ebRN03DvC4uA2ELwPjZAJdBCOzkDpS0CNij7qiZbo,5937
statsmodels/regression/tests/test_quantile_regression.py,sha256=N5kbYcCPTicLvtHOgb1pYW7nN2ElxvPaKBGMxcLTVGU,11115
statsmodels/regression/tests/test_recursive_ls.py,sha256=9EJ1m2oYYHJudUXD1lM6-Z5JPUFL5QBGSR6wOLZ2ZUY,19047
statsmodels/regression/tests/test_regression.py,sha256=wJvLmsA8ls_0z4QdR3WjHIAy19XzxK2dAGPtmIm0s5E,52874
statsmodels/regression/tests/test_robustcov.py,sha256=VAdpmz0ifmYn9juxlRJKHSuYehwcXsUa1q3Wz3iLYow,32496
statsmodels/regression/tests/test_rolling.py,sha256=Mj8hnJ_vI8D7iGAEaBT3ERSDvJei6RJRo66F7DqljKE,10524
statsmodels/regression/tests/test_theil.py,sha256=Y83bZvfY1-df1bZZJvBmJ2Vx11UX7ZKMHy4eJtqCYc4,13451
statsmodels/regression/tests/test_tools.py,sha256=giKC9LMATobah2tiVhAUN5zSNzh5EBniBpzf4p9hHtM,2668
statsmodels/robust/__init__.py,sha256=24yqG7VUStobip4PpsoOsUTVVUtnJloNne5vbTLIJws,260
statsmodels/robust/__pycache__/__init__.cpython-312.pyc,,
statsmodels/robust/__pycache__/norms.cpython-312.pyc,,
statsmodels/robust/__pycache__/robust_linear_model.cpython-312.pyc,,
statsmodels/robust/__pycache__/scale.cpython-312.pyc,,
statsmodels/robust/_qn.cp312-win_amd64.pyd,sha256=ZPr1IaYkBZ7vN5WSCnHVGvxKP0uzShbFqkdQ57ITXXs,173056
statsmodels/robust/norms.py,sha256=SFC2icgtNfMlFt6vtKp6JdVBUhXX5VyjGuiwyr--X74,24978
statsmodels/robust/robust_linear_model.py,sha256=Zp6jX2h0fI-2ER-BssG4_BGnvn5QrzvtD0B8QvyvoI0,22342
statsmodels/robust/scale.py,sha256=ZLe3dOCim2FjbmytZ3JBNoDJWlv0bpZPmCA5qn7j2b0,12446
statsmodels/robust/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/robust/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_mquantiles.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_norms.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_rlm.cpython-312.pyc,,
statsmodels/robust/tests/__pycache__/test_scale.cpython-312.pyc,,
statsmodels/robust/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/robust/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/robust/tests/results/__pycache__/results_norms.cpython-312.pyc,,
statsmodels/robust/tests/results/__pycache__/results_rlm.cpython-312.pyc,,
statsmodels/robust/tests/results/results_norms.py,sha256=zjDarPYZwKJQgCl3UpTQ4bbToyLajo0c_2LaVCTudGE,1408
statsmodels/robust/tests/results/results_rlm.py,sha256=0ZUkfmHGVGbts8jvMWBrBlI7XeQGqBuzAFDfc0uYOAM,19220
statsmodels/robust/tests/test_mquantiles.py,sha256=H7Rtwy6mzQVpDefVpB5qAHg7_ZtoBVPronHrARPJi64,2730
statsmodels/robust/tests/test_norms.py,sha256=yNrWHXxHkC75HP5ID6i-E9G3P6bcTUUDsmqEq56fLUg,3172
statsmodels/robust/tests/test_rlm.py,sha256=YH6K5z9m6hTretwsbstSrPOyjVzPbSCnAlNQ7Y8PiZw,12531
statsmodels/robust/tests/test_scale.py,sha256=AVkT3mPeu3HvpLFI8bZGVLsfWwggWRhCYYK1xOENQC0,8580
statsmodels/sandbox/__init__.py,sha256=hBu1UjYNK9yFtiD8VRS9jD_k2Pk2YYxiABxabysov8I,104
statsmodels/sandbox/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/bspline.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/descstats.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/gam.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/infotheo.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/mle.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/multilinear.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/pca.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/predict_functional.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/rls.cpython-312.pyc,,
statsmodels/sandbox/__pycache__/sysreg.cpython-312.pyc,,
statsmodels/sandbox/archive/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/archive/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/archive/__pycache__/linalg_covmat.cpython-312.pyc,,
statsmodels/sandbox/archive/__pycache__/linalg_decomp_1.cpython-312.pyc,,
statsmodels/sandbox/archive/__pycache__/tsa.cpython-312.pyc,,
statsmodels/sandbox/archive/linalg_covmat.py,sha256=tXtU8zmsJPb-teROYYkQpI4L55r6p2fl1bRXSIxf7rg,8472
statsmodels/sandbox/archive/linalg_decomp_1.py,sha256=p3HRoyn-j1U2rvKebuAq4edUcHHvXOwVNfuJeYJEEbc,7665
statsmodels/sandbox/archive/tsa.py,sha256=l5_85AEyNL3A9xbhkimPlKvBeM4NHfR7j6RG9ipizrA,1503
statsmodels/sandbox/bspline.py,sha256=9sIlLI6m6QCV0OH6c--ZzZCJUnqdnRldfwdzIL1NesI,20438
statsmodels/sandbox/datarich/__init__.py,sha256=fxmYSNWrx0PeLceLzIGXNET7FUggbtLFocjQH9SFQkI,6356
statsmodels/sandbox/datarich/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/datarich/__pycache__/factormodels.cpython-312.pyc,,
statsmodels/sandbox/datarich/factormodels.py,sha256=yRLHAs6X_sdf4IcG8FyQieED-nkZ9_7BscWgnqH38M0,6830
statsmodels/sandbox/descstats.py,sha256=cXNYT83yxIGtVG-JAA9Mha4OFNjalVNntUdm8CbVIpg,5951
statsmodels/sandbox/distributions/__init__.py,sha256=ajeGMUsVeJD_-UAndBB3CgyLkFuHszTEnzHZc2ax2g4,664
statsmodels/sandbox/distributions/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/estimators.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/extras.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/genpareto.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/gof_new.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/multivariate.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/mv_measures.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/mv_normal.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/otherdist.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/quantize.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/sppatch.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/transform_functions.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/transformed.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/try_max.cpython-312.pyc,,
statsmodels/sandbox/distributions/__pycache__/try_pot.cpython-312.pyc,,
statsmodels/sandbox/distributions/estimators.py,sha256=PXNzaWABh0y29UKw9jfcoN-NTGTMfhW2sVCLpVDOtWw,23413
statsmodels/sandbox/distributions/examples/__init__.py,sha256=MsSFjiLMLJZ7QhUPpVBWKiyDnCzryquRyr329NoCACI,2
statsmodels/sandbox/distributions/examples/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_extras.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_fitfr.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_gof.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_mvelliptical.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/ex_transf2.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/__pycache__/matchdist.cpython-312.pyc,,
statsmodels/sandbox/distributions/examples/ex_extras.py,sha256=H8BUo-W8jFrsK4XNmT6ewYXteuNms6KT4pYTHwzWJHY,4023
statsmodels/sandbox/distributions/examples/ex_fitfr.py,sha256=knqAkn1a7Yqb8qU9x9i3db-mB6GYTNsCVfDtR8zxaHs,927
statsmodels/sandbox/distributions/examples/ex_gof.py,sha256=VnEw54dmxTGKFPli6UzbIOU5xaijGXsNljWLAyPj_iA,392
statsmodels/sandbox/distributions/examples/ex_mvelliptical.py,sha256=72yTuCjKAcF7X5231IIUUsacm2_wEW3QJZnw_FCQ63Q,5108
statsmodels/sandbox/distributions/examples/ex_transf2.py,sha256=t1swwdom3iQMKUq9ZOEZjpy_t-VN_bkE1igZa_4_DN0,13458
statsmodels/sandbox/distributions/examples/matchdist.py,sha256=J57I8WTty6Zxpw-4nQz4TSdH_gcv3TkvHLAdoNQWrLY,9763
statsmodels/sandbox/distributions/extras.py,sha256=lJQ6zJeq3cO8_ZT4Lsjgk7rHsddkwjInT7MANmaDK_c,40000
statsmodels/sandbox/distributions/genpareto.py,sha256=RF_1D4ETZodjAV4QdIaoq0hUpD-sYv6yicOOsaGDVvM,10417
statsmodels/sandbox/distributions/gof_new.py,sha256=WBEwkQq3bo6O2qKBt7vFhdVo1gQ1hegmWQgWOORKmgA,21361
statsmodels/sandbox/distributions/multivariate.py,sha256=DslVQlJSltw8837OnXJgMe9M7FlrI1gvL5PpBVmAQrk,4847
statsmodels/sandbox/distributions/mv_measures.py,sha256=BoD2jhoEC_ciDZVhGCO48NDu0i4o6vzyRA2XJ_Gg9QI,6256
statsmodels/sandbox/distributions/mv_normal.py,sha256=I_qaqwp-Aw6NYLg9q0rYVxYsAIFjYDhvnMHFVZJorTw,38617
statsmodels/sandbox/distributions/otherdist.py,sha256=KF59km3GAod7kZ3oKQVzS4adF9JgzeUiXbwKc6kDxew,10079
statsmodels/sandbox/distributions/quantize.py,sha256=P79ib1GsXwayEghPiv17ZPxDX48ldCi1vaFkfNgHGbI,4225
statsmodels/sandbox/distributions/sppatch.py,sha256=-uTLSqtCvjPeTlV1vlRpHSTXoliwvBzwaMzAnYsmook,24038
statsmodels/sandbox/distributions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/distributions/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/_est_fit.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/check_moments.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/distparams.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_extras.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_gof_new.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_multivariate.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_norm_expan.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/__pycache__/test_transf.cpython-312.pyc,,
statsmodels/sandbox/distributions/tests/_est_fit.py,sha256=QUoj9e6jYKn_RtoW0_Blg57ohbAQYJO-vV58o6eyVA8,2560
statsmodels/sandbox/distributions/tests/check_moments.py,sha256=34cR1XmwsXI7p2DnUwAVxqW2DCCjBHHzBnMK6smRVcY,5389
statsmodels/sandbox/distributions/tests/distparams.py,sha256=XukQUasxLloM1gPKA5JOySZHC_zkXKaN9UgcAlqFNiU,5128
statsmodels/sandbox/distributions/tests/test_extras.py,sha256=MLG602v2CLJQSF84RUVut62T6XU4HzNOBOLkTMwXAzw,4762
statsmodels/sandbox/distributions/tests/test_gof_new.py,sha256=7UKd0jxT5zUSXV8oyRn9UjMGl8tPTz_B-Kr5LS-2LpE,962
statsmodels/sandbox/distributions/tests/test_multivariate.py,sha256=THaLIpcrczh8W-eegR_XXHjVU5mRaT3t__H4-61sJiU,6278
statsmodels/sandbox/distributions/tests/test_norm_expan.py,sha256=i83qv52XlJ2TELpQPpNj-HtL86ibsPNVppeMwBUwFRo,2767
statsmodels/sandbox/distributions/tests/test_transf.py,sha256=v29Y2zvv84RKto3SZsn35tyu-G7DPbjG9izcEtOqWBY,6565
statsmodels/sandbox/distributions/transform_functions.py,sha256=gZKiB7204XVzIKvA1IuaxlwBoXni-2iZlGKZOkRR3iA,3623
statsmodels/sandbox/distributions/transformed.py,sha256=2AmClDa9CJXcGvTQBuX9uSD6hrXQghDf7b_VWSyKoUA,16438
statsmodels/sandbox/distributions/try_max.py,sha256=reOzEVS6HfDhG95G0IC-mAt04XpvOtI0jviJyEQaxp4,2345
statsmodels/sandbox/distributions/try_pot.py,sha256=B06UZPiwQ50bt_l8TIJv9VNIvt_b10RvVtQEtvFyrcQ,1919
statsmodels/sandbox/gam.py,sha256=AEWgYytTQuCO0OOfs-MpFAYJw1enKSqaFmIxR8CJ-gk,15359
statsmodels/sandbox/infotheo.py,sha256=pawc1GIO7qD1isSNf4rK4spNKB_hw0nTu8CMpT1p4IE,16548
statsmodels/sandbox/mcevaluate/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/mcevaluate/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/mcevaluate/__pycache__/arma.cpython-312.pyc,,
statsmodels/sandbox/mcevaluate/arma.py,sha256=cp34qx2U90V5CWJh1mUYRHc5ZsmoAAUXOA2u2AnJ9Fo,4555
statsmodels/sandbox/mle.py,sha256=5USs08yM3apF5XGQCwX9W4T4CELX1P8mOtOeDrOH6uo,1658
statsmodels/sandbox/multilinear.py,sha256=DwIKLfoYwnkz00aNVmwjaasMbNjTgL96x7kWab6JYAM,13885
statsmodels/sandbox/nonparametric/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/nonparametric/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/densityorthopoly.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/dgp_examples.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kde2.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kdecovclass.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kernel_extras.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/kernels.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/smoothers.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/__pycache__/testdata.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/densityorthopoly.py,sha256=r3GlsG43gMqI6oL0CBGZ2b_6AiK4Rvsky2RJdnZ8qvs,17622
statsmodels/sandbox/nonparametric/dgp_examples.py,sha256=JQ_0bFSFCH6rN640R4nISb3aMou3N6KJO8dsGaeYfxU,5939
statsmodels/sandbox/nonparametric/kde2.py,sha256=kRH_E4tSpor0UKXnG-yDldLz0saAXZ1NU0P0wGpVEbc,3168
statsmodels/sandbox/nonparametric/kdecovclass.py,sha256=4wxggbOzIZw5gJ3QzIUDd0SN5oDklnoMn5LkZ42aVAk,5669
statsmodels/sandbox/nonparametric/kernel_extras.py,sha256=8bzUQihhMOimrrDZy4duatx2Q6_yBRVSifUVS684aO0,14258
statsmodels/sandbox/nonparametric/kernels.py,sha256=BOIyNgamCmI_9YOJNxh8bbIop_GK5M-El5TrpYXV7ns,19751
statsmodels/sandbox/nonparametric/smoothers.py,sha256=b0eZUbUNF0WJ60ila9agB5zzBWsd47we3O0K2KjkOnc,12280
statsmodels/sandbox/nonparametric/testdata.py,sha256=VTIunxS6UjnE7llvvDzFgwHTApz8juCaIYIMeWFk28o,3508
statsmodels/sandbox/nonparametric/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/nonparametric/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/ex_gam_am_new.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/ex_gam_new.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/ex_smoothers.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/test_kernel_extras.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/__pycache__/test_smoothers.cpython-312.pyc,,
statsmodels/sandbox/nonparametric/tests/ex_gam_am_new.py,sha256=iQm4OwC37o7Oghcf6GpEaaHEsH4cXWzQGssiprqdTDI,2313
statsmodels/sandbox/nonparametric/tests/ex_gam_new.py,sha256=PMMr2UTvRIXG30TFNOfnGSbl5i4Q2_jF7BjBwZcJ8AI,3639
statsmodels/sandbox/nonparametric/tests/ex_smoothers.py,sha256=yRfk-dfFyraqubrQPZRMRLsuzLq3nHlP4WJ1ZDhdLaI,1282
statsmodels/sandbox/nonparametric/tests/test_kernel_extras.py,sha256=yt94si_ha97GV89Y8-ZftE32zwkQG9D5GCU2dWbPeQA,3453
statsmodels/sandbox/nonparametric/tests/test_smoothers.py,sha256=gEysTQL0JrOs72FXihLeNA4MLMVcYy-1Z4ITS2ulsCE,2964
statsmodels/sandbox/panel/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/panel/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/correlation_structures.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/mixed.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/panel_short.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/panelmod.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/random_panel.cpython-312.pyc,,
statsmodels/sandbox/panel/__pycache__/sandwich_covariance_generic.cpython-312.pyc,,
statsmodels/sandbox/panel/correlation_structures.py,sha256=bBrroBkXLHmlVLgoTkz6HTEZqUT0jnAKjkYMEqIWYA0,5107
statsmodels/sandbox/panel/mixed.py,sha256=47uYrplVe-yd7D333ttONlTPr0KuHhrRIzGi8Lsp-AI,20889
statsmodels/sandbox/panel/panel_short.py,sha256=n8FWNFJ6JGzjypVEc98wJOM0j7lkoyOmEwj2VWTXzVA,8069
statsmodels/sandbox/panel/panelmod.py,sha256=cXy0X4-AWp6Ba8VrxWRzByPLknB3mClRF6bTeEs0XZ4,14563
statsmodels/sandbox/panel/random_panel.py,sha256=i2cq3IsnOnZhoArK2IzqePoFxk0fveOF5uDBBRNlre8,4913
statsmodels/sandbox/panel/sandwich_covariance_generic.py,sha256=WzlsSUWkb3LmJapy5aN8udRlv2NMfgh0eJa9uIpF_so,3765
statsmodels/sandbox/panel/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/panel/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/panel/tests/__pycache__/test_random_panel.cpython-312.pyc,,
statsmodels/sandbox/panel/tests/test_random_panel.py,sha256=7g3aby5YXS0ssBzllwuB1DJbNpzMlfUJHnK-75ILI2Y,5413
statsmodels/sandbox/pca.py,sha256=qIzv_Y4ilFG6BkVSwmbyXBXe6UwSlEilhUAOi4WHDSw,7036
statsmodels/sandbox/predict_functional.py,sha256=ArvjoQRrr-nV3ihg8A0Qo6Rh8I2oIrrGRtxVyWnao60,15830
statsmodels/sandbox/regression/__init__.py,sha256=3YGr7pNWnyn7e56F5xbpbdRAAnrlNiMfOAb--PChJ3Q,97
statsmodels/sandbox/regression/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/anova_nistcertified.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/ar_panel.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/example_kernridge.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/gmm.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/kernridgeregress_class.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/ols_anova_original.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/onewaygls.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/penalized.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/predstd.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/runmnl.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/sympy_diff.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/tools.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/treewalkerclass.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/try_catdata.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/try_ols_anova.cpython-312.pyc,,
statsmodels/sandbox/regression/__pycache__/try_treewalker.cpython-312.pyc,,
statsmodels/sandbox/regression/anova_nistcertified.py,sha256=DoW1UCS3zsf4zT_-y5Ggr6e0U7pakDwOy_vZ5ytQ0Wo,4001
statsmodels/sandbox/regression/ar_panel.py,sha256=od7E4y9Xd02go4BNb90gZalZPDXjBHzFhlxxJDHV-Jc,3544
statsmodels/sandbox/regression/example_kernridge.py,sha256=nA0MWXPkJWBdT3c2GHcuONqTljyrx7lLUhisGCTqeJ8,1230
statsmodels/sandbox/regression/gmm.py,sha256=JP9VNvZvo8n_QY6rrK0qq80vHNwjtPnDuOK6lrNeDRs,61999
statsmodels/sandbox/regression/kernridgeregress_class.py,sha256=fonQVmeLi5fKJ8D87JldWSEqvL-2PZq4ss7YMCucuOs,7719
statsmodels/sandbox/regression/ols_anova_original.py,sha256=jegQOsXckBCzHZwHdQWb9MqdKH7GcN7SXVFwGL3bfkE,10760
statsmodels/sandbox/regression/onewaygls.py,sha256=5yeTbJjOKsMcWPcsRhmG78Uylf82XmikFpUwqprPcHM,15104
statsmodels/sandbox/regression/penalized.py,sha256=npUHWnzv5-jzTXAzT4ySpBV8g-OkVkZxY1FHPxXaNkM,17394
statsmodels/sandbox/regression/predstd.py,sha256=-p5hbcgnBaEmofemrhyI0Vd2rMsIqS_RhYGlPMg2L2c,3001
statsmodels/sandbox/regression/runmnl.py,sha256=KhgsOyNu1WRMprmhlEzJ_flqRk4XAopDR456Sh21HB0,11956
statsmodels/sandbox/regression/sympy_diff.py,sha256=PyO_9gLe7eHeuqClKBxCUU-uIuH0q6CKd8QYgl9DcDQ,1717
statsmodels/sandbox/regression/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/regression/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_gmm_griliches.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_gmm_griliches_iter.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_gmm_poisson.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/results_ivreg2_griliches.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/test_gmm.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/__pycache__/test_gmm_poisson.cpython-312.pyc,,
statsmodels/sandbox/regression/tests/griliches76.dta,sha256=qixgQlaOXPQoEyj2zwbBL63XjTdI8fO1sJ3siF7DvwI,64736
statsmodels/sandbox/regression/tests/racd10data_with_transformed.csv,sha256=SEmmsunpmO816oZGf8WmmGfw9dqjkvvcC1NRK7gBRH4,321256
statsmodels/sandbox/regression/tests/results_gmm_griliches.py,sha256=Q95obNqKjuqHR4PrzIWrQRjXTM4xxrbBeltnrGN8q0U,14313
statsmodels/sandbox/regression/tests/results_gmm_griliches_iter.py,sha256=XekTeqTdAdlNmelaNNjz_KxQtaNyI8GWzFQ65GIYpWU,7143
statsmodels/sandbox/regression/tests/results_gmm_poisson.py,sha256=BjHt-vlpnGSXUCNEca8ZB54hTIQrZnhQ-vgunROxpY8,25328
statsmodels/sandbox/regression/tests/results_ivreg2_griliches.py,sha256=0AX_JMbaavQfrphHFM4uicgUf868dxCTu8mih0erB80,36682
statsmodels/sandbox/regression/tests/test_gmm.py,sha256=IHPMw7Qe9u_AwbXZ2__uhOiYMUgZPpsasJifTP6DRYQ,31403
statsmodels/sandbox/regression/tests/test_gmm_poisson.py,sha256=tTYjwEhlmtC-uidNA-IJsiUve5ZXip2lhuTf3Z97rUU,13304
statsmodels/sandbox/regression/tools.py,sha256=rm3FK_eWqUIxa8JlkeP3-iWKuo4CVRFCM49xJ4Gq3xY,12805
statsmodels/sandbox/regression/treewalkerclass.py,sha256=gF-Il___6VR9Aus8ENXm4DfwpXYZ4IHiChOlzibuAN4,21326
statsmodels/sandbox/regression/try_catdata.py,sha256=LOSFVQibwoAXzkrZo6tS-tksFCA0U7ft-611Bgwc_Mk,4705
statsmodels/sandbox/regression/try_ols_anova.py,sha256=cjJYc2-vCmHxrSfXkpqFAy_kivO3S8j7vuqzxUfIaxk,9211
statsmodels/sandbox/regression/try_treewalker.py,sha256=Zwo4EioVKXH-Za-aMO5Ux_cZTDhzdcZyfILHqYAijsQ,4357
statsmodels/sandbox/rls.py,sha256=AtwPRvgCEZC0oQJOv0cVJaSr_HK4jadtgRhnnYhbO2w,5135
statsmodels/sandbox/stats/__init__.py,sha256=ajeGMUsVeJD_-UAndBB3CgyLkFuHszTEnzHZc2ax2g4,664
statsmodels/sandbox/stats/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/contrast_tools.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/diagnostic.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/ex_newtests.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/multicomp.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/runs.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/stats_dhuard.cpython-312.pyc,,
statsmodels/sandbox/stats/__pycache__/stats_mstats_short.cpython-312.pyc,,
statsmodels/sandbox/stats/contrast_tools.py,sha256=l4apwfY1UboWqCCC4eN8OypqxFkAA4c_uUpoLtbE-Xc,28772
statsmodels/sandbox/stats/diagnostic.py,sha256=sPOz3sZBXKKViI2f3XEcPI0mO7xs4dSwhPWPQUvulkQ,1020
statsmodels/sandbox/stats/ex_newtests.py,sha256=L530KEFAHsv07R4ndfqEyvukAo8BIOeoSa162TrMtVg,819
statsmodels/sandbox/stats/multicomp.py,sha256=yeFJRDKioPv8c8zvnv9QhpT0oC_AI8WdIMOzArBxxbg,69095
statsmodels/sandbox/stats/runs.py,sha256=x9pwdH5a2ZteEmW49dOXa8DpEyruhm27LCXCDveLNzU,20322
statsmodels/sandbox/stats/stats_dhuard.py,sha256=xWkDDXSD7OCTKKSCtD9Dt4qpGVIL6j5rk5tC-lf9JMA,10093
statsmodels/sandbox/stats/stats_mstats_short.py,sha256=n4NcgQXErvObnvzRecoPTkZoeIWWyDxAGXer_iCIJ5Y,14871
statsmodels/sandbox/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/stats/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/stats/tests/__pycache__/test_multicomp.cpython-312.pyc,,
statsmodels/sandbox/stats/tests/__pycache__/test_runs.cpython-312.pyc,,
statsmodels/sandbox/stats/tests/test_multicomp.py,sha256=wnvFe1FuqeOfvLR7j8fm9_3LukXhuQrHF8bfkh3bnMw,437
statsmodels/sandbox/stats/tests/test_runs.py,sha256=zLPkC_Ixp0TGcHHrqQm0ka7VVOw-v2xlDM9cZVJODCk,903
statsmodels/sandbox/sysreg.py,sha256=4jqXSXR-yi-9V8AnW5_98gNWc2lgBPjCY7Sle9X65ZA,14603
statsmodels/sandbox/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/sandbox/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/maketests_mlabwrap.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/savervs.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/test_gam.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/test_pca.cpython-312.pyc,,
statsmodels/sandbox/tests/__pycache__/test_predict_functional.cpython-312.pyc,,
statsmodels/sandbox/tests/maketests_mlabwrap.py,sha256=gnr9NS-o1k0BEKM13wSSYpvQzQTwnrm4Ykno_Ume8Uo,9032
statsmodels/sandbox/tests/savervs.py,sha256=6pHqWrC_4707-7N5kbYCylWQibj_zLNBOGhxpuHKwxA,1163
statsmodels/sandbox/tests/test_gam.py,sha256=IIg7zuOfZCtB1qi7q3VUKHWjAumSCLde8Bk-68XmphU,10907
statsmodels/sandbox/tests/test_pca.py,sha256=H6-T2NXhDyJ9UGxP5YnqKBz3A-XrclpCKxWqcqq_AlE,2479
statsmodels/sandbox/tests/test_predict_functional.py,sha256=E_9Bj-pSKWRCrUuyAnmLlvdDVvykWNjzeqGgiy8e-rI,12806
statsmodels/sandbox/tools/__init__.py,sha256=Iad_5ags5g_VgDytgmYYoBrYsddw9NEeH1IOnA2KY3Q,215
statsmodels/sandbox/tools/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/cross_val.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/mctools.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/tools_pca.cpython-312.pyc,,
statsmodels/sandbox/tools/__pycache__/try_mctools.cpython-312.pyc,,
statsmodels/sandbox/tools/cross_val.py,sha256=r3IabpUS0BCQGttBz0H-iB6Y1iSgmHpvrUK65m7RnLA,11807
statsmodels/sandbox/tools/mctools.py,sha256=khtZ5R6IwM2SIv5Dwmp3FvI4ZqNGrOl7T-vHtwPkvFs,16886
statsmodels/sandbox/tools/tools_pca.py,sha256=tthGoNU5m70oyefLr3PnUwk1DHOJVk_tBHjwJMeYY_Q,4105
statsmodels/sandbox/tools/try_mctools.py,sha256=lZFuj8ftLJnEkS542EBzGH1JDpa3xqVyGvL61Enm00Q,1917
statsmodels/sandbox/tsa/__init__.py,sha256=8JsffRvtDtv0uZo41wgmuusiyUzqG96el-2NPm4KhK8,840
statsmodels/sandbox/tsa/__pycache__/__init__.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/diffusion.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/diffusion2.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/example_arma.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/fftarma.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/movstat.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/try_arma_more.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/try_fi.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/try_var_convolve.cpython-312.pyc,,
statsmodels/sandbox/tsa/__pycache__/varma.cpython-312.pyc,,
statsmodels/sandbox/tsa/diffusion.py,sha256=Dj8XHfA36OTxQNXcZrjIeWUIs2zjjJB0cOaCNPNyFXQ,18726
statsmodels/sandbox/tsa/diffusion2.py,sha256=CDIkbK87U2vHQ-ulcUiiEuRIv5q4ls4wueZABEukmRQ,13315
statsmodels/sandbox/tsa/example_arma.py,sha256=LC-tMJjPd5FGv4vKC4IXQQZzT9XF_a3C7Pp7QGn3Nl0,11485
statsmodels/sandbox/tsa/fftarma.py,sha256=orwqaUab8sjSZ8M0130k0PWyOJr2oGXTUGrZsgW6NWU,16286
statsmodels/sandbox/tsa/movstat.py,sha256=9ukLdqThAX8WT3mlEs-vx-2nEBhzFuafaYkEmnGMdFw,14810
statsmodels/sandbox/tsa/try_arma_more.py,sha256=VRaIa87uNqbCLRBbUFaSlLkRAZgsMQ922JXBjdUUCIs,2786
statsmodels/sandbox/tsa/try_fi.py,sha256=9jHPkJvw23QJY2jvBH-UEObPgMePf8ZWDYGJnAEj2wc,2547
statsmodels/sandbox/tsa/try_var_convolve.py,sha256=-T1BsC_K9bXUAxP1lvrR69Lh2BveOYAPJAv0UgZQQqQ,9142
statsmodels/sandbox/tsa/varma.py,sha256=KI8iSLEjUiCzeXOVKPO6pnaq2s4FnUs4g6klnSdLPNU,4894
statsmodels/setup.cfg,sha256=PIIgz4gkg8jib_fiGrdIqw_PIjPk-OrORZVqtEOZTZw,6594
statsmodels/src/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/src/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/stats/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/__pycache__/_adnorm.cpython-312.pyc,,
statsmodels/stats/__pycache__/_delta_method.cpython-312.pyc,,
statsmodels/stats/__pycache__/_diagnostic_other.cpython-312.pyc,,
statsmodels/stats/__pycache__/_inference_tools.cpython-312.pyc,,
statsmodels/stats/__pycache__/_knockoff.cpython-312.pyc,,
statsmodels/stats/__pycache__/_lilliefors.cpython-312.pyc,,
statsmodels/stats/__pycache__/_lilliefors_critical_values.cpython-312.pyc,,
statsmodels/stats/__pycache__/anova.cpython-312.pyc,,
statsmodels/stats/__pycache__/api.cpython-312.pyc,,
statsmodels/stats/__pycache__/base.cpython-312.pyc,,
statsmodels/stats/__pycache__/contingency_tables.cpython-312.pyc,,
statsmodels/stats/__pycache__/contrast.cpython-312.pyc,,
statsmodels/stats/__pycache__/correlation_tools.cpython-312.pyc,,
statsmodels/stats/__pycache__/descriptivestats.cpython-312.pyc,,
statsmodels/stats/__pycache__/diagnostic.cpython-312.pyc,,
statsmodels/stats/__pycache__/diagnostic_gen.cpython-312.pyc,,
statsmodels/stats/__pycache__/dist_dependence_measures.cpython-312.pyc,,
statsmodels/stats/__pycache__/effect_size.cpython-312.pyc,,
statsmodels/stats/__pycache__/gof.cpython-312.pyc,,
statsmodels/stats/__pycache__/inter_rater.cpython-312.pyc,,
statsmodels/stats/__pycache__/knockoff_regeffects.cpython-312.pyc,,
statsmodels/stats/__pycache__/mediation.cpython-312.pyc,,
statsmodels/stats/__pycache__/meta_analysis.cpython-312.pyc,,
statsmodels/stats/__pycache__/moment_helpers.cpython-312.pyc,,
statsmodels/stats/__pycache__/multicomp.cpython-312.pyc,,
statsmodels/stats/__pycache__/multitest.cpython-312.pyc,,
statsmodels/stats/__pycache__/multivariate.cpython-312.pyc,,
statsmodels/stats/__pycache__/multivariate_tools.cpython-312.pyc,,
statsmodels/stats/__pycache__/nonparametric.cpython-312.pyc,,
statsmodels/stats/__pycache__/oaxaca.cpython-312.pyc,,
statsmodels/stats/__pycache__/oneway.cpython-312.pyc,,
statsmodels/stats/__pycache__/outliers_influence.cpython-312.pyc,,
statsmodels/stats/__pycache__/power.cpython-312.pyc,,
statsmodels/stats/__pycache__/proportion.cpython-312.pyc,,
statsmodels/stats/__pycache__/rates.cpython-312.pyc,,
statsmodels/stats/__pycache__/regularized_covariance.cpython-312.pyc,,
statsmodels/stats/__pycache__/robust_compare.cpython-312.pyc,,
statsmodels/stats/__pycache__/sandwich_covariance.cpython-312.pyc,,
statsmodels/stats/__pycache__/stattools.cpython-312.pyc,,
statsmodels/stats/__pycache__/tabledist.cpython-312.pyc,,
statsmodels/stats/__pycache__/weightstats.cpython-312.pyc,,
statsmodels/stats/_adnorm.py,sha256=Akpmqc06aNoa6wFQFI15znAi77zg4zjEPug02WkAW_E,4392
statsmodels/stats/_delta_method.py,sha256=3yN5_eJMJNgJj1Td2ANqa1UmNvY4miykEpx6tw9Xmpc,10022
statsmodels/stats/_diagnostic_other.py,sha256=ut63D2V7aPiquf9NL9QlVQBipXy2zmvhQWQQlCy-mkQ,39952
statsmodels/stats/_inference_tools.py,sha256=Id3l6Hc1TcoQL8YZDYKtEs1NOX0cjqJPwc4NsCifADg,2756
statsmodels/stats/_knockoff.py,sha256=7mf46RejyVnpBw0ozGYk7-ngsumW4qPq-mmjPWQWnUI,7379
statsmodels/stats/_lilliefors.py,sha256=F_gQV-RqUf5z4LQ_FovWR-UQgrxZ0o-4KD1Vwgf7P9Y,9860
statsmodels/stats/_lilliefors_critical_values.py,sha256=VNVBZrM00lcsQYnsyNfQB5rumKShqq00pgNTfJ4C6d0,13918
statsmodels/stats/anova.py,sha256=mpiR_kTtD0IDXC02BooClnjWYp3A-2hw9ezf-it5oO0,22940
statsmodels/stats/api.py,sha256=q7_t5sDZZ5znC9f6YuQTRdpIz0_jR6s-zixj10NdTew,7698
statsmodels/stats/base.py,sha256=-58wyEeGP-11q46hghOUCMfQ7UKIl3IoVKXIQKVWNgQ,4129
statsmodels/stats/contingency_tables.py,sha256=ry5UVd9Fmryc7XkYbhb-zzg0TMYwruwa3p5uZbSICQ8,44229
statsmodels/stats/contrast.py,sha256=YpLPTp86WlFbhSt0jYA1ow5QrhtRU5EkqLms0XZmZRA,27721
statsmodels/stats/correlation_tools.py,sha256=IMUF3iXpfNvkmE3vwxeGE37IAL51byqRSz44B01AqG4,32263
statsmodels/stats/descriptivestats.py,sha256=HMtT4tu_C6JzNIM7x2zVTwounDHeXTAhzpLASO_kncU,21650
statsmodels/stats/diagnostic.py,sha256=x5c6ucjY7YOBjJyjbQ3-5VnGlqlJsA3-yGdHJP94o-k,59916
statsmodels/stats/diagnostic_gen.py,sha256=CEB9m8eNjGIs2GLYZSSHRNsc798n1xckHtVdpp5pgJk,8169
statsmodels/stats/dist_dependence_measures.py,sha256=uXU621hlxTp_YySRT7puYdeO2TMJPWAPc4t3feeYawI,17652
statsmodels/stats/effect_size.py,sha256=-sxK_AN5_qfQLVz4BiT-hGv0zx5VWx5gSYR3Kww1atU,4917
statsmodels/stats/gof.py,sha256=2mrRB2htbyKzuRnIBE5Ea35cpQtv9YykmF-VSRLvpyI,16648
statsmodels/stats/inter_rater.py,sha256=YQeiasEelQNHc7uHfA9U9q7FjJr9NLTZseqD8sMFqV8,18944
statsmodels/stats/knockoff_regeffects.py,sha256=4uySr-iw-eAYwdI3Q-hJ7daX7x1CAbox_PLsfWXX84A,5338
statsmodels/stats/libqsturng/CH.r,sha256=I5rYYZF0Y7xn-RLWbXms51J3C30LlyYZ2Yo7NDkvS4E,2439
statsmodels/stats/libqsturng/LICENSE.txt,sha256=ehAwKIghgyorJblmEDTYi7lduXmX2fvI4yet61fdvoU,1566
statsmodels/stats/libqsturng/__init__.py,sha256=RT9y5W58OBA4UBfN-r8SYXtnlRm-23A3DW35t0qtJIM,193
statsmodels/stats/libqsturng/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/libqsturng/__pycache__/make_tbls.cpython-312.pyc,,
statsmodels/stats/libqsturng/__pycache__/qsturng_.cpython-312.pyc,,
statsmodels/stats/libqsturng/make_tbls.py,sha256=vxaHUJ2NAA2BZA55udG26hsXJwkori_D4F5SwBL9tko,63487
statsmodels/stats/libqsturng/qsturng_.py,sha256=e8bCfeW7o7FYMAIDC_tAqcr9-Q1WB6aKwl5qc8NZiwc,53337
statsmodels/stats/libqsturng/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/stats/libqsturng/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/libqsturng/tests/__pycache__/test_qsturng.cpython-312.pyc,,
statsmodels/stats/libqsturng/tests/bootleg.dat,sha256=B9OVU2zcTTeEIxHxo9OY2J6pkponw2Wt172PyLRZFbg,282992
statsmodels/stats/libqsturng/tests/test_qsturng.py,sha256=nRn2k3eoaN45d6H6prvYDJcgjffmUFbYfG8nnZawgYI,8947
statsmodels/stats/mediation.py,sha256=xMc9Dv1IJRCwKT-FAP990qax1GcVW62k99W0ohJtE_A,16398
statsmodels/stats/meta_analysis.py,sha256=T5IP17xQ1hdzsI1o_G66AkdsOfmcldE9_wTMSpD5R7A,26556
statsmodels/stats/moment_helpers.py,sha256=MQfanUpxY1pxo_d4zaij4XVu4UMpKbbjc9JAJ56FwCw,8646
statsmodels/stats/multicomp.py,sha256=0qYui07pkVXg_9Dp_Qv64e5YQDYiT27XC46diVvxT_o,1023
statsmodels/stats/multitest.py,sha256=EcT3SHzYXF4Ksn_zho1q5Pa2FkQWu3y8WaN5fsMtw90,27505
statsmodels/stats/multivariate.py,sha256=G_jx5a-hcH3rrZXkjm0ysmzF88ZNyP05Q4MHB_gGnYw,19227
statsmodels/stats/multivariate_tools.py,sha256=GXkGZEm8g09Iy0g1kYGaMRaERMNqfGwzYzaxTv_8JjU,7174
statsmodels/stats/nonparametric.py,sha256=slF6Jw3KkiyFua5wyGSVt-ekM9uTsUtBHo2GtuBGXFQ,23284
statsmodels/stats/oaxaca.py,sha256=aVwW7PE3xulvznbMLZK-M7jyDLJG_g-P7K-iLKycebk,21290
statsmodels/stats/oneway.py,sha256=KPYbO_h0-Ynu_HdmjIlhr5qudA4fggvsL1oE2pdGjwk,45787
statsmodels/stats/outliers_influence.py,sha256=qozmCxM-OZocNz_Z7pBpsvsFuuJ4VHhlOQq0r8d002g,55581
statsmodels/stats/power.py,sha256=1gUYP8K4tMgn7UxnXaCpQOSEsAIj3c8Mj3PWzI6O2Cg,63769
statsmodels/stats/proportion.py,sha256=sDnyXPlRZvzIgqn9R_crerkAE7_amCOqes88KVCXjtk,85620
statsmodels/stats/rates.py,sha256=I-hm3JopZiYjvgfdB1zqwM9Ao3OO5kPQ2YUMR74G_Qs,77561
statsmodels/stats/regularized_covariance.py,sha256=A-sYbBDDCCeDib1l0xgbRAbrHA9pj5IY39A_61mihYQ,4793
statsmodels/stats/robust_compare.py,sha256=I5u9wsgZijvrp7hUBjljSOtJv-rkyWenZXP3nPWkI6M,9853
statsmodels/stats/sandwich_covariance.py,sha256=PBb8_zJKdzgHQPpRlZjV6KfzABrEHdr6NosbnwK3eQE,27898
statsmodels/stats/stattools.py,sha256=nCnsqpVMhAltYBp5ifEyBkslOoTimC1ej34oM8Mqkdg,14207
statsmodels/stats/tabledist.py,sha256=oZeXCSMsSW5jrru1ziCR-UVlORp1ktGdv3IMSljXiuI,9715
statsmodels/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/stats/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_anova.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_anova_rm.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_base.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_contingency_tables.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_contrast.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_correlation.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_corrpsd.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_deltacov.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_descriptivestats.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_diagnostic.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_diagnostic_other.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_dist_dependant_measures.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_effectsize.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_gof.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_groups_sw.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_influence.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_inter_rater.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_knockoff.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_lilliefors.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_mediation.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_meta.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_moment_helpers.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_multi.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_multivariate.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_nonparametric.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_oaxaca.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_oneway.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_outliers_influence.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_pairwise.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_panel_robustcov.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_power.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_proportion.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_qsturng.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_rates_poisson.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_regularized_covariance.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_robust_compare.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_sandwich.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_statstools.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_tabledist.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_tost.cpython-312.pyc,,
statsmodels/stats/tests/__pycache__/test_weightstats.cpython-312.pyc,,
statsmodels/stats/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/stats/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/lilliefors_critical_value_simulation.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_meta.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_multinomial_proportions.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_panelrobust.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_power.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_proportion.cpython-312.pyc,,
statsmodels/stats/tests/results/__pycache__/results_rates.cpython-312.pyc,,
statsmodels/stats/tests/results/binary_constrict.csv,sha256=AuswO4n2IkctdxVsvpmq_lfkCJvMT7CIal_U_bQ748I,2727
statsmodels/stats/tests/results/bootleg.csv,sha256=B9OVU2zcTTeEIxHxo9OY2J6pkponw2Wt172PyLRZFbg,282992
statsmodels/stats/tests/results/contingency_table_r_results.csv,sha256=Ce69qZrpKlZF3OWz_X1MhGapgLBRa8lemVyltBPbRMg,861
statsmodels/stats/tests/results/data.dat,sha256=4Yl8Npu9GNAWPwmNYffnscjuDY6ZugH9X-Iebzopa94,1769
statsmodels/stats/tests/results/framing.csv,sha256=oX1PJ8PlU8Gt9XB6mHLMaPSVTB--iiqAZ8ZDM32Tz44,24573
statsmodels/stats/tests/results/influence_lsdiag_R.json,sha256=JyzcNbXPpy4yEswMi9U9hFRTxqdGlM_KBgYefYb9Nv4,19805
statsmodels/stats/tests/results/influence_measures_R.csv,sha256=zR5IAAIo5DKeUB3vOZt_nOGwsTGVIKYgb1IcGel1hUE,28416
statsmodels/stats/tests/results/influence_measures_bool_R.csv,sha256=eOXtHWMm7pADNdqeacS2epW65VhhMXDz82_nHL4DZVM,9622
statsmodels/stats/tests/results/lilliefors_critical_value_simulation.py,sha256=mN6vPTaT8oUeOqLcRylzjND50u_5NAdX1MJqNT0WSDM,5293
statsmodels/stats/tests/results/results_influence_logit.csv,sha256=PgXnyj1UxbX2w9OiYt5jJm3sUPSJC1o-fpE-hVLk4t4,3701
statsmodels/stats/tests/results/results_meta.py,sha256=elUoPR-fgnLOVEifstGpQYvzfzatv8VQnKoBWBs9c1s,18487
statsmodels/stats/tests/results/results_multinomial_proportions.py,sha256=ojTDl06FMeXoztKM7CXAoQ_HXuf--Cutz8KFbloLAfk,2658
statsmodels/stats/tests/results/results_panelrobust.py,sha256=wh_WFo5iHGWaqW-dFP-WyIndFd94wmf2Xt6w4byRSHY,1980
statsmodels/stats/tests/results/results_power.py,sha256=ChISVPKe39Ti-n6wyiD8XpQRmxcRDv9pjoph0gsGjr0,4525
statsmodels/stats/tests/results/results_proportion.py,sha256=NMA8CJD0St2sgz4ZOJOFYP3qrUCStcNXxkkPAOGYd9g,5040
statsmodels/stats/tests/results/results_rates.py,sha256=vBBdUKzbSgfXa-gI-qNizXI0BaNZccyzylgLk3OLaLc,1302
statsmodels/stats/tests/results/wspec1.csv,sha256=P4DDT2BE44yKEJ41aRfmXzz86drFjhJouWmHgNA313Q,799
statsmodels/stats/tests/results/wspec2.csv,sha256=GW506vRlJZwHOMTs6OrIN6P9JkLMMNj8EJIikKRNvP8,1007
statsmodels/stats/tests/results/wspec3.csv,sha256=7Svm1iFf6p0N633KvK1WMD7Rkp9FIbCMqN9xFMFoK-U,905
statsmodels/stats/tests/results/wspec4.csv,sha256=dygS2qAcsT0ST4dFYIngvmYmppFahrjTontBqzCdXHo,1221
statsmodels/stats/tests/test_anova.py,sha256=u-AMMbHovsq-8yRC3h8oND5fgsYoXHeNiBscKMbBwYk,18887
statsmodels/stats/tests/test_anova_rm.py,sha256=4YG5laCQXSt7Yx0bxjrWuksB1u-HUkDh86mTZBkaTSM,7521
statsmodels/stats/tests/test_base.py,sha256=DGBo3u2V5ycTdNIxILl3620oc5CBcBq3rJOxy68q75w,1709
statsmodels/stats/tests/test_contingency_tables.py,sha256=I_gPNigwulUlbJgWTocNACZZusgKBa3NszUCkKaVUok,20302
statsmodels/stats/tests/test_contrast.py,sha256=E2mteYVKAxKwjgLk7NJ149e5QxGZ5tRVWruxrA_O9AY,2429
statsmodels/stats/tests/test_correlation.py,sha256=bR7aF8c7Zruy9iBs646C00Zg7dowRahgfYyZh0Mu9k8,1722
statsmodels/stats/tests/test_corrpsd.py,sha256=pUhVz7NgyuDJ_MT-HjQDDQUD2GwLq7F-xkh45QeDU0s,16403
statsmodels/stats/tests/test_data.txt,sha256=BCZfaB057oz3dfqdHSiTeaKVSGj7OLVj2pOgryKbZdM,215000
statsmodels/stats/tests/test_deltacov.py,sha256=lAh0jQaqAkV9AVK6KDC5ElEQNyhsgggzomHdvwQJoYA,4000
statsmodels/stats/tests/test_descriptivestats.py,sha256=Tx_xedrKLCaaqni8aRR5Uy7h40GCH3YdCHrrzgFnylU,5646
statsmodels/stats/tests/test_diagnostic.py,sha256=MaGU1UlS9hlLqp0lwXbb8zT3PRSFMHpyazXa2nGKK4U,61615
statsmodels/stats/tests/test_diagnostic_other.py,sha256=ksxhacNK-F9ycUCi4Bm-Ed4qke8MXklJXzJJOeMZdIw,9046
statsmodels/stats/tests/test_dist_dependant_measures.py,sha256=XrwgVfcTbnplgIAbUe1LGYlhMmPn1fG291IlB7hPPqU,7341
statsmodels/stats/tests/test_effectsize.py,sha256=Re1NE_w78nPxWqJZFsMrn5aiPZXcMb3ESBGhwV43WQ0,2258
statsmodels/stats/tests/test_gof.py,sha256=XtHv5GmH5bOonP9oAGubZF95aUqB_iBMwNcIE2YrRhc,3402
statsmodels/stats/tests/test_groups_sw.py,sha256=m2hUcy6Yq38IOaIVcbdIcKz4iNveLqKNPq1qoEleuNE,2627
statsmodels/stats/tests/test_influence.py,sha256=g3D3iu938g2aNR61jyssMHQduXALJpqf7hqpYV8MM_g,11681
statsmodels/stats/tests/test_inter_rater.py,sha256=sqLGhAfGdgk9GRt1wkyDhVG1ShUwCvzsNGrsyetu4uE,12781
statsmodels/stats/tests/test_knockoff.py,sha256=yRhO04_PHo_i77zogGZqsPWz1qYarGjWX6mTvnlCSrI,4368
statsmodels/stats/tests/test_lilliefors.py,sha256=0GYhq6da5OHVylrBXCPL2XnEfd5XSltu82ARUmClonU,5115
statsmodels/stats/tests/test_mediation.py,sha256=rGqH4vqhMl_dzEM8Fzz39LWgHV_LcjWKbAF2hGPC9tI,12248
statsmodels/stats/tests/test_meta.py,sha256=c0TsnBw4nokB-6urkfzZyoqCWZaCb4s8KTpyImLyvHs,14823
statsmodels/stats/tests/test_moment_helpers.py,sha256=fhJ95oKig0te-uQdPif0GLRWliLL6t3WVs-LzwQFJpE,4700
statsmodels/stats/tests/test_multi.py,sha256=hjFxBTq932kYegjMgrpj8TAkYclnx1g4ecEGIW6_Xvw,24257
statsmodels/stats/tests/test_multivariate.py,sha256=U3i0qcrON6OBbUJqjj6UCQutCKoahJ4X7yi5HVO9088,11727
statsmodels/stats/tests/test_nonparametric.py,sha256=QnSM1q-_lbDcukB1m7_MiXY6v-VHeXoC3xl7xi993nQ,18599
statsmodels/stats/tests/test_oaxaca.py,sha256=d3FAXMm_25ZP3Sf0TsxjPvRQfJCE01Hf4cHueZ_UIBs,12556
statsmodels/stats/tests/test_oneway.py,sha256=RnxpZMp_8jjwc0v65cx7y2kRJudsk6Bd-vZw7Sp6NsE,22098
statsmodels/stats/tests/test_outliers_influence.py,sha256=HPcjeSDGT8tUqyoI9KnJvk9qybpiYvgOP7UbMKVz80c,958
statsmodels/stats/tests/test_pairwise.py,sha256=dHyepfthpcugdsL7_Ww0pIhldqZIexd77PvfTsAbJPg,12889
statsmodels/stats/tests/test_panel_robustcov.py,sha256=zBUkScBeaLKdB9Vl3v-2lYdaRClR_r9uuoDCJPdb6c4,2713
statsmodels/stats/tests/test_power.py,sha256=zS-oTw_6jUCOLgW6peqDQXMBZNLKWzJ3rkfdUmwIeao,28573
statsmodels/stats/tests/test_proportion.py,sha256=7ktbQpSS4DY-3KXJoh7bMHoUnBt0DeecQx3AQZntKW0,43084
statsmodels/stats/tests/test_qsturng.py,sha256=pGcUsPzicnW-xu81Dvzhb7KxVpICqiZbf_HI3kpmggc,820
statsmodels/stats/tests/test_rates_poisson.py,sha256=LYoI11ibKmabAuSfsvxExXfAK95qz2nlf6lRKGoVAUg,43193
statsmodels/stats/tests/test_regularized_covariance.py,sha256=R-RnEk0XKQJsjFv99eATquvXneNfrbqsYa34cy9Y93A,1516
statsmodels/stats/tests/test_robust_compare.py,sha256=3ej4NMKEQwD80sRZo7lHzwD-AhO9eJ7Rz7KV9K9fiPw,11020
statsmodels/stats/tests/test_sandwich.py,sha256=TlQjgCFZUqfuJZYYp4HNt9WJXdNbKYfxsqhg0s9Kmu4,3786
statsmodels/stats/tests/test_statstools.py,sha256=M0_ePvTWIAwBdg7Worr2bUdRTR7ixolEUhR11_ep0Wk,11439
statsmodels/stats/tests/test_tabledist.py,sha256=i44Vzf_9_vgtHhCY27jZBwPfsbniG6UprqvWTgdljaY,4270
statsmodels/stats/tests/test_tost.py,sha256=OwzgrKjZ_FSOg88Xj_HljDu_mytBcXf8pyhG7FZleJA,24198
statsmodels/stats/tests/test_weightstats.py,sha256=JfBDBfv_XFZiX5l9ZD3L1MEd62nbLttihpNRCGRt1f8,30210
statsmodels/stats/weightstats.py,sha256=DlAC6TScwZZCBozVP-dqzbnDV2zLmp2yFKk8VK0EJ5w,55384
statsmodels/tests/__init__.py,sha256=l1_E1xh8iZmfx6GPipPTOJ3S37TIjwWSWZDunPFrdts,48
statsmodels/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tests/__pycache__/test_package.cpython-312.pyc,,
statsmodels/tests/__pycache__/test_x13.cpython-312.pyc,,
statsmodels/tests/test_package.py,sha256=Pv6uNecK39pYFtG1fMdukT0tomIHxUHZ5EoAEXl4s-A,850
statsmodels/tests/test_x13.py,sha256=OgBn0cHDRL02e_wbzpGmMZSS2jy3riyyIzJHt1Wbdtw,280
statsmodels/tools/__init__.py,sha256=S2_HCjCcRim8a6E88beVCS1hTBkEGvgg9Va66b5Yqvw,171
statsmodels/tools/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/__pycache__/_testing.cpython-312.pyc,,
statsmodels/tools/__pycache__/catadd.cpython-312.pyc,,
statsmodels/tools/__pycache__/data.cpython-312.pyc,,
statsmodels/tools/__pycache__/decorators.cpython-312.pyc,,
statsmodels/tools/__pycache__/docstring.cpython-312.pyc,,
statsmodels/tools/__pycache__/eval_measures.cpython-312.pyc,,
statsmodels/tools/__pycache__/grouputils.cpython-312.pyc,,
statsmodels/tools/__pycache__/linalg.cpython-312.pyc,,
statsmodels/tools/__pycache__/numdiff.cpython-312.pyc,,
statsmodels/tools/__pycache__/parallel.cpython-312.pyc,,
statsmodels/tools/__pycache__/print_version.cpython-312.pyc,,
statsmodels/tools/__pycache__/rng_qrng.cpython-312.pyc,,
statsmodels/tools/__pycache__/rootfinding.cpython-312.pyc,,
statsmodels/tools/__pycache__/sequences.cpython-312.pyc,,
statsmodels/tools/__pycache__/sm_exceptions.cpython-312.pyc,,
statsmodels/tools/__pycache__/testing.cpython-312.pyc,,
statsmodels/tools/__pycache__/tools.cpython-312.pyc,,
statsmodels/tools/__pycache__/transform_model.cpython-312.pyc,,
statsmodels/tools/__pycache__/typing.cpython-312.pyc,,
statsmodels/tools/__pycache__/web.cpython-312.pyc,,
statsmodels/tools/_testing.py,sha256=60r6xZK7f1kwUu_V_c9B8lAq0kK_FFdqpFjy7aoBXl0,6668
statsmodels/tools/catadd.py,sha256=ZWoFiAggIgzkgIe-FcR3LH_vMpq0nLIPK3eq599cSxA,1010
statsmodels/tools/data.py,sha256=4KVMQFdXLgKmMg2q8BMUBSVy2AMwNwR1bqMh2eCTi50,3510
statsmodels/tools/decorators.py,sha256=2kZbSw2rxZ21eCM57hr2MkHMKfY3pyl9uN8MNZ-tQPw,4336
statsmodels/tools/docstring.py,sha256=8PyEeCUyIv1LUEQFWmQsrlz8RREiJALQYiJs5rYiy8I,21993
statsmodels/tools/eval_measures.py,sha256=CKnxu3R9zQJ41OVFRpDh51eAf64QNVwrF7d5hEMT0U0,15768
statsmodels/tools/grouputils.py,sha256=ZFWscYfBsJ3vV1PFonCUbwEsLVX5FRvQU8QCMFYY0LI,18270
statsmodels/tools/linalg.py,sha256=_UKabWSzuI3l1Mou-wy77-MLIQsocSCKVWGfj5pGzGQ,4805
statsmodels/tools/numdiff.py,sha256=sjTgbKyxlnexDKg7eJKlQ4dqw_0doWzAx8tPizQzdec,15208
statsmodels/tools/parallel.py,sha256=1YSqCWdxCV6_9qcwFQRM9Nh9S1aFhACEkkRkTAga6Ok,2149
statsmodels/tools/print_version.py,sha256=HXU5XU8I7CkTj2Owinb1WQnzNN2iirmG97Gi7DO-JOc,8629
statsmodels/tools/rng_qrng.py,sha256=K6DfJATK-CyQzUKwpGAHBTcwpxmIz4ozF6BWSQwowiI,1855
statsmodels/tools/rootfinding.py,sha256=4LMR7SyjAOvQ6bD-onoCP7KjMSeq6YBWugZQi_YD3Pw,7703
statsmodels/tools/sequences.py,sha256=uIsV21AVe5PMXkFdpABsY2B00o-NSZomskcjoBKlAao,6725
statsmodels/tools/sm_exceptions.py,sha256=nj--UcnVxZTryMQEFABJv3lJlXaZsxgjBrjnDJIluFc,4309
statsmodels/tools/testing.py,sha256=9ZbL-B0_OvuM0KMpxF9g_XvCSaX5M8T-WENszZZJPa4,2424
statsmodels/tools/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tools/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_catadd.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_data.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_decorators.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_docstring.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_eval_measures.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_grouputils.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_linalg.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_numdiff.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_parallel.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_rootfinding.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_sequences.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_testing.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_transform_model.cpython-312.pyc,,
statsmodels/tools/tests/__pycache__/test_web.cpython-312.pyc,,
statsmodels/tools/tests/test_catadd.py,sha256=HwfvignXyoNvfEql6QG0BlbSAVkf3IKHgSJh7nhJpzk,672
statsmodels/tools/tests/test_data.py,sha256=J9DxrSNym6fkZ8LTLkrqSO8Pwb31UWlw6ELON_L7XCM,982
statsmodels/tools/tests/test_decorators.py,sha256=lVR17yPiHQOb2899Qi6ucTclmNoN8xP18vtYuGSNA0c,1918
statsmodels/tools/tests/test_docstring.py,sha256=-zeY0LajqWOShwNVzemK3MW5H4LuBiom6dQA_Ox4nzQ,3252
statsmodels/tools/tests/test_eval_measures.py,sha256=9oLN1KDU8X-DD26NC0POOwJNEwaz2KhFeT3NkHMCwSg,4073
statsmodels/tools/tests/test_grouputils.py,sha256=-2-BAmNb7fAmeK1aIe16TD7WQ-oe6GDxL0x1JLC_EwA,14539
statsmodels/tools/tests/test_linalg.py,sha256=yFMg7ObvG-zoP4JCG12jdeTUfU_wzFWG1pXMngPe1q0,731
statsmodels/tools/tests/test_numdiff.py,sha256=G_PD8fXIEkZuoiuEvjmf08lQ5IFzCrsW9NGFLePY5is,14970
statsmodels/tools/tests/test_parallel.py,sha256=uTNyZGqKMnVKgLV6owVMtTls0CA6SLgJ-unxXO_uL-E,404
statsmodels/tools/tests/test_rootfinding.py,sha256=g-S1I6B4W-mNTWWHXvMO9fFpYeI6JCPmrt1-kFY0aZk,2859
statsmodels/tools/tests/test_sequences.py,sha256=7-k0oLKE6xA5Y9zd0MeQhbhEZdLUsMPH3OJBrbF84hg,1636
statsmodels/tools/tests/test_testing.py,sha256=hcc43IDt094XviIPbUNP7akkzxi1Gt1UMvaVAWbu0hM,769
statsmodels/tools/tests/test_tools.py,sha256=pxL0eGqWve1KT0EAM8hdMXAbMJCp5FPeyAha8nxELzA,10835
statsmodels/tools/tests/test_transform_model.py,sha256=peqcWdn9HgHFi1_WerYJpAyAYnwOZQboViG8Sc7Q98c,1760
statsmodels/tools/tests/test_web.py,sha256=vwXBzKD7710QsC5PzKeu-plA1nrSkYfeCRklcTUm4vE,1633
statsmodels/tools/tools.py,sha256=M2Pxg4U4nyeepkZwW8sRcOFNhDtRFInRyr2QeDLkzEY,15419
statsmodels/tools/transform_model.py,sha256=8uImQZt8gXTJl8zbFe3dXs-O-5CEYJsQB9KKk3ZHf9w,2799
statsmodels/tools/typing.py,sha256=suu5eNRkf2VzY1OB_sJqhCMabNT4NcS6_7Ncea-3aHg,1186
statsmodels/tools/validation/__init__.py,sha256=D6trM7nQgj9SRN_l2YjsOahqkJoVXOX1S7ygRbEIEgs,330
statsmodels/tools/validation/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/validation/__pycache__/decorators.cpython-312.pyc,,
statsmodels/tools/validation/__pycache__/validation.cpython-312.pyc,,
statsmodels/tools/validation/decorators.py,sha256=ewR4-AgNQNhyNGohXTQ3RLhJrln9aWx3y8s3dtUjuiQ,985
statsmodels/tools/validation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tools/validation/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tools/validation/tests/__pycache__/test_validation.cpython-312.pyc,,
statsmodels/tools/validation/tests/test_validation.py,sha256=qxGTX_EYyCl5FxUAUOfrfN_Yezq4-LslJzkKVhoLnV0,12579
statsmodels/tools/validation/validation.py,sha256=_6ydpRrd0FIG6wxPQp-MKwWXBC5YLTxhJETZX1F_s0Q,14453
statsmodels/tools/web.py,sha256=ZsebNOubT1mXeNH5ylmSdIvbIgX0FnAp25IeukbxY_o,2254
statsmodels/treatment/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/treatment/__pycache__/__init__.cpython-312.pyc,,
statsmodels/treatment/__pycache__/treatment_effects.cpython-312.pyc,,
statsmodels/treatment/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/treatment/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/treatment/tests/__pycache__/test_teffects.cpython-312.pyc,,
statsmodels/treatment/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/treatment/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/treatment/tests/results/__pycache__/results_teffects.cpython-312.pyc,,
statsmodels/treatment/tests/results/cataneo2.csv,sha256=lRHVLfRrCPOdZzYbI7SYrWB-nXpoekK26_lVOuWTLio,429517
statsmodels/treatment/tests/results/results_teffects.py,sha256=M0rr4iLfVJnhWnm7ouSB2kUD0mJ6xJDnPGMFc5YX1vs,21380
statsmodels/treatment/tests/test_teffects.py,sha256=GPYj8Gwi9GLSpuGoULl-5yGQjjroXoEjBf00-2vXc7o,4677
statsmodels/treatment/treatment_effects.py,sha256=pkyDT9HLouamBYGCS67_yvROSV-0L9Jji4TAg5WPnMc,32831
statsmodels/tsa/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/__pycache__/_bds.cpython-312.pyc,,
statsmodels/tsa/__pycache__/adfvalues.cpython-312.pyc,,
statsmodels/tsa/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/__pycache__/ar_model.cpython-312.pyc,,
statsmodels/tsa/__pycache__/arima_model.cpython-312.pyc,,
statsmodels/tsa/__pycache__/arima_process.cpython-312.pyc,,
statsmodels/tsa/__pycache__/arma_mle.cpython-312.pyc,,
statsmodels/tsa/__pycache__/coint_tables.cpython-312.pyc,,
statsmodels/tsa/__pycache__/descriptivestats.cpython-312.pyc,,
statsmodels/tsa/__pycache__/deterministic.cpython-312.pyc,,
statsmodels/tsa/__pycache__/mlemodel.cpython-312.pyc,,
statsmodels/tsa/__pycache__/seasonal.cpython-312.pyc,,
statsmodels/tsa/__pycache__/stattools.cpython-312.pyc,,
statsmodels/tsa/__pycache__/tsatools.cpython-312.pyc,,
statsmodels/tsa/__pycache__/varma_process.cpython-312.pyc,,
statsmodels/tsa/__pycache__/x13.cpython-312.pyc,,
statsmodels/tsa/_bds.py,sha256=ouOZNmvuNSfBjRX-T_8ryzafKDVGeu4CS_1CMfBK_Ug,7401
statsmodels/tsa/_innovations.cp312-win_amd64.pyd,sha256=fhuUbssdeIGcsNC_o1Q7RDb0iHEysDtbRjUt3sTEOyc,168448
statsmodels/tsa/adfvalues.py,sha256=s2VtGsRQIiGVUOogM3vASA6oaItKzTCjQrHdJRVliAM,16272
statsmodels/tsa/api.py,sha256=wESxFcyR42DGH35RgW8v8kMgtv24JGs-L56VhxewhFM,2678
statsmodels/tsa/ar_model.py,sha256=GRtqS0yIKXRBuXoF8-9JblFgJVYShRgabnKnKlYRPLM,79714
statsmodels/tsa/ardl/__init__.py,sha256=ZjwYb2KnwjuhUQjaTvbvH3mDW6K4SjcwSO0bGJaG62A,332
statsmodels/tsa/ardl/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/ardl/__pycache__/model.cpython-312.pyc,,
statsmodels/tsa/ardl/__pycache__/pss_critical_values.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/ardl/_pss_critical_values/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/__pycache__/pss-process.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/__pycache__/pss.cpython-312.pyc,,
statsmodels/tsa/ardl/_pss_critical_values/pss-process.py,sha256=9fuJRDleiZKAjqlZ9Ei6b0pqqsspCPau2QIyWl6xjnM,5580
statsmodels/tsa/ardl/_pss_critical_values/pss.py,sha256=CZ2FQsZ5pAJRo4GZM_gRz80jz0qd2sNZDiGYOpNzRUU,2631
statsmodels/tsa/ardl/model.py,sha256=0V_c3E8MHSQWIZ9bx0_AD73Kh8TTBE4oCRQMS5K4bq0,96659
statsmodels/tsa/ardl/pss_critical_values.py,sha256=8GkVocw5Y6D2WKhB3R6-9jE5ffQshsAfymxqVsqv1jY,22428
statsmodels/tsa/ardl/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/ardl/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/ardl/tests/__pycache__/test_ardl.cpython-312.pyc,,
statsmodels/tsa/ardl/tests/test_ardl.py,sha256=1ZZpxQPTH9YJpiImSSNoWI6b_ujDbrxVkIFgL3ZW6v0,24282
statsmodels/tsa/arima/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/model.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/params.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/specification.cpython-312.pyc,,
statsmodels/tsa/arima/__pycache__/tools.cpython-312.pyc,,
statsmodels/tsa/arima/api.py,sha256=eg35VGtbIr4WTz2GamOilgVOdHC9b_i7QECFEwLgF1s,67
statsmodels/tsa/arima/datasets/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/datasets/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/__init__.py,sha256=EvTC4cEZdJBgev76TiWKUcwkJ2UV9K_jSzhE98J2Oi8,373
statsmodels/tsa/arima/datasets/brockwell_davis_2002/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/dowj.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/lake.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/oshorts.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/__pycache__/sbl.cpython-312.pyc,,
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/dowj.py,sha256=8VnvMVyb5xH5vR814eUupKxcZNc0yZenaCRUNKWAzLk,1371
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/lake.py,sha256=T4kBXD4A2qe3bLFhPNBTRZE3NLNEgUv19AaHJmKM2gE,1220
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/oshorts.py,sha256=Pz1U0YGfv5Ty131DK5p3fY0NnpHf50XdByQGR5opH2k,819
statsmodels/tsa/arima/datasets/brockwell_davis_2002/data/sbl.py,sha256=n62mnPyrsmGOGkfaA8TVqDIKUDs4d_orqo2BPQVtvWw,1399
statsmodels/tsa/arima/estimators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/estimators/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/burg.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/durbin_levinson.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/gls.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/hannan_rissanen.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/innovations.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/statespace.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/__pycache__/yule_walker.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/burg.py,sha256=cxQl_lD8-Jlssh51lroBBhuhkik4uW9dBYHsKpfdnOg,2287
statsmodels/tsa/arima/estimators/durbin_levinson.py,sha256=Ph3B5LMMs4i8Z9osLvZEO_tZOg55tR6VpcfFX4M4k84,3580
statsmodels/tsa/arima/estimators/gls.py,sha256=Y71jKP-XJmt50JzqexMSjT4zbMne69F3AmYsYZNm33I,13364
statsmodels/tsa/arima/estimators/hannan_rissanen.py,sha256=aEgcgPFU-mkop2boWXA8JWtyBBM5ue9o9WjqmbkMcn8,17114
statsmodels/tsa/arima/estimators/innovations.py,sha256=RIPkHkYf8RLQlG0eZOFi2Lx0rqA7d1YC7sKU1zjtps0,9666
statsmodels/tsa/arima/estimators/statespace.py,sha256=Jlp2C3XbiJYv2epoqTpR_oWwAUfT1bOpzb5QjDVHxzI,4950
statsmodels/tsa/arima/estimators/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/estimators/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_burg.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_durbin_levinson.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_gls.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_hannan_rissanen.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_innovations.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_statespace.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/__pycache__/test_yule_walker.cpython-312.pyc,,
statsmodels/tsa/arima/estimators/tests/test_burg.py,sha256=PWhi06Dge3dTinvWNOKtDp9FRXkjxKVpPSWmvWDj5Hc,3969
statsmodels/tsa/arima/estimators/tests/test_durbin_levinson.py,sha256=XygRN5FFfgK_B75Pa4FqQQT-sfURuB0FG6rdItx6YwM,4035
statsmodels/tsa/arima/estimators/tests/test_gls.py,sha256=plqRvhrVazb_6KLD_r81iGcYwzSTeLb_BTGcp_vbPoo,8419
statsmodels/tsa/arima/estimators/tests/test_hannan_rissanen.py,sha256=OlZjmFJE9j5w4F7qMml4Lq-TSmEHD379ThDbOhNm8QY,14315
statsmodels/tsa/arima/estimators/tests/test_innovations.py,sha256=inwca0wA0O3_3AOvPTBWCAtKOS1qgWN0uSrWHw6mePs,13027
statsmodels/tsa/arima/estimators/tests/test_statespace.py,sha256=dUvtG7xr_UGpGcFpnRh428PrObvUdlMoprK5TA6Km6k,2197
statsmodels/tsa/arima/estimators/tests/test_yule_walker.py,sha256=0OY_zakmBOsIxyb1ID5l2FnnqMd11dGKgw3wvYfx2ho,2977
statsmodels/tsa/arima/estimators/yule_walker.py,sha256=S0NxE3xHXb0AKcORog5fbuXcavFZrWWP_ZnQzA0e8j0,2517
statsmodels/tsa/arima/model.py,sha256=qe8Tx5ACGuoswyNGm_OMR1yThVrLBU-62O4SgcSA8ac,25542
statsmodels/tsa/arima/params.py,sha256=mKosQfX7RkfqrkZmZ9fkdGck697WxyzFPhz2yFXYoDo,15117
statsmodels/tsa/arima/specification.py,sha256=lmBOqCm7l_RuVR6wMuhkWYRjQzqzF7yj45YKkzqtsqY,46331
statsmodels/tsa/arima/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/arima/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_model.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_params.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_specification.cpython-312.pyc,,
statsmodels/tsa/arima/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/tsa/arima/tests/test_model.py,sha256=UKRXUxJn_LsTzyLRQyPBIo6E0gZIgMiYAiatDwUdbfc,15630
statsmodels/tsa/arima/tests/test_params.py,sha256=Bria_K30kBkBihnNTcHoLROmGXfDWVschpx5WVGuMds,22438
statsmodels/tsa/arima/tests/test_specification.py,sha256=ZIdhLfnzKr-cwuyYQzLdN98QwZGDrYfNUXJSVwFWuD0,24443
statsmodels/tsa/arima/tests/test_tools.py,sha256=OC4lOlupT6fxpeGEzuReuw-rm12ELBPm0IpjnVfDiD4,3375
statsmodels/tsa/arima/tools.py,sha256=UgZ7_tzoDHeiCO4emmyTi6LyKcDqu_Z2fsspr42HL3c,5481
statsmodels/tsa/arima_model.py,sha256=5w3DWz-XasTerWt706WqscN4PLBqnF5xbY1XMLbit1M,1807
statsmodels/tsa/arima_process.py,sha256=XZPtPMxKtmqb8BY6xLfo-1QORoswaytlpYomOloobLE,30771
statsmodels/tsa/arma_mle.py,sha256=JZt786ks-ZjQLw4hZuddoHl7wNiDICmDYmBZXx5NjQc,538
statsmodels/tsa/base/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/base/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/base/__pycache__/datetools.cpython-312.pyc,,
statsmodels/tsa/base/__pycache__/prediction.cpython-312.pyc,,
statsmodels/tsa/base/__pycache__/tsa_model.cpython-312.pyc,,
statsmodels/tsa/base/datetools.py,sha256=eZtZscP1ESrdWtDOts4Gwxj-WTA-m7orm0MiqhHpjPc,5785
statsmodels/tsa/base/prediction.py,sha256=TE-lIbLCBvdVWZFJqQMUxEu1ymQlt08iMms33QR-cNQ,6155
statsmodels/tsa/base/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/base/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_base.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_datetools.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_prediction.cpython-312.pyc,,
statsmodels/tsa/base/tests/__pycache__/test_tsa_indexes.cpython-312.pyc,,
statsmodels/tsa/base/tests/test_base.py,sha256=RvQxpDiAR0UcPdLFkp2SznrKn6JayOJ6mOhCjaGAnO4,3889
statsmodels/tsa/base/tests/test_datetools.py,sha256=t1o0gbc_pIVfDUxkYxulQ2UajH0Xk9Mh6CloR9qZkw0,3173
statsmodels/tsa/base/tests/test_prediction.py,sha256=3IUfr9Gdrvr4KKu1RFNCeUNsYmn1qfks0fxMp2vg2RI,1534
statsmodels/tsa/base/tests/test_tsa_indexes.py,sha256=vA2yXbRVBn3nU35MNeuiv4QzOdQYymvgH8hJLsxxOpM,45992
statsmodels/tsa/base/tsa_model.py,sha256=wbXwb6PT_0BU_baHQFTW3p2135xEUqEUwGnK5zq25C0,35004
statsmodels/tsa/coint_tables.py,sha256=5TdFsS9Qz34g4fQHaqGCqpBf8jHR3639QJQS8fubmug,7318
statsmodels/tsa/descriptivestats.py,sha256=RLpTSEzubImVbRXiWVnAOexADDNuR6rzRyPdxY9jMDI,2271
statsmodels/tsa/deterministic.py,sha256=lqBzuNP8eiqdw6VlGRsRMyGWlnNDLj-WMdo_90vPVBU,52238
statsmodels/tsa/exponential_smoothing/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/exponential_smoothing/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/__pycache__/base.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/__pycache__/ets.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/__pycache__/initialization.cpython-312.pyc,,
statsmodels/tsa/exponential_smoothing/_ets_smooth.cp312-win_amd64.pyd,sha256=2SkPi_h94k8sETZ-IZH1XrqucWeTb9mTTwrcmBi3lS8,436224
statsmodels/tsa/exponential_smoothing/base.py,sha256=iEAKtOUjafo-79f16vqhp1JqW_vqMK_n-_e7WkU9wGE,36459
statsmodels/tsa/exponential_smoothing/ets.py,sha256=YPhbAfs3V3UCr62-FKqusBorwc8pz54hk5yuEdEw-fU,88615
statsmodels/tsa/exponential_smoothing/initialization.py,sha256=hg_uJ68kHIKa9wYEm5rOXx8KcuHdopMsDv4Ygunn3us,4483
statsmodels/tsa/filters/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/filters/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/_utils.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/bk_filter.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/cf_filter.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/filtertools.cpython-312.pyc,,
statsmodels/tsa/filters/__pycache__/hp_filter.cpython-312.pyc,,
statsmodels/tsa/filters/_utils.py,sha256=Ud_6L62mWTPCQN1adaCDEnrTJJI8cG3yxMyaOIdeOdA,2975
statsmodels/tsa/filters/api.py,sha256=xyOd3g2Yb8-s3YRZwPHXXBVjJC4l_yCeRpMNaXtDahk,288
statsmodels/tsa/filters/bk_filter.py,sha256=OclHYxa0PUmfSiQKs18ntovR8iFyntBg2Veaj2OjjJs,3570
statsmodels/tsa/filters/cf_filter.py,sha256=k0TfwEzfVQ_mPmJ09DaIDM1IljaxPVMlueZ55-yhCtc,3848
statsmodels/tsa/filters/filtertools.py,sha256=JZ46136ibcuwcgmg1NqnLWnmXpFONwS2nqB4_66OWHw,12126
statsmodels/tsa/filters/hp_filter.py,sha256=4SUgIHSWVUKIF6vkvSAXFlVvIJ75z4hMlVtD-aCegX4,3357
statsmodels/tsa/filters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/filters/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/filters/tests/__pycache__/test_filters.cpython-312.pyc,,
statsmodels/tsa/filters/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/filters/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/filters/tests/results/__pycache__/filter_results.cpython-312.pyc,,
statsmodels/tsa/filters/tests/results/filter_results.py,sha256=Af2MhBHKvaVNIY5bFRfYmcN1uzt4nwYFcPgYdsxitN0,3862
statsmodels/tsa/filters/tests/test_filters.py,sha256=3A8yP02IMusr43UzMmpLZXm3IXGEFRKtta7zguHPph4,39378
statsmodels/tsa/forecasting/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/forecasting/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/forecasting/__pycache__/stl.cpython-312.pyc,,
statsmodels/tsa/forecasting/__pycache__/theta.cpython-312.pyc,,
statsmodels/tsa/forecasting/stl.py,sha256=3yJns3TPuGX4gHXdd-68VGAZDOjC4b8qBlNmq3Kt8_k,18519
statsmodels/tsa/forecasting/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/forecasting/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/forecasting/tests/__pycache__/test_stl.cpython-312.pyc,,
statsmodels/tsa/forecasting/tests/__pycache__/test_theta.cpython-312.pyc,,
statsmodels/tsa/forecasting/tests/test_stl.py,sha256=mRCx3TIRSGPIRryJgCsS8GGYQjHDXxY4jW3qDt56bGM,6618
statsmodels/tsa/forecasting/tests/test_theta.py,sha256=6ayM4S1PVBTo20C5WxqkNXAxuWCG1mM0ZX9qJAmO0V4,5306
statsmodels/tsa/forecasting/theta.py,sha256=jZGUxnafJN3TJWNwBU5djHGl1nMpB9SCAvOpWixM-OE,23075
statsmodels/tsa/holtwinters/__init__.py,sha256=_U3zs6M6HhOPojGvj4EwHgNMvXBfcs1-AwPWOiqZxMk,354
statsmodels/tsa/holtwinters/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/holtwinters/__pycache__/_smoothers.cpython-312.pyc,,
statsmodels/tsa/holtwinters/__pycache__/model.cpython-312.pyc,,
statsmodels/tsa/holtwinters/__pycache__/results.cpython-312.pyc,,
statsmodels/tsa/holtwinters/_exponential_smoothers.cp312-win_amd64.pyd,sha256=hAA4QRhaSl2GoqUx79WL9jnleOS7IilSea1qdNvN5Tk,271360
statsmodels/tsa/holtwinters/_smoothers.py,sha256=yvpuzq-2-WcXB7a77oE64CPmi40f0WLBJ7My8XqPsA4,10134
statsmodels/tsa/holtwinters/model.py,sha256=Fpq1kKLYr2qaxYVTHL4IvP86RZQw9_JsYLQE8uOyRWU,68228
statsmodels/tsa/holtwinters/results.py,sha256=VecaBXSqXtti0tnY4PAoUkr8Evnkwhc-KpOJwz3eBGc,25986
statsmodels/tsa/holtwinters/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/holtwinters/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/holtwinters/tests/__pycache__/test_holtwinters.cpython-312.pyc,,
statsmodels/tsa/holtwinters/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/holtwinters/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/holtwinters/tests/results/housing-data.csv,sha256=R7M0kyvJFku6zzWaETM2DDhxfQgBeXlmA8mlfaPfFkA,11918
statsmodels/tsa/holtwinters/tests/test_holtwinters.py,sha256=3uTCZRMEGE1FjrXalVpJki8bX4P-FNJ70pZyQYttCWg,68263
statsmodels/tsa/innovations/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/innovations/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/innovations/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/innovations/__pycache__/arma_innovations.cpython-312.pyc,,
statsmodels/tsa/innovations/_arma_innovations.cp312-win_amd64.pyd,sha256=RCwuRSTAfo3hn8yL36fe1Q_KAa-TjF4iNcxzgKRoHgg,372224
statsmodels/tsa/innovations/api.py,sha256=H51pIf6b3aCJsgUJt7NGFH8fWb5SWhownxIHQQ_ovnY,265
statsmodels/tsa/innovations/arma_innovations.py,sha256=KOuurUDWOqaM6o1mgBkbXblXKJNWNjPkuMipe7W-WP0,9786
statsmodels/tsa/innovations/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/innovations/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/innovations/tests/__pycache__/test_arma_innovations.cpython-312.pyc,,
statsmodels/tsa/innovations/tests/__pycache__/test_cython_arma_innovations_fast.cpython-312.pyc,,
statsmodels/tsa/innovations/tests/test_arma_innovations.py,sha256=MttcwjHe_EEROkCdDIaQofwkHjL4KV-rVomdle8-qPo,2138
statsmodels/tsa/innovations/tests/test_cython_arma_innovations_fast.py,sha256=qSZVQhj7ybsQ_oxAIp-YQU7ZdL3vS4tHyqeqt3gfsPc,13879
statsmodels/tsa/interp/__init__.py,sha256=4Mt66PgoLjxLrl0G5cn_XGVghLAJFjehorxriJ2bDSs,133
statsmodels/tsa/interp/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/interp/__pycache__/denton.cpython-312.pyc,,
statsmodels/tsa/interp/denton.py,sha256=5-jMCULL8NtJLhBqHMnkZd8BPrOGGEz473w34Z7IFxE,10815
statsmodels/tsa/interp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/interp/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/interp/tests/__pycache__/test_denton.cpython-312.pyc,,
statsmodels/tsa/interp/tests/test_denton.py,sha256=7ikwsgXSQO9g9ZvjRMnOkyyXB1h8CfyLxi4YLT-HRxI,1232
statsmodels/tsa/mlemodel.py,sha256=Nfeoxfp6Yys-swRjNbN1GDyMJCDMtBeOvsT8WQCyJR0,2149
statsmodels/tsa/regime_switching/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/regime_switching/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/regime_switching/__pycache__/markov_autoregression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/__pycache__/markov_regression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/__pycache__/markov_switching.cpython-312.pyc,,
statsmodels/tsa/regime_switching/_hamilton_filter.cp312-win_amd64.pyd,sha256=EKxiZiEngeXwbVNZT-QcCtkbJjpHhvBH3AjljetLYug,218624
statsmodels/tsa/regime_switching/_kim_smoother.cp312-win_amd64.pyd,sha256=XBqJf5b2H4WoDY64nYYitu7t3IQx6wwGNc0sNsE-s-M,207360
statsmodels/tsa/regime_switching/markov_autoregression.py,sha256=-Wekm0b74vrsYz7YIlx4czRNqXnXBnQgGRXBxPxhKig,18071
statsmodels/tsa/regime_switching/markov_regression.py,sha256=lKQvUjR6Ru0IBqVdW9hLkp17LNkp4iF3Fu5tgc9ZdyU,16556
statsmodels/tsa/regime_switching/markov_switching.py,sha256=J0mOD00AOJebHFmFUUq00ZupZLmcISlw9LpNAxLP0-c,82920
statsmodels/tsa/regime_switching/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/regime_switching/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/__pycache__/test_markov_autoregression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/__pycache__/test_markov_regression.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/__pycache__/test_markov_switching.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/regime_switching/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/regime_switching/tests/results/mar_filardo.csv,sha256=692YPaqk4_51Zm9nP51BoYQYgNWvAPU8OByoR7UVuPo,39966
statsmodels/tsa/regime_switching/tests/results/results_predict_fedfunds.csv,sha256=Zp1fDDl8w-kYgGTxMCmwHoiRIsz4HyT8N7r0_sUxU2M,28001
statsmodels/tsa/regime_switching/tests/results/results_predict_rgnp.csv,sha256=3E5BR8h1zbTZ-ysyzCP6LnX4_ul4WAALzLynj00kE6k,13266
statsmodels/tsa/regime_switching/tests/test_markov_autoregression.py,sha256=GCBZ7VHbM-Uc7ZiVAc963Z3Q-HmoKZKCS12GGSL_JeQ,41138
statsmodels/tsa/regime_switching/tests/test_markov_regression.py,sha256=9NT__IyZ48QEN7BcZYuG0X4eYtCOBNzI5g9rbwd9nTA,69745
statsmodels/tsa/regime_switching/tests/test_markov_switching.py,sha256=OAhkVtQWrmK1b6_gF6-A7T3PoBusFMaVymARyqOb8ss,12243
statsmodels/tsa/seasonal.py,sha256=_vhd2gNlGGRPvNg2HQLV2c5g7KdELpNbTtnNrxPONEY,11350
statsmodels/tsa/statespace/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/statespace/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/_pykalman_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/_quarterly_ar1.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/cfa_simulation_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/dynamic_factor.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/dynamic_factor_mq.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/exponential_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/initialization.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/kalman_filter.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/kalman_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/mlemodel.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/news.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/representation.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/sarimax.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/simulation_smoother.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/structural.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/tools.cpython-312.pyc,,
statsmodels/tsa/statespace/__pycache__/varmax.cpython-312.pyc,,
statsmodels/tsa/statespace/_cfa_simulation_smoother.cp312-win_amd64.pyd,sha256=u62mrqElhpRxPkMYHy8kP-HtfsmRp79-eyXiZ862inA,378880
statsmodels/tsa/statespace/_filters/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/_filters/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/_filters/_conventional.cp312-win_amd64.pyd,sha256=5QKY3kWaNf5cdoVOc0qHEhT2BXGHEvwJVLEIhn7uQio,165888
statsmodels/tsa/statespace/_filters/_inversions.cp312-win_amd64.pyd,sha256=ZprgnYdXx9ZvkkJEHAPJLXz-LixYLbC_tU_tyXn8Xzo,194560
statsmodels/tsa/statespace/_filters/_univariate.cp312-win_amd64.pyd,sha256=ofz-U8IRYv9StR49nqKkNwwEY1mGcxRlrQGUPGOZ1yw,193536
statsmodels/tsa/statespace/_filters/_univariate_diffuse.cp312-win_amd64.pyd,sha256=nUwx3428wleFYxONNBK_IYtuQqN3-Exw85ICKzYa3T8,163328
statsmodels/tsa/statespace/_initialization.cp312-win_amd64.pyd,sha256=jmiftLSSu5sd8V1MrAKNacWJE8EG0dPZyW27r4hUhcY,275968
statsmodels/tsa/statespace/_kalman_filter.cp312-win_amd64.pyd,sha256=CWCikKP2Pke6TJLfx_0wWdaFKACgaezQHrsiM86uujs,708608
statsmodels/tsa/statespace/_kalman_smoother.cp312-win_amd64.pyd,sha256=3TACrZUMc1WwSeP7dScUbru8GbLz0POTHn7H7ZnUJfM,459264
statsmodels/tsa/statespace/_pykalman_smoother.py,sha256=ZJSqDbDqsNmea4URcqc71zn2C2cdp9uoki6LlAVzDdY,11021
statsmodels/tsa/statespace/_quarterly_ar1.py,sha256=vLsnRNt1422StIZY-N2u1wB-2dlqRjSqqBR1nM2lpsw,7451
statsmodels/tsa/statespace/_representation.cp312-win_amd64.pyd,sha256=ImtTb1t-5yJiBu7wHZh6W-wQ4vuzw_b4NSDulHLoEqM,674304
statsmodels/tsa/statespace/_simulation_smoother.cp312-win_amd64.pyd,sha256=MilRbNc1TRe_FdVhyQ_5R2RhVK9PjxeU_8TYMwDXTac,503808
statsmodels/tsa/statespace/_smoothers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/_smoothers/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/_smoothers/_alternative.cp312-win_amd64.pyd,sha256=owtYbq_4B09w4Amg0F3xpxBnCLy_uxjoNyzcxw9P9kI,157184
statsmodels/tsa/statespace/_smoothers/_classical.cp312-win_amd64.pyd,sha256=T3wzYBY5rWMTBtj9393Mi4ZNVVRkMf2PNCne-nH1H0Q,158208
statsmodels/tsa/statespace/_smoothers/_conventional.cp312-win_amd64.pyd,sha256=k9aLaujCBNVtx87qfQYluOgTEzE8_xtwIb_2nSusFok,159232
statsmodels/tsa/statespace/_smoothers/_univariate.cp312-win_amd64.pyd,sha256=zReRUDaDc8ns76CVxCPxU_4Y2Yg0dCzk8jQOzWjgJsA,174080
statsmodels/tsa/statespace/_smoothers/_univariate_diffuse.cp312-win_amd64.pyd,sha256=Tl_OLuRaAz--O5QAeupRxJGuEo80ktAGGLF14GaWY4M,192512
statsmodels/tsa/statespace/_tools.cp312-win_amd64.pyd,sha256=JiTZxKe0P4WZjO4sQ4Oq6aVD58IpNmwQrq3CBodPXoE,564736
statsmodels/tsa/statespace/api.py,sha256=5fAAi8sDFAZ8InvHI7eImxDH93dwm3XvHz8u2pm0CGo,301
statsmodels/tsa/statespace/cfa_simulation_smoother.py,sha256=WLSMm3x1GdgBGxO9p7E0faAuWn_vPck70QWOyeAxJAk,10275
statsmodels/tsa/statespace/dynamic_factor.py,sha256=Q9UuR_OhlhGt8Y8Pblnmwa6oXdaCePNEyRMf9IysUmw,54288
statsmodels/tsa/statespace/dynamic_factor_mq.py,sha256=soe7auhnRp-ArC7VE6CuBYHiEPX_7WWs_SR4pZow-vM,197286
statsmodels/tsa/statespace/exponential_smoothing.py,sha256=QB8J7i51iDQovX7zGAsQvoFKxZC_O3h8BIa_8ZPTnZk,30662
statsmodels/tsa/statespace/initialization.py,sha256=72gq13bUtWw8FavGfxSqEAu4_ECq0_7nT4Zi3NfAMBU,33517
statsmodels/tsa/statespace/kalman_filter.py,sha256=8ebmw67oVnzjeF0cUfZI8ksYYG-tV5aq2Gim_HSljC8,106133
statsmodels/tsa/statespace/kalman_smoother.py,sha256=0RAtTKiHSYNkVk5GJ7JlfPtTmQ25xjeAgChMGyTHehE,83323
statsmodels/tsa/statespace/mlemodel.py,sha256=8Ecv6fF3xOrP0DgVG3c6oFQxmv0b6hMZnaOs3ruYmU8,218370
statsmodels/tsa/statespace/news.py,sha256=Izi-di3V1dcaNFib4W9UNLgIHOxxw7ft3sasssBUs-o,71593
statsmodels/tsa/statespace/representation.py,sha256=Rwzxa327mdWBaN9aJT4qmHUEyGE87GS5SGq81M4e51I,48712
statsmodels/tsa/statespace/sarimax.py,sha256=EUfaGEslLgpR4_m3bATLBtgqpgxc8Hfs3-2LSWrv_Yo,84200
statsmodels/tsa/statespace/simulation_smoother.py,sha256=kKI-TdRmYj58X2fd8BBkQW_VXMnx91e00bHQKafNEUA,30927
statsmodels/tsa/statespace/structural.py,sha256=9UV6tgC4Qs_9F5slUyj7yb5POSdrIarwghNs4UU6QSg,79052
statsmodels/tsa/statespace/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/kfas_helpers.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_cfa_simulation_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_cfa_tvpvar.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_chandrasekhar.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_collapsed.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_concentrated.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_conserve_memory.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_decompose.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor_mq.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor_mq_frbny_nowcast.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_dynamic_factor_mq_monte_carlo.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_exact_diffuse_filtering.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_exponential_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_fixed_params.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_forecasting.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_impulse_responses.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_initialization.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_kalman.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_mlemodel.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_models.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_multivariate_switch_univariate.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_news.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_options.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_pickle.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_prediction.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_representation.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_sarimax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_save.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_simulate.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_simulation_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_smoothing.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_structural.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_tools.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_univariate.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_var.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_varmax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/__pycache__/test_weights.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/kfas_helpers.py,sha256=1QRw3cG-Sx8Nsuu81_fKXnYR_lmgYPlpXWeJho2FIRU,3679
statsmodels/tsa/statespace/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_dynamic_factor.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_kalman_filter.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_sarimax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_structural.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_var_R.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_var_misc.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/__pycache__/results_varmax.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_Omega_11.csv,sha256=l44NS4hMTGtRuMcfGdW6zIkgV57ZWz6nnYwsDAbJ2LI,94
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_Omega_22.csv,sha256=zx2UMT1o_OeISoF7C8fUI3AjPAcVqMZ6nb0PlOlyPPw,178
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_S10.csv,sha256=ZXsqZSDSi6BGQlRB1sXU4F4Lw7POUbonypaBnriS65s,95
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_Si0.csv,sha256=B6aWn3LYilQlwoTcT9NH5ixxtR1kwE-iGwinAfttdwg,166
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_beta.csv,sha256=FRUZ5REEgGUrVLFyhZH-GGPsTtYkm0_VN0XasOjMSJU,1396
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_invP.csv,sha256=Cxml7v8CK-3YkzbhNg8rgDlmAegw6SRAUpnnlvEY8PQ,24059
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_posterior_mean.csv,sha256=e-eiUVeSC2dZQ2Cu9Lq-cvZ60ayhY8uNcrvUyJf1fIg,1380
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_state_variates.csv,sha256=IMWwjl1WipedSIiULubFjZgW8AbQXIMxElFjxpeCgSM,1403
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_v10.csv,sha256=N3StPH-YZjbhBVIOh6qCH35QcgcvL1nih7VmA1rBQNY,6
statsmodels/tsa/statespace/tests/results/cfa_tvpvar_vi0.csv,sha256=PCH0RVgNcv9GY5IRp_-DPBa4EnA_O3uKHrd29TtfFHY,4
statsmodels/tsa/statespace/tests/results/clark1989.csv,sha256=XLHnGze2L7FDMyAW6dcJC7ceTnGw8FifMRSlsR-cUiQ,4434
statsmodels/tsa/statespace/tests/results/exponential_smoothing_params.csv,sha256=Sws9uYablM9rWJ7qMeceFxpfUjsZM0-crVw6VykYemc,1774
statsmodels/tsa/statespace/tests/results/exponential_smoothing_predict.csv,sha256=CHc99vZpuTiDQ0DI1XsKBU0e0-ex1kv1YJ03y1NuRGE,11562
statsmodels/tsa/statespace/tests/results/exponential_smoothing_states.csv,sha256=wxdK63N2DMSkixs-zlqFKUXsHTl27yM345epgvnckhw,13350
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/2016-06-29.csv,sha256=4na80v-Y_ljkKk50q97yeLOiYyIecFLUdJaX5zzb_AY,58690
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/2016-07-29.csv,sha256=rPBdE2vkZsVFsYKoNNm4xDsHRtrsBh_awjsbXSKz2Ig,58849
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/US/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/data/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/functions/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/Nowcasting/functions/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/statespace/tests/results/frbny_nowcast/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_111.mat,sha256=bbmFp738mAr1QikZson0ZvGgXdkInk4nrqHDmKxshco,11477
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_112.mat,sha256=LucPsDOBuWcBubngpobaxtT0PjVtgRcPBnJf7YjwHSw,11486
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_11F.mat,sha256=l4N0TD_yuZwnaJMwsnsEZEefVnCMOaOdUQF7kYg66Gg,11522
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_221.mat,sha256=qqTCHR36F6tijaimRNBAnEc4zJ2TlruxET8G347-nWw,12548
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_222.mat,sha256=vZlZ4YT4GUSQcpWT3XQTSikPwGHSycHVDE8GCBJHDZM,12557
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_22F.mat,sha256=Fo_4bmGM0pnHF5fRyO_OsqvsTQ74f1QDrG-NgvCsZQw,12685
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_111.mat,sha256=POTDaVOOhGQc0DX4qDUBAPP2OF4oX8KxUDeqRshMb9c,20685
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_112.mat,sha256=5MTbAcUVk9ZR2EPwQwvuOlSA7mOJ8la6w2O5SLgyMmg,20700
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_221.mat,sha256=FYyqZPADI6KMKjAvDis9XMQq6U0fBLVp5HLXd1-vWC4,30747
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_dfm_blocks_222.mat,sha256=0i8PXJ_4dqWIoTm2vqzOr1YT67Tb9xNT-qtVNkk4SBQ,30790
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_112.mat,sha256=TKZKit__SN-9FCdYxT4mkV4qdkBUd9wjoABEBinHoIY,11723
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_222.mat,sha256=ItqDdynAQwBhJQTtRRiGjZusbcp1xxEqYiLHX2F1FCs,12782
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_blocks_112.mat,sha256=f8Z8OQASO6b0vl5nbumBRkP37_-Ed1XZSuF7H51_k-c,20915
statsmodels/tsa/statespace/tests/results/frbny_nowcast/test_news_blocks_222.mat,sha256=W9GuQ3hefgj3PCZjhR1rywdNY_lgz1cbCDrdyXW6Y7o,31025
statsmodels/tsa/statespace/tests/results/manufac.dta,sha256=1e6VE2SMYdontWCwqua_Ipr1R5vb1i6Sa63jS6vtPwU,19146
statsmodels/tsa/statespace/tests/results/results_clark1989_R.csv,sha256=uqKmccGPUbF7CnzD6nM5jJAjqdtewE-1FMsZ6MqFAno,25620
statsmodels/tsa/statespace/tests/results/results_dynamic_factor.py,sha256=m0-ELh5fsMHdhaCm8gHCUsXaKpfRlBtQclPXPwHBh20,6766
statsmodels/tsa/statespace/tests/results/results_dynamic_factor_stata.csv,sha256=qs3EyE2e2FWKo8wq8Ub22GtLDDLl9Qd1hRGcAbzXRt8,43266
statsmodels/tsa/statespace/tests/results/results_exact_initial_common_level_R.csv,sha256=tma1tUgAXBd7Bqw-xJ_qlkAk7e0dCFxGlIQ3RCd9MnA,5301
statsmodels/tsa/statespace/tests/results/results_exact_initial_common_level_restricted_R.csv,sha256=Enjwx4-06VS_KnnkYHYkSsMfZqGRwbDQiVsR4CReq5w,4711
statsmodels/tsa/statespace/tests/results/results_exact_initial_dfm_R.csv,sha256=5MXeIWTNbWKhqQOgRNiBRl-9XBPOADbARR6aseiC9gc,17440
statsmodels/tsa/statespace/tests/results/results_exact_initial_local_level_R.csv,sha256=xpWPHzC60hYXpt03zcnKf0aKeDjDqHuzyFyfKGGF0Hc,3461
statsmodels/tsa/statespace/tests/results/results_exact_initial_local_linear_trend_R.csv,sha256=1imbAPuVeu_y-d1xil9MJUceblFwRziFWhORrX5nZ5Y,4296
statsmodels/tsa/statespace/tests/results/results_exact_initial_local_linear_trend_missing_R.csv,sha256=J6CsBbV7BM7SWF2fEEwD_641EEI0KHoVIbaHz9iW_HY,4250
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_R.csv,sha256=OhH6LgWHh717II3Dsd9aW2iRL5Jc3ZfruY_1ir6HcK8,9643
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_measurement_error_R.csv,sha256=OcxCPpcTOzHpl1BD2Nxi3aXJzy-XMFCNIWsdJlhNEOA,19093
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_missing_R.csv,sha256=2TfojlTlBPDAWM--bnCUfWdMnqwKQeVG4P6yrfN9aUM,12763
statsmodels/tsa/statespace/tests/results/results_exact_initial_var1_mixed_R.csv,sha256=xLQzSzZpeAx0RV9jFq0KWFEw6NrewyvLiIoJW5snSCc,9721
statsmodels/tsa/statespace/tests/results/results_intercepts_R.csv,sha256=ELJF3tpvyNrPZIQ-VDAjSwlPH6gUMKA_AcTKjyolFWg,120060
statsmodels/tsa/statespace/tests/results/results_kalman_filter.py,sha256=3kiRmbISZdOeDOxYsriI8VXRW8FhvGHYwnyi2q6dniE,41500
statsmodels/tsa/statespace/tests/results/results_realgdpar_stata.csv,sha256=22RAQFvphK-TOgSjxw0GpcmnOw5lGeETpZNP8fDHsI0,62621
statsmodels/tsa/statespace/tests/results/results_sarimax.py,sha256=HOf41Walg43vmibeIQ8U8k73oJ3K7_laxFn9AOWIiXs,13785
statsmodels/tsa/statespace/tests/results/results_sarimax_coverage.csv,sha256=nBkC7weayT80xc9I_k70abaYc6ZcfccmKrGx7yGKLzs,6267
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing0.csv,sha256=cqkxGZi2KoEuG1Nb2IflXGoNm1mWX3sr9MzcIedWdp8,2195
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing1.csv,sha256=nofjVjIUWd2QWNU1Nka3xAgTorhjibWO4K_WGRlGH_s,2210
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing2.csv,sha256=QUnQQsZ1Z0H4zbZGk0bTAQlQ8Ol3yMCcVUe0EcFPLoo,2242
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing3.csv,sha256=bx_-hONgYjGiqORtGL8xgfB42q9-EzvabKgDhXZ3_j8,48270
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing3_variates.csv,sha256=tUF5VWXy8gljEC9sMW9sSVd6P4cMhntFxd-sHj6leIY,22122
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing4.csv,sha256=4uPZQqGnDvadR1S8UxjglFKCA2dQCAzmdUGMdQYxLd4,48472
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing5.csv,sha256=kIAsdup01JTBuHMKmeuIYWD2ZFM03CzbaLVCa82Iqos,47350
statsmodels/tsa/statespace/tests/results/results_simulation_smoothing6.csv,sha256=Zx-9DIIBd9Q2nB0OcxEl8xRzbCTYba4ly3IjQBilC_E,44517
statsmodels/tsa/statespace/tests/results/results_smoothing2_R.csv,sha256=7a70QVUgBqT1oAKfIedJkqHi19I2HBPQ5mUSLiDk304,144766
statsmodels/tsa/statespace/tests/results/results_smoothing3_R.csv,sha256=eFZpfGnU0oTm81tpq4nm5O1VYJczvWwTkbxKS_l4Ubs,32529
statsmodels/tsa/statespace/tests/results/results_smoothing_R.csv,sha256=ndbw7UIkolmjDqf0sZhXJoQRYfyvW0Ci3256xNLfA-E,117225
statsmodels/tsa/statespace/tests/results/results_smoothing_generalobscov_R.csv,sha256=vDC4jKRM8oSqt2XBQlB6s-mTf6Fuj5jWNx7kNMc-Dso,121543
statsmodels/tsa/statespace/tests/results/results_structural.py,sha256=MWBsP9reLIfQ_yKNM7iXJNqyDXojRkNALlAUVMH2Nms,9324
statsmodels/tsa/statespace/tests/results/results_var_R.py,sha256=KU-LYPge3K4omTjEDJvT5e54ExmqNF3zFlClhlgRd_I,5619
statsmodels/tsa/statespace/tests/results/results_var_R_output.csv,sha256=FqmXDsdt5NsPkG1Zu_0sajj13nQ1uOgbIgY9TCAHfIY,68681
statsmodels/tsa/statespace/tests/results/results_var_misc.py,sha256=tGCeq_eFTju_1_myorZmsF9giVaVIOJpPqQvSlqlYTE,5676
statsmodels/tsa/statespace/tests/results/results_var_stata.csv,sha256=drAabVw4E5kF-XngOpoBXdNg4ipiYzhfcpkET1ObVi0,24592
statsmodels/tsa/statespace/tests/results/results_varmax.py,sha256=XRmeKdPsIeuth9Qo-gssMuX4JzywHBQEg19-9BwYnj4,8709
statsmodels/tsa/statespace/tests/results/results_varmax_stata.csv,sha256=oXWf5_H7j4LlNSq8sts47McoI4xntNMKN2boCzKCQfc,26064
statsmodels/tsa/statespace/tests/results/results_wpi1_ar3_matlab_ssm.csv,sha256=FLWEEPDeeT5wEI2WUcJrGeLEAd3kXl-Il96n4m9vJfI,6253
statsmodels/tsa/statespace/tests/results/results_wpi1_ar3_stata.csv,sha256=TlsIVJciqo29HYnCNNtS6s_IXjs-xcm6L-lTvQq-5Oo,14470
statsmodels/tsa/statespace/tests/results/results_wpi1_missing_ar3_matlab_ssm.csv,sha256=-a1LqxbjowF1NGN4DovPZTdd_1IXeeE4nT0NSWmmPOA,6599
statsmodels/tsa/statespace/tests/results/sm-0.9-sarimax.pkl,sha256=ZSQYFs-9nCBal_wiIsTbdlKsm76nQhkGsPwujyT2t8Q,6653
statsmodels/tsa/statespace/tests/test_cfa_simulation_smoothing.py,sha256=jQDb9DAsrERtUZQC_s6lVx5by8MRTkikXBwLfvjVgvQ,6848
statsmodels/tsa/statespace/tests/test_cfa_tvpvar.py,sha256=8A9VUknQ5UgDcx8XvjlSeqpKwXI0s4tAf9_Iq4tgoHc,7560
statsmodels/tsa/statespace/tests/test_chandrasekhar.py,sha256=R8sW0_sAkjuM9c7iB_9QbD5SMt-9RolbO5141aUXAFE,8894
statsmodels/tsa/statespace/tests/test_collapsed.py,sha256=9qk8ZNP2hffXFa1ocveUx6aiJhYUbS1NQXpSIEjkRsU,27468
statsmodels/tsa/statespace/tests/test_concentrated.py,sha256=F7OXx7nZD3zZxuX5cuXrQjeUIj2nkSp6WRYQunoEffo,11840
statsmodels/tsa/statespace/tests/test_conserve_memory.py,sha256=pQzG2BWzgnzAjDqFqaNxP4ss1jVAK_C1cCnu2bsgBQc,13565
statsmodels/tsa/statespace/tests/test_decompose.py,sha256=RZEQjaqwjrOo_B-KLn_n261N3HGZZr2xbY3BXyOUAY4,11994
statsmodels/tsa/statespace/tests/test_dynamic_factor.py,sha256=30XuI4l5Fjec-ZOBb_adzTNAS6uclNZ2YZU13m9yPLY,38309
statsmodels/tsa/statespace/tests/test_dynamic_factor_mq.py,sha256=CzhxFXmG4kUcHJihUMmKHZUMPc_GBvoRIRbtSTMRAx8,83511
statsmodels/tsa/statespace/tests/test_dynamic_factor_mq_frbny_nowcast.py,sha256=66wAKheSx2iKwq6rM-VTL0h9U0h8PcMYAudbf7yGBpQ,18982
statsmodels/tsa/statespace/tests/test_dynamic_factor_mq_monte_carlo.py,sha256=Y2Cl2cJ7iXAojqOooJ3EI8bsIXYyYrmdJCZ5d5frAF0,24594
statsmodels/tsa/statespace/tests/test_exact_diffuse_filtering.py,sha256=dJiVfkf84CFt6iTlkSWxaH2uXV7bjMboQJs9qiwcsOw,40073
statsmodels/tsa/statespace/tests/test_exponential_smoothing.py,sha256=FoyC5Syh13o2PQvrZWZFpl9ZMe4yxpOXxJr8Dn81FoY,35892
statsmodels/tsa/statespace/tests/test_fixed_params.py,sha256=c7PJYoIZGkbkKT31nJnEBXVVFB92prJH34T4bdGA_q4,29101
statsmodels/tsa/statespace/tests/test_forecasting.py,sha256=c2PKUDqNy86jhCiXXKYJb6o7d9kBeHuPhE8J4zadpJM,1577
statsmodels/tsa/statespace/tests/test_impulse_responses.py,sha256=VNzw2mulD3OV-1hI1Ad2QAQQ5ZYOkALap9xfu8XsqKg,27045
statsmodels/tsa/statespace/tests/test_initialization.py,sha256=iRQ6Ffa8MvPKs_H2nOOGU8_7C929dfOtLBcNX8-O28U,13812
statsmodels/tsa/statespace/tests/test_kalman.py,sha256=PCCGa50GSTHQtFAdk6l2m0w9qTATtCe5hWY4c9xlYPM,26788
statsmodels/tsa/statespace/tests/test_mlemodel.py,sha256=seF5zzGz5p2WuLMxxFRgQb1OjdFLTQ66-JO05S2Mjsc,47559
statsmodels/tsa/statespace/tests/test_models.py,sha256=Xf1-CLLOlvLpaVqP3eKP0TCEF7JdkILMVE9Ph2H2Sbk,10741
statsmodels/tsa/statespace/tests/test_multivariate_switch_univariate.py,sha256=IvYm0GXL783kHhDFkTBczBP-039Auh1-3X8padNYgJM,18981
statsmodels/tsa/statespace/tests/test_news.py,sha256=StoOrV2aEGgH8UZM7a55WKAl2mkqrQeUXspUInci7sY,54436
statsmodels/tsa/statespace/tests/test_options.py,sha256=nqK1b6Px0_oHSBa8f1GyyAMwOGk6fDrnkFJSL4SPNIs,9372
statsmodels/tsa/statespace/tests/test_pickle.py,sha256=HrpbvqzzF2-vK_2CmFqj8tAAvDu0D9aIqfngYUL4e5k,5110
statsmodels/tsa/statespace/tests/test_prediction.py,sha256=7UzcEjET5haAepJ7ceRVbMjXOaiqKFlI_Mbb7roGGp4,24013
statsmodels/tsa/statespace/tests/test_representation.py,sha256=xM5TRfbdeBfdHnNFgzBuFpdcZZANbfFUY5ywPSQ020Q,46673
statsmodels/tsa/statespace/tests/test_sarimax.py,sha256=Mvi3sdh8IpGjOYu4pqoYDujZee1uzWtPpmFaiZ-7fYo,103821
statsmodels/tsa/statespace/tests/test_save.py,sha256=RCkjXTkc754szZ7CwNRS6CvsqZ-UAIM8snCQCxBOD-Q,4887
statsmodels/tsa/statespace/tests/test_simulate.py,sha256=0UiVB3_djcZR8eEQoTxqDgqS6TJuwyFDsz-tZBgVKgE,71530
statsmodels/tsa/statespace/tests/test_simulation_smoothing.py,sha256=3gi-q7tBOGkMPXVPpBygqLUkcHLG0M2kiwJLZO6qaaM,33599
statsmodels/tsa/statespace/tests/test_smoothing.py,sha256=GrNZh3gNfI3HFyTfxb1dXdajRdDRWdAJVa1hsKrpZPM,61396
statsmodels/tsa/statespace/tests/test_structural.py,sha256=18hU3WGGq3bhA6Bjdb1oGIdn1DCOeW25TFRaW9XlAWg,26022
statsmodels/tsa/statespace/tests/test_tools.py,sha256=_wtX8mgX4c8fYVd2GDNrGTOWlzFFVUJK23rlipA93tg,30337
statsmodels/tsa/statespace/tests/test_univariate.py,sha256=GeINSm66YLWu0Bfv20xNAacMXef0qGoCwIggBsgibdI,26879
statsmodels/tsa/statespace/tests/test_var.py,sha256=fXbalIib3uSH49U11vWeT06Y9qRXg08QfDPawnJ9MqQ,8532
statsmodels/tsa/statespace/tests/test_varmax.py,sha256=MM3mevljOHE9ouCLnC9N1y0zQOcGOpdfZuuAzOOrJVI,49164
statsmodels/tsa/statespace/tests/test_weights.py,sha256=vxQXZJjRsGEHHuUy7xyyIZNgzrY7k4WbVzkWZwF4psE,21324
statsmodels/tsa/statespace/tools.py,sha256=rugTtTmMXPaNAFVHppdyugA43BtP9-oB3KntG2Bli7Y,82027
statsmodels/tsa/statespace/varmax.py,sha256=0U4X1G64sZOJZzXnsk2jc7vrRbA9-voNjRjStag_QDY,50866
statsmodels/tsa/stattools.py,sha256=pO8CRVLAAbQ9Ciz8Q9eMecP8SCq7SyLBn7_t4F6MClE,95979
statsmodels/tsa/stl/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/stl/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/stl/__pycache__/mstl.cpython-312.pyc,,
statsmodels/tsa/stl/_stl.cp312-win_amd64.pyd,sha256=leSo26QAkbn_fUs76t01fxsD2WpgLFfr5JA0wdpmebs,210944
statsmodels/tsa/stl/mstl.py,sha256=oR8alisqfkZtivEa9YcK5SHNnhY1DH0AnaULqA8WTTs,10051
statsmodels/tsa/stl/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/stl/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/stl/tests/__pycache__/test_mstl.cpython-312.pyc,,
statsmodels/tsa/stl/tests/__pycache__/test_stl.cpython-312.pyc,,
statsmodels/tsa/stl/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/stl/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/stl/tests/results/mstl_elec_vic.csv,sha256=53LSJTM9lpN2znQp5RjGo7a2nWIMc8bElZuAbT6UeiM,121517
statsmodels/tsa/stl/tests/results/mstl_test_results.csv,sha256=DR2_GoFFjJ2k8DZAHxEtjtytzMZHQK7H9CDFPO-w75o,291823
statsmodels/tsa/stl/tests/results/stl_co2.csv,sha256=Qd-X3v7auyULFYnOvC4gtAXUCaldVh5MQrMxF9kDBao,2397
statsmodels/tsa/stl/tests/results/stl_test_results.csv,sha256=DNSdNKL0imcAc50m6NTmlOt9WjT7hdsu82yOtb5cv7c,263014
statsmodels/tsa/stl/tests/test_mstl.py,sha256=QLnsfEzb4Yng4YdPfeRt56d8As5M7cvQZJzxoqx8cpQ,5128
statsmodels/tsa/stl/tests/test_stl.py,sha256=gKaHTkfiO8RpaQ8D2BWFCW2IRapuVhybYFX59XeCr9k,11437
statsmodels/tsa/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_adfuller_lag.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_ar.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_arima_process.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_bds.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_deterministic.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_exponential_smoothing.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_seasonal.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_stattools.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_tsa_tools.cpython-312.pyc,,
statsmodels/tsa/tests/__pycache__/test_x13.cpython-312.pyc,,
statsmodels/tsa/tests/results/ARMLEConstantPredict.csv,sha256=Y3MAbdq3RN1S_uCdJ8rlWcnSG17wCNlEfToa8LbYncA,4695
statsmodels/tsa/tests/results/AROLSConstantPredict.csv,sha256=1wA8C1MucKdajcQLNYR6U3WdB7rts_qocuVfrsKCjak,21668
statsmodels/tsa/tests/results/AROLSNoConstantPredict.csv,sha256=kvmmDMBKLfsmQ55S9BWjadUpUUBqkO2KizaP9OkaSDo,20967
statsmodels/tsa/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111nc_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima111nc_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112nc_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima112nc_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211nc_css_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/arima211nc_results.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/datamlw_tls.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/make_arma.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_ar.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_arima.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_arma.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_arma_acf.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/results_process.cpython-312.pyc,,
statsmodels/tsa/tests/results/__pycache__/savedrvs.cpython-312.pyc,,
statsmodels/tsa/tests/results/arima111_css_results.py,sha256=aXOLPoRNtrCPy-zBRS5nVYRVRIyPZd4hDrDO5CtmS_8,26973
statsmodels/tsa/tests/results/arima111_forecasts.csv,sha256=--Gd-ca3-Lg-rT-xmIY0Hk1XzHeWC6Ux6eeKHTkIGaI,6585
statsmodels/tsa/tests/results/arima111_results.py,sha256=pBJUn1yIdzal4ynXJgmvPxEajwHq7v0imYiJCri84dE,26963
statsmodels/tsa/tests/results/arima111nc_css_results.py,sha256=Vj3PVVgO9jXYXCw3babzJp0AYk09kU4M3ocSWLCSn4s,26700
statsmodels/tsa/tests/results/arima111nc_results.py,sha256=7hkeIT0kyVjI_l9NROpbNYKkpMxeYo9GPnAdB35A5Mc,26703
statsmodels/tsa/tests/results/arima112_css_results.py,sha256=DAZaLN0HC2bV2AcVzyxsKsrxOtLIXb_Nn_6Z-LgGp8w,27174
statsmodels/tsa/tests/results/arima112_results.py,sha256=MmXP-eD1M2dm5Ei4EIwEUiSSfGOiWahQ-UEiR_WBOPQ,27176
statsmodels/tsa/tests/results/arima112nc_css_results.py,sha256=8gl6cDBEtfPYN5jedeDowDXAzhCiWMmPpx8TkDffzus,26734
statsmodels/tsa/tests/results/arima112nc_results.py,sha256=Ejt6aT5DgHkEVTNp24B2ASPQsBo9_J6KJZ-6sZb1srs,22617
statsmodels/tsa/tests/results/arima211_css_results.py,sha256=Tlf5_sJXZS-DStlKsJQy_e5buD_WzloFBDM4xbOd2zM,27164
statsmodels/tsa/tests/results/arima211_results.py,sha256=5wN1t19A1vHHxUICMFf3lTrX8nQSlPgnTggFNBXBm7E,27157
statsmodels/tsa/tests/results/arima211nc_css_results.py,sha256=MX0_b_Wd37u_vavlcf6HTYeZ6ji_RKQnmPeShdl4REA,26862
statsmodels/tsa/tests/results/arima211nc_results.py,sha256=qhc9Rk8YEqah8mTfN0hxQmvM5ajq54RZ8wqJaBm51y4,22618
statsmodels/tsa/tests/results/arima212_forecast.csv,sha256=dZ1vDZ0U2w6gDIeweSbU4Z6kPhyjXI9GYFjyutCKGUM,6285
statsmodels/tsa/tests/results/bds_data.csv,sha256=6PJZLfnGRxikryO7IcI0IT2wESjPW9va4d-1BQRpT1g,3971
statsmodels/tsa/tests/results/bds_results.csv,sha256=1KcXui0x2tw7BsywYM4Sl5xy0gSjGL3KXISsROF4CZs,1465
statsmodels/tsa/tests/results/datamlw_tls.py,sha256=ECeEOHXiMfPKt9YTIR2bDAPczbaspKMaZWGrP-bAFSg,6548
statsmodels/tsa/tests/results/fit_ets_results.json,sha256=3lACyYll_kKrzs1T_t9bJb_K88EtJWFiJonwouYhE_U,118496
statsmodels/tsa/tests/results/fit_ets_results_nonseasonal.json,sha256=C8F0GTlDFCpJ5Yx4LYvDr5U3_-o26lkh149uRCoRhRM,26377
statsmodels/tsa/tests/results/fit_ets_results_seasonal.json,sha256=thEhbEHvQ8Z-_XN1Dc0Zj9YVvazDu8d65OHBgfqqNbI,85617
statsmodels/tsa/tests/results/gnpdef.csv,sha256=KBhr0nWk1fs7wDfakyzaYRJPgunKkHd6YTrZeTDQn8c,986
statsmodels/tsa/tests/results/lutkepohl2.dta,sha256=cBGaDynLKWOUmikCn7bm9egv4yFu7cW8TNQIYUqyTyw,6000
statsmodels/tsa/tests/results/make_arma.py,sha256=frBM21wAQpGTHBwE40C_oq92yxbHgTv6fA_ojR3e7C4,1876
statsmodels/tsa/tests/results/rand10000.csv,sha256=-8tS0LO_v2giGR5vWx_tyGZ3i2c56AuUiTGAwleahpU,123712
statsmodels/tsa/tests/results/resids_css_c.csv,sha256=17novyEuxdEaWVQ_ew37YhrWZqRAnLSxLgLroLqhNYM,20152
statsmodels/tsa/tests/results/resids_css_nc.csv,sha256=DsC2KAKFoZTPzxCCGi4PS5rA5EYcgZv3Na3bQlguLA0,20163
statsmodels/tsa/tests/results/resids_exact_c.csv,sha256=CRQNJ0I07EF09gjxBv82c61gtIcx5O1yWomTfNREMGQ,20287
statsmodels/tsa/tests/results/resids_exact_nc.csv,sha256=K2d5rzsOJ-Y2_a6UswZnhRS2KfgLqJJzVdWHLA-RSUc,20306
statsmodels/tsa/tests/results/results_ar.py,sha256=ERnQJ0YC82sAKH7ux0CVeLK6kjyHwwi5fDQ7cr-9USI,9336
statsmodels/tsa/tests/results/results_ar_forecast_mle_dynamic.csv,sha256=kBfPwj4jecaFlUN1BC1RDZLsxvsTqiHTwtaZfp_VAG8,10522
statsmodels/tsa/tests/results/results_arima.py,sha256=izqcFknbdJsI4arhe56I3yQuwODpPanAU1XpCig6yTo,23358
statsmodels/tsa/tests/results/results_arima_exog_forecasts_css.csv,sha256=q9rrgHidBv0iSK_GKy_DnU38tPBCWqWvS3gFfM1_xS4,4370
statsmodels/tsa/tests/results/results_arima_exog_forecasts_mle.csv,sha256=rA8wpS_BWzB568jG0BKjo4D2jz-su-ZGRJWzoYjg_v8,4636
statsmodels/tsa/tests/results/results_arima_forecasts.csv,sha256=x8YozzMXp3yjFvI-GNnRGzu0llLGm2DPUxeEL3JMAQM,23749
statsmodels/tsa/tests/results/results_arima_forecasts_all_css.csv,sha256=bb1E19XG1U4UrVzHzBBaQo9b4gYOHV1OM1qb_rhrKXM,15076
statsmodels/tsa/tests/results/results_arima_forecasts_all_css_diff.csv,sha256=NcU8ez8NFvnuIep9w1R3Jk_7XZ-r7r6vGD6HRp03OKc,14227
statsmodels/tsa/tests/results/results_arima_forecasts_all_mle.csv,sha256=kz5iw0YuVlS_FrGUTGvR7zrV9Ymq2qhyJyDnkANIfAk,14013
statsmodels/tsa/tests/results/results_arima_forecasts_all_mle_diff.csv,sha256=KT9nXAugKKEWXI5eTi_2VKBXxLedlgFcG8d3C9dy1KI,15478
statsmodels/tsa/tests/results/results_arma.py,sha256=ZF3a-UQdzVw3soX45BFElxqN_luMfj7bs8KYJNyIzGg,42416
statsmodels/tsa/tests/results/results_arma_acf.py,sha256=pGgS7kl6XOiaVeX9CO5ICS0J2NGBlRTx4w0botEzGAA,2005
statsmodels/tsa/tests/results/results_arma_forecasts.csv,sha256=SnxE3RGuPcvA9E-mwU9mhPCgTzX79vMJ8_3uTbVbU6A,1314
statsmodels/tsa/tests/results/results_ccf.csv,sha256=tska59TzHsdeKpttcvMvGnQowA7q1RMf75FxXVoCVSY,388
statsmodels/tsa/tests/results/results_corrgram.csv,sha256=pXei0oD8x-Y2kb0ftmhv7WarWMVNQl665pdoYmtRK9I,3063
statsmodels/tsa/tests/results/results_process.py,sha256=9eRSpwxMIWCAnd6ryfvUTyqW6sXRzCVPEk_bkFRcfGc,1379
statsmodels/tsa/tests/results/rgnp.csv,sha256=-d7KmkpxRy6aHdGd4j2EyrIcOmFCtW6MfZ-zyApX_v4,743
statsmodels/tsa/tests/results/rgnpq.csv,sha256=W8NhhoHlP5FOS_lgTWsWTtT9135QvrSAj0bgkrtcuHU,1903
statsmodels/tsa/tests/results/savedrvs.py,sha256=-jPyf-jzgDsikU599nTWJpEGaFklpuxgiwMENC7oAw0,19303
statsmodels/tsa/tests/results/stkprc.csv,sha256=0wvd3WWf-k3WVjEuz11RxMLF8M9rKDOmjwnHQRkJ3Yg,1207
statsmodels/tsa/tests/results/y_arma_data.csv,sha256=APzRWKY0buA4w96eVD_41CqgCsyAVme3feeKMFRH8Os,76151
statsmodels/tsa/tests/results/yhat_css_c.csv,sha256=vNhIai7jm62FxOkOu3rwPuxQmM49iUyq0pAJy0ikOfo,19566
statsmodels/tsa/tests/results/yhat_css_nc.csv,sha256=yQqfWuaUdZyWmQcnk-kfTihQFHbMxlmCZUcYaxc-mWc,20273
statsmodels/tsa/tests/results/yhat_exact_c.csv,sha256=c3gBkySUdn9kA7spmSJJPQ5vMy3CE9GDGdTFnSIQmoQ,19695
statsmodels/tsa/tests/results/yhat_exact_nc.csv,sha256=_F8glmvkd1e6pTUOYd9JOwM1BQYXPtL4t1O9kJn-yI4,20413
statsmodels/tsa/tests/test_adfuller_lag.py,sha256=Sb5ZpruISPdN9aebOZ4m9OXjK40PI4Ik49XIMOr_ex4,1800
statsmodels/tsa/tests/test_ar.py,sha256=Fk6vBcqiGMa4qhhkZo7xhioAOASjzgABFnGybYYy9gI,43649
statsmodels/tsa/tests/test_arima_process.py,sha256=aqNRpK4PxFbzP9591EbTmVDXtmjVxpo25O0veiFCzxg,17447
statsmodels/tsa/tests/test_bds.py,sha256=XjSBziYIa_6T4L74hdd4xJC3XPODjQiJ2b0IKH9izt0,2707
statsmodels/tsa/tests/test_deterministic.py,sha256=pfA5hf7e32cVf48KmIiOIT_8gEkO45o3RIP-BXLjVJs,23137
statsmodels/tsa/tests/test_exponential_smoothing.py,sha256=5213jXDMEKI6ETkGLSa5rlkxUYlcx1_bxD1GTElNlXA,32477
statsmodels/tsa/tests/test_seasonal.py,sha256=EjAxkdEpnjFjLi3kiZSoMf8wM1QHCENyeTk4t6JnSyg,15936
statsmodels/tsa/tests/test_stattools.py,sha256=F9zSedVjbE2Bo1mvldn7QuqSrwGVow2F0c1fuUsM-Co,51797
statsmodels/tsa/tests/test_tsa_tools.py,sha256=kmqMS7x7CznQzWbiQHVFjzfLQRpnuOe3B-PPGrlX3u8,28742
statsmodels/tsa/tests/test_x13.py,sha256=Lv4FTXFXeM53LqNaAmXU_I_zNaomvsZHMaM2cxge2bs,1556
statsmodels/tsa/tsatools.py,sha256=m2u0QpMqFSg8gcqswsGxXHyoe4vSoaBEkH2PoJOh1A4,23236
statsmodels/tsa/varma_process.py,sha256=v3Dvdrv7vAH3PQuWOqI6eFL42UrJ67701MWqjvQMoso,19696
statsmodels/tsa/vector_ar/__init__.py,sha256=wmZE4S25YB801cOHgFTsWzJxh5SFeezTBW1HnXfheLo,75
statsmodels/tsa/vector_ar/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/api.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/hypothesis_test_results.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/irf.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/output.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/plotting.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/svar_model.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/util.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/var_model.cpython-312.pyc,,
statsmodels/tsa/vector_ar/__pycache__/vecm.cpython-312.pyc,,
statsmodels/tsa/vector_ar/api.py,sha256=vooCaQCFwC8Zv9Ga5_hzTTxaqKJsl3AJgm_P4YWD6U8,83
statsmodels/tsa/vector_ar/hypothesis_test_results.py,sha256=da6FKB0tAvoEvrot8wMPy0io-84-HoZxdEbbbseAfZE,7339
statsmodels/tsa/vector_ar/irf.py,sha256=UlOiSR3qrT_fAzxD997DzJLQWARM_NDMxsq7Gn_4jUw,24392
statsmodels/tsa/vector_ar/output.py,sha256=vyfqPcZPqlax6lZ1K4kh546v04SQ9wXQAQW5Hz32rL0,6937
statsmodels/tsa/vector_ar/plotting.py,sha256=X-4K2m7w61W1fK0yyAtVdkXILfhhQMWLsYqiXl5LOXI,7486
statsmodels/tsa/vector_ar/svar_model.py,sha256=-WIm-ZGYJI7Adht7ZEvDS92aQRcbqa5VhLmiiC4bHcE,22222
statsmodels/tsa/vector_ar/tests/JMulTi_results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/JMulTi_results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/JMulTi_results/__pycache__/parse_jmulti_var_output.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/JMulTi_results/__pycache__/parse_jmulti_vecm_output.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c.txt,sha256=88oSPEOM9rfaUthvdNQp3IbxyGSzZv0zznbA2Nc1Fkg,3500
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_Sigmau.txt,sha256=VFVCr6CPWYt75kWE0raTDPAW6AvjIT54puYZyOi3G-Y,645
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_diag.txt,sha256=SEeB2_Hz_ttRACWPtSIAjqYSbfDhXHfw2GoWG1Z5oNY,2247
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_fc5.txt,sha256=jbt4IUVXacFB5pidqbHfrLvNfUbYKou9CJQ0fDgXNKw,1492
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realcons.txt,sha256=X07zJF70vEzjjspaXBmKzRHB7s_eZd4MiCxKU9CTHZU,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realcons_realgdp.txt,sha256=QHLvU8w0b5NKf5rkTOPovL22iG3HXn3LKdkHHkZSP_I,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realcons_realinv.txt,sha256=3SXtCCITaBmKBSdO4_p1XSbbBid7vQuWkDY7PXCrfec,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realgdp.txt,sha256=uzLLUaEd7PVI4UDp9JW1f4lC15oaIw96PhYwj4uzB6o,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realgdp_realinv.txt,sha256=CaqGl7VOftferIv7EojCOHbVXUkf8C2cygdhxq3Rty0,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_granger_causality_realinv.txt,sha256=sXFTEYICDYrXb1WYL81lZ2Y6JLW9PaLz1qStSjZwaY0,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_ir.txt,sha256=WMMzzo2dbNcSkZtWRrAlR8qvGJMyRwkmNT26avKQFek,4739
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_c_lagorder.txt,sha256=PWLP-m20kVUJJrOeNLFUOnUd1pdmEtIwF92JFczrrkY,444
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs.txt,sha256=9fuxmMpNwzHY-wvgLnSugjvXwq4p17FbPS6zOz3tA5Y,4001
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_Sigmau.txt,sha256=xjkLs6oUS3wEByerNSQoU6lHO1bwlqMKqx_kgrbH_Xs,645
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_diag.txt,sha256=Vs1pjL75jnXeHdPYR06FGb7T4TYvMN8lgWmwN3amIJc,2247
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_fc5.txt,sha256=0DJzLkV9j_rAuDx84mren7rtETt_TNhNlAp1Ysp4IBw,1492
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realcons.txt,sha256=BBFJDMGQNb0xTReNcbeg0Spu7rCOLjlZO-lCLmp12pI,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realcons_realgdp.txt,sha256=PYNu_lKqA57eHOUQCNvrLHk0-oG79bSsrtnbQOvJhms,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realcons_realinv.txt,sha256=nxYT3dT3P_D03QEvoilbcXZLYlK6wsWocrFu-pnmOT8,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realgdp.txt,sha256=xSnYvUjk5IEGCdNzV07Ce_uhW66CY4S14eA78hPUZ6Y,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realgdp_realinv.txt,sha256=HNNObEfnLbLqErsS8sHDW0LewTryEC0JZ4cWCgHN0Hw,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_granger_causality_realinv.txt,sha256=0bc7YfYC8aM1R9Lxx8vv47gAC0rmCBIdt6v2RayxwiM,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_ir.txt,sha256=he26SOruNqtR9sFRVYWcc66TrojmwjiOf7h65zY_4mw,4739
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cs_lagorder.txt,sha256=Tn8TSZOa14tGk7RyQBrh0-9OOz5OaE2WEe4Sl-t8QNM,453
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst.txt,sha256=86EELT2LPZ1pOyqvmy6nNqpvkfVKoaaeA1tHRmmT0dY,4171
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_Sigmau.txt,sha256=QH7aJpQobFNeGnYmPUUe8FCSZui1VppePkOlgUWzdtE,645
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_diag.txt,sha256=fX1enSoVaLtH8K3J1Cssygr2vgqIe7O0t--WhFSFcws,2247
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_fc5.txt,sha256=ycIbHbDcNdmMDo22ykTPhbCToTsUdKpfy-Bje_QGkrI,1492
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realcons.txt,sha256=anjO0pez-YSXtk52WxlTEoXxnW8k7GqJ-HnGaPl5xW8,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realcons_realgdp.txt,sha256=eIHhgD1IqdXU1fETJtIPgVFnMYCCzFzDJh7sF1aY2DU,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realcons_realinv.txt,sha256=rwE4uWzos24MHFmc7TuxZ9aySm7EDdD9qPD4pH1redM,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realgdp.txt,sha256=BAzvx7EcmvgsWw5zptLOXv-p6gomwaDqMACnD9DbTw8,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realgdp_realinv.txt,sha256=_5sTpzYij1D1qc_6E4aKcIwngTcm-v_L-JoX7pgdn-c,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_granger_causality_realinv.txt,sha256=04y8Mt7YEr5BmE5BwfzEo7138INlGmKxvDBluBFm-Tg,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_ir.txt,sha256=TJ_Jrm7QVfTG9Az_Le-qnESafhPBaWZuuAw0_Zd8sag,4739
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_cst_lagorder.txt,sha256=G7u0DE_vjEwGp9ZubjC6_DDVFS7leaSh85cv5kuz7Bc,459
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct.txt,sha256=mrPdVZ0_6HDxMogbMWafJ0HsNuz5M4_LPuG9O5Geu4s,3670
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_Sigmau.txt,sha256=3jX4pbBJk26310kM30K96_aMnPjFxVJ1yqX1YrPYTgE,645
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_diag.txt,sha256=rrby4XZ5-k0v0g0Ta7lMWIKUfsPgFOvQ-SruJuDeYQg,2247
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_fc5.txt,sha256=9RqwFTgXElliVjWlkwxRBTuCzSJ6boFeIQZ97ry7VJg,1492
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realcons.txt,sha256=of20DDH5OfCe4DW3M-Ys14GdbInGNuRpMKGHkf6XOSE,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realcons_realgdp.txt,sha256=sbGdmll5JYVG_fFYyRTMzFhtYJgtpNB327TRu0OeCLE,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realcons_realinv.txt,sha256=CZ0GMEJ6lUCNCxWIY-l7WSyu0O8zDSEbv9RIFXGYae4,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realgdp.txt,sha256=no_-ZRel8Sb1Py4fyT3totu8LtPeeXM1g-eqtRpHJXM,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realgdp_realinv.txt,sha256=2YCX108qxTV4dATAiGH5Q7i42XX3LcnVyCcLL7u_eCw,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_granger_causality_realinv.txt,sha256=ks8N2vVhIsvPVTuLSRwm57ThD0T0zJgir-XkcTEjYTY,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_ir.txt,sha256=uLRMb-VBBKdP9yO0roPeiKoaE-DrbEDs8ryiF9_8iHo,4739
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ct_lagorder.txt,sha256=bWoH_x62iMQQ_1NaJu28-c09yiWrAeLY1w1V6kWcN08,450
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc.txt,sha256=LDvJY5EHPjneDe8HUUowxk_AgZOVIO1ILLg0nTP82Ws,3170
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_Sigmau.txt,sha256=85Apg5rO5HB5BvSQijoy37nZHGMO-VmOXEorNhC9SoY,645
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_diag.txt,sha256=JaRCM6lfNV9wTsfZWOEOTdLa2kOkQmTSgnsAUGk3uwM,2247
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_fc5.txt,sha256=jkVaGm-uqXgTxUELXTKMnoo55VaSexVULFgIep8oK2Q,1492
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realcons.txt,sha256=rk6P8VmNLP5GqFe1g8oi7RwVadpPREeZjjmkwa5HIOw,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realcons_realgdp.txt,sha256=qI2liuJn60wW3b4QdCFTLEhz34t33fNauRGhsOggLcE,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realcons_realinv.txt,sha256=THN8VroIO87ZENVhG8NzUPmX7SRa0FPpic690FMRa88,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realgdp.txt,sha256=-mB-1pmdvHTxhz1m7ozLbVZ6_lrtSkEhSfLFn4Ongq0,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realgdp_realinv.txt,sha256=w6y8lXdtrhfd0DKnfkYzaHd0KOTHMeAIuVYma9xvpWA,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_granger_causality_realinv.txt,sha256=mNjCfDKNxZv2Vwc-yt8QkUMDrG05WPsh771VkVaiSvI,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_ir.txt,sha256=s4PQOheiNyyoIAm48WFaTuKaa8oYzA3gfXryZjytWZI,4739
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_nc_lagorder.txt,sha256=j99Y5Udp7hY-T1mJWPzFYOEtAPwbf3cnhT_KiaHqrsM,411
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs.txt,sha256=jhttXPx7KeU3Mnqz_GP73t0rUi8QbAO4yp_01VAyTic,3831
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_Sigmau.txt,sha256=P4caWfwitMAm1jWWc90lmu-Y9rUdobqXHXIH-iDs-Uk,645
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_diag.txt,sha256=VsKO82ivM6Kpm8pFtmQe7_jvYKOhpqZT2UwA2vSwLfA,2247
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_fc5.txt,sha256=fa2hjX9nqEhb22VVUrTlmzw1KAze4b1BP_k6Fokz6_U,1492
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realcons.txt,sha256=DI1drsHWQF16rnCe9QLVVUl2lb2Zr9LyS5TTt76V-_c,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realcons_realgdp.txt,sha256=SjEVZ7udEmwM3JLQTjwrQIp1lXVh0z_oKn25RH9Mfrg,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realcons_realinv.txt,sha256=huIbM3FYI3HJ6nPy64Mbn5vd-zVWyoMX0Jt8acphcaM,337
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realgdp.txt,sha256=gXiTVL7anT3x31kmHHUtMaU_6zlptFjcPF-cdqPqbK8,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realgdp_realinv.txt,sha256=oDqjuagHbpQVX3Na0Iggm9-Gnq_fPN5Dl__fFUHIXSk,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_granger_causality_realinv.txt,sha256=nP1MnK2VjDCy4JyZNbJR6cOEYhAxrrX5bro8AJhUGR8,336
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_ir.txt,sha256=MWm8jjjZ0Xd7v2E0pZvkLl7ntY19r2tC4_-SBcVOIjw,4739
statsmodels/tsa/vector_ar/tests/JMulTi_results/macrodata_jmulti_ncs_lagorder.txt,sha256=jrZASK-mBpxSZKnt85Kf2ra4GZoljlh0-A3Cinb5MZ8,447
statsmodels/tsa/vector_ar/tests/JMulTi_results/parse_jmulti_var_output.py,sha256=fCBleSjkcBXgyWIThCI3lNqPxeOZ2Zack1HdAh3pRpQ,17989
statsmodels/tsa/vector_ar/tests/JMulTi_results/parse_jmulti_vecm_output.py,sha256=5jY7bQdk2XSoyAdkTC8yZDmXclXv7Xv79ntTNPAMqFw,23658
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci.txt,sha256=bBjX09YonLRyF4-0tpSiJOLuzPhF7IPlrXXt1Cjfu34,3770
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_Sigmau.txt,sha256=aYJUp08I5XmMeCJWV5Um3kv5qW0cWZd0-euE6LLmYh0,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_diag.txt,sha256=qcfEAn8Gk_rGkiTm3OfDSlsSzYBFIT6CrNL8IYyBSog,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_fc5.txt,sha256=eCRRjtj17xX9pH0LTXC3VHfjr8df7O7KJhZcxAjLV8k,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_granger_causality_dp_r.txt,sha256=5MEdk9Ckk_sIat7bxkpE0WXlem2mHDFApLRb1eXctcA,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_granger_causality_r_dp.txt,sha256=9CVuRI0nWt_WT03VaPhVmnQAFJhinvMPG8ccKLey47o,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_inst_causality_dp_r.txt,sha256=koa1CjfAXqEJv1A57KJF9JFqmweas_v3Oy-IvHMDidY,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_inst_causality_r_dp.txt,sha256=XTDI3un0kduWFgPtXIXBlex8DLJ6F1MU8p_8fbyIE5w,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_ir.txt,sha256=bZRDLLA7i1Dm60vdGNtI6_IHq_JZh8d0mLhVsAO0P8w,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ci_lagorder.txt,sha256=mNLff2IcC5ZzNzli1naPalxRfN4UfWtSqkPzy9WYcXs,432
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili.txt,sha256=DFIw5T60-AodCGZPCwbMzbERpEF3TEbAaFJCuGz7kOo,4071
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_Sigmau.txt,sha256=GVC3HPYOiUNTjwHZFLfao6XvuRD94CMIR3aqltgagF8,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_diag.txt,sha256=iCUNeiCWioGVgXTgfzDS8SnDosl2n-NfJ8d3FLjNmQg,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_fc5.txt,sha256=Ra4lQZt80L98T5sgx69QVsb6RVT5AfrXZ45MeZk3SPw,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_granger_causality_dp_r.txt,sha256=-EAh3NJBzYpZv-LGf2YM8i7Rr5lmkp1cSGKu6wAXoow,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_granger_causality_r_dp.txt,sha256=5lD1KDmdyB_QaQbr3pLVwRCZjJTTp894cD9atB7Zf8w,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_inst_causality_dp_r.txt,sha256=vle9d3lntYXScI0ehw84oAup5Mk3h0BszWGJfJzBwB4,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_inst_causality_r_dp.txt,sha256=35KdDPOKHfWngDUs-yEUWX29EeR3JT7KD2sOnjbPelM,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_ir.txt,sha256=u113ALFnEosE-XdmcojTOd6H9CuuZLNdt883ibAGTss,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cili_lagorder.txt,sha256=4FnNmyT1ENsLsUNy9LEb7FVMrvb5KEa-fxaNyCBAxyA,438
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis.txt,sha256=mPE2wcYQ7Ss165N_htUooTRnkt8xNFbEVbEbZY7sGCo,4654
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_Sigmau.txt,sha256=Hw-NO6Y0O8ZLHeZvs3abdiwhL_l9LTD0Nmqa-vGPm5Y,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_diag.txt,sha256=0BjL0IcafJO8zVf1PvLKB7VIdAdm2URDG7FSxqkCXX0,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_fc5.txt,sha256=naEZ1O6ad7581Wf_sMTBM02SXkT5_ZJ11m1Xx3frr3g,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_granger_causality_dp_r.txt,sha256=m6e1salxJNFqAcqZIbdmuPK_mVdh9_TVpHdt-RhoNT0,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_granger_causality_r_dp.txt,sha256=H2zqaDfl7VqsNFy2YcKDsoCY39S8RUvDLxJUMzQsbgA,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_inst_causality_dp_r.txt,sha256=h4aaNStdTmHTgA2v88s3VihkMK-fVGzrEwR36BhMTp0,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_inst_causality_r_dp.txt,sha256=ImKWwQsUMbjbZ2ARVSiH6DgAmLYoQjwzAVnvYDbMPX4,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_ir.txt,sha256=DPE32petAVEzXgOkJSxhTKQjzuuxKaniS_5_WrAAeOo,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cis_lagorder.txt,sha256=khdocACaPnbjHe-toQmZTKTH0xk-foHZgZcagOcAHrI,441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli.txt,sha256=bgGlHnynq8NCOQRaRwS437BoQ4rkXDhBZD6nKm2ZKMk,5015
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_Sigmau.txt,sha256=SN5xJBirwqs6y-GKI8u7DRJozb_qy-5Zesul2iixLuw,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_diag.txt,sha256=Gdx9HIBHme6geZHHfbLO88LJjEshaXyUrpBOghjjA3s,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_fc5.txt,sha256=hAqMv4v1F8-_e3B_1wsbedZVjDQTRX2v3UB8uY50Myo,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_granger_causality_dp_r.txt,sha256=rAdlsIXqyI_FgHjDWpH1xy7rvfWpDFvIZ1-2rFWM0-0,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_granger_causality_r_dp.txt,sha256=SVWFstSeF4s3EGRJ_6TPFAaK_LrvScG6BUww-PjPfXM,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_inst_causality_dp_r.txt,sha256=kyEnHbn-rR8PrfSW35zKW78DMzXC2KxCxqpDZKNRfcY,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_inst_causality_r_dp.txt,sha256=OSVsf9d7nZKOlzhQ4e0_mx-lXErRWg2hilE_cSvPJ7Y,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_ir.txt,sha256=3PJ8Kj2I1-2u9YxeM5sBe3hglGiSkI5ZhyjMuLaKooM,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cisli_lagorder.txt,sha256=CpJzrKq6UcUlhhkyAUtztYpjd7SMlspA4xDoE08PFkc,447
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co.txt,sha256=l8SuD0cww1LSWBAgPrqh9vlCfAcopiH4S_N7aMz1HLM,3941
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_Sigmau.txt,sha256=IXYzTMhRUgPkYvLDWxhYG2TCbo8oCeZexOFcBAktK6o,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_diag.txt,sha256=z-xjJvfVED0JCVeEXMlufZZBCwJaWMY60xea5_d8rII,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_fc5.txt,sha256=0I2PpH4R0EgCQG4k-vXNFg_7sC5u7ZDORMPhna3SP8I,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_granger_causality_dp_r.txt,sha256=8i5yQ_rEKp-IAf6gwOPd3I1ehyBp98HW2RKdFWeoheE,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_granger_causality_r_dp.txt,sha256=XZPiQcff18fAVVvlVA4D1LDQX9SaNwfJgLdjiIajRSc,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_inst_causality_dp_r.txt,sha256=K5kk5ZxVOy8fbgWNKCTm-wJDhNNVAXvmRloml2U2B8E,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_inst_causality_r_dp.txt,sha256=5EgXp0hQ3bZyIJexEg6arNyk_9H0Z4EqoHu5_eUB6Fs,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_ir.txt,sha256=qhDf0Xbk36kmoM_RvJHNM8UHpAiOGa1NyyBlfBowu1g,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_co_lagorder.txt,sha256=D6tTkxv8ogKm5QAVsRaFad0Z8To74UfBUPZ09z7MsOM,432
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo.txt,sha256=HezLMnkIZKNNR_3moVETg72A80IwkrH41AK7fw1WCd4,4195
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_Sigmau.txt,sha256=Y9V_z_rvwJDjDK1OweICcdbh6QB48ACcSe4Er5bKeDk,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_diag.txt,sha256=x9I6-4v4xKWvVxZphKFlZXUW0hBwfmpr0NJ45FNQcyU,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_fc5.txt,sha256=vW9_T8dQnc7gvFvPPk46hsP-J0Z5ieCe5r5nsjqnMYA,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_granger_causality_dp_r.txt,sha256=znSe26JzqohaesqYHq3ix1HgVz2WkR0CsrQxwishRJ8,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_granger_causality_r_dp.txt,sha256=mPWZXhNgROBtRPwhTyWT_HUEaluiL7_rnZevzkC0UTs,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_inst_causality_dp_r.txt,sha256=2KXGLP2qwjN1a-lRJUU70Iy6fCCShNBQK6zSqG57eI8,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_inst_causality_r_dp.txt,sha256=Y8BW_VJyr7muc9b-wYvGG0Jnddnj99arWI238x_kZz0,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_ir.txt,sha256=w1N-Yumpi7UR9CHIDg2oQxIm0nVdZv66udV0JNL1ZZo,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_colo_lagorder.txt,sha256=cyUCsmHAOdGdfn-N1X1RLDU_7olj9ccOIN4BnWtD54E,438
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos.txt,sha256=w9gww2MEyq1gFDInxOTmRD76l4v0u9n3iIqArn6m3fM,4694
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_Sigmau.txt,sha256=dO0ZYBDUhn1_ljzjckEU2LUvJu5Ltzv5iMHAjchTQ_g,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_diag.txt,sha256=zr5hyRBsFmmfLiM5vCGFXj5FSXpWBukheXMDEfgB7ZE,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_fc5.txt,sha256=6y50qAU3GdRSQonYq3pgaKXwGEFksZ0kjREgW-AekUg,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_granger_causality_dp_r.txt,sha256=XiOqKl3VKpqPJ_Gv9i-F_DePs5K6Ph8ZFaGMmKkxwlc,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_granger_causality_r_dp.txt,sha256=B-08P80rkebACupfrAEfC39JQ0_kYAWs2UnXVzMhN5I,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_inst_causality_dp_r.txt,sha256=15vHiyJa06OG21tr2sVeE30POnA04MbmrZ7ezXUrsjA,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_inst_causality_r_dp.txt,sha256=8uXvXU5_06tizvOGHcwP1hA6bGo6MpOQVsuy_6R16I0,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_ir.txt,sha256=5cb0PyUaKyN2FjkBd55SQ0Bt3QYzvWkcrk4mb8-ayD4,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_cos_lagorder.txt,sha256=WZsnhluyaa1AiIpj0-B3QTkc9VeaKlncrGwu-8-yIFo,441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo.txt,sha256=7tgTNecFlNtABKZsKrKVuNBXQwd1ChCQlelhia2baXc,4948
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_Sigmau.txt,sha256=115YTsU98MjVygZLjPToyCe1CbzfR5KzJp05KGGcQT8,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_diag.txt,sha256=74M-mA9X-1LwGommfQln5MB4NH8BMNoPnucnmSTupWc,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_fc5.txt,sha256=RsK4muRRPUaps_iXkG9GZgC2MCb0fXSmWTRzc8IXCbs,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_granger_causality_dp_r.txt,sha256=d5pqbAJVcp0zf7LGljf7eXeIKgWyeBgZU5dEPBODJzY,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_granger_causality_r_dp.txt,sha256=P71Vzxx9hPA0PykZ1rnHIZbw9m48PZOiTHwGRMJAOtA,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_inst_causality_dp_r.txt,sha256=d6gDTiaZb6_9TFxFt4YezJFJtSUHFZO0njby4HTo6tc,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_inst_causality_r_dp.txt,sha256=kIkRMYy0vatO-xs_XQrn6ajcaavFs6FdQF4d-_-UQKo,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_ir.txt,sha256=DxGyrr2ffKBqMqIO5KMDJc3ay84m4KcV6s81y-tbIWc,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_coslo_lagorder.txt,sha256=QlYuoYo3XQJL0sSkfjkNpHnem9JXBJZC1_KMNDJ4wXc,447
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc.txt,sha256=mpupOBhE-QbK64TFb9xxAts5d8yfc-lrqZlemtFqFmQ,3426
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_Sigmau.txt,sha256=-HrAk6DsjVKL7Dw67QQYdMyBYFggR0Qd06c3Z1_0j3A,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_diag.txt,sha256=NOoRfSUxZCRIGRh65-BOgDs7cjk5z7H5Lanpy5IGL84,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_fc5.txt,sha256=8Fwi-v-wf8ITgvjVJQo36FiYkniR0c2hP-4UHf8FHvY,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_granger_causality_dp_r.txt,sha256=RmxlUy4rFw6vOnE91HW2A41FU9JtYczo4c4zJ4yYlVc,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_granger_causality_r_dp.txt,sha256=XaWfvMPwAVLnxIe8CeSLboPA6s2MSkw3YsLTdoRGeek,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_inst_causality_dp_r.txt,sha256=KWKdPawC-jg9LgtpPqrUFr0b2QSPElUiU0nsQrlbO2k,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_inst_causality_r_dp.txt,sha256=rgOrgg5IFKgBH6v0fn52i-ihyJh58_JGi4j3zhZp7j8,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_ir.txt,sha256=z_ZgHniWX0BB_3g68bfvPFeDE_mkbYUtaWRfSv98Mo4,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_nc_lagorder.txt,sha256=dJwr3giyQf5QMsmKBFCxFL_1kPz6tScbU3jGRH-KZXM,399
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs.txt,sha256=tEF5WEHUhhWPHFfIdSFZFmoswcKzaj6phItISIPtL2Y,4440
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_Sigmau.txt,sha256=JTwdO1-QndIKhMqmFu1GoTCta2PqLkAnemhSohro404,371
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_diag.txt,sha256=B0Eot9vMboTduUJ-ezxqxh1-rCIHg7Vs9HItfnNH42o,1820
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_fc5.txt,sha256=rBXSQJLTjeMBFVLbwjp4ubS7VpE5OUEoc6EKTHlLclk,1068
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_granger_causality_dp_r.txt,sha256=LEYbSQRN2lfyjXDFNiKzQB_0iA48d2oQJ2x-gL6UAeE,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_granger_causality_r_dp.txt,sha256=2D-_dJ9l4uAC-6eCKxmIJxV9kb1wAGi-aNUKr_si8lY,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_inst_causality_dp_r.txt,sha256=Ab3-RvBuKl4qMtWnGHbGT2AT-7BVjQUXkMyE0xtIoRk,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_inst_causality_r_dp.txt,sha256=u1cmPyTynAqeL4VS8HA7uuvavOnB32MKLTqJcz3NFhU,294
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_ir.txt,sha256=aDNfx-Qen7BOSOSYPMudX82UzdeaxvElk62I6NN0C0k,2441
statsmodels/tsa/vector_ar/tests/JMulTi_results/vecm_e6_jmulti_ncs_lagorder.txt,sha256=X2ApuFppYom7O_VoOOfxBwI5aaB3fE8t_AcZOMrdLQw,435
statsmodels/tsa/vector_ar/tests/Matlab_results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/Matlab_results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/Matlab_results/test_coint.csv,sha256=XkcV8hlApCLOfmqH-zN4YXQG9B7YJ8dvk8G1pEXdZbo,22317
statsmodels/tsa/vector_ar/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/example_svar.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_coint.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_svar.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_var.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_var_jmulti.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/__pycache__/test_vecm.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/example_svar.py,sha256=X2xUIzQNhchKqLauttjFBj0EPfzd7pl2vXjo9MXE5Xo,1002
statsmodels/tsa/vector_ar/tests/results/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
statsmodels/tsa/vector_ar/tests/results/__pycache__/__init__.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_svar.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_svar_st.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_var.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/__pycache__/results_var_data.cpython-312.pyc,,
statsmodels/tsa/vector_ar/tests/results/e1.dat,sha256=qFy7I_GZBpmAYeVOMmcwEfag6bf2hVHB-c6EfxA1SWQ,2603
statsmodels/tsa/vector_ar/tests/results/e2.dat,sha256=i5DCYj_3FTK1hWvFc27DSIVmvc9mYoqbrUQ7R4O8I-0,2052
statsmodels/tsa/vector_ar/tests/results/e3.dat,sha256=KFrLUjwFxSeqGQW0A5tLLXkomHvpHsYw74y17wTC_pk,7619
statsmodels/tsa/vector_ar/tests/results/e4.dat,sha256=YFA4NyatQcR0OXxz_5NrNqyj7DrQmvow4K8Hvh1mn-8,3245
statsmodels/tsa/vector_ar/tests/results/e5.dat,sha256=_GPT8GX2EPLxPhJUYUXi4CYLzJN63bjSmFN-_oAt3wY,11672
statsmodels/tsa/vector_ar/tests/results/e6.dat,sha256=vjWPGDDNobqvtnmFndTf86vvrGIQNI9mGYAtRbUGM48,5079
statsmodels/tsa/vector_ar/tests/results/results_svar.py,sha256=V4qhXeaW356wzWkFmVRvc7_ncESs5VvqfJKiZ9qY2CM,387
statsmodels/tsa/vector_ar/tests/results/results_svar_st.py,sha256=HzOEeV3VYQF2Wqd_EGfaoH5jhsrDmTOZmK8WyYvVq9I,41933
statsmodels/tsa/vector_ar/tests/results/results_var.py,sha256=lLecEP4rxMMIr_YK_26tqT_mjPYXj7g9_8QhY_woCjc,2647
statsmodels/tsa/vector_ar/tests/results/results_var_data.py,sha256=_-5k2lpENjnvL1mKtXmtT0-eKG7KMjDJIQhSjDJQQgo,6778
statsmodels/tsa/vector_ar/tests/results/vars_results.npz,sha256=SAdu-Qivvqq9ULg6iSQ8ohp--uLkIkI-NZ2Bl9HZkw8,5696
statsmodels/tsa/vector_ar/tests/test_coint.py,sha256=EQW-EEF4mJ6f65OSbTnkW7_dPwKEEeWE7T2jQxOn2rY,9907
statsmodels/tsa/vector_ar/tests/test_svar.py,sha256=5K9r_oZYbZxgSqJuYmGMMDXAskXGhxP5UP4kIuvv03o,2878
statsmodels/tsa/vector_ar/tests/test_var.py,sha256=ZfgCMP1lHkBMW8y8GWCy4w3hlRkxEgaMVWg3d_AjaqA,31005
statsmodels/tsa/vector_ar/tests/test_var_jmulti.py,sha256=UZOUYAhqRIJuwtM9t_xoseY3Ywu9Pr5Y-AUSE6tqCNY,26294
statsmodels/tsa/vector_ar/tests/test_vecm.py,sha256=EAw9EsbHEMYHC0c4fgYDq_IYJ6xuPZSaosdClXRsURA,77283
statsmodels/tsa/vector_ar/util.py,sha256=kD-ImNuujDcwvo_WICBkBlsqX52GS3FP6xN9O15BzcQ,11195
statsmodels/tsa/vector_ar/var_model.py,sha256=hQLrMonFqSiXvsPhASJWRlisKWCjv7UduXHBYb-Hr3c,76827
statsmodels/tsa/vector_ar/vecm.py,sha256=SkpvF_bw7nalp7cNbhFCWT55FvZE8-grMOhBwIBA4z0,94378
statsmodels/tsa/x13.py,sha256=EwOTnQYQSc74XKD2Tyo6yucC8OKz5qYpj2lWFLTdAGU,23084
