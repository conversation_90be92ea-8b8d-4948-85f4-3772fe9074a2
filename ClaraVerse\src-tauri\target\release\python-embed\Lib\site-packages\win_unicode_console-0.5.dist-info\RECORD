__pycache__/run.cpython-312.pyc,,
run.py,sha256=D-V2ycFd0NFaW2BO33zlS1-pUxgeyYvnU5TBaAWQCME,147
win_unicode_console-0.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
win_unicode_console-0.5.dist-info/METADATA,sha256=6BnzaLmrtSnJuo1cL6YRqbzcA7rYncomaWtHDs3BI2k,14571
win_unicode_console-0.5.dist-info/RECORD,,
win_unicode_console-0.5.dist-info/WHEEL,sha256=GV9aMThwP_4oNCtvEC2ec3qUYutgWeAzklro_0m4WJQ,91
win_unicode_console-0.5.dist-info/top_level.txt,sha256=4fq6GVH3h6fYgsgaFkEAKV3IO-kVRT2UUIbw-juaQcY,24
win_unicode_console/__init__.py,sha256=iMkzoSiIp7EkXBqaFOPYZbfcCYO4JzSBdrJNNVywwoY,1096
win_unicode_console/__pycache__/__init__.cpython-312.pyc,,
win_unicode_console/__pycache__/buffer.cpython-312.pyc,,
win_unicode_console/__pycache__/console.cpython-312.pyc,,
win_unicode_console/__pycache__/file_object.cpython-312.pyc,,
win_unicode_console/__pycache__/info.cpython-312.pyc,,
win_unicode_console/__pycache__/raw_input.cpython-312.pyc,,
win_unicode_console/__pycache__/readline_hook.cpython-312.pyc,,
win_unicode_console/__pycache__/runner.cpython-312.pyc,,
win_unicode_console/__pycache__/streams.cpython-312.pyc,,
win_unicode_console/__pycache__/tokenize_open.cpython-312.pyc,,
win_unicode_console/__pycache__/unicode_argv.cpython-312.pyc,,
win_unicode_console/buffer.py,sha256=fTyjaZ8jAfKdo1uWKsTXZEu1k4IaKLiSKTTGrfIP7kc,1169
win_unicode_console/console.py,sha256=PNWvoWvlbP79fQFV1JqvpLug2GqY8fLxgUvBCMcxq-w,2379
win_unicode_console/file_object.py,sha256=too3_RshJnYByhC_DJEmFk4JLXwVxYdC7OEn4FAV0C8,1311
win_unicode_console/info.py,sha256=F97iFXaOSTtZ-B0ChEljHKR_S8UZLzu3EAkcYyrrcwk,372
win_unicode_console/raw_input.py,sha256=svSr_BE0jerJbBIT2xJi5U4Gm6miVNccirhkVgXx7uM,2971
win_unicode_console/readline_hook.py,sha256=EtC2sIgK0PS8TRfDB2GNWfAVIdArpgM5m8_rnWLTQ1E,3947
win_unicode_console/runner.py,sha256=yR0U3hJo6uMEnoOxw7hUdl9yxKY-avFFkvMBl0kpTsQ,5741
win_unicode_console/streams.py,sha256=JhMuUDI_nPbALN6H87gxx9dIkYPBhPHUBWx9wDpUCf8,9381
win_unicode_console/tokenize_open.py,sha256=GTcm2A-wgTWdR3kjOZhxnDNbN2xXdpm2OJ6DV2fBhZQ,4806
win_unicode_console/unicode_argv.py,sha256=6HYniWclnIQa4Daw4DMRn_dyAQH_-QD7221osoEL4i0,1787
