Metadata-Version: 2.1
Name: xlrd
Version: 2.0.1
Summary: Library for developers to extract data from Microsoft Excel (tm) .xls spreadsheet files
Home-page: http://www.python-excel.org/
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Keywords: xls,excel,spreadsheet,workbook
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Operating System :: OS Independent
Classifier: Topic :: Database
Classifier: Topic :: Office/Business
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*, !=3.4.*, !=3.5.*
Provides-Extra: build
Requires-Dist: wheel ; extra == 'build'
Requires-Dist: twine ; extra == 'build'
Provides-Extra: docs
Requires-Dist: sphinx ; extra == 'docs'
Provides-Extra: test
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'

xlrd
====

|Build Status|_ |Coverage Status|_ |Documentation|_ |PyPI version|_

.. |Build Status| image:: https://circleci.com/gh/python-excel/xlrd/tree/master.svg?style=shield
.. _Build Status: https://circleci.com/gh/python-excel/xlrd/tree/master

.. |Coverage Status| image:: https://codecov.io/gh/python-excel/xlrd/branch/master/graph/badge.svg?token=lNSqwBBbvk
.. _Coverage Status: https://codecov.io/gh/python-excel/xlrd

.. |Documentation| image:: https://readthedocs.org/projects/xlrd/badge/?version=latest
.. _Documentation: http://xlrd.readthedocs.io/en/latest/?badge=latest

.. |PyPI version| image:: https://badge.fury.io/py/xlrd.svg
.. _PyPI version: https://badge.fury.io/py/xlrd


xlrd is a library for reading data and formatting information from Excel
files in the historical ``.xls`` format.

.. warning::

  This library will no longer read anything other than ``.xls`` files. For
  alternatives that read newer file formats, please see http://www.python-excel.org/.

The following are also not supported but will safely and reliably be ignored:

*   Charts, Macros, Pictures, any other embedded object, **including** embedded worksheets.
*   VBA modules
*   Formulas, but results of formula calculations are extracted.
*   Comments
*   Hyperlinks
*   Autofilters, advanced filters, pivot tables, conditional formatting, data validation

Password-protected files are not supported and cannot be read by this library.

Quick start:

.. code-block:: python

    import xlrd
    book = xlrd.open_workbook("myfile.xls")
    print("The number of worksheets is {0}".format(book.nsheets))
    print("Worksheet name(s): {0}".format(book.sheet_names()))
    sh = book.sheet_by_index(0)
    print("{0} {1} {2}".format(sh.name, sh.nrows, sh.ncols))
    print("Cell D30 is {0}".format(sh.cell_value(rowx=29, colx=3)))
    for rx in range(sh.nrows):
        print(sh.row(rx))

From the command line, this will show the first, second and last rows of each sheet in each file:

.. code-block:: bash

    python PYDIR/scripts/runxlrd.py 3rows *blah*.xls


