{"rustc": 16591470773350601817, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 8433163163091947982, "profile": 367816849085071872, "path": 8899930628728231716, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 1966067146311246396], [16413620717702030930, "brotli_decompressor", false, 8717691306573876486], [17470296833448545982, "alloc_stdlib", false, 5434284025380580201]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\brotli-dc19bced52b698b8\\dep-lib-brotli", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}