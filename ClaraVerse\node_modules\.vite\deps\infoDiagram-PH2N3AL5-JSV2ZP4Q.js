import {
  parse
} from "./chunk-64E6KBPY.js";
import "./chunk-6KQIPLYQ.js";
import "./chunk-FSEACCM2.js";
import "./chunk-A22GKBOV.js";
import "./chunk-XE5PEP3K.js";
import "./chunk-KBKZELRR.js";
import "./chunk-MV57MVUT.js";
import "./chunk-CURONKCY.js";
import "./chunk-QO5PEC5C.js";
import {
  package_default
} from "./chunk-TZZ2KRSI.js";
import {
  selectSvgElement
} from "./chunk-CTAMZLGO.js";
import {
  __name,
  configureSvgSize,
  log
} from "./chunk-AEQEJSV4.js";
import "./chunk-BBUSXV25.js";
import "./chunk-I6RL7E24.js";
import "./chunk-256EKJAK.js";

// node_modules/mermaid/dist/chunks/mermaid.core/infoDiagram-PH2N3AL5.mjs
var parser = {
  parse: __name(async (input) => {
    const ast = await parse("info", input);
    log.debug(ast);
  }, "parse")
};
var DEFAULT_INFO_DB = { version: package_default.version };
var getVersion = __name(() => DEFAULT_INFO_DB.version, "getVersion");
var db = {
  getVersion
};
var draw = __name((text, id, version) => {
  log.debug("rendering info diagram\n" + text);
  const svg = selectSvgElement(id);
  configureSvgSize(svg, 100, 400, true);
  const group = svg.append("g");
  group.append("text").attr("x", 100).attr("y", 40).attr("class", "version").attr("font-size", 32).style("text-anchor", "middle").text(`v${version}`);
}, "draw");
var renderer = { draw };
var diagram = {
  parser,
  db,
  renderer
};
export {
  diagram
};
//# sourceMappingURL=infoDiagram-PH2N3AL5-JSV2ZP4Q.js.map
