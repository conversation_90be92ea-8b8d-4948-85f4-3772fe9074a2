{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 2587956275252978377, "path": 14332140982366814503, "deps": [[7026957619838884710, "serde_with_macros", false, 13624601128503074005], [9689903380558560274, "serde", false, 2694757531789484273], [16257276029081467297, "serde_derive", false, 15287546704676213715]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\serde_with-9a6af1336e17f8af\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}