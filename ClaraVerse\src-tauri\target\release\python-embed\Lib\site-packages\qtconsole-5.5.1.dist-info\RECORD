../../Scripts/jupyter-qtconsole.exe,sha256=KEDoXozLrVq1hJPuY7jlS6WvNaFp8W7cz8ZdRlC2Wgo,102283
qtconsole-5.5.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
qtconsole-5.5.1.dist-info/LICENSE,sha256=iHnKwx1qJa6IAYBWnqTYDKmyTqnnOnO1YEqXEfF0Sik,1528
qtconsole-5.5.1.dist-info/METADATA,sha256=oG7fM7VkGC4koFMKdQNSd3mzepvKjZJwRW0JoAzMg8c,5181
qtconsole-5.5.1.dist-info/RECORD,,
qtconsole-5.5.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
qtconsole-5.5.1.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
qtconsole-5.5.1.dist-info/direct_url.json,sha256=5QGQgFAFiNtfVH4v44MzLhjMEzMFcKhC1tNbUrZjEAs,89
qtconsole-5.5.1.dist-info/entry_points.txt,sha256=YV6LkkBzgnQF-Z_93NGdR5YkLKcsS--RwF8EPIaKGPw,62
qtconsole-5.5.1.dist-info/top_level.txt,sha256=Vo9i7Mvpf6_8kJtNTCtBhnk--hyj_ZJ6mzrOsJaYzyg,10
qtconsole/__init__.py,sha256=xmCKewcDvgni7pVRmItYwQYkzpdYG_wZAgdmx66IHaw,48
qtconsole/__main__.py,sha256=oiE8djOUI2ZAa0HPdOOCztrk2Yl7Y8WS1JwzoRZfewI,82
qtconsole/__pycache__/__init__.cpython-312.pyc,,
qtconsole/__pycache__/__main__.cpython-312.pyc,,
qtconsole/__pycache__/_version.cpython-312.pyc,,
qtconsole/__pycache__/ansi_code_processor.cpython-312.pyc,,
qtconsole/__pycache__/base_frontend_mixin.cpython-312.pyc,,
qtconsole/__pycache__/bracket_matcher.cpython-312.pyc,,
qtconsole/__pycache__/call_tip_widget.cpython-312.pyc,,
qtconsole/__pycache__/client.cpython-312.pyc,,
qtconsole/__pycache__/comms.cpython-312.pyc,,
qtconsole/__pycache__/completion_html.cpython-312.pyc,,
qtconsole/__pycache__/completion_plain.cpython-312.pyc,,
qtconsole/__pycache__/completion_widget.cpython-312.pyc,,
qtconsole/__pycache__/console_widget.cpython-312.pyc,,
qtconsole/__pycache__/frontend_widget.cpython-312.pyc,,
qtconsole/__pycache__/history_console_widget.cpython-312.pyc,,
qtconsole/__pycache__/inprocess.cpython-312.pyc,,
qtconsole/__pycache__/ipython_widget.cpython-312.pyc,,
qtconsole/__pycache__/jupyter_widget.cpython-312.pyc,,
qtconsole/__pycache__/kernel_mixins.cpython-312.pyc,,
qtconsole/__pycache__/kill_ring.cpython-312.pyc,,
qtconsole/__pycache__/mainwindow.cpython-312.pyc,,
qtconsole/__pycache__/manager.cpython-312.pyc,,
qtconsole/__pycache__/pygments_highlighter.cpython-312.pyc,,
qtconsole/__pycache__/qstringhelpers.cpython-312.pyc,,
qtconsole/__pycache__/qtconsoleapp.cpython-312.pyc,,
qtconsole/__pycache__/rich_ipython_widget.cpython-312.pyc,,
qtconsole/__pycache__/rich_jupyter_widget.cpython-312.pyc,,
qtconsole/__pycache__/rich_text.cpython-312.pyc,,
qtconsole/__pycache__/styles.cpython-312.pyc,,
qtconsole/__pycache__/svg.cpython-312.pyc,,
qtconsole/__pycache__/usage.cpython-312.pyc,,
qtconsole/__pycache__/util.cpython-312.pyc,,
qtconsole/_version.py,sha256=Goodp6hyHwGkDas4ArfwO5ySD7XRtSVFzjIT338qLG8,72
qtconsole/ansi_code_processor.py,sha256=Nc2fUg_mY3hP1av3Cp4XwWX3TGrta4DCd9bt7Uqs5TE,14185
qtconsole/base_frontend_mixin.py,sha256=AKMujKcx3vN58Cv8xz-7DJG0e85helVd1kzpl9CXeio,6295
qtconsole/bracket_matcher.py,sha256=KR4HLb-M0Myvv8BTY4vathevMimefvQpY2NJlK0Xv8A,3733
qtconsole/call_tip_widget.py,sha256=CK63Y8EGRLxe8xv_7Lizn4iXQbt96EOFO0UYoSxlRyo,10683
qtconsole/client.py,sha256=cywgwvpqsHy4ljhdNzaLMC2Nq7J7wAqkHkBV6JcZ_TE,1971
qtconsole/comms.py,sha256=0V6OdNYwdlzg3OimTOlLUQp5KU0IHvE8zkgIZotLp18,8848
qtconsole/completion_html.py,sha256=qXOE5w5zHGMxpjyndpjVZVSk8P0bZfsuK5mW-0Hl3W8,12909
qtconsole/completion_plain.py,sha256=z5unM5yNIHX2nv_M0qjKV1s-lg1yI2rGKm2kRge_8lA,2234
qtconsole/completion_widget.py,sha256=KuA-r0UGXHpWkqGB2CKHjcq2g5FFl4_lI1SO_69vhls,8151
qtconsole/console_widget.py,sha256=0iwOVod74oHYFaW_sNLONRjsa_nCaRbSeZMtkWVzirY,104735
qtconsole/frontend_widget.py,sha256=yR-WvAIAPnnGBVYu01Xx6yLfapQAogmELAW4mSeHcvY,34873
qtconsole/history_console_widget.py,sha256=0Cn0ZFxnCn2e0nGVHpPDM06ixj7bG187L_7-6a0sFxc,9947
qtconsole/inprocess.py,sha256=S0VeGK-Cv_Svl1o9cq_p9YaitJNJfzpwwU8X_QbuDUI,2874
qtconsole/ipython_widget.py,sha256=8r67wSW8XcyfIYtwEpCcferlSXCaBumzxh9FR_LhtvA,169
qtconsole/jupyter_widget.py,sha256=b3lB_QmDACbB7yLRGOt1cTxv_RgWVHv4h9szndXbM6E,24994
qtconsole/kernel_mixins.py,sha256=dbYz7pLYC1amtKxmgWmVjX3pJl6YD-9ZiB_Nf7mVDE4,1813
qtconsole/kill_ring.py,sha256=CNLSzOMY-d7USvH7fDzP3TbVU0t4UnfvAraKHZQGdL0,3793
qtconsole/mainwindow.py,sha256=pwpLBt1aRSXd-LAjh_bmELNdBAT3Vlv9cq7vDI3FQ6Q,38402
qtconsole/manager.py,sha256=RW6rfDQjC1B5s0QTRd4g3A8o9r3z9JbOzzrv-KoJclA,2602
qtconsole/pygments_highlighter.py,sha256=2UcGMhwPYeLLiwwIKVgQlj9Ch1uEVt1kvxnZFUnb3_w,9119
qtconsole/qstringhelpers.py,sha256=gO3LTHA4BE0RpwpCm0HTw_wUg7FAc6OPzRTvbQcnHT0,540
qtconsole/qtconsoleapp.py,sha256=5uR1o4MT6G1C7Z2qvT1Zg2v74DMkJq9hb9iqpvFOY18,17229
qtconsole/resources/icon/JupyterConsole.svg,sha256=XJZOyZK3Nu3Ou6rEnrHOGPR0oSaJ56VDfzD1Tj9yPOw,26397
qtconsole/rich_ipython_widget.py,sha256=1NQ0TZgBX6g0ihnNLWhaXqUR0cNp96AQY6TQQVP9Niw,184
qtconsole/rich_jupyter_widget.py,sha256=j_R--mq94FgvshJS31uDncKtXu8WmMeXMioDtXzxcdE,18478
qtconsole/rich_text.py,sha256=Fi03Km9Gb7pnua7yLTEHOjQ5gLH5zBbtFYxrhQGPJBU,8638
qtconsole/styles.py,sha256=YO7taAiFRgWFhVaftNUziCPmcjgtVKtLESPorAKtqPM,3801
qtconsole/svg.py,sha256=D3v5H7iN9Eav3GODhAxBikgV5f3nYDSJRFVDeqJbJFo,2394
qtconsole/tests/__init__.py,sha256=7_vIA_bAaOUhcZvEAYB1-sihwNGbpUZY-Mg5G_ymzos,132
qtconsole/tests/__pycache__/__init__.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_00_console_widget.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_ansi_code_processor.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_app.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_comms.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_completion_widget.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_frontend_widget.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_inprocess_kernel.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_jupyter_widget.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_kill_ring.cpython-312.pyc,,
qtconsole/tests/__pycache__/test_styles.cpython-312.pyc,,
qtconsole/tests/test_00_console_widget.py,sha256=8EpDZrZdLYKQ-ezAU7FqDq-GboALqqVmrDmrrSNw1x4,26302
qtconsole/tests/test_ansi_code_processor.py,sha256=mQXXCzxLvjBN2uSMDz2eNGHKfUiG4eDEwUHJ0SfKF1s,7430
qtconsole/tests/test_app.py,sha256=UHI3rSH6K9cgVMIEYWvGpdv8YR_xM3InZbJdm5GuljU,1003
qtconsole/tests/test_comms.py,sha256=9-5MKagWMEEimJHcjg1LqLRfSUkis9D4nfJmCHheou0,5400
qtconsole/tests/test_completion_widget.py,sha256=-gG8kd_uGNcUjvAvrEmbb1RMVj5ZEvAe_ajVKPcu33M,3343
qtconsole/tests/test_frontend_widget.py,sha256=If0YZva9_1Z2TzFs6pj3OEL7ObO9YNeBMtXB9LnhYOw,3219
qtconsole/tests/test_inprocess_kernel.py,sha256=xxdX6nNOiWhPsUsfEYp9K_xcfX-3uWcsFZw3eaTTNsc,976
qtconsole/tests/test_jupyter_widget.py,sha256=xSdLCuO6J0KpP80mWsZbtJU1JVLJyVuZMqNTIIB-pW8,4976
qtconsole/tests/test_kill_ring.py,sha256=pe3BGzF2_eh2qdEM9Wd0B3hjGJNAeYm21s3rrA97w6I,2278
qtconsole/tests/test_styles.py,sha256=Cvnil-t9wOCZlEEpUlnjkrf7uhUj2hAHqCdOrQBt-Ig,519
qtconsole/usage.py,sha256=SZm9_6SSB6D8Ow0aMr4XyrOGvJPLfn9ZKlFU7oXcxOk,8349
qtconsole/util.py,sha256=mCRqD26BUIfogZDOqLjXNT6oketxmqK5wNJjaf7EgAg,8354
