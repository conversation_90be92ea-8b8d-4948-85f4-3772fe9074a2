Metadata-Version: 2.1
Name: rfc3986-validator
Version: 0.1.1
Summary: Pure python rfc3986 validator
Home-page: https://github.com/naimetti/rfc3986-validator
Author: <PERSON>
Author-email: <EMAIL>
License: MIT license
Keywords: rfc3986 validator
Classifier: Development Status :: 2 - Pre-Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Requires-Python: >=2.7, !=3.0.*, !=3.1.*, !=3.2.*, !=3.3.*, !=3.4.*
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: AUTHORS.rst

# rfc3986-validator

A pure python RFC3986 validator


[![image](https://img.shields.io/pypi/v/rfc3986_validator.svg)](https://pypi.python.org/pypi/rfc3986_validator)
[![Build Status](https://travis-ci.org/naimetti/rfc3986-validator.svg?branch=master)](https://travis-ci.org/naimetti/rfc3986-validator)

# Install

```shell script
pip install rfc3986-validator
```

# Usage

```pycon
>>> from rfc3986_validator import validate_rfc3986
>>> validate_rfc3986('http://foo.bar?q=Spaces should be encoded')
False

>>> validate_rfc3986('http://foo.com/blah_blah_(wikipedia)')
True
```

It also support validate [URI-reference](https://tools.ietf.org/html/rfc3986#page-49) rule 

```pycon
>>> validate_rfc3986('//foo.com/blah_blah', rule='URI_reference')
True
```

  - Free software: MIT license

