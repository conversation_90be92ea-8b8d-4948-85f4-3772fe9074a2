{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 17984201634715228204, "path": 12619617251061245366, "deps": [[2671782512663819132, "tauri_utils", false, 8944806976073788331], [3060637413840920116, "proc_macro2", false, 1268470941054599307], [3150220818285335163, "url", false, 7312290741255693730], [4899080583175475170, "semver", false, 3269051643575976202], [4974441333307933176, "syn", false, 4882867613712703763], [7170110829644101142, "json_patch", false, 1275626119446388925], [7392050791754369441, "ico", false, 7205394907753893387], [8319709847752024821, "uuid", false, 10675156288580978029], [9556762810601084293, "brotli", false, 1188962338205123977], [9689903380558560274, "serde", false, 11963765274265209295], [9857275760291862238, "sha2", false, 15284423489976726817], [10806645703491011684, "thiserror", false, 2844407423438860906], [12687914511023397207, "png", false, 8441074092918439930], [13077212702700853852, "base64", false, 9099264468468203408], [15367738274754116744, "serde_json", false, 14742541281671691756], [15622660310229662834, "walkdir", false, 11372007230200888154], [17990358020177143287, "quote", false, 10852422733387940731]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-ad78f4fb8399dee4\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}