screeninfo-0.8.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
screeninfo-0.8.1.dist-info/LICENSE.md,sha256=9YJ7qmT0Igvb30L5B0ipooOsx2HRlwUQXd10kgYbUSY,2637
screeninfo-0.8.1.dist-info/METADATA,sha256=cGatBz-ZB8mCq0FAluuD3J5mTYZQzHaDdHIFIat8jJ0,2869
screeninfo-0.8.1.dist-info/RECORD,,
screeninfo-0.8.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
screeninfo-0.8.1.dist-info/WHEEL,sha256=DA86_h4QwwzGeRoz62o1svYt5kGEXpoUTuTtwzoTb30,83
screeninfo/__init__.py,sha256=_yvctYlNh6qOePMn0DnTiDFLz1bOWPtyaYDm-Ts6Fas,185
screeninfo/__main__.py,sha256=vj1B2_6hRT9jDiywXCSA3Xv8D1qJx6LP8OZIhaz63wA,480
screeninfo/__pycache__/__init__.cpython-312.pyc,,
screeninfo/__pycache__/__main__.cpython-312.pyc,,
screeninfo/__pycache__/common.cpython-312.pyc,,
screeninfo/__pycache__/screeninfo.cpython-312.pyc,,
screeninfo/__pycache__/util.cpython-312.pyc,,
screeninfo/common.py,sha256=4fVFaA978j6yC0mz5JLmUWfHb0weO3Q8msYKcCNPE4M,904
screeninfo/enumerators/__init__.py,sha256=al0B28RjdP3TagusES-Hne2CIrTOZW99w8ehSzmP0Ok,219
screeninfo/enumerators/__pycache__/__init__.cpython-312.pyc,,
screeninfo/enumerators/__pycache__/cygwin.cpython-312.pyc,,
screeninfo/enumerators/__pycache__/drm.cpython-312.pyc,,
screeninfo/enumerators/__pycache__/osx.cpython-312.pyc,,
screeninfo/enumerators/__pycache__/windows.cpython-312.pyc,,
screeninfo/enumerators/__pycache__/xinerama.cpython-312.pyc,,
screeninfo/enumerators/__pycache__/xrandr.cpython-312.pyc,,
screeninfo/enumerators/cygwin.py,sha256=1SM4Hn9pSt4vJOJ4H7cQC7f_nJ8Iv7Y9Ln7BHSglILA,1654
screeninfo/enumerators/drm.py,sha256=3095b2-MRvJRuQzIc9KobLcjQQ-6Qa4OHygYGLLb4G4,10324
screeninfo/enumerators/osx.py,sha256=YAAf5kugwc1ioTVlm5-PQnVpWGLx6TuegUKI2G_c8b4,716
screeninfo/enumerators/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
screeninfo/enumerators/windows.py,sha256=mw58ikrc9o2Cd6MjgLPWeAsQixIfl0vo-YVTFuGxod4,3131
screeninfo/enumerators/xinerama.py,sha256=lbTA0bbyttQJUoZMaQRxumPSjq5Z6I5BPUK_mju4N7c,1581
screeninfo/enumerators/xrandr.py,sha256=JlSHxfh4QBkipWvqSfdQWZYdAhFLSnWIramqLX6WFKQ,4353
screeninfo/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
screeninfo/screeninfo.py,sha256=V_x7EGFcoApAJgHA5KflJ51Vur3PtP4pc9JfBwtsIaU,935
screeninfo/util.py,sha256=YHXJ1P6vYklEZkVzZknwi7UF9G0intY5-PHZIlJYosY,281
