<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>WeMa IA - Test Simple</title>
    <style>
        body {
            margin: 0;
            padding: 50px;
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px;
            background: #34495e;
            border-radius: 10px;
        }
        h1 {
            color: #3498db;
            font-size: 3em;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.5em;
            margin: 20px 0;
        }
        .success {
            color: #2ecc71;
            font-weight: bold;
        }
        .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 WeMa IA</h1>
        <p class="success">✅ TAURI FONCTIONNE !</p>
        <p>Interface Tauri chargée avec succès</p>
        <button class="button" onclick="testFunction()">Test JavaScript</button>
        <p id="result"></p>
    </div>

    <script>
        function testFunction() {
            document.getElementById('result').innerHTML = '🚀 JavaScript fonctionne !';
            console.log('Test réussi !');
        }
        
        // Test au chargement
        console.log('WeMa IA - Interface Tauri chargée');
        
        // Test si Tauri est disponible
        if (window.__TAURI__) {
            console.log('✅ Tauri API disponible');
        } else {
            console.log('❌ Tauri API non disponible');
        }
    </script>
</body>
</html>
