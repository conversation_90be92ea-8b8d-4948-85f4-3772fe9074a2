pywin32_ctypes-0.2.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pywin32_ctypes-0.2.2.dist-info/METADATA,sha256=wMbQqpWCsiQCUEm4xIV8JF27EwquoDoFo7hS8AXDqSc,3906
pywin32_ctypes-0.2.2.dist-info/RECORD,,
pywin32_ctypes-0.2.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pywin32_ctypes-0.2.2.dist-info/WHEEL,sha256=yQN5g4mg4AybRjkgi-9yy4iQEFibGQmlz78Pik5Or-A,92
pywin32_ctypes-0.2.2.dist-info/direct_url.json,sha256=VuFIS6C_1NW3tblbXmdfzFvNsZuqgPCZ3mlKsgvt5KM,94
pywin32_ctypes-0.2.2.dist-info/top_level.txt,sha256=Q67ar0C8ghsHWr96rJ8iA0mLCxbYQLxeS5fHmMODw0k,12
win32ctypes/__init__.py,sha256=dEUghCWvAOWd4Y6v7wLkOTfECjY-qtPa1MI9evDm1_A,206
win32ctypes/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/__pycache__/pywintypes.cpython-312.pyc,,
win32ctypes/__pycache__/version.cpython-312.pyc,,
win32ctypes/__pycache__/win32api.cpython-312.pyc,,
win32ctypes/__pycache__/win32cred.cpython-312.pyc,,
win32ctypes/core/__init__.py,sha256=k004Oc3d8oVW8ra6TOFb7BzSDkmppwsIGI_DpyKBSTY,1564
win32ctypes/core/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/core/__pycache__/_winerrors.cpython-312.pyc,,
win32ctypes/core/__pycache__/compat.cpython-312.pyc,,
win32ctypes/core/_winerrors.py,sha256=_aNqnn6FF5gO9r6d8kGHuei1QjV7my8BN2ych42zR8Q,190
win32ctypes/core/cffi/__init__.py,sha256=CLH-_pZf6bNsJ3nT-mEoWKz2CfxdxaUcOpCaOKZR7nI,259
win32ctypes/core/cffi/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_authentication.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_common.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_dll.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_nl_support.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_resource.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_system_information.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_time.cpython-312.pyc,,
win32ctypes/core/cffi/__pycache__/_util.cpython-312.pyc,,
win32ctypes/core/cffi/_authentication.py,sha256=jPTpaaLLwDzgYGdAylcEnlxFPFbm_nPe9GVaASmUULg,5161
win32ctypes/core/cffi/_common.py,sha256=-ev6sd_QBAnQ3zNzecGcVcGfxBTWHxpanM_8sizvTTA,547
win32ctypes/core/cffi/_dll.py,sha256=V8aVpzzBIKObQr64Ux8ZmTe9ecM-76i3s5bt86rG_gk,741
win32ctypes/core/cffi/_nl_support.py,sha256=oWikBtqf2CX-6g5e7krnQzvyhpOORdYHzaVIqXx7cD8,295
win32ctypes/core/cffi/_resource.py,sha256=PrNiKS_JlfB3hfRBrpCIHjf9H1_3wr5HByjjdTydq4U,4423
win32ctypes/core/cffi/_system_information.py,sha256=2_OQ4QQ7kJ_kxT2qwRoQHKcwgNkDgBS8s-t8XQQhyfo,840
win32ctypes/core/cffi/_time.py,sha256=rgDpdO1gWHchn4m8s8DBRii44HU0LnGqRUxS-I-lrwA,314
win32ctypes/core/cffi/_util.py,sha256=maE-Dn11dn_Lclv0LGu8ontiF80YhCdjmi84CK7LtF0,2557
win32ctypes/core/compat.py,sha256=jYPl3XxvsDK_ihgFB1czpNg8hU3IJ9WvCK8UKMyZ4Lo,148
win32ctypes/core/ctypes/__init__.py,sha256=vUjGZkPxwnbygeWuWFZ598t77LiOpImKSQqGJZVRgL4,261
win32ctypes/core/ctypes/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_authentication.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_common.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_dll.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_nl_support.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_resource.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_system_information.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_time.cpython-312.pyc,,
win32ctypes/core/ctypes/__pycache__/_util.cpython-312.pyc,,
win32ctypes/core/ctypes/_authentication.py,sha256=VBwAU1dOr_9Ha5arSJApwRH2h_jd2GRN8P0Ui70fev0,3700
win32ctypes/core/ctypes/_common.py,sha256=vW-IpHBsYDHoGplO5hV7D7gVEeSO-Iundd-vhNAsJKI,1170
win32ctypes/core/ctypes/_dll.py,sha256=d72jJhkNlHKWqbFi2WzC_AgStm9AbNLK-UNZANew-_Y,531
win32ctypes/core/ctypes/_nl_support.py,sha256=MplazTMWxz01lMqD4oc1XUtXqacC8vd3s8tPjcTPQJo,303
win32ctypes/core/ctypes/_resource.py,sha256=JjQ3umik0D0ezNkwZZEguPwPCc2A4RGp5yRXCvlVHBM,4116
win32ctypes/core/ctypes/_system_information.py,sha256=QjgZiYCJXUeonEqAwjzrzpqIuvud6EQ0CjUcFZX1k3g,905
win32ctypes/core/ctypes/_time.py,sha256=jYuamnVddfRuBg2IU6YYbMuccxrH6bIwlPWu4RA-4QY,327
win32ctypes/core/ctypes/_util.py,sha256=ZXQ7BQqVMl51_WnLV6bJDYo5k1scDDnfsmH304CvfcY,1952
win32ctypes/pywin32/__init__.py,sha256=2XSKbKNMNsq6v0Lib5VV_6_K7h1UZDgrNvVN3NYm2Uw,342
win32ctypes/pywin32/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/pywin32/__pycache__/pywintypes.cpython-312.pyc,,
win32ctypes/pywin32/__pycache__/win32api.cpython-312.pyc,,
win32ctypes/pywin32/__pycache__/win32cred.cpython-312.pyc,,
win32ctypes/pywin32/pywintypes.py,sha256=QLrIu5WYjyt52N8bFNJwokDKuKX2t_f4jMAiO6oxjnM,967
win32ctypes/pywin32/win32api.py,sha256=lHCJ2r-2xYXoG0vL0Qx-TPGhYv2-zENm3jhPAqhnqb8,7430
win32ctypes/pywin32/win32cred.py,sha256=EmHhp4uPw1k1Xy-syuRnPgjcumJ9p8OW9sdm8ZMQA_0,4656
win32ctypes/pywintypes.py,sha256=4HzYV_1Zk3BRyrrWLPC_qBuhUYD5Y43JccWIAqlRNIc,337
win32ctypes/tests/__init__.py,sha256=2K34dv1eGoGZAy2jFS4uJyoF9bAp3gsv995-Qp2kIVE,672
win32ctypes/tests/__pycache__/__init__.cpython-312.pyc,,
win32ctypes/tests/__pycache__/test_backends.cpython-312.pyc,,
win32ctypes/tests/__pycache__/test_win32api.cpython-312.pyc,,
win32ctypes/tests/__pycache__/test_win32cred.cpython-312.pyc,,
win32ctypes/tests/test_backends.py,sha256=HZlISYYs9RydLuKCQv1nVvlau1U_1Vwgfgt1wBHJG8c,1017
win32ctypes/tests/test_win32api.py,sha256=hdU2UNRwNl6dzSqO16ZYYL5XHfEqAJVryC5GYKaFlW4,11383
win32ctypes/tests/test_win32cred.py,sha256=-9JteaGgg3Ov5iXNDR8vz7OxMxZG_qP3f0_2XCNuYq8,7718
win32ctypes/version.py,sha256=anBcPGggCP0f-o9w0IsZYV49rl6HvJbHcuDN39x3mxw,23
win32ctypes/win32api.py,sha256=7Rfsv91T5CSkjy5WJClnNzbnMOl7D6zfVnWJqg-CHoA,333
win32ctypes/win32cred.py,sha256=jn0q_5g0vZRXHiPetnrtkqOJE_agor26NiZUyWxdHrw,335
