{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"fastrand\", \"futures-io\", \"parking\", \"race\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"fastrand\", \"futures-io\", \"memchr\", \"parking\", \"race\", \"std\"]", "target": 4894038637245960899, "profile": 367816849085071872, "path": 16766330231573689319, "deps": [[5103565458935487, "futures_io", false, 11584626382814736522], [189982446159473706, "parking", false, 6556095692504101631], [1906322745568073236, "pin_project_lite", false, 13981041783533823058], [7620660491849607393, "futures_core", false, 12919646655898606666], [12285238697122577036, "fastrand", false, 10021032609934505713]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-lite-bda6759a0fd4952f\\dep-lib-futures_lite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}