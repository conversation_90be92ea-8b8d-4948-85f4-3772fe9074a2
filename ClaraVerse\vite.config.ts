import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import fs from 'fs-extra';
import type { PluginOption } from 'vite';

// Function to copy the PDF.js worker to the public directory
function copyPdfWorker(): PluginOption {
  return {
    name: 'copy-pdf-worker',
    buildStart() {
      try {
        const workerSrc = path.resolve(
          __dirname,
          'node_modules/pdfjs-dist/build/pdf.worker.min.js'
        );
        const workerDest = path.resolve(
          __dirname,
          'public/pdf.worker.min.js'
        );
        
        // Skip if file already exists and source exists
        if (fs.existsSync(workerSrc)) {
          console.log('Copying PDF.js worker to public directory');
          fs.copySync(workerSrc, workerDest);
        } else {
          console.warn('PDF.js worker source file not found:', workerSrc);
        }
        return Promise.resolve();
      } catch (err) {
        console.error('Error copying PDF.js worker:', err);
        return Promise.resolve();
      }
    }
  };
}

// Plugin to add WebContainer headers for production
function webContainerHeaders(): PluginOption {
  return {
    name: 'webcontainer-headers',
    generateBundle() {
      // Create _headers file for Netlify
      const netlifyHeaders = `/*
  Cross-Origin-Embedder-Policy: credentialless
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: cross-origin`;
      
      // Create vercel.json for Vercel
      const vercelConfig = {
        headers: [
          {
            source: "/(.*)",
            headers: [
              {
                key: "Cross-Origin-Embedder-Policy",
                value: "credentialless"
              },
              {
                key: "Cross-Origin-Opener-Policy", 
                value: "same-origin"
              },
              {
                key: "Cross-Origin-Resource-Policy",
                value: "cross-origin"
              }
            ]
          }
        ]
      };

      this.emitFile({
        type: 'asset',
        fileName: '_headers',
        source: netlifyHeaders
      });

      this.emitFile({
        type: 'asset',
        fileName: 'vercel.json',
        source: JSON.stringify(vercelConfig, null, 2)
      });
    }
  };
}

// Plugin pour copier les logos WeMa IA
function copyWemaLogos(): PluginOption {
  return {
    name: 'copy-wema-logos',
    async generateBundle() {
      // Copier les logos WeMa IA depuis public vers dist
      const fs = await import('fs');
      const path = await import('path');

      const publicDir = path.resolve(process.cwd(), 'public');
      const logos = [
        'wema-logo-light.svg',
        'wema-logo-dark.svg',
        'wema-logo-light.png',
        'wema-logo-dark.png'
      ];

      logos.forEach(logo => {
        const logoPath = path.join(publicDir, logo);
        if (fs.existsSync(logoPath)) {
          const content = fs.readFileSync(logoPath);
          this.emitFile({
            type: 'asset',
            fileName: logo,
            source: content
          });
          console.log(`✅ Logo copié: ${logo}`);
        } else {
          console.log(`⚠️ Logo non trouvé: ${logoPath}`);
        }
      });
    }
  };
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), copyPdfWorker(), copyWemaLogos(), webContainerHeaders()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  base: './', // FORCER CHEMINS RELATIFS POUR TAURI
  server: {
    headers: {
      'Cross-Origin-Embedder-Policy': 'credentialless',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'cross-origin',
    },
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      external: [
        // Exclure les modules lourds optionnels
        /^monaco-editor/,
        /^@monaco-editor/,
        /^mermaid/,
        /^katex/,
        /^cytoscape/
      ],
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          pdfjs: ['pdfjs-dist'],
          utils: ['axios', 'lodash-es']
        },
      },
    },
    chunkSizeWarningLimit: 1000,
    minify: 'esbuild'
  },
  preview: {
    headers: {
      'Cross-Origin-Embedder-Policy': 'credentialless',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'cross-origin',
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});
