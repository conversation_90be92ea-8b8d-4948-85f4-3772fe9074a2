Metadata-Version: 2.1
Name: pyproject_hooks
Version: 1.2.0
Summary: Wrappers to call pyproject.toml-based build backend hooks.
Author-email: <PERSON> <<EMAIL>>
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Project-URL: Changelog, https://pyproject-hooks.readthedocs.io/en/latest/changelog.html
Project-URL: Documentation, https://pyproject-hooks.readthedocs.io/
Project-URL: Source, https://github.com/pypa/pyproject-hooks

``pyproject-hooks``
===================

This is a low-level library for calling build-backends in ``pyproject.toml``-based project. It provides the basic functionality to help write tooling that generates distribution files from Python projects.

If you want a tool that builds Python packages, you'll want to use https://github.com/pypa/build instead. This is an underlying piece for `pip`, `build` and other "build frontends" use to call "build backends" within them.

You can read more in the `documentation <https://pyproject-hooks.readthedocs.io/>`_.

  Note: The ``pep517`` project has been replaced by this project (low level) and the ``build`` project (high level).

