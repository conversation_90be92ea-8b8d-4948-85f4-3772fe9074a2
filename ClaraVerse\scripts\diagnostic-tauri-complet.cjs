const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC COMPLET - ÉCRAN BLANC TAURI');
console.log('==========================================');

// 1. VÉRIFICATION DU BUILD REACT
console.log('\n📦 1. VÉRIFICATION DU BUILD REACT');
const distPath = 'dist';
if (fs.existsSync(distPath)) {
    console.log('✅ Dossier dist existe');
    
    // Vérifier index.html
    const indexPath = path.join(distPath, 'index.html');
    if (fs.existsSync(indexPath)) {
        const indexContent = fs.readFileSync(indexPath, 'utf8');
        console.log('✅ index.html existe');
        
        // Analyser les liens
        const scriptMatches = indexContent.match(/src="([^"]+)"/g);
        const linkMatches = indexContent.match(/href="([^"]+)"/g);
        
        console.log('📄 Scripts trouvés:', scriptMatches?.length || 0);
        scriptMatches?.forEach(match => console.log(`  ${match}`));
        
        console.log('🔗 Links trouvés:', linkMatches?.length || 0);
        linkMatches?.forEach(match => console.log(`  ${match}`));
        
        // Vérifier que les fichiers existent
        const assetsPath = path.join(distPath, 'assets');
        if (fs.existsSync(assetsPath)) {
            const assets = fs.readdirSync(assetsPath);
            console.log(`✅ Dossier assets: ${assets.length} fichiers`);
            assets.forEach(file => console.log(`  📁 ${file}`));
        } else {
            console.log('❌ Dossier assets manquant');
        }
    } else {
        console.log('❌ index.html manquant');
    }
} else {
    console.log('❌ Dossier dist manquant');
}

// 2. VÉRIFICATION CONFIGURATION TAURI
console.log('\n⚙️ 2. CONFIGURATION TAURI');
const tauriConfigPath = 'src-tauri/tauri.conf.json';
if (fs.existsSync(tauriConfigPath)) {
    try {
        const config = JSON.parse(fs.readFileSync(tauriConfigPath, 'utf8'));
        console.log('✅ Configuration Tauri valide');
        console.log(`📦 Product: ${config.productName} v${config.version}`);
        console.log(`🆔 Identifier: ${config.identifier}`);
        console.log(`📁 Frontend Dist: ${config.build.frontendDist}`);
        
        // Vérifier la sécurité
        if (config.app.security) {
            console.log(`🔒 CSP: ${config.app.security.csp || 'null'}`);
        }
        
        // Vérifier les fenêtres
        if (config.app.windows && config.app.windows[0]) {
            const window = config.app.windows[0];
            console.log(`🪟 Fenêtre: ${window.width}x${window.height}`);
            console.log(`🎨 Décorations: ${window.decorations}`);
            console.log(`👁️ Visible: ${window.visible}`);
        }
    } catch (error) {
        console.log('❌ Configuration Tauri invalide:', error.message);
    }
} else {
    console.log('❌ Configuration Tauri manquante');
}

// 3. VÉRIFICATION VITE CONFIG
console.log('\n⚡ 3. CONFIGURATION VITE');
const viteConfigPath = 'vite.config.ts';
if (fs.existsSync(viteConfigPath)) {
    const viteContent = fs.readFileSync(viteConfigPath, 'utf8');
    console.log('✅ vite.config.ts existe');
    
    // Chercher la base
    const baseMatch = viteContent.match(/base:\s*([^,\n]+)/);
    if (baseMatch) {
        console.log(`🔗 Base path: ${baseMatch[1]}`);
    }
    
    // Chercher outDir
    const outDirMatch = viteContent.match(/outDir:\s*['"]([^'"]+)['"]/);
    if (outDirMatch) {
        console.log(`📁 Output dir: ${outDirMatch[1]}`);
    }
} else {
    console.log('❌ vite.config.ts manquant');
}

// 4. VÉRIFICATION DES LOGS TAURI
console.log('\n📋 4. RECHERCHE DE LOGS');
const possibleLogPaths = [
    path.join(process.env.APPDATA || '', 'com.wema.ia', 'logs'),
    path.join(process.env.LOCALAPPDATA || '', 'com.wema.ia', 'logs'),
    path.join(process.env.USERPROFILE || '', '.tauri', 'logs')
];

possibleLogPaths.forEach(logPath => {
    if (fs.existsSync(logPath)) {
        console.log(`✅ Logs trouvés: ${logPath}`);
        try {
            const logs = fs.readdirSync(logPath);
            logs.forEach(log => console.log(`  📄 ${log}`));
        } catch (error) {
            console.log(`❌ Erreur lecture logs: ${error.message}`);
        }
    } else {
        console.log(`❌ Pas de logs: ${logPath}`);
    }
});

// 5. DIAGNOSTIC SPÉCIFIQUE ÉCRAN BLANC
console.log('\n🔍 5. DIAGNOSTIC ÉCRAN BLANC');
console.log('Causes possibles:');
console.log('1. 🔗 Chemins incorrects dans index.html');
console.log('2. 🔒 CSP trop restrictif');
console.log('3. 📁 Frontend dist mal configuré');
console.log('4. ⚡ Base path Vite incorrect');
console.log('5. 🪟 Configuration fenêtre Tauri');
console.log('6. 📦 Assets manquants ou mal liés');

// 6. SOLUTIONS RECOMMANDÉES
console.log('\n💡 6. SOLUTIONS À TESTER');
console.log('1. Vérifier que tous les assets existent');
console.log('2. Tester avec CSP complètement désactivé');
console.log('3. Utiliser chemins relatifs dans index.html');
console.log('4. Ajouter des logs de debug dans main.tsx');
console.log('5. Tester avec une page HTML simple d\'abord');

console.log('\n🎯 PROCHAINES ÉTAPES:');
console.log('1. Créer une page de test simple');
console.log('2. Modifier la configuration pour debug');
console.log('3. Ajouter des logs détaillés');
console.log('4. Tester étape par étape');
