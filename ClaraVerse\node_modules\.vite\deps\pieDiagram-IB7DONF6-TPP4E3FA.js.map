{"version": 3, "sources": ["../../mermaid/dist/chunks/mermaid.core/pieDiagram-IB7DONF6.mjs"], "sourcesContent": ["import {\n  populateCommonDb\n} from \"./chunk-4BMEZGHF.mjs\";\nimport {\n  cleanAndMerge,\n  parseFontSize\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-7B677QYD.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  log,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/pie/pieParser.ts\nimport { parse } from \"@mermaid-js/parser\";\n\n// src/diagrams/pie/pieDb.ts\nvar DEFAULT_PIE_CONFIG = defaultConfig_default.pie;\nvar DEFAULT_PIE_DB = {\n  sections: /* @__PURE__ */ new Map(),\n  showData: false,\n  config: DEFAULT_PIE_CONFIG\n};\nvar sections = DEFAULT_PIE_DB.sections;\nvar showData = DEFAULT_PIE_DB.showData;\nvar config = structuredClone(DEFAULT_PIE_CONFIG);\nvar getConfig2 = /* @__PURE__ */ __name(() => structuredClone(config), \"getConfig\");\nvar clear2 = /* @__PURE__ */ __name(() => {\n  sections = /* @__PURE__ */ new Map();\n  showData = DEFAULT_PIE_DB.showData;\n  clear();\n}, \"clear\");\nvar addSection = /* @__PURE__ */ __name(({ label, value }) => {\n  if (!sections.has(label)) {\n    sections.set(label, value);\n    log.debug(`added new section: ${label}, with value: ${value}`);\n  }\n}, \"addSection\");\nvar getSections = /* @__PURE__ */ __name(() => sections, \"getSections\");\nvar setShowData = /* @__PURE__ */ __name((toggle) => {\n  showData = toggle;\n}, \"setShowData\");\nvar getShowData = /* @__PURE__ */ __name(() => showData, \"getShowData\");\nvar db = {\n  getConfig: getConfig2,\n  clear: clear2,\n  setDiagramTitle,\n  getDiagramTitle,\n  setAccTitle,\n  getAccTitle,\n  setAccDescription,\n  getAccDescription,\n  addSection,\n  getSections,\n  setShowData,\n  getShowData\n};\n\n// src/diagrams/pie/pieParser.ts\nvar populateDb = /* @__PURE__ */ __name((ast, db2) => {\n  populateCommonDb(ast, db2);\n  db2.setShowData(ast.showData);\n  ast.sections.map(db2.addSection);\n}, \"populateDb\");\nvar parser = {\n  parse: /* @__PURE__ */ __name(async (input) => {\n    const ast = await parse(\"pie\", input);\n    log.debug(ast);\n    populateDb(ast, db);\n  }, \"parse\")\n};\n\n// src/diagrams/pie/pieStyles.ts\nvar getStyles = /* @__PURE__ */ __name((options) => `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`, \"getStyles\");\nvar pieStyles_default = getStyles;\n\n// src/diagrams/pie/pieRenderer.ts\nimport { arc, pie as d3pie, scaleOrdinal } from \"d3\";\nvar createPieArcs = /* @__PURE__ */ __name((sections2) => {\n  const pieData = [...sections2.entries()].map((element) => {\n    return {\n      label: element[0],\n      value: element[1]\n    };\n  }).sort((a, b) => {\n    return b.value - a.value;\n  });\n  const pie = d3pie().value(\n    (d3Section) => d3Section.value\n  );\n  return pie(pieData);\n}, \"createPieArcs\");\nvar draw = /* @__PURE__ */ __name((text, id, _version, diagObj) => {\n  log.debug(\"rendering pie chart\\n\" + text);\n  const db2 = diagObj.db;\n  const globalConfig = getConfig();\n  const pieConfig = cleanAndMerge(db2.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth = height;\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\");\n  group.attr(\"transform\", \"translate(\" + pieWidth / 2 + \",\" + height / 2 + \")\");\n  const { themeVariables } = globalConfig;\n  let [outerStrokeWidth] = parseFontSize(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ??= 2;\n  const textPosition = pieConfig.textPosition;\n  const radius = Math.min(pieWidth, height) / 2 - MARGIN;\n  const arcGenerator = arc().innerRadius(0).outerRadius(radius);\n  const labelArcGenerator = arc().innerRadius(radius * textPosition).outerRadius(radius * textPosition);\n  group.append(\"circle\").attr(\"cx\", 0).attr(\"cy\", 0).attr(\"r\", radius + outerStrokeWidth / 2).attr(\"class\", \"pieOuterCircle\");\n  const sections2 = db2.getSections();\n  const arcs = createPieArcs(sections2);\n  const myGeneratedColors = [\n    themeVariables.pie1,\n    themeVariables.pie2,\n    themeVariables.pie3,\n    themeVariables.pie4,\n    themeVariables.pie5,\n    themeVariables.pie6,\n    themeVariables.pie7,\n    themeVariables.pie8,\n    themeVariables.pie9,\n    themeVariables.pie10,\n    themeVariables.pie11,\n    themeVariables.pie12\n  ];\n  const color = scaleOrdinal(myGeneratedColors);\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"path\").attr(\"d\", arcGenerator).attr(\"fill\", (datum) => {\n    return color(datum.data.label);\n  }).attr(\"class\", \"pieCircle\");\n  let sum = 0;\n  sections2.forEach((section) => {\n    sum += section;\n  });\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"text\").text((datum) => {\n    return (datum.data.value / sum * 100).toFixed(0) + \"%\";\n  }).attr(\"transform\", (datum) => {\n    return \"translate(\" + labelArcGenerator.centroid(datum) + \")\";\n  }).style(\"text-anchor\", \"middle\").attr(\"class\", \"slice\");\n  group.append(\"text\").text(db2.getDiagramTitle()).attr(\"x\", 0).attr(\"y\", -(height - 50) / 2).attr(\"class\", \"pieTitleText\");\n  const legend = group.selectAll(\".legend\").data(color.domain()).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (_datum, index) => {\n    const height2 = LEGEND_RECT_SIZE + LEGEND_SPACING;\n    const offset = height2 * color.domain().length / 2;\n    const horizontal = 12 * LEGEND_RECT_SIZE;\n    const vertical = index * height2 - offset;\n    return \"translate(\" + horizontal + \",\" + vertical + \")\";\n  });\n  legend.append(\"rect\").attr(\"width\", LEGEND_RECT_SIZE).attr(\"height\", LEGEND_RECT_SIZE).style(\"fill\", color).style(\"stroke\", color);\n  legend.data(arcs).append(\"text\").attr(\"x\", LEGEND_RECT_SIZE + LEGEND_SPACING).attr(\"y\", LEGEND_RECT_SIZE - LEGEND_SPACING).text((datum) => {\n    const { label, value } = datum.data;\n    if (db2.getShowData()) {\n      return `${label} [${value}]`;\n    }\n    return label;\n  });\n  const longestTextWidth = Math.max(\n    ...legend.selectAll(\"text\").nodes().map((node) => node?.getBoundingClientRect().width ?? 0)\n  );\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n  svg.attr(\"viewBox\", `0 0 ${totalWidth} ${height}`);\n  configureSvgSize(svg, height, totalWidth, pieConfig.useMaxWidth);\n}, \"draw\");\nvar renderer = { draw };\n\n// src/diagrams/pie/pieDiagram.ts\nvar diagram = {\n  parser,\n  db,\n  renderer,\n  styles: pieStyles_default\n};\nexport {\n  diagram\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,IAAI,qBAAqB,sBAAsB;AAC/C,IAAI,iBAAiB;AAAA,EACnB,UAA0B,oBAAI,IAAI;AAAA,EAClC,UAAU;AAAA,EACV,QAAQ;AACV;AACA,IAAI,WAAW,eAAe;AAC9B,IAAI,WAAW,eAAe;AAC9B,IAAI,SAAS,gBAAgB,kBAAkB;AAC/C,IAAIA,cAA6B,OAAO,MAAM,gBAAgB,MAAM,GAAG,WAAW;AAClF,IAAI,SAAyB,OAAO,MAAM;AACxC,aAA2B,oBAAI,IAAI;AACnC,aAAW,eAAe;AAC1B,QAAM;AACR,GAAG,OAAO;AACV,IAAI,aAA6B,OAAO,CAAC,EAAE,OAAO,MAAM,MAAM;AAC5D,MAAI,CAAC,SAAS,IAAI,KAAK,GAAG;AACxB,aAAS,IAAI,OAAO,KAAK;AACzB,QAAI,MAAM,sBAAsB,KAAK,iBAAiB,KAAK,EAAE;AAAA,EAC/D;AACF,GAAG,YAAY;AACf,IAAI,cAA8B,OAAO,MAAM,UAAU,aAAa;AACtE,IAAI,cAA8B,OAAO,CAAC,WAAW;AACnD,aAAW;AACb,GAAG,aAAa;AAChB,IAAI,cAA8B,OAAO,MAAM,UAAU,aAAa;AACtE,IAAI,KAAK;AAAA,EACP,WAAWA;AAAA,EACX,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,aAA6B,OAAO,CAAC,KAAK,QAAQ;AACpD,mBAAiB,KAAK,GAAG;AACzB,MAAI,YAAY,IAAI,QAAQ;AAC5B,MAAI,SAAS,IAAI,IAAI,UAAU;AACjC,GAAG,YAAY;AACf,IAAI,SAAS;AAAA,EACX,OAAuB,OAAO,OAAO,UAAU;AAC7C,UAAM,MAAM,MAAM,MAAM,OAAO,KAAK;AACpC,QAAI,MAAM,GAAG;AACb,eAAW,KAAK,EAAE;AAAA,EACpB,GAAG,OAAO;AACZ;AAGA,IAAI,YAA4B,OAAO,CAAC,YAAY;AAAA;AAAA,cAEtC,QAAQ,cAAc;AAAA,qBACf,QAAQ,cAAc;AAAA,gBAC3B,QAAQ,UAAU;AAAA;AAAA;AAAA,cAGpB,QAAQ,mBAAmB;AAAA,oBACrB,QAAQ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA,iBAK9B,QAAQ,gBAAgB;AAAA,YAC7B,QAAQ,iBAAiB;AAAA,mBAClB,QAAQ,UAAU;AAAA;AAAA;AAAA,mBAGlB,QAAQ,UAAU;AAAA,YACzB,QAAQ,mBAAmB;AAAA,gBACvB,QAAQ,kBAAkB;AAAA;AAAA;AAAA;AAAA,YAI9B,QAAQ,kBAAkB;AAAA,mBACnB,QAAQ,UAAU;AAAA,iBACpB,QAAQ,iBAAiB;AAAA;AAAA,GAEvC,WAAW;AACd,IAAI,oBAAoB;AAIxB,IAAI,gBAAgC,OAAO,CAAC,cAAc;AACxD,QAAM,UAAU,CAAC,GAAG,UAAU,QAAQ,CAAC,EAAE,IAAI,CAAC,YAAY;AACxD,WAAO;AAAA,MACL,OAAO,QAAQ,CAAC;AAAA,MAChB,OAAO,QAAQ,CAAC;AAAA,IAClB;AAAA,EACF,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM;AAChB,WAAO,EAAE,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,QAAM,MAAM,YAAM,EAAE;AAAA,IAClB,CAAC,cAAc,UAAU;AAAA,EAC3B;AACA,SAAO,IAAI,OAAO;AACpB,GAAG,eAAe;AAClB,IAAI,OAAuB,OAAO,CAAC,MAAM,IAAI,UAAU,YAAY;AACjE,MAAI,MAAM,0BAA0B,IAAI;AACxC,QAAM,MAAM,QAAQ;AACpB,QAAM,eAAe,WAAU;AAC/B,QAAM,YAAY,cAAc,IAAI,UAAU,GAAG,aAAa,GAAG;AACjE,QAAM,SAAS;AACf,QAAM,mBAAmB;AACzB,QAAM,iBAAiB;AACvB,QAAM,SAAS;AACf,QAAM,WAAW;AACjB,QAAM,MAAM,iBAAiB,EAAE;AAC/B,QAAM,QAAQ,IAAI,OAAO,GAAG;AAC5B,QAAM,KAAK,aAAa,eAAe,WAAW,IAAI,MAAM,SAAS,IAAI,GAAG;AAC5E,QAAM,EAAE,eAAe,IAAI;AAC3B,MAAI,CAAC,gBAAgB,IAAI,cAAc,eAAe,mBAAmB;AACzE,0CAAqB;AACrB,QAAM,eAAe,UAAU;AAC/B,QAAM,SAAS,KAAK,IAAI,UAAU,MAAM,IAAI,IAAI;AAChD,QAAM,eAAe,YAAI,EAAE,YAAY,CAAC,EAAE,YAAY,MAAM;AAC5D,QAAM,oBAAoB,YAAI,EAAE,YAAY,SAAS,YAAY,EAAE,YAAY,SAAS,YAAY;AACpG,QAAM,OAAO,QAAQ,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,KAAK,KAAK,SAAS,mBAAmB,CAAC,EAAE,KAAK,SAAS,gBAAgB;AAC1H,QAAM,YAAY,IAAI,YAAY;AAClC,QAAM,OAAO,cAAc,SAAS;AACpC,QAAM,oBAAoB;AAAA,IACxB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,IACf,eAAe;AAAA,EACjB;AACA,QAAM,QAAQ,QAAa,iBAAiB;AAC5C,QAAM,UAAU,UAAU,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,QAAQ,CAAC,UAAU;AAC5G,WAAO,MAAM,MAAM,KAAK,KAAK;AAAA,EAC/B,CAAC,EAAE,KAAK,SAAS,WAAW;AAC5B,MAAI,MAAM;AACV,YAAU,QAAQ,CAAC,YAAY;AAC7B,WAAO;AAAA,EACT,CAAC;AACD,QAAM,UAAU,UAAU,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,MAAM,EAAE,KAAK,CAAC,UAAU;AAC5E,YAAQ,MAAM,KAAK,QAAQ,MAAM,KAAK,QAAQ,CAAC,IAAI;AAAA,EACrD,CAAC,EAAE,KAAK,aAAa,CAAC,UAAU;AAC9B,WAAO,eAAe,kBAAkB,SAAS,KAAK,IAAI;AAAA,EAC5D,CAAC,EAAE,MAAM,eAAe,QAAQ,EAAE,KAAK,SAAS,OAAO;AACvD,QAAM,OAAO,MAAM,EAAE,KAAK,IAAI,gBAAgB,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,KAAK,SAAS,cAAc;AACxH,QAAM,SAAS,MAAM,UAAU,SAAS,EAAE,KAAK,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,SAAS,QAAQ,EAAE,KAAK,aAAa,CAAC,QAAQ,UAAU;AAC9I,UAAM,UAAU,mBAAmB;AACnC,UAAM,SAAS,UAAU,MAAM,OAAO,EAAE,SAAS;AACjD,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,QAAQ,UAAU;AACnC,WAAO,eAAe,aAAa,MAAM,WAAW;AAAA,EACtD,CAAC;AACD,SAAO,OAAO,MAAM,EAAE,KAAK,SAAS,gBAAgB,EAAE,KAAK,UAAU,gBAAgB,EAAE,MAAM,QAAQ,KAAK,EAAE,MAAM,UAAU,KAAK;AACjI,SAAO,KAAK,IAAI,EAAE,OAAO,MAAM,EAAE,KAAK,KAAK,mBAAmB,cAAc,EAAE,KAAK,KAAK,mBAAmB,cAAc,EAAE,KAAK,CAAC,UAAU;AACzI,UAAM,EAAE,OAAO,MAAM,IAAI,MAAM;AAC/B,QAAI,IAAI,YAAY,GAAG;AACrB,aAAO,GAAG,KAAK,KAAK,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,mBAAmB,KAAK;AAAA,IAC5B,GAAG,OAAO,UAAU,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,UAAS,6BAAM,wBAAwB,UAAS,CAAC;AAAA,EAC5F;AACA,QAAM,aAAa,WAAW,SAAS,mBAAmB,iBAAiB;AAC3E,MAAI,KAAK,WAAW,OAAO,UAAU,IAAI,MAAM,EAAE;AACjD,mBAAiB,KAAK,QAAQ,YAAY,UAAU,WAAW;AACjE,GAAG,MAAM;AACT,IAAI,WAAW,EAAE,KAAK;AAGtB,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AACV;", "names": ["getConfig2"]}