{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 367816849085071872, "path": 6151539756729700035, "deps": [[2828590642173593838, "cfg_if", false, 9798098219009484732]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\encoding_rs-7a06daba247ef984\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}