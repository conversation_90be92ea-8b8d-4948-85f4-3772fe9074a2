#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Clara Backend - Version Propre (RAG Premium uniquement)
Suppression complète de ChromaDB et DocumentAI legacy
"""

import os
import sys
import json
import logging
import signal
import sqlite3
import subprocess
import traceback
import time
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Request
from fastapi.responses import JSONResponse, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configuration - Serveur mode
HOST = os.getenv("CLARA_HOST", "0.0.0.0")  # 0.0.0.0 pour accepter toutes les connexions
PORT = int(os.getenv("CLARA_PORT", "5001"))  # Port par défaut pour WeMa IA
START_TIME = datetime.now().isoformat()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("clara-backend")

# 🚀 Import Langfuse pour le monitoring
try:
    from langfuse_service import langfuse_service
    from langfuse.decorators import observe, langfuse_context
    LANGFUSE_AVAILABLE = True
    logger.info("✅ Langfuse observability service available")
except ImportError as e:
    LANGFUSE_AVAILABLE = False
    logger.warning(f"⚠️ Langfuse not available: {e}")

# 🚀 DÉMARRAGE RAPIDE : Import RAG Premium Service seulement quand nécessaire
RAG_UNIFIED_AVAILABLE = False
_rag_premium_service = None
_rag_import_attempted = False

async def get_rag_premium_service():
    """Obtenir l'instance du service RAG premium avec chargement paresseux"""
    global _rag_premium_service, RAG_UNIFIED_AVAILABLE, _rag_import_attempted

    if _rag_premium_service is not None:
        return _rag_premium_service

    if not _rag_import_attempted:
        _rag_import_attempted = True
        try:
            logger.info("🔄 Loading RAG Premium Service (first use)...")
            from rag_premium_service import PremiumRAGService
            RAG_UNIFIED_AVAILABLE = True
            logger.info("✅ RAG Premium Service loaded successfully")

            _rag_premium_service = PremiumRAGService()
            await _rag_premium_service.initialize()
            logger.info("✅ RAG Premium Service initialized")
            return _rag_premium_service

        except ImportError as e:
            RAG_UNIFIED_AVAILABLE = False
            logger.warning(f"❌ RAG Premium Service not available: {e}")
            raise RuntimeError("RAG Premium Service not available")
    else:
        raise RuntimeError("RAG Premium Service not available")

# FastAPI app
app = FastAPI(title="Clara Backend - Clean", version="2.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 🗜️ Routes de compression intégrées
from pydantic import BaseModel
from typing import Optional, Dict
import time

class CompressionRequest(BaseModel):
    context: str
    compression_ratio: Optional[float] = 0.5
    preserve_recent_messages: Optional[int] = 3

class CompressionResponse(BaseModel):
    success: bool
    compressed_context: Optional[str] = None
    original_length: Optional[int] = None
    compressed_length: Optional[int] = None
    compression_ratio: Optional[float] = None
    compression_time: Optional[int] = None
    error: Optional[str] = None

@app.post('/api/compress-context', response_model=CompressionResponse)
async def compress_context(request: CompressionRequest):
    """🗜️ Endpoint pour compresser le contexte de conversation"""
    try:
        context = request.context.strip()
        if not context:
            raise HTTPException(status_code=400, detail="Le contexte ne peut pas être vide")

        # Obtenir le compresseur
        from perfect_compressor import PerfectCompressor
        compressor = PerfectCompressor()

        # Simuler une conversation
        fake_conversation = [{"role": "assistant", "content": context}]

        # Compression
        start_time = time.time()
        compressed_messages, compression_info = compressor.compress_conversation(fake_conversation)
        end_time = time.time()

        # Extraire le contexte compressé nettoyé du message système
        if compressed_messages and compressed_messages[0].get('role') == 'system':
            system_content = compressed_messages[0]['content']
            # Extraire le contenu après "Contexte de conversation précédent (compressé):"
            if "Contexte de conversation précédent (compressé):" in system_content:
                compressed_context = system_content.split("Contexte de conversation précédent (compressé):")[1].strip()
            else:
                compressed_context = system_content

            # NETTOYER LES BALISES <think> (Qwen3 génère toujours ces balises)
            import re

            # Méthode 1: Couper avant <think>
            if '<think>' in compressed_context:
                compressed_context = compressed_context.split('<think>')[0].strip()

            # Méthode 2: Supprimer toutes balises XML/HTML
            compressed_context = re.sub(r'<[^>]*>', '', compressed_context)

            # Méthode 3: Supprimer lignes qui commencent par des balises
            lines = compressed_context.split('\n')
            clean_lines = []
            for line in lines:
                line = line.strip()
                if not line.startswith('<') and not line.endswith('>') and line:
                    clean_lines.append(line)

            if clean_lines:
                compressed_context = '\n'.join(clean_lines)

            compressed_context = compressed_context.strip()
        else:
            compressed_context = context
        compression_time = int((end_time - start_time) * 1000)

        original_length = len(context)
        compressed_length = len(compressed_context)
        actual_ratio = compressed_length / original_length if original_length > 0 else 1.0

        logger.info(f"✅ Compression: {original_length} → {compressed_length} chars")
        logger.debug(f"🔍 Contexte compressé (debug): {compressed_context[:200]}...")

        return CompressionResponse(
            success=True,
            compressed_context=compressed_context,
            original_length=original_length,
            compressed_length=compressed_length,
            compression_ratio=actual_ratio,
            compression_time=compression_time
        )

    except Exception as e:
        logger.error(f"❌ Erreur compression: {e}")
        return CompressionResponse(success=False, error=str(e))

@app.get('/api/compression/status')
async def compression_status():
    """📊 Statut du système de compression"""
    try:
        from perfect_compressor import PerfectCompressor
        compressor = PerfectCompressor()
        return {
            "success": True,
            "status": "ready",
            "lmstudio_url": compressor.lmstudio_url,
            "compression_model": compressor.compression_model
        }
    except Exception as e:
        logger.error(f"❌ Erreur statut: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post('/api/compression/test')
async def compression_test():
    """🧪 Test de compression avec contexte prédéfini"""
    try:
        test_context = """Voici une conversation de test pour vérifier le fonctionnement de la compression.

Utilisateur: Bonjour, comment allez-vous ?
Assistant: Bonjour ! Je vais très bien, merci de demander. Comment puis-je vous aider aujourd'hui ?

Utilisateur: Pouvez-vous m'expliquer l'intelligence artificielle ?
Assistant: Bien sûr ! L'intelligence artificielle est un domaine de l'informatique qui vise à créer des systèmes capables de réaliser des tâches qui nécessitent normalement l'intelligence humaine."""

        from perfect_compressor import PerfectCompressor
        compressor = PerfectCompressor()

        fake_conversation = [{"role": "assistant", "content": test_context}]

        start_time = time.time()
        compressed_messages, _ = compressor.compress_conversation(fake_conversation)
        end_time = time.time()

        compressed_context = compressed_messages[0]['content'] if compressed_messages else test_context

        return {
            "success": True,
            "test_result": "compression_working",
            "original_length": len(test_context),
            "compressed_length": len(compressed_context),
            "compression_time": int((end_time - start_time) * 1000),
            "compressed_preview": compressed_context[:200] + "..." if len(compressed_context) > 200 else compressed_context
        }

    except Exception as e:
        logger.error(f"❌ Erreur test compression: {e}")
        raise HTTPException(status_code=500, detail=str(e))

logger.info("✅ Routes de compression intégrées")

# Database setup
home_dir = os.path.expanduser("~")
data_dir = os.path.join(home_dir, ".clara")
os.makedirs(data_dir, exist_ok=True)
DATABASE = os.path.join(data_dir, "clara.db")

MAX_RETRIES = 3

from contextlib import contextmanager

@contextmanager
def get_db_connection(timeout=30.0):
    """Get database connection with retry logic"""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE, timeout=timeout)
        conn.row_factory = sqlite3.Row
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        yield conn
    finally:
        if conn:
            conn.close()

def init_database():
    """Initialize database tables"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # Collections table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS collections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                document_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Documents table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_type TEXT,
                collection_name TEXT,
                content TEXT,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (collection_name) REFERENCES collections (name)
            )
        """)

        # Ajouter la colonne content si elle n'existe pas (migration)
        try:
            cursor.execute("ALTER TABLE documents ADD COLUMN content TEXT")
            logger.info("✅ Colonne 'content' ajoutée à la table documents")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                logger.info("✅ Colonne 'content' existe déjà")
            else:
                logger.warning(f"⚠️ Erreur ajout colonne content: {e}")
        
        # Test table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                value TEXT
            )
        """)
        
        # Insert test data
        cursor.execute("SELECT COUNT(*) FROM test")
        count = cursor.fetchone()[0]
        if count == 0:
            cursor.execute("INSERT INTO test (value) VALUES ('Hello from SQLite')")
        
        conn.commit()
        logger.info("Database initialized successfully")

# Initialize database
init_database()

# 🚀 FONCTION POUR DÉMARRER LM STUDIO AUTOMATIQUEMENT
def start_lm_studio_server():
    """Démarre le serveur LM Studio automatiquement si disponible"""
    try:
        # Vérifier si LM Studio CLI est disponible
        result = subprocess.run(['lms', '--version'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            logger.info("✅ LM Studio CLI trouvé, démarrage du serveur...")

            # Démarrer le serveur en arrière-plan
            subprocess.Popen(['lms', 'server', 'start', '--port', '1234'],
                           stdout=subprocess.DEVNULL,
                           stderr=subprocess.DEVNULL)

            # Attendre un peu pour que le serveur démarre
            time.sleep(3)

            # Vérifier que le serveur répond
            import requests
            response = requests.get('http://localhost:1234/v1/models', timeout=5)
            if response.status_code == 200:
                logger.info("🚀 LM Studio serveur démarré avec succès sur le port 1234")
                return True
            else:
                logger.warning("⚠️ LM Studio serveur ne répond pas")
                return False

        else:
            logger.info("ℹ️ LM Studio CLI non trouvé, démarrage manuel requis")
            return False

    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        logger.info("ℹ️ LM Studio non disponible, démarrage manuel requis")
        return False
    except Exception as e:
        logger.warning(f"⚠️ Erreur lors du démarrage de LM Studio: {e}")
        return False

# Démarrer LM Studio automatiquement au démarrage
logger.info("🔧 Tentative de démarrage automatique de LM Studio...")
start_lm_studio_server()

# Pydantic models
class CollectionCreate(BaseModel):
    name: str
    description: Optional[str] = None

# ============================================================================
# ENDPOINTS PRINCIPAUX
# ============================================================================

@app.get("/")
def read_root():
    """Root endpoint for basic health check"""
    return {
        "status": "ok",
        "service": "Clara Backend Clean",
        "version": "2.0.0",
        "port": PORT,
        "uptime": str(datetime.now() - datetime.fromisoformat(START_TIME)),
        "start_time": START_TIME,
        "rag_available": RAG_UNIFIED_AVAILABLE
    }

# ============================================================================
# PROXY ENDPOINTS POUR CONTOURNER CORS
# ============================================================================

# 🚀 ENDPOINTS LM STUDIO SUPPRIMÉS - CORS DIRECT MAINTENANT UTILISÉ
# Les endpoints proxy pour LM Studio ont été supprimés car nous utilisons
# maintenant CORS directement depuis le frontend vers LM Studio.
# Cela améliore les performances et simplifie l'architecture.

@app.get("/proxy/lmstudio/models")
async def proxy_lmstudio_models():
    """Proxy pour récupérer les modèles LM Studio disponibles"""
    try:
        import requests

        # 🌐 SERVEUR LM STUDIO CENTRAL
        lmstudio_url = "http://10.4.123.77:1234/v1"
        target_url = f"{lmstudio_url}/models"

        logger.info(f"🔄 Récupération des modèles LM Studio depuis: {target_url}")

        response = requests.get(target_url, timeout=10)

        if response.status_code == 200:
            models_data = response.json()
            logger.info(f"✅ {len(models_data.get('data', []))} modèles LM Studio récupérés")
            return models_data
        else:
            logger.error(f"❌ Erreur LM Studio models: {response.status_code}")
            raise HTTPException(status_code=response.status_code, detail="LM Studio models unavailable")

    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Erreur connexion LM Studio: {e}")
        raise HTTPException(status_code=503, detail="LM Studio server unavailable")
    except Exception as e:
        logger.error(f"❌ Erreur proxy LM Studio models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/proxy/ollama/chat")
async def proxy_ollama_chat_specific(request: Request):
    """Proxy intelligent pour le chat LM Studio avec gestion des tool calls - SERVEUR: 10.4.123.77:1234"""
    openai_body = {}  # Initialiser pour éviter UnboundLocalError
    try:
        import requests
        import json
        import asyncio
        from tools.internet_search_tools import INTERNET_SEARCH_TOOLS

        # 🌐 SERVEUR LM STUDIO FIXE
        lmstudio_url = "http://10.4.123.77:1234/v1"

        # Récupérer le body de la requête avec gestion d'encodage
        body_bytes = await request.body()
        body_text = body_bytes.decode('utf-8', errors='replace')  # Gestion des erreurs d'encodage
        openai_body = json.loads(body_text)

        # 🔧 ÉTAPE 1 : Première requête à LM Studio pour obtenir la réponse initiale
        target_url = f"{lmstudio_url}/chat/completions"

        # LM Studio utilise directement le format OpenAI
        lmstudio_body = {
            "model": openai_body.get("model", "qwen3-14b-optimized"),
            "messages": openai_body.get("messages", []),
            "stream": False,  # Forcer non-streaming pour gérer les tool calls
            "temperature": openai_body.get("temperature", 0.7),
            "max_tokens": openai_body.get("max_tokens", 4000),
            "top_p": openai_body.get("top_p", 0.9)
        }

        # Ajouter les tools si présents dans la requête OpenAI
        if "tools" in openai_body and openai_body["tools"]:
            # LM Studio supporte nativement les tools au format OpenAI
            lmstudio_body["tools"] = openai_body["tools"]

        logger.info(f"🔄 Proxying chat to {target_url} with model: {lmstudio_body['model']}")
        logger.info(f"🔧 Tools provided: {len(openai_body.get('tools', []))}")

        # Faire la première requête vers le serveur LM Studio
        response = requests.post(
            target_url,
            json=lmstudio_body,
            timeout=120
        )

        response.raise_for_status()
        lmstudio_response = response.json()

        logger.info(f"✅ LM Studio response received")

        # 🔧 ÉTAPE 2 : Vérifier s'il y a des tool calls dans la réponse
        # LM Studio retourne les tool calls dans choices[0].message.tool_calls
        lmstudio_message = lmstudio_response.get("choices", [{}])[0].get("message", {})
        tool_calls = lmstudio_message.get("tool_calls", [])

        if tool_calls:
            logger.info(f"🔧 {len(tool_calls)} tool calls détectés")

            # 🎨 ÉTAPE 3 : Exécuter les tool calls et émettre les événements de progression
            tool_results = []

            for tool_call in tool_calls:
                function_name = tool_call.get("function", {}).get("name", "")
                function_args = tool_call.get("function", {}).get("arguments", {})

                if isinstance(function_args, str):
                    try:
                        function_args = json.loads(function_args)
                    except json.JSONDecodeError:
                        function_args = {}

                logger.info(f"🔧 Executing tool: {function_name}")

                # Exécuter l'outil spécifique
                if function_name == "search_internet":
                    # 🎨 ÉVÉNEMENTS DE PROGRESSION : Analyser la requête
                    logger.info("🎨 Search step: analyzing")

                    try:
                        # Utiliser l'outil de recherche internet
                        search_tool = INTERNET_SEARCH_TOOLS.get('internet_search', {}).get('function')
                        if search_tool:
                            # 🎨 ÉVÉNEMENTS DE PROGRESSION : Recherche active
                            logger.info("🎨 Search step: searching")

                            result = await search_tool(
                                query=function_args.get("query", ""),
                                search_type=function_args.get("search_type", "general"),
                                max_results=6,
                                time_range="month"
                            )

                            # 🎨 ÉVÉNEMENTS DE PROGRESSION : Extraction du contenu
                            logger.info("🎨 Search step: extracting")

                            tool_results.append({
                                "role": "tool",
                                "content": json.dumps(result) if result.get("success") else f"Error: {result.get('error', 'Unknown error')}",
                                "tool_call_id": tool_call.get("id", ""),
                                "name": function_name
                            })

                            # 🎨 ÉVÉNEMENTS DE PROGRESSION : Terminé
                            logger.info("🎨 Search step: completed")
                        else:
                            logger.error("❌ Search tool not found")
                            tool_results.append({
                                "role": "tool",
                                "content": "Error: Search tool not available",
                                "tool_call_id": tool_call.get("id", ""),
                                "name": function_name
                            })
                    except Exception as e:
                        logger.error(f"❌ Tool execution error: {e}")
                        tool_results.append({
                            "role": "tool",
                            "content": f"Error: {str(e)}",
                            "tool_call_id": tool_call.get("id", ""),
                            "name": function_name
                        })
                else:
                    # Autres outils non supportés pour l'instant
                    tool_results.append({
                        "role": "tool",
                        "content": f"Error: Tool {function_name} not supported",
                        "tool_call_id": tool_call.get("id", ""),
                        "name": function_name
                    })

            # 🔧 ÉTAPE 4 : Deuxième requête à LM Studio avec les résultats des tools
            messages_with_tools = lmstudio_body["messages"] + [
                {
                    "role": "assistant",
                    "content": lmstudio_message.get("content", ""),
                    "tool_calls": tool_calls
                }
            ] + tool_results

            # Nouvelle requête avec les résultats des tools
            final_lmstudio_body = {
                **lmstudio_body,
                "messages": messages_with_tools,
                "tools": None  # Retirer les tools pour la réponse finale
            }

            logger.info("🔄 Sending final request to LM Studio with tool results")
            final_response = requests.post(target_url, json=final_lmstudio_body, timeout=120)
            final_response.raise_for_status()
            final_lmstudio_response = final_response.json()

            # LM Studio retourne déjà au format OpenAI
            openai_response = final_lmstudio_response

            return openai_response

        else:
            # Pas de tool calls, retourner la réponse directement
            logger.info("✅ No tool calls, returning direct response")

            # LM Studio retourne déjà au format OpenAI
            openai_response = lmstudio_response

            return openai_response

    except Exception as e:
        logger.error(f"❌ Erreur proxy LM Studio: {e}")
        import time

        # Retourner une réponse d'erreur au format OpenAI
        error_response = {
            "id": f"chatcmpl-error-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": openai_body.get("model", "unknown"),
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "Je m'excuse, mais je n'ai pas pu générer de réponse. Veuillez réessayer."
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }

        return error_response


@app.api_route("/proxy/ollama/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def proxy_ollama_generic(path: str, request: Request):
    """Proxy générique pour toutes les requêtes Ollama - DÉLÉGATION AU SERVEUR CENTRAL"""
    try:
        import requests

        # 🌐 VÉRIFICATION RAPIDE DE LA DISPONIBILITÉ D'OLLAMA
        from network_config import network_config
        if not network_config.is_ollama_available():
            logger.warning(f"⚠️ Ollama non disponible - rejet rapide de {path}")
            raise HTTPException(
                status_code=503,
                detail="Ollama server not available. Please check your network connection or try again when connected to the office network."
            )

        ollama_url = network_config.get_ollama_url()

        # Construire l'URL complète
        target_url = f"{ollama_url}/{path}"

        # Récupérer le body si c'est une requête POST/PUT
        body = None
        if request.method in ["POST", "PUT"]:
            body = await request.json()

        logger.info(f"🔄 Proxying {request.method} {target_url}")

        # Faire la requête vers le serveur central avec timeout réduit
        response = requests.request(
            method=request.method,
            url=target_url,
            json=body,
            timeout=5,  # Timeout réduit pour éviter les blocages
            stream=True if body and body.get('stream') else False
        )

        # Si c'est du streaming, retourner une StreamingResponse
        if response.headers.get('content-type', '').startswith('text/plain') or (body and body.get('stream')):
            from fastapi.responses import StreamingResponse
            return StreamingResponse(
                response.iter_content(chunk_size=1024),
                media_type=response.headers.get('content-type', 'text/plain'),
                status_code=response.status_code
            )
        else:
            # Retourner la réponse JSON normale
            response.raise_for_status()
            return response.json()

    except HTTPException:
        # Re-lever les HTTPException (comme le 503 ci-dessus)
        raise
    except Exception as e:
        logger.error(f"❌ Proxy Ollama error for {path}: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Failed to proxy Ollama request: {str(e)}")


# 🚀 SUPPRIMÉ: Préchargement LM Studio maintenant fait en direct via CORS

# 🚀 ENDPOINT /proxy/providers SUPPRIMÉ
# Les providers sont maintenant gérés directement par le frontend via CORS
# et la base de données locale. Cela améliore les performances et la fiabilité.

# ============================================================================
# 🚀 SUPPRIMÉ: PROXY LM STUDIO - Maintenant utilise CORS direct
# Communication directe Frontend ↔ LM Studio pour de meilleures performances
# ============================================================================

# 🚀 ENDPOINT GÉNÉRIQUE POUR TOUS LES PROVIDERS
@app.post("/proxy/chat")
async def proxy_chat_universal(request: Request):
    """Proxy universel pour le chat - Route automatiquement vers le bon provider"""
    try:
        # Récupérer le body de la requête
        body = await request.json()

        # Déterminer le provider depuis le modèle ou les headers
        model = body.get("model", "")
        provider_type = body.get("_provider", "lmstudio")  # Par défaut LM Studio

        logger.info(f"🔄 Proxy chat universel - Model: {model}")
        logger.info(f"🔄 Proxying {provider_type} request to: http://10.4.123.77:1234/v1/chat/completions")

        # Pour LM Studio, faire un appel direct
        if provider_type == "lmstudio":
            target_url = "http://10.4.123.77:1234/v1/chat/completions"

            # Nettoyer le body pour LM Studio (enlever _provider)
            clean_body = {k: v for k, v in body.items() if k != "_provider"}

            # Headers pour LM Studio
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            # Vérifier si c'est une requête streaming
            is_streaming = clean_body.get('stream', False)

            if is_streaming:
                # 🚀 STREAMING: Utiliser httpx pour le streaming
                import httpx
                from fastapi.responses import StreamingResponse

                logger.info(f"🚀 Starting streaming request to {target_url}")

                try:
                    # 🔧 APPROCHE SIMPLIFIÉE: Utiliser requests synchrone pour le streaming
                    import requests

                    logger.info(f"🔄 Sending streaming POST to {target_url}")
                    # 🔍 Timeout adaptatif selon le contexte
                    context_size = len(str(clean_body.get('messages', [])))
                    if context_size > 10000:  # Contexte volumineux (recherche internet)
                        timeout = 120  # 2 minutes pour contexte volumineux
                    else:
                        timeout = 60   # 1 minute pour contexte normal

                    logger.info(f"🔄 Timeout adaptatif: {timeout}s (contexte: {context_size} chars)")

                    response = requests.post(
                        target_url,
                        json=clean_body,
                        headers=headers,
                        stream=True,
                        timeout=timeout
                    )

                    logger.info(f"📡 Streaming response status: {response.status_code}")

                    if response.status_code != 200:
                        logger.error(f"❌ Streaming error: {response.status_code} - {response.text}")
                        raise HTTPException(
                            status_code=response.status_code,
                            detail=f"LM Studio request failed: {response.status_code}: {response.text}"
                        )

                    def stream_generator():
                        try:
                            logger.info("🌊 Starting stream generation...")
                            chunk_count = 0
                            for chunk in response.iter_content(chunk_size=1024):
                                if chunk:
                                    chunk_count += 1
                                    yield chunk
                            logger.info(f"✅ Stream generation completed - {chunk_count} chunks")
                        except Exception as e:
                            logger.error(f"❌ Stream generation error: {str(e)}")
                        finally:
                            try:
                                response.close()
                            except:
                                pass

                    return StreamingResponse(
                        stream_generator(),
                        media_type="text/plain",
                        headers={
                            "Cache-Control": "no-cache",
                            "Connection": "keep-alive",
                            "Access-Control-Allow-Origin": "*",
                            "Access-Control-Allow-Headers": "*"
                        }
                    )
                except Exception as e:
                    logger.error(f"❌ Streaming request failed: {str(e)}")
                    raise HTTPException(status_code=500, detail=f"Streaming failed: {str(e)}")
            else:
                # 📝 NON-STREAMING: Utiliser requests classique
                import requests

                # 🔍 Timeout adaptatif selon le contexte
                context_size = len(str(clean_body.get('messages', [])))
                if context_size > 10000:  # Contexte volumineux (recherche internet)
                    timeout = 120  # 2 minutes pour contexte volumineux
                else:
                    timeout = 60   # 1 minute pour contexte normal

                logger.info(f"🔄 Timeout adaptatif: {timeout}s (contexte: {context_size} chars)")

                response = requests.post(
                    target_url,
                    json=clean_body,
                    headers=headers,
                    timeout=timeout
                )

                if response.status_code != 200:
                    logger.error(f"❌ LM Studio error: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"LM Studio request failed: {response.status_code}: {response.text}"
                    )

                return response.json()
        else:
            # Pour d'autres providers, utiliser le proxy générique
            return await proxy_generic_provider(request)

    except Exception as e:
        logger.error(f"❌ Proxy chat universel error: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Failed to proxy chat request: {str(e)}")


@app.post("/proxy/provider")
async def proxy_generic_provider(request: Request):
    """Proxy générique pour tous les autres providers (non-Ollama)"""
    try:
        import requests

        # Récupérer le body de la requête
        body = await request.json()

        # Extraire les infos du provider
        provider_info = body.pop('_provider', {})
        provider_type = provider_info.get('type', 'unknown')
        base_url = provider_info.get('baseUrl', '')
        api_key = provider_info.get('apiKey', '')
        endpoint = provider_info.get('endpoint', '')

        if not base_url or not endpoint:
            raise HTTPException(status_code=400, detail="Provider info manquante")

        # Construire l'URL complète
        target_url = f"{base_url.rstrip('/')}{endpoint}"

        # Préparer les headers
        headers = {'Content-Type': 'application/json'}
        if api_key and provider_type in ['openai', 'lmstudio']:
            headers['Authorization'] = f'Bearer {api_key}'

        logger.info(f"🔄 Proxying {provider_type} request to: {target_url}")

        # Vérifier si c'est une requête streaming
        is_streaming = body.get('stream', False)

        if is_streaming:
            # 🚀 STREAMING: Utiliser httpx pour le streaming
            import httpx
            from fastapi.responses import StreamingResponse

            logger.info(f"🚀 Starting streaming request to {target_url}")

            try:
                # 🔧 APPROCHE SIMPLIFIÉE: Utiliser requests synchrone pour le streaming
                import requests

                logger.info(f"🔄 Sending streaming POST to {target_url}")
                # 🔍 Timeout adaptatif selon le contexte
                context_size = len(str(body.get('messages', [])))
                if context_size > 10000:  # Contexte volumineux (recherche internet)
                    timeout = 120  # 2 minutes pour contexte volumineux
                else:
                    timeout = 60   # 1 minute pour contexte normal

                logger.info(f"🔄 Timeout adaptatif streaming: {timeout}s (contexte: {context_size} chars)")

                response = requests.post(
                    target_url,
                    json=body,
                    headers=headers,
                    stream=True,
                    timeout=timeout
                )

                logger.info(f"📡 Streaming response status: {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"❌ Streaming error: {response.status_code} - {response.text}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Provider request failed: {response.status_code}: {response.text}"
                    )

                def stream_generator():
                    try:
                        logger.info("🌊 Starting stream generation...")
                        chunk_count = 0
                        for chunk in response.iter_content(chunk_size=1024):
                            if chunk:
                                chunk_count += 1
                                yield chunk
                        logger.info(f"✅ Stream generation completed - {chunk_count} chunks")
                    except Exception as e:
                        logger.error(f"❌ Stream generation error: {str(e)}")
                    finally:
                        try:
                            response.close()
                        except:
                            pass

                return StreamingResponse(
                    stream_generator(),
                    media_type="text/plain",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                                "Access-Control-Allow-Origin": "*",
                                "Access-Control-Allow-Headers": "*"
                            }
                        )
            except Exception as e:
                logger.error(f"❌ Streaming request failed: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Streaming failed: {str(e)}")
        else:
            # 🔄 NON-STREAMING: Requête normale avec requests
            # 🔍 Timeout adaptatif selon le contexte
            context_size = len(str(body.get('messages', [])))
            if context_size > 10000:  # Contexte volumineux (recherche internet)
                timeout = 120  # 2 minutes pour contexte volumineux
            else:
                timeout = 60   # 1 minute pour contexte normal

            logger.info(f"🔄 Timeout adaptatif non-streaming: {timeout}s (contexte: {context_size} chars)")

            response = requests.post(
                target_url,
                json=body,
                headers=headers,
                timeout=timeout
            )

            response.raise_for_status()
            return response.json()

    except Exception as e:
        logger.error(f"❌ Proxy provider error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to proxy provider request: {str(e)}")


@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "port": PORT,
        "uptime": str(datetime.now() - datetime.fromisoformat(START_TIME)),
        "rag_available": RAG_UNIFIED_AVAILABLE
    }

@app.get("/network/status")
def network_status():
    """Statut de la configuration réseau"""
    try:
        from network_config import get_server_info, network_diagnostics

        server_info = get_server_info()
        diagnostics = network_diagnostics()

        return {
            "status": "ok",
            "current_server": server_info,
            "diagnostics": diagnostics,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"❌ Erreur network status: {e}")
        return {
            "status": "error",
            "error": str(e),
            "fallback_server": "http://10.4.123.77:11434"
        }

@app.post("/network/refresh")
def refresh_network():
    """Forcer une nouvelle détection du serveur Ollama"""
    try:
        from network_config import refresh_network

        result = refresh_network()
        return {
            "status": "refreshed",
            "server": result,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"❌ Erreur network refresh: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

# ============================================================================
# 🚀 SYSTÈME DE MISE À JOUR ADMIN
# ============================================================================

@app.get("/api/admin/features")
async def get_available_features():
    """Obtenir les fonctionnalités disponibles"""
    try:
        from admin_system.update_manager import UpdateManager
        manager = UpdateManager()
        return manager.available_features
    except Exception as e:
        logger.error(f"Erreur récupération fonctionnalités: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/admin/poles")
async def get_pole_configs():
    """Obtenir les configurations des pôles"""
    try:
        from admin_system.update_manager import UpdateManager
        manager = UpdateManager()

        # Ajouter le statut en temps réel
        configs = manager.pole_configs.copy()
        for pole, config in configs.items():
            # Vérifier le statut des IPs (simulation)
            config["status"] = "online"  # À implémenter avec ping réel
            config["ips"] = manager._get_default_ips_for_poles([pole])

        return configs
    except Exception as e:
        logger.error(f"Erreur récupération pôles: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/admin/packages")
async def get_update_packages():
    """Obtenir les packages de mise à jour"""
    try:
        from admin_system.update_manager import UpdateManager
        manager = UpdateManager()

        packages = []
        updates_dir = manager.updates_dir

        if updates_dir.exists():
            for package_dir in updates_dir.iterdir():
                if package_dir.is_dir():
                    manifest_file = package_dir / "manifest.json"
                    if manifest_file.exists():
                        with open(manifest_file, 'r', encoding='utf-8') as f:
                            manifest = json.load(f)
                            manifest["status"] = "created"  # À implémenter avec statut réel
                            packages.append(manifest)

        return sorted(packages, key=lambda x: x["created_at"], reverse=True)
    except Exception as e:
        logger.error(f"Erreur récupération packages: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/admin/build")
async def create_custom_build(request: dict):
    """Créer un build personnalisé"""
    try:
        from admin_system.update_manager import UpdateManager
        manager = UpdateManager()

        pole = request.get("pole")
        features = request.get("features", {})
        description = request.get("description", "")

        if not pole:
            raise HTTPException(status_code=400, detail="Pôle requis")

        build_path = manager.create_custom_build(pole, features)

        return {
            "success": True,
            "build_path": build_path,
            "pole": pole,
            "features": features
        }
    except Exception as e:
        logger.error(f"Erreur création build: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/admin/package")
async def create_update_package(request: dict):
    """Créer un package de mise à jour"""
    try:
        from admin_system.update_manager import UpdateManager
        manager = UpdateManager()

        poles = request.get("poles", [])
        features = request.get("features", {})
        description = request.get("description", "")

        if not poles:
            raise HTTPException(status_code=400, detail="Pôles requis")

        package_path = manager.create_update_package(
            poles=poles,
            features=features,
            description=description
        )

        return {
            "success": True,
            "package_path": package_path,
            "poles": poles,
            "features": features
        }
    except Exception as e:
        logger.error(f"Erreur création package: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/admin/deploy")
async def deploy_update_package(request: dict):
    """Déployer un package de mise à jour"""
    try:
        from admin_system.update_manager import UpdateManager
        manager = UpdateManager()

        package_version = request.get("package_version")
        target_ips = request.get("target_ips")

        if not package_version:
            raise HTTPException(status_code=400, detail="Version du package requise")

        package_path = manager.updates_dir / f"update_{package_version}"

        if not package_path.exists():
            raise HTTPException(status_code=404, detail="Package introuvable")

        # Déploiement asynchrone
        import asyncio
        asyncio.create_task(manager.deploy_update(str(package_path), target_ips))

        return {
            "success": True,
            "message": "Déploiement lancé",
            "package_version": package_version
        }
    except Exception as e:
        logger.error(f"Erreur déploiement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# 👥 SYSTÈME D'ENREGISTREMENT UTILISATEURS
# ============================================================================

# Base de données simple pour les utilisateurs (fichier JSON)
USERS_DB_FILE = "data/registered_users.json"

def load_users_db():
    """Charger la base de données des utilisateurs"""
    try:
        if os.path.exists(USERS_DB_FILE):
            with open(USERS_DB_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"Erreur chargement DB utilisateurs: {e}")
    return []

def save_users_db(users):
    """Sauvegarder la base de données des utilisateurs"""
    try:
        os.makedirs(os.path.dirname(USERS_DB_FILE), exist_ok=True)
        with open(USERS_DB_FILE, 'w', encoding='utf-8') as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Erreur sauvegarde DB utilisateurs: {e}")

@app.post("/api/admin/register-user")
async def register_user(user_info: dict):
    """Enregistrer un nouvel utilisateur"""
    try:
        # Valider les données
        required_fields = ['firstName', 'lastName', 'pole', 'pcName', 'ipAddress', 'registeredAt']
        for field in required_fields:
            if field not in user_info:
                raise HTTPException(status_code=400, detail=f"Champ requis: {field}")

        # Charger la DB existante
        users = load_users_db()

        # Ajouter le nouvel utilisateur
        user_info['status'] = 'online'  # Statut par défaut
        user_info['lastSeen'] = user_info['registeredAt']
        users.append(user_info)

        # Sauvegarder
        save_users_db(users)

        logger.info(f"Nouvel utilisateur enregistré: {user_info['firstName']} {user_info['lastName']} ({user_info['pole']})")

        return {
            "success": True,
            "message": "Utilisateur enregistré avec succès",
            "user_id": len(users) - 1
        }
    except Exception as e:
        logger.error(f"Erreur enregistrement utilisateur: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/admin/users")
async def get_registered_users():
    """Obtenir la liste des utilisateurs enregistrés"""
    try:
        users = load_users_db()

        # Mettre à jour le statut (simulation - en production, ping réel)
        for user in users:
            # Simuler le statut basé sur la dernière activité
            if 'lastSeen' in user:
                last_seen = datetime.fromisoformat(user['lastSeen'].replace('Z', '+00:00'))
                now = datetime.now(last_seen.tzinfo) if last_seen.tzinfo else datetime.now()
                minutes_since = (now - last_seen).total_seconds() / 60

                if minutes_since < 5:
                    user['status'] = 'online'
                elif minutes_since < 60:
                    user['status'] = 'offline'
                else:
                    user['status'] = 'unknown'
            else:
                user['status'] = 'unknown'

        return users
    except Exception as e:
        logger.error(f"Erreur récupération utilisateurs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/system/ip")
async def get_system_ip():
    """Obtenir l'IP locale du système"""
    try:
        import socket

        # Obtenir l'IP locale
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)

        return {
            "ip": local_ip,
            "hostname": hostname
        }
    except Exception as e:
        logger.error(f"Erreur récupération IP: {e}")
        return {
            "ip": "Unknown",
            "hostname": "Unknown"
        }

# 🔧 CORRECTION: Ajouter les endpoints manquants pour éviter les erreurs 404
@app.get("/files")
def list_files():
    """Endpoint temporaire pour éviter les erreurs 404 - retourne une liste vide"""
    return []

@app.get("/check-update")
def check_update(version: str = "1.0.0"):
    """Endpoint de vérification des mises à jour"""
    return {
        "update_available": False,
        "current_version": version,
        "latest_version": version,
        "message": "Aucune mise à jour disponible"
    }

# ============================================================================
# COLLECTIONS
# ============================================================================

@app.post("/collections")
async def create_collection(collection: CollectionCreate):
    """Create a new collection"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Check if collection exists
            cursor.execute("SELECT name FROM collections WHERE name = ?", (collection.name,))
            existing = cursor.fetchone()
            
            if existing:
                return JSONResponse(
                    status_code=409,
                    content={"detail": f"Collection '{collection.name}' already exists"}
                )
            
            # Create the collection
            cursor.execute(
                "INSERT INTO collections (name, description) VALUES (?, ?)",
                (collection.name, collection.description or "")
            )
            conn.commit()
            
            return {"message": f"Collection '{collection.name}' created successfully"}
            
    except Exception as e:
        logger.error(f"Error creating collection: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get("/collections")
def list_collections():
    """List all available document collections"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name, description, document_count, created_at FROM collections")
            collections = [dict(row) for row in cursor.fetchall()]
            return {"collections": collections}
    except Exception as e:
        logger.error(f"Error listing collections: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/collections/{collection_name}")
async def delete_collection(collection_name: str):
    """Delete a collection and all its documents"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 🚀 SUPPRIMER DU RAG PREMIUM
            if RAG_UNIFIED_AVAILABLE:
                try:
                    # Get all documents in this collection
                    cursor.execute("SELECT filename FROM documents WHERE collection_name = ?", (collection_name,))
                    filenames = [row['filename'] for row in cursor.fetchall()]
                    
                    rag_service = await get_rag_premium_service()
                    for filename in filenames:
                        await rag_service.delete_document(filename)
                    
                    logger.info(f"🗑️ Collection {collection_name} supprimée du RAG Premium")
                except Exception as e:
                    logger.warning(f"Échec suppression RAG Premium: {e}")
            
            # Delete from SQLite
            cursor.execute("DELETE FROM documents WHERE collection_name = ?", (collection_name,))
            cursor.execute("DELETE FROM collections WHERE name = ?", (collection_name,))
            conn.commit()
            
        return {"message": f"Collection {collection_name} deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# DOCUMENTS
# ============================================================================

@app.post("/documents/ocr-only")
async def ocr_only_document(
    file: UploadFile = File(...),
    use_ocr: bool = Form(True),
    ocr_language: str = Form("fra+eng"),  # 🚀 MÊME CONFIG QUE DOC MANAGER
    preserve_layout: bool = Form(True),
    temp_processing: bool = Form(True)
):
    """
    🎯 NOUVEAU : OCR uniquement sans sauvegarde en base
    Utilisé pour permettre à l'utilisateur de choisir entre temporaire/permanent
    """
    try:
        logger.info(f"🔄 OCR-only processing: {file.filename}")

        # Créer un fichier temporaire
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)

        # Sauvegarder le fichier temporairement
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Traitement OCR
        if use_ocr:
            from ocr_processor_premium import PremiumOcrProcessor
            ocr_processor = PremiumOcrProcessor()

            # 🔧 CORRECTION : process_document ne prend pas de paramètre language
            ocr_result = ocr_processor.process_document(temp_file_path)

            # Nettoyer le fichier temporaire
            shutil.rmtree(temp_dir)

            if ocr_result and ocr_result.get("text"):
                return {
                    "success": True,
                    "processed_with_ocr": True,
                    "text": ocr_result["text"],
                    "text_length": len(ocr_result["text"]),
                    "quality_score": ocr_result.get("quality_score", 0),
                    "filename": file.filename,
                    "temp_processing": True
                }
            else:
                return {
                    "success": False,
                    "error": "OCR processing failed",
                    "processed_with_ocr": False
                }
        else:
            # Lecture directe du fichier texte
            with open(temp_file_path, 'r', encoding='utf-8') as f:
                text_content = f.read()

            shutil.rmtree(temp_dir)

            return {
                "success": True,
                "processed_with_ocr": False,
                "text": text_content,
                "text_length": len(text_content),
                "filename": file.filename,
                "temp_processing": True
            }

    except Exception as e:
        logger.error(f"❌ Error in OCR-only processing: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"OCR processing failed: {str(e)}"}
        )

@app.post("/documents/save-processed")
async def save_processed_document(request: dict):
    """🚀 ENDPOINT OPTIMISÉ : Sauvegarder un document déjà traité par OCR"""
    try:
        filename = request.get('filename')
        content = request.get('content')
        file_type = request.get('file_type', 'pdf')
        collection_name = request.get('collection_name', 'default_collection')
        metadata = request.get('metadata', {})

        if not filename or not content:
            raise HTTPException(status_code=400, detail="Filename et content requis")

        # Vérifier/créer la collection
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM collections WHERE name = ?", (collection_name,))
            if not cursor.fetchone():
                cursor.execute(
                    "INSERT INTO collections (name, description) VALUES (?, ?)",
                    (collection_name, f"Auto-created for {filename}")
                )

        # Sauvegarder directement le contenu traité
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO documents (filename, file_type, collection_name, content, metadata) VALUES (?, ?, ?, ?, ?)",
                (filename, file_type, collection_name, content, json.dumps(metadata))
            )
            document_id = cursor.lastrowid

            # Mettre à jour le compteur de documents
            cursor.execute(
                "UPDATE collections SET document_count = document_count + 1 WHERE name = ?",
                (collection_name,)
            )
            conn.commit()

        logger.info(f"✅ Document pré-traité sauvegardé: {filename} (ID: {document_id})")

        return {
            "success": True,
            "message": f"Document {filename} sauvegardé avec succès",
            "document_id": document_id,
            "text_length": len(content),
            "processed_with_ocr": True,
            "reused_ocr": True
        }

    except Exception as e:
        logger.error(f"❌ Erreur sauvegarde document pré-traité: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    collection_name: str = Form("default_collection"),
    metadata: str = Form("{}"),
    use_ocr: bool = Form(True),
    ocr_language: str = Form("fra"),
    preserve_layout: bool = Form(True)
):
    """Upload a document file (PDF, CSV, or plain text) and add it to RAG Premium"""
    # Check if collection exists, create if not
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM collections WHERE name = ?", (collection_name,))
            if not cursor.fetchone():
                cursor.execute(
                    "INSERT INTO collections (name, description) VALUES (?, ?)",
                    (collection_name, f"Auto-created for {file.filename}")
                )
                conn.commit()
    except Exception as e:
        logger.error(f"Error checking/creating collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    # Create a temporary directory to save the uploaded file
    with tempfile.TemporaryDirectory() as temp_dir:
        file_path = Path(temp_dir) / file.filename

        # Save uploaded file
        try:
            with open(file_path, "wb") as f:
                shutil.copyfileobj(file.file, f)
        except Exception as e:
            logger.error(f"Error saving file: {e}")
            raise HTTPException(status_code=500, detail=f"Could not save file: {str(e)}")

        # Process the file based on extension
        file_extension = file.filename.lower().split('.')[-1]
        documents = []
        file_type = file_extension

        try:
            if file_extension == 'pdf':
                if use_ocr:
                    # 🚀 UTILISER OCR RÉVOLUTIONNAIRE PREMIUM
                    from ocr_processor_premium import PremiumOcrProcessor

                    logger.info(f"🚀 Traitement OCR RÉVOLUTIONNAIRE du PDF: {file.filename}")
                    ocr_processor = PremiumOcrProcessor()
                    ocr_result = ocr_processor.process_document(str(file_path))

                    if ocr_result["success"]:
                        # 🚀 CRÉER DOCUMENT AVEC TEXTE OCR RÉVOLUTIONNAIRE
                        from langchain_core.documents import Document
                        documents = [Document(
                            page_content=ocr_result["text"],
                            metadata={
                                "source_file": file.filename,
                                "file_type": file_extension,
                                "processed_with_ocr": True,
                                "ocr_language": ocr_result.get("detected_language", ocr_language),
                                "total_pages": ocr_result.get("pages_count", 1),
                                "processing_time": ocr_result.get("processing_time", 0),
                                "ocr_confidence": ocr_result.get("confidence", 0),
                                "quality_score": ocr_result.get("quality_score", 0),
                                "method_used": ocr_result.get("method_used", "revolutionary"),
                                "revolutionary_ocr": True
                            }
                        )]
                        logger.info(f"🚀 OCR RÉVOLUTIONNAIRE réussi: {len(ocr_result['text'])} caractères extraits")
                        logger.info(f"   🎯 Confiance: {ocr_result.get('confidence', 0):.1f}%")
                        logger.info(f"   ⭐ Qualité: {ocr_result.get('quality_score', 0):.1f}/100")
                        logger.info(f"   🔧 Méthode: {ocr_result.get('method_used', 'unknown')}")
                    else:
                        logger.warning(f"❌ OCR révolutionnaire échoué: {ocr_result.get('error_message', 'Erreur inconnue')}")
                        # Fallback vers extraction simple
                        from langchain_community.document_loaders import PyPDFLoader
                        loader = PyPDFLoader(str(file_path))
                        documents = loader.load()
                        for doc in documents:
                            doc.metadata["ocr_attempted"] = True
                            doc.metadata["ocr_failed"] = True
                            doc.metadata["ocr_error"] = ocr_result.get("error_message", "Erreur inconnue")
                else:
                    # Extraction PDF simple
                    from langchain_community.document_loaders import PyPDFLoader
                    loader = PyPDFLoader(str(file_path))
                    documents = loader.load()
            elif file_extension == 'csv':
                from langchain_community.document_loaders import CSVLoader
                loader = CSVLoader(file_path=str(file_path))
                documents = loader.load()
            elif file_extension in ['txt', 'md', 'html']:
                from langchain_community.document_loaders import TextLoader
                # Forcer l'encodage UTF-8 pour éviter les problèmes d'encodage
                loader = TextLoader(str(file_path), encoding='utf-8')
                documents = loader.load()
            elif file_extension in ['doc', 'docx']:
                # 🚀 SUPPORT MICROSOFT WORD
                try:
                    from langchain_community.document_loaders import Docx2txtLoader
                    loader = Docx2txtLoader(str(file_path))
                    documents = loader.load()
                    logger.info(f"📄 Document Word traité: {file.filename}")
                except ImportError:
                    raise HTTPException(status_code=500, detail="Support Word non disponible - pip install docx2txt")
                except Exception as e:
                    logger.error(f"Erreur traitement Word: {e}")
                    raise HTTPException(status_code=500, detail=f"Erreur traitement Word: {str(e)}")
            elif file_extension in ['ppt', 'pptx']:
                # 🚀 SUPPORT MICROSOFT POWERPOINT
                try:
                    from langchain_community.document_loaders import UnstructuredPowerPointLoader
                    loader = UnstructuredPowerPointLoader(str(file_path))
                    documents = loader.load()
                    logger.info(f"📊 Présentation PowerPoint traitée: {file.filename}")
                except ImportError:
                    raise HTTPException(status_code=500, detail="Support PowerPoint non disponible - pip install unstructured[pptx]")
                except Exception as e:
                    logger.error(f"Erreur traitement PowerPoint: {e}")
                    raise HTTPException(status_code=500, detail=f"Erreur traitement PowerPoint: {str(e)}")
            elif file_extension in ['xls', 'xlsx']:
                # 🚀 SUPPORT MICROSOFT EXCEL
                try:
                    from langchain_community.document_loaders import UnstructuredExcelLoader
                    loader = UnstructuredExcelLoader(str(file_path))
                    documents = loader.load()
                    logger.info(f"📈 Fichier Excel traité: {file.filename}")
                except ImportError:
                    raise HTTPException(status_code=500, detail="Support Excel non disponible - pip install unstructured[xlsx]")
                except Exception as e:
                    logger.error(f"Erreur traitement Excel: {e}")
                    raise HTTPException(status_code=500, detail=f"Erreur traitement Excel: {str(e)}")
            else:
                raise HTTPException(status_code=400, detail=f"Type de fichier non supporté: {file_extension}. Types supportés: PDF, TXT, MD, HTML, CSV, DOC, DOCX, PPT, PPTX, XLS, XLSX, PNG, JPG, JPEG")

            # Parse metadata if provided
            try:
                meta_dict = json.loads(metadata)

                # Add file metadata to each document
                for doc in documents:
                    doc.metadata.update(meta_dict)
                    doc.metadata["source_file"] = file.filename
                    doc.metadata["file_type"] = file_extension
            except json.JSONDecodeError:
                logger.warning(f"Invalid metadata JSON: {metadata}")

            # 🚀 INTÉGRATION RAG PREMIUM
            if RAG_UNIFIED_AVAILABLE:
                try:
                    rag_service = await get_rag_premium_service()

                    # Ajouter au système RAG Premium
                    for doc in documents:
                        await rag_service.add_document(
                            content=doc.page_content,
                            metadata={
                                **doc.metadata,
                                "collection_name": collection_name,
                                "document_id": None  # Sera mis à jour après insertion DB
                            }
                        )
                    logger.info(f"✅ Document ajouté au RAG Premium: {file.filename}")
                except Exception as e:
                    logger.warning(f"⚠️ Échec ajout RAG Premium: {e}")

            # Combiner tout le contenu des documents
            full_content = "\n\n".join([doc.page_content for doc in documents])

            # Update database
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO documents (filename, file_type, collection_name, content, metadata) VALUES (?, ?, ?, ?, ?)",
                    (file.filename, file_type, collection_name, full_content, metadata)
                )
                document_id = cursor.lastrowid

                # Update document count in collection
                cursor.execute(
                    "UPDATE collections SET document_count = document_count + 1 WHERE name = ?",
                    (collection_name,)
                )
                conn.commit()

            # Préparer la réponse avec informations OCR
            response_data = {
                "status": "success",
                "filename": file.filename,
                "collection": collection_name,
                "document_count": len(documents),
                "document_id": document_id,
                "processed_with_ocr": False,
                "text_length": len(full_content)
            }

            # Ajouter informations OCR si utilisé
            if documents and documents[0].metadata.get("processed_with_ocr"):
                response_data.update({
                    "processed_with_ocr": True,
                    "ocr_language": documents[0].metadata.get("ocr_language"),
                    "total_pages": documents[0].metadata.get("total_pages"),
                    "processing_time": documents[0].metadata.get("processing_time"),
                    "ocr_confidence": documents[0].metadata.get("ocr_confidence"),
                    "text_length": len(full_content)
                })

            return response_data

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@app.post("/documents/ocr-only")
async def process_ocr_only(
    file: UploadFile = File(...),
    ocr_language: str = Form("fra+eng"),
    preserve_layout: bool = Form(True),
    temp_processing: bool = Form(True)
):
    """🚀 ENDPOINT OCR OPTIMISÉ - Traitement OCR uniquement sans sauvegarde"""
    try:
        # Sauvegarder le fichier temporairement
        temp_dir = Path("uploads")
        temp_dir.mkdir(exist_ok=True)

        file_path = temp_dir / f"temp_ocr_{int(time.time())}_{file.filename}"

        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 🚀 TRAITEMENT OCR OPTIMISÉ
        from ocr_processor_premium import PremiumOcrProcessor

        logger.info(f"🚀 OCR optimisé (sans sauvegarde): {file.filename}")
        ocr_processor = PremiumOcrProcessor()
        ocr_result = ocr_processor.process_document(str(file_path))

        # Nettoyer le fichier temporaire
        try:
            file_path.unlink()
        except:
            pass

        return {
            "success": True,
            "text": ocr_result.get("text", ""),
            "confidence": ocr_result.get("confidence", 0),
            "processing_time": ocr_result.get("processing_time", 0),
            "temp_processing": True,
            "filename": file.filename
        }

    except Exception as e:
        logger.error(f"❌ Erreur OCR optimisé: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ocr/process")
async def process_ocr_revolutionary(
    file: UploadFile = File(...),
    language: str = Form("fra")
):
    """🚀 ENDPOINT OCR RÉVOLUTIONNAIRE - Test direct"""
    try:
        # Sauvegarder le fichier temporairement
        temp_dir = Path("uploads")
        temp_dir.mkdir(exist_ok=True)

        file_path = temp_dir / f"temp_ocr_{int(time.time())}_{file.filename}"

        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 🚀 TRAITEMENT OCR RÉVOLUTIONNAIRE
        from ocr_processor_premium import PremiumOcrProcessor

        logger.info(f"🚀 Test OCR révolutionnaire: {file.filename}")
        ocr_processor = PremiumOcrProcessor()
        ocr_result = ocr_processor.process_document(str(file_path))

        # Nettoyer le fichier temporaire
        try:
            file_path.unlink()
        except:
            pass

        # Retourner le résultat complet
        return {
            "status": "success",
            "filename": file.filename,
            "ocr_result": ocr_result,
            "revolutionary_processing": True
        }

    except Exception as e:
        logger.error(f"❌ Erreur OCR révolutionnaire: {e}")
        return {
            "status": "error",
            "error": str(e),
            "filename": file.filename if file else "unknown"
        }

@app.get("/documents")
async def list_documents(collection_name: Optional[str] = None):
    """List all documents, optionally filtered by collection"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT id, filename, file_type, collection_name, content, metadata, created_at
                FROM documents
            """

            params = []
            if collection_name:
                query += " WHERE collection_name = ?"
                params.append(collection_name)

            query += " ORDER BY created_at DESC"

            cursor.execute(query, params)
            documents = []

            for row in cursor.fetchall():
                doc_dict = dict(row)
                # Parse metadata JSON
                try:
                    doc_dict['metadata'] = json.loads(doc_dict['metadata']) if doc_dict['metadata'] else {}
                except:
                    doc_dict['metadata'] = {}
                documents.append(doc_dict)

            return {"documents": documents}
    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing documents: {str(e)}")

@app.get("/documents/{document_id}")
async def get_document(document_id: int):
    """Get a document by ID from SQLite"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, filename, file_type, collection_name, content,
                       created_at, metadata
                FROM documents
                WHERE id = ?
            """, (document_id,))

            row = cursor.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail=f"Document with ID {document_id} not found")

            # Convert row to dict
            document = {
                "id": row[0],
                "filename": row[1],
                "file_type": row[2],
                "collection_name": row[3],
                "content": row[4],
                "created_at": row[5],
                "metadata": json.loads(row[6]) if row[6] else {}
            }

            return document

    except Exception as e:
        logger.error(f"Error getting document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document: {str(e)}")

@app.delete("/documents/{document_id}")
async def delete_document(document_id: int):
    """Delete a document from SQLite and RAG Premium"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT collection_name, filename FROM documents WHERE id = ?", (document_id,))
            document = cursor.fetchone()

            if not document:
                raise HTTPException(status_code=404, detail=f"Document with ID {document_id} not found")

            collection_name = document['collection_name']
            filename = document['filename']

            # 🚀 SUPPRIMER DU RAG PREMIUM
            if RAG_UNIFIED_AVAILABLE:
                try:
                    rag_service = await get_rag_premium_service()
                    await rag_service.delete_document(filename)
                    logger.info(f"🗑️ Document {filename} supprimé du RAG Premium")
                except Exception as e:
                    logger.warning(f"Échec suppression RAG Premium: {e}")

            # Delete from SQLite
            cursor.execute("DELETE FROM documents WHERE id = ?", (document_id,))
            cursor.execute(
                "UPDATE collections SET document_count = document_count - 1 WHERE name = ? AND document_count > 0",
                (collection_name,)
            )
            conn.commit()

        return {
            "status": "success",
            "message": f"Document {document_id} ({filename}) deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")

# ============================================================================
# RAG PREMIUM ENDPOINTS
# ============================================================================

@app.post("/rag/search")
async def premium_rag_search(request: dict):
    """Recherche RAG Premium avec LightRAG + Qdrant + BGE-M3"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        rag_service = await get_rag_premium_service()

        # Extraire les paramètres de la requête
        query = request.get("query", "")
        selected_documents = request.get("selectedDocuments", [])
        use_cache = request.get("use_cache", True)
        use_lightrag = request.get("use_lightrag", True)
        use_vector_store = request.get("use_vector_store", True)
        limit = request.get("limit", 5)
        fast_mode = request.get("fast_mode", False)  # 🚀 Mode rapide

        # 🚀 OPTIMISATION: Log pour debugging performance
        start_time = time.time()
        logger.info(f"🔍 RAG Search: query='{query[:50]}...', fast_mode={fast_mode}, limit={limit}")

        # Effectuer la recherche premium optimisée
        result = await rag_service.search(
            query=query,
            use_cache=use_cache,
            use_lightrag=use_lightrag and not fast_mode,  # 🚀 Désactiver LightRAG en mode rapide
            use_vector_store=use_vector_store,
            limit=limit,
            fast_mode=fast_mode
        )

        # 🚀 Log performance
        processing_time = time.time() - start_time
        result["processing_time"] = round(processing_time, 3)
        logger.info(f"🚀 RAG Search completed in {processing_time:.3f}s")

        return result

    except Exception as e:
        logger.error(f"Premium RAG search failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/rag/metrics")
async def rag_metrics():
    """Obtenir les métriques de performance du RAG Premium"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        rag_service = await get_rag_premium_service()
        metrics = await rag_service.get_metrics()
        return metrics

    except Exception as e:
        logger.error(f"Failed to get RAG metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/rag/delete_document")
async def rag_delete_document(request: dict):
    """Supprimer un document du RAG Premium (appelé lors suppression conversation)"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        filename = request.get("filename")
        session_id = request.get("session_id", "unknown")

        if not filename:
            raise HTTPException(status_code=400, detail="Filename is required")

        rag_service = await get_rag_premium_service()
        success = await rag_service.delete_document(filename)

        if success:
            logger.info(f"🗑️ Document {filename} supprimé du RAG Premium (session: {session_id})")
            return {
                "status": "success",
                "message": f"Document {filename} removed from RAG Premium",
                "session_id": session_id
            }
        else:
            logger.warning(f"⚠️ Échec suppression RAG Premium: {filename}")
            return {
                "status": "partial_success",
                "message": f"Document {filename} may not have been in RAG Premium",
                "session_id": session_id
            }

    except Exception as e:
        logger.error(f"Error deleting document from RAG: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting document from RAG: {str(e)}")

@app.post("/rag/clear_all")
async def rag_clear_all(request: dict):
    """Supprimer TOUS les documents du RAG Premium (appelé lors clear all conversations)"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        confirm = request.get("confirm", False)
        clear_all_documents = request.get("clear_all_documents", False)

        if not confirm or not clear_all_documents:
            raise HTTPException(status_code=400, detail="Confirmation required for clearing all RAG data")

        # 🗑️ SUPPRESSION COMPLÈTE RAG PREMIUM
        try:
            # Supprimer toute la collection Qdrant
            rag_service = await get_rag_premium_service()
            if rag_service.qdrant_client:
                collection_name = rag_service.config["qdrant"]["collection_name"]
                rag_service.qdrant_client.delete_collection(collection_name)
                logger.info(f"🗑️ Collection Qdrant {collection_name} supprimée complètement")

                # Recréer la collection vide
                await rag_service._create_collection()
                logger.info(f"✅ Collection Qdrant {collection_name} recréée vide")

            # TODO: Supprimer LightRAG storage si nécessaire
            # shutil.rmtree("./lightrag_storage", ignore_errors=True)

            logger.info("🗑️ RAG Premium complètement nettoyé")
            return {
                "status": "success",
                "message": "All RAG Premium data cleared successfully",
                "cleared_collections": [collection_name] if 'collection_name' in locals() else []
            }

        except Exception as e:
            logger.error(f"Error clearing RAG Premium: {e}")
            return {
                "status": "partial_success",
                "message": f"RAG clearing encountered errors: {str(e)}"
            }

    except Exception as e:
        logger.error(f"Error clearing all RAG data: {e}")
        raise HTTPException(status_code=500, detail=f"Error clearing all RAG data: {str(e)}")

@app.get("/rag/health")
async def rag_health():
    """Vérification de santé du système RAG Premium"""
    if not RAG_UNIFIED_AVAILABLE:
        return {"status": "unavailable", "message": "RAG Premium Service not available"}

    try:
        rag_service = await get_rag_premium_service()
        health = await rag_service.health_check()
        return health

    except Exception as e:
        logger.error(f"RAG health check failed: {e}")
        return {"status": "unhealthy", "error": str(e)}

# ============================================================================
# INTERNET SEARCH ENDPOINTS
# ============================================================================

class SearchRequest(BaseModel):
    query: str
    search_type: str = "general"
    max_results: int = 6
    time_range: str = "month"
    model_context: str = "32b"  # Nouveau paramètre pour l'adaptation

@app.post("/search/internet")
async def internet_search_endpoint(request: SearchRequest):
    """Recherche internet via SearXNG avec adaptation intelligente selon le modèle"""
    try:
        from tools.internet_search_tools import INTERNET_SEARCH_TOOLS

        if not request.query:
            raise HTTPException(status_code=400, detail="Query is required")

        logger.info(f"🔍 Recherche internet: '{request.query}' (modèle: {request.model_context})")

        # Utiliser l'outil approprié avec contexte du modèle
        tool_function = INTERNET_SEARCH_TOOLS['internet_search']['function']
        result = await tool_function(
            query=request.query,
            search_type=request.search_type,
            max_results=request.max_results,
            time_range=request.time_range,
            model_context=request.model_context  # Passer le contexte du modèle
        )

        return result

    except Exception as e:
        logger.error(f"❌ Erreur recherche internet: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class NewsSearchRequest(BaseModel):
    query: str
    time_range: str = "month"
    max_results: int = 6

@app.post("/search/news")
async def news_search_endpoint(request: NewsSearchRequest):
    """Recherche d'actualités via SearXNG"""
    try:
        from tools.internet_search_tools import INTERNET_SEARCH_TOOLS

        if not request.query:
            raise HTTPException(status_code=400, detail="Query is required")

        tool_function = INTERNET_SEARCH_TOOLS['news_search']['function']
        result = await tool_function(
            query=request.query,
            time_range=request.time_range,
            max_results=request.max_results
        )

        return result

    except Exception as e:
        logger.error(f"❌ Erreur recherche actualités: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class TechnicalSearchRequest(BaseModel):
    query: str
    max_results: int = 6

@app.post("/search/technical")
async def technical_search_endpoint(request: TechnicalSearchRequest):
    """Recherche technique via SearXNG"""
    try:
        from tools.internet_search_tools import INTERNET_SEARCH_TOOLS

        if not request.query:
            raise HTTPException(status_code=400, detail="Query is required")

        tool_function = INTERNET_SEARCH_TOOLS['technical_search']['function']
        result = await tool_function(
            query=request.query,
            max_results=request.max_results
        )

        return result

    except Exception as e:
        logger.error(f"❌ Erreur recherche technique: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/search/health")
async def search_health_check():
    """Vérification de santé du service de recherche internet"""
    try:
        # Réponse rapide - le service fonctionne en mode test même sans SearXNG
        return {
            "status": "available",
            "mode": "central_server",
            "searxng_url": "http://10.4.123.77:8888",
            "message": "Service de recherche via serveur central SearXNG"
        }

    except Exception as e:
        logger.error(f"❌ Erreur vérification recherche: {e}")
        return {
            "status": "error",
            "message": str(e)
        }

# ============================================================================
# SHUTDOWN
# ============================================================================

def handle_exit(signum, frame):
    logger.info(f"Received signal {signum}, shutting down gracefully")
    sys.exit(0)

signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

async def startup_event():
    """Événement de démarrage automatique"""
    try:
        from services.startup_service import startup_service
        logger.info("🚀 Démarrage automatique des services...")

        # Démarrer tous les services automatiquement
        startup_results = await startup_service.start_all_services()

        # Stocker les résultats pour les endpoints de statut
        app.state.startup_results = startup_results

    except Exception as e:
        logger.error(f"❌ Erreur lors du démarrage automatique: {e}")
        app.state.startup_results = {
            "success": False,
            "error": str(e),
            "services": {}
        }

# Ajouter l'événement de démarrage
@app.on_event("startup")
async def on_startup():
    await startup_event()

@app.get("/startup/status")
async def get_startup_status():
    """Obtenir le statut du démarrage automatique"""
    return getattr(app.state, 'startup_results', {"status": "not_started"})

if __name__ == "__main__":
    import uvicorn

    # 🚀 INITIALISATION DE LA BASE DE DONNÉES
    logger.info("🔧 Initialisation de la base de données...")
    init_database()
    logger.info("✅ Base de données initialisée avec succès")

    # 🚀 DÉMARRAGE BACKEND WEMA IA
    logger.info(f"🚀 Starting WeMa IA Backend on {HOST}:{PORT}")
    logger.info("🔧 Vérification des services centraux...")

    uvicorn.run(
        "main:app",
        host=HOST,
        port=PORT,
        log_level="info",
        reload=False,
        access_log=False  # Désactiver les logs d'accès pour éviter les erreurs Windows
    )
