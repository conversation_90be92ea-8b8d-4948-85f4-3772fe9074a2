Metadata-Version: 2.1
Name: warc3-wet
Version: 0.2.5
Summary: Python library to work with ARC and WARC files
Home-page: https://github.com/<PERSON><PERSON>-<PERSON>/warc3
Author: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
Author-email: <EMAIL>
License: GPLv2
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU General Public License v2 (GPLv2)
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Description-Content-Type: text/x-rst
License-File: LICENSE

warc3-wet: Python3 library to work with WARC and WET files
==========================================================

Note: This is a fork of the original (now dead) warc repository.

WARC (Web ARChive) is a file format for storing web crawls.

http://bibnum.bnf.fr/WARC/ 

This `warc` library makes it very easy to work with WARC files.::

    import warc
    with warc.open("test.warc") as f:
        for record in f:
            print(record['WARC-Target-URI'], record['Content-Length'])

And WET files.::

    import warc
    with warc.open("test.warc.wet") as f:
        for record in f:
            print(record['WARC-Target-URI'], record['Content-Length'])

Documentation
-------------

The documentation of the warc library is available at http://warc.readthedocs.org/.

Apart from the install from pip, which will not work for this warc3 version, the
interface as described there is unchanged.
	
License
-------------

This software is licensed under GPL v2. See LICENSE_ file for details.

.. LICENSE: http://github.com/internetarchive/warc/blob/master/LICENSE

Authors
-------------

Original Python2 Versions:

* Anand Chitipothu
* Noufal Ibrahim

Python3 Port:

* Ryan Chartier 
* Jan Pieter Bruins Slot
* Almer S. Tigelaar
* Sean MacAvaney (3.10)

WET and seek support

* Willian Zhang


Change Log
-------------
0.2.4
Python 3.10 compatibility (thanks to @seanmacavaney)

0.2.3
Support seeking in WARC/WET

0.2.2
Allow WET parse

older...
see https://github.com/internetarchive/warc
